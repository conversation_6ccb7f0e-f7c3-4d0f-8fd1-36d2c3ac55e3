<template>
  <div class="aui-card--fill">
    <div class="mod-co__inorder flex_wrap" v-show="!panelShow" ref="listPanel">
      <el-card class="search_box" shadow="never">
        <el-form ref="searchForm" class="form_no_margin" :model="dataForm" @keyup.enter.native="queryPageByParam()" label-width="80px">
          <el-row :gutter="10" type="flex">
            <el-col >
              <el-row :gutter="10">
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderShipper.shipperName')" prop="shipperName">
                    <el-input v-model="dataForm.shipperName" :placeholder="$t('coOrderShipper.shipperName')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderShipper.shipperCompany')" prop="shipperCompany">
                    <el-input v-model="dataForm.shipperCompany" :placeholder="$t('coOrderShipper.shipperCompany')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderShipper.shipperPhone')" prop="shipperPhone">
                    <el-input v-model="dataForm.shipperPhone" :placeholder="$t('coOrderShipper.shipperPhone')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderShipper.shipperEmail')" prop="shipperEmail">
                    <el-input v-model="dataForm.shipperEmail" :placeholder="$t('coOrderShipper.shipperEmail')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderShipper.shipperCountry')" prop="shipperCountry">
                    <el-input v-model="dataForm.shipperCountry" :placeholder="$t('coOrderShipper.shipperCountry')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderShipper.shipperProvince')" prop="shipperProvince">
                    <el-input v-model="dataForm.shipperProvince" :placeholder="$t('coOrderShipper.shipperProvince')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderShipper.shipperCity')" prop="shipperCity">
                    <el-input v-model="dataForm.shipperCity" :placeholder="$t('coOrderShipper.shipperCity')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderShipper.shipperDistrict')" prop="shipperDistrict">
                    <el-input v-model="dataForm.shipperDistrict" :placeholder="$t('coOrderShipper.shipperDistrict')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderShipper.shipperAddress')" prop="shipperAddress">
                    <el-input v-model="dataForm.shipperAddress" :placeholder="$t('coOrderShipper.shipperAddress')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderShipper.shipperPostcode')" prop="shipperPostcode">
                    <el-input v-model="dataForm.shipperPostcode" :placeholder="$t('coOrderShipper.shipperPostcode')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderShipper.shipperDoorplate')" prop="shipperDoorplate">
                    <el-input v-model="dataForm.shipperDoorplate" :placeholder="$t('coOrderShipper.shipperDoorplate')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderShipper.shipperStreet')" prop="shipperStreet">
                    <el-input v-model="dataForm.shipperStreet" :placeholder="$t('coOrderShipper.shipperStreet')" clearable ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <div class="search_box_btn no_more">
              <el-button type="primary" @click="queryPageByParam()" icon="el-icon-search">查询</el-button>
              <el-button @click.native="_resetForm('searchForm')" icon="el-icon-refresh-right">重置</el-button>
            </div>
          </el-row>
        </el-form>
      </el-card>

      <el-card class="flex_tab no_shadow" type="border-card" >
        <el-row class="optBtn_panel">
          <el-col :md="12" class='optBtn_leftFixed'>
            <el-button size="mini" @click="deleteHandle()">{{ $t('delete') }}</el-button>
            <el-button size="mini" >{{ $t('audit') }}</el-button>
            <!--保留空格符-->
          </el-col>
          <el-col :md="12" class="text-right">
            <el-button size="mini"  v-if="$hasPermission('co:ordershipper:save')" plain @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
          </el-col>
        </el-row>
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" :max-height="tableHeight">
            <!-- 动态显示表格 -->
            <el-table-column type="selection" width='50' fixed="left"></el-table-column>
            <el-table-column v-for="(item, index) in tableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
              <template slot-scope="scope">
                <div>
                  <div v-if="item.prop === 'createDate'">
                    <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                      <span>{{formatterFn(scope,item.prop)}}</span>
                    </el-tooltip>
                  </div>
                  <div v-else>
                    {{formatterFn(scope,item.prop)}}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="150">
              <template slot="header" slot-scope="scope">
                <span>{{$t('handle')}}</span>
                <el-tooltip :content="$t('tableConfigTooltip')" placement="top">
                  <el-link style="margin-left:10px; font-size:16px" class="el-icon-setting" @click="settingCol(tableColumns)"></el-link>
                </el-tooltip>
              </template>
              <template slot-scope="scope">
                <el-link :underline="false"  @click="viewHandle(scope.row.id)">{{ $t('view') }}</el-link>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination background :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle" @current-change="pageCurrentChangeHandle">
        </el-pagination>
      </el-card>
    </div>
    <!-- 自定义列显示弹窗 -->
    <table-set ref="tableSet"></table-set>
    <!-- 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList" @cancelAddOrUpdate="cancelAddOrUpdate"></add-or-update>
    <!-- 详情页面 -->
    <viewDetail v-if="viewVisible" ref="viewDetail" @backView="backView"></viewDetail>
  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import listPage from '@/mixins/listPage'
import { formatterType, gtmToLtm, timestampFormat } from '@/filters/filters'
import dictTypeMixins from '@/mixins/dictTypeMixins'
// table 自定义显示
import tableSet from '@/components/tableSet'
import AddOrUpdate from './order-shipper-add-or-update'
import ViewDetail from './order-shipper-view-detail'
export default {
  mixins: [listPage, mixinViewModule, dictTypeMixins],
  data () {
    return {
      tableColumns: [
        { type: '', width: '150', prop: 'shipperName', label: this.$t('coOrderShipper.shipperName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'shipperCompany', label: this.$t('coOrderShipper.shipperCompany'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'shipperPhone', label: this.$t('coOrderShipper.shipperPhone'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'shipperEmail', label: this.$t('coOrderShipper.shipperEmail'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'shipperCountry', label: this.$t('coOrderShipper.shipperCountry'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'shipperProvince', label: this.$t('coOrderShipper.shipperProvince'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'shipperCity', label: this.$t('coOrderShipper.shipperCity'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'shipperDistrict', label: this.$t('coOrderShipper.shipperDistrict'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'shipperAddress', label: this.$t('coOrderShipper.shipperAddress'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'shipperPostcode', label: this.$t('coOrderShipper.shipperPostcode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'shipperDoorplate', label: this.$t('coOrderShipper.shipperDoorplate'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'shipperStreet', label: this.$t('coOrderShipper.shipperStreet'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'id', label: this.$t('system.id'), align: 'center', isShow: true, disabled: false }
      ],
      mixinViewModuleOptions: {
        getDataListURL: '/co/ordershipper/page',
        getDataListIsPage: true,
        deleteURL: '/co/ordershipper',
        deleteIsBatch: true
      },
      dataForm: {
        id: ''
      },
      activeName: 'all',
      tableName: 'co-ordershipper'
    }
  },
  created () {
  },
  methods: {
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'createDate':
          value = timestampFormat(gtmToLtm(scope.row.createDate), 'YYYY-MM-DD')
          break
        case 'status':
          value = formatterType(scope.row.status, this.statusList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    // 自定义列显示
    settingCol (tableColumns) {
      this.$nextTick(() => {
        this.$refs['tableSet'].init(this.tableName, tableColumns)
      })
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat
  },
  computed: {
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    panelShow () {
      let ret = false
      if (this.addOrUpdateVisible) {
        ret = true
      } else if (this.viewVisible) {
        ret = true
      }
      return ret
    }
  },
  components: {
    AddOrUpdate,
    ViewDetail,
    tableSet
  }
}
</script>
