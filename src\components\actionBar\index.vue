<template>
  <div :style="isInline">
    <el-button
      v-show="showBtnList && showBtnList.length"
      v-for="(obt, index) in showBtnList"
      :key="index"
      v-if="obt.hasPermission"
      type="text"
      size="small"
      :class="{disabled: !!obt.disabled}"
      @click="$_onBtnClick($event, obt)"
    >{{ $t(obt.name) }}</el-button>
    <el-popover v-if="hideBtnList && hideBtnList.length" class="cs_moreBtn" popper-class="cs_poper_class" v-model="visible">
      <div class="cs_more_btn_view" v-if="hideBtnList && hideBtnList.length" ref="cs_popover">
        <el-button
          v-for="(obt, index) in hideBtnList"
          :key="index"
          v-if="obt.hasPermission"
          type="text"
          size="mini"
          :class="{disabled: !!obt.disabled}"
          @click="$_onBtnClick($event, obt)"
          :disabled="obt.disabled"
        >{{ $t(obt.name) }}</el-button>
      </div>
      <i slot="reference" class="el-icon-arrow-down icon_down" @click="visible = !visible"></i>
    </el-popover>
  </div>
</template>
<script>
export default {
  props: {
    actions: {
      type: Array,
      default: () => {
        return []
      }
    },
    dataP: {},
    trigger: {
      type: String,
      default: 'hover'
    },
    inline: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      visible: false
    }
  },
  computed: {
    isInline () {
      return this.inline ? 'display:inline' : ''
    },
    showBtnList () {
      let temArr = this.actions.filter(item => {
        return item.isShow
      })
      return temArr
    },
    hideBtnList () {
      let temArr = this.actions.filter(item => {
        return !item.isShow && item.hasPermission
      })
      return temArr
    }
  },
  methods: {
    // MARK: events handler
    $_onBtnClick (event, action) {
      if (action.disabled) {
        return
      }
      action.onClick && action.onClick(this.dataP, event, action)
      this.$emit('click', this.dataP, event, action)
      this.visible = false
    }
  }
}
</script>
<style lang="scss">
  .cs_more_btn_view{
    .el-button{
      display: block;
      width: 100%;
      margin-left: 0 !important;
      overflow: hidden;
    }
  }
</style>
