<template>
  <div class="aui-card--fill">
    <div class="mod-co__inorder flex_wrap" ref="listPanel">
      <el-card class="search_box" shadow="never">
        <el-form ref="searchForm" class="form_no_margin" :model="dataForm" @keyup.enter.native="queryPageByParam()" label-width="120px">
          <el-row :gutter="10" type="flex">
            <el-col >
              <el-row :gutter="10">
                <el-col :sm="24" :md="8">
                <el-form-item :label="$t('baReceivableBill.id')" prop="id">
                  <el-input v-model="dataForm.id" :placeholder="$t('baReceivableBill.id')" clearable @change="checkBillId()"/>
                </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('baReceivableBill.billDeadline')" prop="billDeadline">
                    <el-date-picker class="w-percent-100" v-model="billDeadlineDateArr" type="datetimerange"
                                    :start-placeholder="$t('datePicker.start')"
                                    :end-placeholder="$t('datePicker.end')"
                                    :default-time="['00:00:00', '23:59:59']">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('baReceivableBill.closeDate')" prop="closeDate">
                    <el-date-picker class="w-percent-100" v-model="closeDateArr" type="datetimerange"
                                    :start-placeholder="$t('datePicker.start')"
                                    :end-placeholder="$t('datePicker.end')"
                                    :default-time="['00:00:00', '23:59:59']">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <div class="search_box_btn no_more">
              <el-button type="primary" @click="queryPageByParam()" icon="el-icon-search">查询</el-button>
              <el-button @click.native="_resetForm('searchForm')" icon="el-icon-refresh-right">重置</el-button>
            </div>
          </el-row>
        </el-form>
      </el-card>

      <el-card class="flex_tab no_shadow" type="border-card" >
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <el-table v-loading="dataListLoading" :data="dataList" :max-height="tableHeight">
            <!-- 动态显示表格 -->
            <el-table-column v-for="(item, index) in tableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
              <template slot-scope="scope">
                <div>
                  <div v-if="item.prop === 'createDate'">
                    <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                      <span>{{formatterFn(scope,item.prop)}}</span>
                    </el-tooltip>
                  </div>
                  <div v-else-if="item.prop === 'orderSum'">
                    <div v-for="(item, index) in scope.row.orderSum" :key="index" >
                      <span>{{ item.sum | numberFormat(2)}}</span>
                      <span class="order-sum">{{item.currency }}</span>
                    </div>
                  </div>
                  <!--<div v-else-if="item.prop === 'id'">
                    <el-link style="color: #3a8ee6" :underline="false" @click="viewHandle(scope.row.id)">{{scope.row.id}}</el-link>
                  </div>-->
                  <div v-else>
                    {{formatterFn(scope,item.prop)}}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="150">
              <template slot="header" slot-scope="scope">
                <span>{{$t('handle')}}</span>
                <el-tooltip :content="$t('tableConfigTooltip')" placement="top">
                  <el-link style="margin-left:10px; font-size:16px" class="el-icon-setting" @click="settingCol(tableColumns)"/>
                </el-tooltip>
              </template>
              <template slot-scope="scope">
                <el-link :underline="false" v-if="$hasPermission('bd:receivablebill:approved') && scope.row.status === 20" @click="approved(scope.row.id)">{{ $t('baReceivableFee.approved') }}</el-link>
                <el-link :underline="false" v-if="$hasPermission('bd:receivablebill:noapproved') && scope.row.status === 20" @click="noApprove(scope.row.id)">{{ $t('baReceivableFee.no_approved') }}</el-link>
                <el-link :underline="false" v-if="$hasPermission('bd:receivablebill:downloadFile')" :disabled="scope.row.fileUrl === null" @click="downloadBill(scope.row.fileUrl)">{{ $t('baReceivableBill.downloadFile') }}</el-link>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination background :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle" @current-change="pageCurrentChangeHandle">
        </el-pagination>
      </el-card>
    </div>
    <!-- 自定义列显示弹窗 -->
    <table-set ref="tableSet"/>
  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import listPage from '@/mixins/listPage'
import { formatterType, gtmToLtm, timestampFormat, numberFormat } from '@/filters/filters'
import dictTypeMixins from '@/mixins/dictTypeMixins'
import { isInteger } from '@/utils/validate'
// table 自定义显示
import tableSet from '@/components/tableSet'
export default {
  mixins: [listPage, mixinViewModule, dictTypeMixins],
  data () {
    return {
      tableColumns: [
        { type: '', width: '160', prop: 'id', label: this.$t('baReceivableBill.id'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'orderSum', label: this.$t('baReceivableBill.totalSum'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'billDeadline', label: this.$t('baReceivableBill.billDeadline'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'status', label: this.$t('system.status'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'remark', label: this.$t('baReceivableBill.remark'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'closeDate', label: this.$t('baReceivableBill.closeDate'), align: 'center', isShow: true, disabled: false }
      ],
      mixinViewModuleOptions: {
        getDataListURL: '/ba/receivablebill/page',
        getDataListIsPage: true
      },
      dataForm: {
        id: '',
        settlementObjectId: this.$store.state.user.customerId,
        settlementObjectType: 0,
        closeBeginDate: '',
        closeEndDate: '',
        billDeadlineBeginDate: '',
        billDeadlineEndDate: ''
      },
      changeStatusForm: {
        id: ''
      },
      closeDateArr: [],
      billDeadlineDateArr: [],
      deleted: false,
      audited: false,
      activeName: 'all',
      receivableBillStatusList: [],
      tableName: 'ba-receivablebill'
    }
  },
  created () {
    this.getDictTypeList('receivableBillStatus').then(res => {
      this.receivableBillStatusList = res
    })
  },
  methods: {
    checkBillId () {
      if (this.dataForm.id !== null && this.dataForm.id !== '' && this.dataForm.id !== undefined) {
        if (!isInteger(this.dataForm.id)) {
          this.dataForm.id = ''
          return this.$message.error(this.$t('validate.format', { 'attr': this.$t('baReceivableBill.id') }))
        }
      }
    },
    downloadBill (fileUrl) {
      window.location.href = fileUrl
    },
    noApprove (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: this.$t('prompt.auditBatch'),
          type: 'warning',
          duration: 1000
        })
      }
      this.$confirm(this.$t('prompt.info', { 'handle': this.$t('baReceivableFee.no_approved') }), this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        this.$http['get'](`/ba/receivablebill/noApprove/${id}`).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.queryPageByParam()
            }
          })
        }).catch(() => {})
      }).catch(() => {})
    },
    approved (id) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: this.$t('prompt.auditBatch'),
          type: 'warning',
          duration: 1000
        })
      }
      this.$confirm(this.$t('prompt.info', { 'handle': this.$t('baReceivableFee.approved') }), this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        this.$http['get'](`/ba/receivablebill/approved/${id}`).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.queryPageByParam()
            }
          })
        }).catch(() => {})
      }).catch(() => {})
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      if (prop === 'createDate') {
        value = timestampFormat(gtmToLtm(scope.row.createDate), 'YYYY-MM-DD')
      } else if (prop === 'status') {
        value = formatterType(scope.row.status, this.receivableBillStatusList)
      } else {
        value = scope.row[prop]
      }
      return value
    },
    // 自定义列显示
    settingCol (tableColumns) {
      this.$nextTick(() => {
        this.$refs['tableSet'].init(this.tableName, tableColumns)
      })
    }
  },
  watch: {
    closeDateArr: {
      handler (newVal, oldVal) {
        if (newVal !== null) {
          this.dataForm.closeBeginDate = newVal[0]
          this.dataForm.closeEndDate = newVal[1]
          return
        }
        this.dataForm.closeBeginDate = this.dataForm.closeEndDate = ''
      },
      deep: true
    },
    billDeadlineDateArr: {
      handler (newVal, oldVal) {
        if (newVal !== null) {
          this.dataForm.billDeadlineBeginDate = newVal[0]
          this.dataForm.billDeadlineEndDate = newVal[1]
          return
        }
        this.dataForm.billDeadlineBeginDate = this.dataForm.billDeadlineEndDate = ''
      },
      deep: true
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat,
    numberFormat
  },
  computed: {
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    }
  },
  components: {
    tableSet
  }
}
</script>
<style lang="scss">
  .order-sum {
    font-size:8px;
    transform: translateY(2px) scale(0.8);
    display: inline-block;
  }
</style>
