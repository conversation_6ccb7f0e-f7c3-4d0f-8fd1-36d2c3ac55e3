<template>
  <el-card shadow="never" class="tksTrack-panel">
    <el-row :gutter="10" type="flex">
     <el-col :sm="12">
       <el-input type="textarea" class="margin_bottom15" v-model="nos" :autosize="{ minRows: 5, maxRows: 20}" :placeholder="$t(lang.tksTracking.waybillNo)" @input="setNo"></el-input>
<!--       <el-row :gutter="20" type="flex" justify="space-between">-->
<!--       </el-row>-->
     </el-col>
      <el-col :sm="6" class="text-center">
        <el-button type="primary" v-if="$hasPermission('csm:tksTracking:tksCN')" :loading="loadingFlag" @click="getTrackInfo('tksCN')">{{$t(lang.tksTracking.cnSearch)}}</el-button>
        <el-button type="primary" v-if="$hasPermission('csm:tksTracking:tksEN')" :loading="loadingFlag" @click="getTrackInfo('tksEN')">{{$t(lang.tksTracking.enSearch)}}</el-button>
      </el-col>
    </el-row>
    <h2 class="margin_bottom15">
      {{$t(lang.tksTracking.result)}}： {{$t(lang.tksTracking.normal)}} （{{result.normalCount}}），<span class="danger">{{$t(lang.tksTracking.abnormal)}}（{{result.abnormalCount}}）</span>
    </h2>
    <el-divider />
    <splitpanes class="default-theme list_panel_400" vertical>
      <pane size="45">
        <el-row :gutter="20" class="panel_box">
         <el-col :span="22" :offset="1">
           <div  class="listPanel panel-right" :class="[trackActive === index? 'actived':'',index === 0 ? 'firstCard' :  '']" v-for="(item,index) in result.list" :key="index" @click="toggleQueryNO(item, index)">
             <el-card shadow="hover" :body-style="{ padding: '20px', marginTop: index === 0 ? '5px': '0px' }">
               <h3 class="fontSize16">
                 <span>{{$t(lang.tksTracking.queryNO)}}：{{item.waybillNo}}</span>
                 <span style='float: right'>{{$t(lang.tksTracking.destinationCountry)}}：{{item.destinationCountry | formatterCodeName(countryList, cnEnFlag)}}</span>
               </h3>
               <p class="fontSize14">
                 {{$t(lang.tksTracking.btyNo)}}:{{item.waybillNo}}<br />
                 <span>{{$t(lang.tksTracking.trackingNO)}}:{{item.subDeliveryNo || item.deliveryNo}}</span><br />
                 <span v-show='item.postalTrackingNo'>{{$t(lang.tksTracking.postalTrackingNo)}}:{{item.postalTrackingNo}}</span>
               </p>
               <p class="fontSize14">
                 {{!item.tksTrackingDetailDTOList || 1 > item.tksTrackingDetailDTOList.length ? '' : item.tksTrackingDetailDTOList[0].trackingLocation}} {{!item.tksTrackingDetailDTOList || 1 > item.tksTrackingDetailDTOList.length ? '' : cnEnFlag === 'tksCN' ? item.tksTrackingDetailDTOList[0].trackingContentCn : item.tksTrackingDetailDTOList[0].trackingContentEn  }}<br/>
                 {{!item.tksTrackingDetailDTOList || 1 > item.tksTrackingDetailDTOList.length ? '' : item.tksTrackingDetailDTOList[0].trackingTime}}
               </p>
               <p class="fontSize14" v-if="item.collectPackageTimeliness || item.deliveryPackageTimeliness">
                 <span v-if='item.collectPackageTimeliness'>{{$t(lang.tksTracking.collectPackageTimeliness)}}：{{item.collectPackageTimeliness}}&nbsp;&nbsp;{{lang.tksTracking.day}}</span><br />
                 <span v-if='item.deliveryPackageTimeliness'>{{$t(lang.tksTracking.deliveryPackageTimeliness)}}：{{ item.deliveryPackageTimeliness}}&nbsp;&nbsp;{{lang.tksTracking.day}}</span>
               </p>
             </el-card>
             <el-divider/>
           </div>
         </el-col>
        </el-row>

      </pane>
      <pane size="55">
        <transition name="on"  mode="out-in">
        <div v-if="trackActivating" >
        <el-row :gutter="10" v-show='clickItem'>
         <el-col :sm="22" :offset="1">
           <div class="margin_bottom22">
             <h3 class="font-size18">
               <span key="s1" v-show='clickItem'>{{$t(lang.tksTracking.logisticsCompanyTrackNo)}}:{{clickItem ? clickItem.waybillNo : ''}}</span>
               <span style="padding-left: 30px" v-show="clickItem">{{$t(lang.tksTracking.trackingNumber)}}:{{clickItem ? clickItem.subDeliveryNo || clickItem.deliveryNo : ''}}</span>
               <span key="s2" style='float: right' v-show='clickItem'>{{clickItem && clickItem.destinationCountry | formatterCodeName(countryList, cnEnFlag)}}</span>
             </h3>
             <p v-show='clickItem'>
               <span key="s3" class="font-size14 danger">{{$t(lang.tksTracking.trackDescription)}}</span>
             </p>
           </div>
           <div class="timeline-box block">
             <el-timeline>
               <el-timeline-item
                 v-for="(activity, index) in activities"
                 :key="index"
                 :icon="activity.icon"
                 :type="activity.type"
                 :color="activity.color"
                 :size="activity.size"
                 :timestamp="activity.timestamp">
                 {{activity.content}}
               </el-timeline-item>
             </el-timeline>
           </div>
         </el-col>
        </el-row>
        </div>
        </transition>
      </pane>
    </splitpanes>
  </el-card>
</template>

<script>
import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'
import tksCN from '@/i18n/tks-CN'
import tksEN from '@/i18n/tks-en-US'
import comMixins from '@/mixins/comMixins'
import dictTypeMixins from '@/mixins/dictTypeMixins'
export default {
  mixins: [ comMixins, dictTypeMixins ],
  data () {
    return {
      comMixins: {
        isGetCountryList: true
      },
      nos: '',
      loadingFlag: false,
      // 查询单号个数限制
      noSizeInner: 100,
      // 查询结果
      result: {
        // 查询正常条数
        normalCount: 0,
        // 查询异常条数
        abnormalCount: 0,
        // 轨迹信息
        list: []
      },
      lang: tksCN,
      trackActive: 0,
      trackActivating: true,
      // 当前点击的item
      curItem: null,
      // 中英文标识--便于数据转换 tksCN:中文,tksEN:英文
      cnEnFlag: 'tksCN',
      countryList: [],
      activities: []
    }
  },
  filters: {
    formatterCodeName: (value, list, cnEnFlag) => {
      if (cnEnFlag === 'tksEN') {
        return value
      }
      let name = ''
      if (!list) {
        console.warn('formatterName filtes must be a list in the parameter')
        return
      }
      list.forEach(object => {
        if (object.code === value) {
          name = object.name
          return false
        }
      })
      return name
    },
    formatterStatus: (value, list, cnEnFlag) => {
      let name = ''
      if (!list) {
        console.warn('formatterName filtes must be a list in the parameter')
        return
      }
      list.forEach(object => {
        if (object.dictValue === value) {
          name = cnEnFlag === 'tksCN' ? object.dictName : object.dictNameEn
          return false
        }
      })
      return name
    }
  },
  computed: {
    // 点击查询结果的当前item
    clickItem: {
      get () {
        return this.curItem
      },
      set (val) {
        this.activities = []
        // 加载明细
        if (!val || !val.tksTrackingDetailDTOList) {
          return this.activities
        } else {
          for (var i = 0; i < val.tksTrackingDetailDTOList.length; i++) {
            let data
            // 轨迹节点中英文切换
            let trackingContent = this.cnEnFlag === 'tksCN' ? val.tksTrackingDetailDTOList[i].trackingContentCn : val.tksTrackingDetailDTOList[i].trackingContentEn
            // 轨迹时间
            let trackingTime = val.tksTrackingDetailDTOList[i].trackingTime
            // 轨迹内容
            let content = val.tksTrackingDetailDTOList[i].trackingLocation ? '[' + val.tksTrackingDetailDTOList[i].trackingLocation + '] ' + trackingContent : trackingContent
            switch (i) {
              case 0 :
                data = {
                  content: content,
                  timestamp: trackingTime,
                  size: 'large',
                  type: 'primary',
                  icon: 'el-icon-more'
                }
                break
              case 1 :
                data = {
                  content: content,
                  timestamp: trackingTime,
                  color: '#0bbd87'
                }
                break
              case 2 :
                data = {
                  content: content,
                  timestamp: trackingTime,
                  size: 'large'
                }
                break
              default :
                data = {
                  content: content,
                  timestamp: trackingTime
                }
                break
            }
            this.activities.push(data)
          }
        }
      }
    }
  },
  created () {
  },
  methods: {
    // 获取轨迹信息
    getTrackInfo (val) {
      this.cnEnFlag = val
      this.lang = val === 'tksCN' ? tksCN : tksEN
      this.loadingFlag = true
      this.trackActivating = true
      if (!this.nos) {
        this.loadingFlag = false
        this.result.normalCount = 0
        this.result.abnormalCount = 0
        this.result.list = []
        this.clickItem = []
        this.curItem = ''
        return
      }
      if (this.nos.split(',').length > this.noSizeInner) {
        this.$message.warning(this.$t('threeNoInput.outMessage', { 'size': `${this.noSizeInner}` }))
        this.loadingFlag = false
        return
      }
      this.$http.get('/tks/trackingwaybill/getTrackInfo', { params: { nos: this.nos, internalFlag: 0 } }).then(({ data: res }) => {
        if (res.code === 0 && res.data) {
          this.result.normalCount = res.data.normalCount
          this.result.abnormalCount = res.data.abnormalCount
          this.result.list = res.data.list
          // 默认第一条
          this.clickItem = this.curItem = !res.data.list || res.data.list.length < 1 ? null : res.data.list[0]
        }
        this.loadingFlag = false
      }).catch(() => {})
    },
    toggleQueryNO (item, index) {
      this.trackActivating = false
      this.trackActive = index
      // 点击div获取的当前item进行加载右边明细
      this.clickItem = this.curItem = item
      this.$nextTick(() => {
        this.trackActivating = true
      })
    },
    // 将单号转换为,隔开的格式
    setNo () {
      if (!this.nos) {
        return
      }
      this.nos = this.nos.replace(/\n|\s+/g, ',').trim()
    }
  },
  components: {
    Splitpanes,
    Pane
  }
}
</script>
<style lang="scss" scoped>
.margin_left20 {
  margin-left: 20px;
}
::v-deep .el-card.is-always-shadow, .el-card.is-hover-shadow:focus, .el-card.is-hover-shadow:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .65) !important;
}
.firstCard{
  margin-top: 10px;
}
.on-enter-active{
  transition: 2s ease;
}
.on-leave-active{
  transition: 3s ease;
}
.on-enter,.on-leave-to{
  opacity: 0;
}
</style>

<style lang="scss">
  .panel_box{
    max-height: calc(calc(100vh - 38px - 30px - 48px) - 2px);
    overflow: hidden;
    overflow-y: auto;
  }
  .listPanel{
    margin-bottom: 5px;
    &.actived{
      .el-card{
        background: $--color-primary;
        color: white;
        font-weight: bolder;
      }
    }
    .el-divider--horizontal {
      margin: 5px 0 !important;
    }
  }
  .tksTrack-panel{
    .timeline-box{
      margin-left: 102px;
    }
    .el-timeline-item__content{
      padding-top: 4px;
    }
    .el-timeline-item__timestamp {
      transform: translate(-170px, -22px);
    }
  }
</style>
