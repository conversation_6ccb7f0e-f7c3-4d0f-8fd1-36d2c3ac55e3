<template>
  <div class="modern-register-container">
    <!-- 错误状态页面 -->
    <div class="error-wrapper" v-if="!isValidateUrl">
      <div class="error-content">
        <div class="error-icon">
          <i class="el-icon-loading"></i>
        </div>
        <h2 class="error-message">{{ validateMsg }}</h2>
      </div>
    </div>

    <!-- 主注册页面 -->
    <div class="register-wrapper" v-if="isValidateUrl">
      <div class="register-card">
        <!-- 头部品牌区域 -->
        <div class="brand-header">
          <div class="brand-logo">
            <i class="el-icon-office-building"></i>
          </div>
          <h1 class="brand-name">{{ dataForm.companyName }}</h1>
        </div>

        <!-- 成功注册状态 -->
        <div v-if="isSaved" class="success-content">
          <div class="success-icon">
            <i class="el-icon-circle-check"></i>
          </div>
          <h2 class="success-title">注册成功！</h2>
          <p class="success-message">
            {{ dataForm.name }}，您好！您已成功注册，我们的客服会尽快联系您并审核。
          </p>
          <p class="success-thanks">谢谢！</p>
        </div>

        <!-- 注册表单 -->
        <div v-else class="form-content">
          <div class="form-header">
            <h2 class="form-title">会员注册</h2>
            <p class="form-subtitle">创建您的账户，开始您的商务之旅</p>
          </div>

          <el-form
            :model="dataForm"
            :rules="dataRule"
            ref="dataForm"
            @keyup.enter.native="dataFormSubmitHandle()"
            status-icon
            size="large"
            class="modern-form"
            label-width="120px"
            label-position="left"
          >
            <el-form-item label="公司名称" prop="name" class="form-item">
              <el-input
                ref="username"
                v-model="dataForm.name"
                :placeholder="$t('bdCustomerRegister.name')"
                class="modern-input"
              >
                <i slot="prefix" class="el-icon-office-building input-icon"></i>
              </el-input>
            </el-form-item>

            <el-form-item :label="$t('bdCustomerRegister.phone')" prop="phone" class="form-item">
              <el-input
                ref="phone"
                v-model="dataForm.phone"
                placeholder="请填写手机号码"
                class="modern-input"
              >
                <i slot="prefix" class="el-icon-phone input-icon"></i>
              </el-input>
            </el-form-item>

            <el-form-item :label="$t('bdCustomerRegister.email')" prop="email" class="form-item">
              <el-input
                ref="email"
                v-model="dataForm.email"
                placeholder="用于接收业务相关的通知，请正确填写"
                class="modern-input"
              >
                <i slot="prefix" class="el-icon-message input-icon"></i>
              </el-input>
            </el-form-item>

            <el-form-item :label="$t('bdCustomerRegister.password')" prop="password" class="form-item">
              <el-input
                type="password"
                ref="password"
                v-model="dataForm.password"
                placeholder="密码长度不能小于 6 位"
                class="modern-input"
                show-password
              >
                <i slot="prefix" class="el-icon-lock input-icon"></i>
              </el-input>
            </el-form-item>

            <el-form-item :label="$t('bdCustomerRegister.confirmPassword')" prop="confirmPassword" class="form-item">
              <el-input
                type="password"
                ref="confirmPassword"
                v-model="dataForm.confirmPassword"
                placeholder="请再次输入密码"
                class="modern-input"
                show-password
              >
                <i slot="prefix" class="el-icon-lock input-icon"></i>
              </el-input>
            </el-form-item>

            <el-form-item label="验证码" prop="captcha" class="captcha-item">
              <div class="captcha-wrapper">
                <el-input
                  ref="captcha"
                  v-model="dataForm.captcha"
                  :placeholder="$t('login.captcha')"
                  class="captcha-input"
                >
                  <i slot="prefix" class="el-icon-key input-icon"></i>
                </el-input>
                <div class="captcha-image-wrapper" @click="getCaptcha()">
                  <img :src="captchaPath" class="captcha-image" alt="验证码"/>
                  <div class="captcha-refresh">
                    <i class="el-icon-refresh"></i>
                  </div>
                </div>
              </div>
            </el-form-item>

            <el-form-item class="submit-item">
              <el-button
                type="primary"
                @click="dataFormSubmitHandle()"
                :loading="saveLoading"
                class="submit-button"
                size="large"
              >
                <span v-if="!saveLoading">{{ $t('submit') }}</span>
                <span v-else>注册中...</span>
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 底部链接 -->
        <div class="footer-links">
          <el-link
            icon="el-icon-back"
            type="primary"
            @click="gotoLoginPage()"
            class="back-link"
          >
            返回首页
          </el-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import debounce from 'lodash/debounce'
import dictTypeMixins from '@/mixins/dictTypeMixins'
import { isEmail } from '@/utils/validate'
import { getUUID } from '@/utils'
export default {
  mixins: [dictTypeMixins],
  data () {
    return {
      isSaved: false,
      saveLoading: false,
      captchaPath: '',
      dataForm: {
        name: '',
        phone: '',
        verificationCode: '',
        email: '',
        password: '',
        uuid: '',
        captcha: ''
      },
      url: '',
      params: {},
      isValidateUrl: false,
      validateMsg: '',
      loading: false
    }
  },
  computed: {
    dataRule () {
      const validateName = (rule, value, callback) => {
        if (this.dataForm.name && this.dataForm.name.length < 2) {
          return callback(new Error('公司名称（或个人姓名）长度不能小于 2 位'))
        }
        callback()
      }
      const validatePassword = (rule, value, callback) => {
        if (this.dataForm.password && this.dataForm.password.length < 6) {
          return callback(new Error('密码长度不能小于 6 位'))
        }
        callback()
      }
      const isMobileValidtor = (rule, value, callback) => {
        // 考虑海外的手机，取消该验证
        // if (!is86Mobile(this.dataForm.phone)) {
        //   return callback(new Error('请填写正确的手机号码'))
        // }
        callback()
      }
      const isEmailValidtor = (rule, value, callback) => {
        if (!isEmail(this.dataForm.email)) {
          return callback(new Error('请填写正确的邮箱格式'))
        }
        callback()
      }
      var validateconfirmPassword = (rule, value, callback) => {
        if (this.dataForm.password !== value) {
          return callback(new Error('确认密码与密码输入不一致'))
        }
        callback()
      }
      return {
        name: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: validateName, trigger: 'blur' }
        ],
        phone: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isMobileValidtor, trigger: 'blur' }
        ],
        verificationCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        email: [
          { validator: isEmailValidtor, trigger: 'blur' }
        ],
        captcha: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        password: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: validatePassword, trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: validateconfirmPassword, trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    Promise.all([this.getByCompany(), this.getCaptcha()]).then(() => {})
  },
  methods: {
    getByCompany() {
      // 获取公司登录信息
      const host = window.location.host
      // this.pageShow = true
      this.$http
        .get('/sys/website/getByDomain/' + host)
        .then(({ data: res }) => {
          // this.pageShow = false
          if (res.code !== 0) {
            this.validateMsg = res.msg
            return
          }
          this.companyInfo = res.data || {}
          this.dataForm.companyId = res.data.companyId
          this.dataForm.companyName = res.data.companyName
          this.isValidateUrl = true
        })
        .catch(() => {
          // this.pageShow = false
          this.validateMsg = '系统访问出错'
        })
    },
    // 获取验证码
    getCaptcha() {
      this.dataForm.uuid = getUUID()
      this.captchaPath = `${this.$baseUrl}/cs/auth/captcha?uuid=${this.dataForm.uuid}`
    },
    validatedPassword (firstInput, secondInput) {
      if (firstInput === secondInput) {
        this.dataForm.newPassword = firstInput
        return true
      }
      return false
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.saveLoading = true
        this.$http.post('/bd/customerregister', this.dataForm).then(({ data: res }) => {
          this.id = ''
          if (res.code !== 0) {
            this.getCaptcha()
            return this.$message.error(res.msg)
          }
          // 显示注册成功信息
          this.isSaved = true
        }).catch(() => {
        }).finally(() => {
          this.saveLoading = false
        })
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    gotoLoginPage () {
      this.$router.push({ name: 'login' })
    }
  }
}
</script>
<style lang="scss" scoped>
.modern-register-container {
  min-height: 100vh;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.error-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
}

.error-content {
  text-align: center;
  background: #ffffff;
  padding: 50px 40px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8eaec;

  .error-icon {
    font-size: 48px;
    color: #6cccf5;
    margin-bottom: 16px;
  }

  .error-message {
    color: #333;
    font-size: 18px;
    font-weight: 500;
    margin: 0;
    line-height: 1.5;
  }
}

.register-wrapper {
  width: 100%;
  max-width: 520px;
}

.register-card {
  background: #ffffff;
  border-radius: 8px;
  padding: 48px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8eaec;
}

.brand-header {
  text-align: center;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;

  .brand-logo {
    width: 64px;
    height: 64px;
    background: #409eff;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;

    i {
      font-size: 28px;
      color: white;
    }
  }

  .brand-name {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    margin: 0;
    letter-spacing: 0.5px;
  }
}

.success-content {
  text-align: center;
  padding: 40px 20px;

  .success-icon {
    font-size: 64px;
    color: #67c23a;
    margin-bottom: 24px;
  }

  .success-title {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 16px;
  }

  .success-message {
    font-size: 16px;
    color: #606266;
    line-height: 1.6;
    margin-bottom: 12px;
  }

  .success-thanks {
    font-size: 16px;
    font-weight: 500;
    color: #67c23a;
    margin: 0;
  }
}

.form-content {
  .form-header {
    text-align: center;
    margin-bottom: 32px;

    .form-title {
      font-size: 24px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 8px;
    }

    .form-subtitle {
      font-size: 14px;
      color: #909399;
      margin: 0;
    }
  }
}

.modern-form {
  .form-item {
    margin-bottom: 18px;

    ::v-deep .el-form-item__label {
      font-weight: 500;
      color: #606266;
      font-size: 14px;
      line-height: 40px;
      text-align: right;
      padding-right: 12px;
    }

    ::v-deep .el-form-item__content {
      line-height: 40px;
    }

    .modern-input {
      ::v-deep .el-input__inner {
        height: 40px;
        border-radius: 4px;
        border: 1px solid #dcdfe6;
        font-size: 14px;
        padding-left: 40px;
        transition: border-color 0.2s ease;
        background: #ffffff;

        &:focus {
          border-color: #409eff;
          outline: none;
        }

        &:hover {
          border-color: #c0c4cc;
        }

        &::placeholder {
          color: #c0c4cc;
          font-size: 14px;
        }
      }

      .input-icon {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #c0c4cc;
        font-size: 16px;
        z-index: 10;
      }
    }
  }

  .captcha-item {

    ::v-deep .el-form-item__label {
        font-weight: 500;
        color: #606266;
        font-size: 14px;
        line-height: 40px;
        text-align: right;
        padding-right: 12px;
      }

    ::v-deep .el-input__inner {
        height: 40px;
        border-radius: 4px;
        border: 1px solid #dcdfe6;
        font-size: 14px;
        padding-left: 40px;
        transition: border-color 0.2s ease;
        background: #ffffff;

        &:focus {
          border-color: #409eff;
          outline: none;
        }

        &:hover {
          border-color: #c0c4cc;
        }

        &::placeholder {
          color: #c0c4cc;
          font-size: 14px;
        }
    }

    .captcha-wrapper {
      display: flex;
      gap: 12px;
      align-items: center;

      .captcha-input {
        flex: 1;
        max-width: 200px;
        height: 40px;
      }

      .captcha-image-wrapper {
        position: relative;
        cursor: pointer;
        border-radius: 4px;
        overflow: hidden;
        border: 1px solid #dcdfe6;
        transition: border-color 0.2s ease;
        flex-shrink: 0;

        &:hover {
          border-color: #409eff;
        }

        .captcha-image {
          height: 40px;
          width: 100px;
          object-fit: cover;
          display: block;
        }

        .captcha-refresh {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.6);
          color: white;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.2s ease;
          font-size: 11px;

          i {
            font-size: 14px;
            margin-bottom: 2px;
          }
        }

        &:hover .captcha-refresh {
          opacity: 1;
        }
      }
    }
  }

  .submit-item {
    margin-top: 32px;
    margin-bottom: 0;

    ::v-deep .el-form-item__content {
      margin-left: 120px !important;
    }

    .submit-button {
      width: 200px;
      height: 40px;
      border-radius: 4px;
      font-size: 14px;
      font-weight: 500;
      letter-spacing: 0.5px;
      background: #409eff;
      border: none;
      transition: background-color 0.2s ease;

      &:hover {
        background: #66b1ff;
      }

      &:active {
        background: #3a8ee6;
      }

      &:focus {
        outline: none;
      }
    }
  }
}

.footer-links {
  text-align: center;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;

  .back-link {
    font-size: 14px;
    font-weight: 400;
    text-decoration: none;
    transition: color 0.2s ease;

    &:hover {
      color: #66b1ff;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .modern-register-container {
    padding: 16px;
  }

  .register-card {
    padding: 32px 24px;
    border-radius: 8px;
  }

  .brand-header {
    margin-bottom: 24px;

    .brand-logo {
      width: 56px;
      height: 56px;

      i {
        font-size: 24px;
      }
    }

    .brand-name {
      font-size: 20px;
    }
  }

  .form-content .form-header {
    margin-bottom: 24px;

    .form-title {
      font-size: 20px;
    }
  }

  .modern-form {
    ::v-deep .el-form-item__label {
      text-align: left !important;
      padding-right: 0 !important;
      line-height: 1.4 !important;
      margin-bottom: 6px;
    }

    ::v-deep .el-form-item__content {
      margin-left: 0 !important;
      line-height: 1.4 !important;
    }

    .form-item {
      margin-bottom: 16px;

      .modern-input ::v-deep .el-input__inner {
        height: 36px;
        font-size: 14px;
        padding-left: 36px;
      }

      .input-icon {
        left: 10px;
        font-size: 14px;
      }
    }

    .captcha-item .captcha-wrapper {
      flex-direction: column;
      gap: 8px;

      .captcha-input {
        max-width: none;
      }

      .captcha-image-wrapper {
        align-self: flex-start;

        .captcha-image {
          height: 36px;
          width: 90px;
        }
      }
    }

    .submit-item {
      ::v-deep .el-form-item__content {
        margin-left: 0 !important;
      }

      .submit-button {
        width: 100%;
        height: 36px;
        font-size: 14px;
      }
    }
  }
}

// 表单验证错误样式优化
.modern-form {
  ::v-deep .el-form-item.is-error {
    .el-input__inner {
      border-color: #f56c6c;
    }
  }

  ::v-deep .el-form-item__error {
    font-size: 12px;
    color: #f56c6c;
    padding-top: 4px;
  }
}

// 保持原有的主题色兼容
.bg {
  background: $--color-primary;
  border-color: $--color-primary;
}
</style>
