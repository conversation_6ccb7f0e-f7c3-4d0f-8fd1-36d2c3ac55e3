<template>
  <div class="aui-card--fill">
    <div class="mod-co__inorder flex_wrap" v-show="!panelShow" ref="listPanel">
      <el-card class="search_box" shadow="never">
        <el-form ref="searchForm" class="form_no_margin" :model="dataForm" @keyup.enter.native="queryPageByParam()" label-width="80px">
          <el-row :gutter="10" type="flex">
            <el-col >
              <el-row :gutter="10">
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderConsignee.consigneeName')" prop="consigneeName">
                    <el-input v-model="dataForm.consigneeName" :placeholder="$t('coOrderConsignee.consigneeName')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderConsignee.consigneeCompany')" prop="consigneeCompany">
                    <el-input v-model="dataForm.consigneeCompany" :placeholder="$t('coOrderConsignee.consigneeCompany')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderConsignee.consigneePhone')" prop="consigneePhone">
                    <el-input v-model="dataForm.consigneePhone" :placeholder="$t('coOrderConsignee.consigneePhone')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderConsignee.consigneeEmail')" prop="consigneeEmail">
                    <el-input v-model="dataForm.consigneeEmail" :placeholder="$t('coOrderConsignee.consigneeEmail')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderConsignee.consigneeCountry')" prop="consigneeCountry">
                    <el-input v-model="dataForm.consigneeCountry" :placeholder="$t('coOrderConsignee.consigneeCountry')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderConsignee.consigneeProvince')" prop="consigneeProvince">
                    <el-input v-model="dataForm.consigneeProvince" :placeholder="$t('coOrderConsignee.consigneeProvince')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderConsignee.consigneeCity')" prop="consigneeCity">
                    <el-input v-model="dataForm.consigneeCity" :placeholder="$t('coOrderConsignee.consigneeCity')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderConsignee.consigneeDistrict')" prop="consigneeDistrict">
                    <el-input v-model="dataForm.consigneeDistrict" :placeholder="$t('coOrderConsignee.consigneeDistrict')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderConsignee.consigneeAddress')" prop="consigneeAddress">
                    <el-input v-model="dataForm.consigneeAddress" :placeholder="$t('coOrderConsignee.consigneeAddress')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderConsignee.consigneePostcode')" prop="consigneePostcode">
                    <el-input v-model="dataForm.consigneePostcode" :placeholder="$t('coOrderConsignee.consigneePostcode')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderConsignee.consigneeDoorplate')" prop="consigneeDoorplate">
                    <el-input v-model="dataForm.consigneeDoorplate" :placeholder="$t('coOrderConsignee.consigneeDoorplate')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderConsignee.consigneeStreet')" prop="consigneeStreet">
                    <el-input v-model="dataForm.consigneeStreet" :placeholder="$t('coOrderConsignee.consigneeStreet')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderConsignee.consigneeIdcard')" prop="consigneeIdcard">
                    <el-input v-model="dataForm.consigneeIdcard" :placeholder="$t('coOrderConsignee.consigneeIdcard')" clearable ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <div class="search_box_btn no_more">
              <el-button type="primary" @click="queryPageByParam()" icon="el-icon-search">查询</el-button>
              <el-button @click.native="_resetForm('searchForm')" icon="el-icon-refresh-right">重置</el-button>
            </div>
          </el-row>
        </el-form>
      </el-card>

      <el-card class="flex_tab no_shadow" type="border-card" >
        <el-row class="optBtn_panel">
          <el-col :md="12" class='optBtn_leftFixed'>
            <el-button size="mini" @click="deleteHandle()">{{ $t('delete') }}</el-button>
            <el-button size="mini" >{{ $t('audit') }}</el-button>
            <!--保留空格符-->
          </el-col>
          <el-col :md="12" class="text-right">
            <el-button size="mini"  v-if="$hasPermission('co:orderconsignee:save')" plain @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
          </el-col>
        </el-row>
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" :max-height="tableHeight">
            <!-- 动态显示表格 -->
            <el-table-column type="selection" width='50' fixed="left"></el-table-column>
            <el-table-column v-for="(item, index) in tableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
              <template slot-scope="scope">
                <div>
                  <div v-if="item.prop === 'createDate'">
                    <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                      <span>{{formatterFn(scope,item.prop)}}</span>
                    </el-tooltip>
                  </div>
                  <div v-else>
                    {{formatterFn(scope,item.prop)}}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="150">
              <template slot="header" slot-scope="scope">
                <span>{{$t('handle')}}</span>
                <el-tooltip :content="$t('tableConfigTooltip')" placement="top">
                  <el-link style="margin-left:10px; font-size:16px" class="el-icon-setting" @click="settingCol(tableColumns)"></el-link>
                </el-tooltip>
              </template>
              <template slot-scope="scope">
                <el-link :underline="false"  @click="viewHandle(scope.row.id)">{{ $t('view') }}</el-link>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination background :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle" @current-change="pageCurrentChangeHandle">
        </el-pagination>
      </el-card>
    </div>
    <!-- 自定义列显示弹窗 -->
    <table-set ref="tableSet"></table-set>
    <!-- 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList" @cancelAddOrUpdate="cancelAddOrUpdate"></add-or-update>
    <!-- 详情页面 -->
    <viewDetail v-if="viewVisible" ref="viewDetail" @backView="backView"></viewDetail>
  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import listPage from '@/mixins/listPage'
import { formatterType, gtmToLtm, timestampFormat } from '@/filters/filters'
import dictTypeMixins from '@/mixins/dictTypeMixins'
// table 自定义显示
import tableSet from '@/components/tableSet'
import AddOrUpdate from './order-consignee-add-or-update'
import ViewDetail from './order-consignee-view-detail'
export default {
  mixins: [listPage, mixinViewModule, dictTypeMixins],
  data () {
    return {
      tableColumns: [
        { type: '', width: '150', prop: 'consigneeName', label: this.$t('coOrderConsignee.consigneeName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'consigneeCompany', label: this.$t('coOrderConsignee.consigneeCompany'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'consigneePhone', label: this.$t('coOrderConsignee.consigneePhone'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'consigneeEmail', label: this.$t('coOrderConsignee.consigneeEmail'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'consigneeCountry', label: this.$t('coOrderConsignee.consigneeCountry'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'consigneeProvince', label: this.$t('coOrderConsignee.consigneeProvince'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'consigneeCity', label: this.$t('coOrderConsignee.consigneeCity'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'consigneeDistrict', label: this.$t('coOrderConsignee.consigneeDistrict'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'consigneeAddress', label: this.$t('coOrderConsignee.consigneeAddress'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'consigneePostcode', label: this.$t('coOrderConsignee.consigneePostcode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'consigneeDoorplate', label: this.$t('coOrderConsignee.consigneeDoorplate'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'consigneeStreet', label: this.$t('coOrderConsignee.consigneeStreet'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'consigneeIdcard', label: this.$t('coOrderConsignee.consigneeIdcard'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'id', label: this.$t('system.id'), align: 'center', isShow: true, disabled: false }
      ],
      mixinViewModuleOptions: {
        getDataListURL: '/co/orderconsignee/page',
        getDataListIsPage: true,
        deleteURL: '/co/orderconsignee',
        deleteIsBatch: true
      },
      dataForm: {
        id: ''
      },
      activeName: 'all',
      tableName: 'co-orderconsignee'
    }
  },
  created () {
  },
  methods: {
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'createDate':
          value = timestampFormat(gtmToLtm(scope.row.createDate), 'YYYY-MM-DD')
          break
        case 'status':
          value = formatterType(scope.row.status, this.statusList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    // 自定义列显示
    settingCol (tableColumns) {
      this.$nextTick(() => {
        this.$refs['tableSet'].init(this.tableName, tableColumns)
      })
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat
  },
  computed: {
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    panelShow () {
      let ret = false
      if (this.addOrUpdateVisible) {
        ret = true
      } else if (this.viewVisible) {
        ret = true
      }
      return ret
    }
  },
  components: {
    AddOrUpdate,
    ViewDetail,
    tableSet
  }
}
</script>
