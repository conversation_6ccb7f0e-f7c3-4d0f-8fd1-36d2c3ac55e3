/**
 * 配置参考: https://cli.vuejs.org/zh/config/
 */
const IS_PROD = ['production', 'prod'].includes(process.env.NODE_ENV)
let date = new Date()
const Timestamp = date.getTime()
const MM_dd_HH_mm = date.getMonth() + '_' + date.getDate() + '_' + date.getHours() + '_' + date.getMinutes() + '_' + process.env.VUE_APP_NODE_ENV
// const CompressionWebpackPlugin = require('compression-webpack-plugin')
module.exports = {
  publicPath: process.env.NODE_ENV === 'production' ? './' : '/',
  // outputDir: `its-client-web-${MM_dd_HH_mm}`,
  productionSourceMap: false,
  parallel: true,
  configureWebpack: config => {
    if (IS_PROD) {
      // 输出重构  打包编译后的 文件名称  【模块名称.版本号.时间戳】
      config.output.filename = `js/[name].js?v=[contenthash:8].${Timestamp}`
      config.output.chunkFilename = `js/[name].js?v=[contenthash:8].${Timestamp}`
    }
    config.externals = {
      // 公共类库
      // 'vue': 'Vue',
      // 'element-ui': 'ELEMENT',
      // 'vue-router': 'VueRouter',
      // 'vuex': 'Vuex',
      // 'axios': 'axios'
    }
  },
  chainWebpack: config => {
    if (IS_PROD) {
      // 删除预加载
      config.plugins.delete('preload')
      config.plugins.delete('prefetch')
      // 压缩代码
      config.optimization.minimize(true)
      // 分割代码
      config.optimization.splitChunks({
        chunks: 'all',
        maxSize: 512000,
        minChunks: 20,
        cacheGroups: {
          handsontable: {
            test: /[\\/]node_modules[\\/]_?handsontable(.*)/,
            enforce: true,
            reuseExistingChunk: true,
            name: 'common-handsontable'
          },
          quill: {
            test: /[\\/]node_modules[\\/]_?quill(.*)/,
            enforce: true,
            reuseExistingChunk: true,
            name: 'common-quill'
          },
          xlsx: {
            test: /[\\/]node_modules[\\/]_?xlsx(.*)/,
            enforce: true,
            reuseExistingChunk: true,
            name: 'common-xlsx'
          },
          lodash: {
            test: /[\\/]node_modules[\\/]_?lodash(.*)/,
            enforce: true,
            reuseExistingChunk: true,
            name: 'common-lodash'
          },
          elementUI: {
            name: 'common-elementUI',
            enforce: true,
            reuseExistingChunk: true,
            test: /[\\/]node_modules[\\/]_?element-ui(.*)/
          },
          luckyexcel: {
            test: /[\\/]node_modules[\\/]_?luckyexcel(.*)/,
            enforce: true,
            reuseExistingChunk: true,
            name: 'common-luckyexcel'
          },
          echarts: {
            test: /[\\/]node_modules[\\/]_?echarts(.*)/,
            enforce: true,
            reuseExistingChunk: true,
            name: 'common-echarts'
          },
          luckysheet: {
            test: /[\\/]node_modules[\\/]_?luckysheet[\\/]/,
            enforce: true,
            reuseExistingChunk: true,
            name: 'common-luckysheet'
          },
          moment: {
            test: /[\\/]node_modules[\\/]_?moment[\\/]/,
            enforce: true,
            reuseExistingChunk: true,
            name: 'common-moment'
          },
          zrender: {
            test: /[\\/]node_modules[\\/]_?zrender[\\/]/,
            enforce: true,
            reuseExistingChunk: true,
            name: 'common-zrender'
          },
          default: {
            priority: -20,
            reuseExistingChunk: true
          }
        }
      })
    }
    config.module
      .rule('image')
      .test(/\.ico$/)
      .use('url-loader')
      .loader('url-loader')
  },
  css: {
    modules: false,
    extract: IS_PROD,
    sourceMap: false,
    loaderOptions: {
      sass: {
        // 向全局sass样式传入共享的全局变量
        data: `@import '~@/element-ui/theme-variables.scss';$src: '${process.env.VUE_APP_SRC}';`
      }
    }
  },
  devServer: {
    allowedHosts: [
      '127.0.0.1',
      'goto56.cn'
    ],
    open: false,
    port: 8002,
    proxy: {
      [process.env.VUE_APP_API_PREFIX_URL]: {
        target: process.env.VUE_APP_API_PROXY_URL,
        ws: true,
        changeOrigin: true,
        pathRewrite: {
          [process.env.VUE_APP_API_PREFIX_URL]: ''
        }
      }
    },
    overlay: {
      errors: true,
      warnings: true
    },
    disableHostCheck: true
  }
}
