<template>
  <div class="add-body panel_body">
    <div class="panel-hd">{{!dataForm.id ? $t('add') : $t('update')}}</div>
    <div class="addOrUpdatePanel" >
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" label-width="160px">
        <el-row type="flex" justify="start">
          <el-col :md="{span:13, offset: 2}">
            <el-form-item :label="$t('coSetDiyimportModule.mainObjectName')" prop="mainObjectName">
              <el-select filterable v-model="dataForm.mainObjectName"
                         :placeholder="$t('coSetDiyimportModule.mainObjectName')" clearable>
                <el-option v-for="item in excelMasterList" v-if="item.className === 'CoOrderParamDTO'" :key="item.className" :label="item.name" :value="item.className" :disabled="item.disabled"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('coSetDiyimportModule.name')" prop="name">
              <el-input v-model="dataForm.name" :placeholder="$t('coSetDiyimportModule.name')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coSetDiyimportModule.description')" prop="description">
              <el-input v-model="dataForm.description" :placeholder="$t('coSetDiyimportModule.description')"></el-input>
            </el-form-item>
          </el-col>

        </el-row>
      </el-form>
      <div id="cs_FormFooter" class="el-form-footer">
        <el-button @click="cancelFn">{{ $t('cancel') }}</el-button>
        <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import debounce from 'lodash/debounce'
import api from '@/api'
import baseData from '@/api/baseData'
export default {
  data () {
    return {
      dataForm: {
        id: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: '',
        status: '',
        version: '',
        customerId: '',
        mainObjectName: '',
        name: '',
        description: ''
      },
      excelMasterList: []
    }
  },
  computed: {
    dataRule () {
      return {
        mainObjectName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        name: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    this.getBaseData()
  },
  methods: {
    async getBaseData () {
      // urlDTO对应的字段
      baseData(api.getExcelExportMaster).then(res => {
        this.excelMasterList = res
      })
    },
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/co/setdiyimportmodule/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/co/setdiyimportmodule/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    // 取消
    cancelFn () {
      this.visible = false
      this.$emit('cancelAddOrUpdate')
    }
  }
}
</script>
