<template>
  <el-dialog :visible.sync="visible"  :title="$t('fba.sortRate')"  width="90%"  top="10px" :append-to-body="false" :close-on-click-modal="false" :close-on-press-escape="false" :lock-scroll="true" class="location_model">
    <el-card class="no_shadow" type="border-card">
      <el-row class="optBtn_panel">
        <el-col :md="12" class="optBtn_leftFixed">
          <!--保留空格符-->
        </el-col>
        <el-col :md="12" class="text-right">
          <el-button size="mini" type="primary" plain  @click="cleanAndReSortRate()">{{ $t('fba.reSortRate') }}</el-button>
        </el-col>
      </el-row>
      <el-form :model='sortLogForm' style="margin-top: 15px"  ref="sortLogForm" key='9' :inline-message='true'>
        <el-table id="declareTable" ref="sortTable" v-loading="dataListLoading" :data="sortLogForm.dataList"   @selection-change="dataListSelectionChangeHandle" max-height="410px">
          <!-- 动态显示表格 -->
          <el-table-column type="selection" width='50' fixed="left"/>
          <el-table-column v-for="(item, index) in tableColumnsArr" :show-overflow-tooltip="item.prop === 'receivableRemark'" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
            <template slot-scope="scope">
              <div>
                <div v-if="item.prop === 'sortByReceivableSum'">
                  <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                    <span>{{formatterFn(scope,'createDate')}}</span>
                  </el-tooltip>
                </div>
                <div v-else-if="item.prop === 'businessId'">
                  <el-link  type="primary" :underline="false" @click="clickExpand(scope.row, 'tableExpand', $event)">{{scope.row.businessId}}</el-link>
                </div>
                <div v-else-if="item.prop === 'status'">
                  <el-badge is-dot class="badge_status" v-if="scope.row.status === 2" type="warning" style="margin-top: 10px"></el-badge>
                  <el-badge is-dot class="badge_status" v-else-if="scope.row.status === 0" type="success" style="margin-top: 10px"></el-badge>
                  <el-badge is-dot class="badge_status" v-else-if="scope.row.status === 3" type="primary" style="margin-top: 10px"></el-badge>
                  <el-badge is-dot class="badge_status" v-else-if="scope.row.status === 1" type="danger" style="margin-top: 10px"></el-badge>
                  <el-badge is-dot class="badge_status" v-else-if="scope.row.status === 4" type="info" style="margin-top: 10px"></el-badge>
                  <span>{{ formatterFn(scope,item.prop) }}</span>
                </div>
                <div v-else-if="item.prop === 'message'">
                  <el-tooltip placement="top">
                    <div slot="content" style="max-width: 400px;">{{scope.row.message}}</div>
                    <span class="text-overflow">{{formatterFn(scope,item.prop)}}</span>
                  </el-tooltip>
                </div>
                <div v-else>
                  <span class="text-overflow">{{formatterFn(scope,item.prop)}}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="150">
            <template slot="header" slot-scope="scope">
              <span>{{$t('handle')}}</span>
              <el-tooltip :content="$t('tableConfigTooltip')" placement="top">
              </el-tooltip>
            </template>
            <template slot-scope="scope">
              <div v-loading="scope.row.status===4" element-loading-spinner="el-icon-loading">
                <el-link v-if="scope.row.status!==1 && scope.row.status!==4" :underline='false' @click="usedAndForecasting(scope.row.id)" :class="'el-link--default'">
                  {{ $t('fba.usedAndForecasting') }}
                </el-link>
                <el-link v-if="scope.row.status!==3" :underline='false' @click="reRate(scope.row)" :class="'el-link--default'">
                  {{ $t('fba.reRate') }}
                </el-link>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <el-pagination :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle" @current-change="pageCurrentChangeHandle">
      </el-pagination>
    </el-card>
  </el-dialog>
</template>

<script>
import listPage from '@/mixins/listPage'
import mixinViewModule from '@/mixins/view-module'
import api from '@/api'
import baseData from '@/api/baseData'
import { formatterType, gtmToLtm, timestampFormat, formatterCodeName, formatterName } from '@/filters/filters'
export default {
  mixins: [mixinViewModule, listPage],
  data () {
    return {
      visible: false,
      dataForm: {
        orderId: '',
        logisticsChannelCode: ''
      },
      requestCount: 0,
      orderIds: [],
      getSortChannelUrl: `/co/order/getSortRateChannel`,
      waybillDataForm: {
      },
      sortLogForm: {
        dataList: []
      },
      logisticsChannelList: [],
      weightUnitList: [],
      quotationSourceList: [],
      mixinViewModuleOptions: {
        getDataListURL: '/ba/sortratelog/page',
        getDataListIsPage: true,
        activatedIsNeed: false
      },
      // 能否修改重量和尺寸
      canModifySizeOrWeight: false,
      dataListLoading: false,
      tableColumns: [
        { type: '', width: '10', prop: 'id', label: this.$t('comInSubWaybill.id'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '80', prop: 'type', label: this.$t('baSortRateLog.type'), align: 'center', isShow: true, disabled: false },
        // { type: '', width: '120', prop: 'channelRouterId', label: this.$t('baSortRateLog.channelRouterId'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'logisticsChannelCode', label: this.$t('baSortRateLog.logisticsChannelCode'), align: 'center', isShow: true, disabled: false },
        // { type: '', width: '120', prop: 'providerChannelCode', label: this.$t('baSortRateLog.providerChannelCode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'status', label: this.$t('system.status'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'currency', label: this.$t('baSortRateLog.currency'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'receivableSumD', label: this.$t('baSortRateLog.payableSum'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'balanceWeightD', label: this.$t('baSortRateLog.balanceWeight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'balanceWeightUnit', label: this.$t('baSortRateLog.balanceWeightUnit'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '200', prop: 'message', label: this.$t('baSortRateLog.message'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'sortByReceivableSum', label: this.$t('baSortRateLog.createDate'), align: 'center', isShow: true, disabled: false }
      ]
    }
  },
  computed: {
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    }
  },
  created () {
    this.getDict()
    this.getBaseData()
  },
  methods: {
    init (orderId) {
      this.visible = true
      this.canModifySizeOrWeight = false
      this.dataForm.orderId = orderId
      this.orderIds = [orderId]
      this.$nextTick(() => {
        this.getSortChannelUrl = `/co/order/getSortRateChannel`
        this.sortRate()
      })
    },
    /**
     * 比价
     */
    sortRate () {
      new Promise(this.getSortChannelList).then(() => {
        if (this.sortLogForm.dataList.length > 0) {
          let sortRateList = []
          this.sortLogForm.dataList.filter(item => item.status === 4).forEach(item => sortRateList.push(item))
          // 批量询价
          this.batchReRate(sortRateList)
        }
      }).finally(() => {

      })
    },
    // 获取比价渠道
    getSortChannelList (resolve, reject) {
      this.dataListLoading = true
      this.$http.post(
        this.getSortChannelUrl,
        this.orderIds
      ).then(({ data: res }) => {
        this.dataListLoading = false
        if (res.code !== 0) {
          return reject(this.$message.error(res.msg))
        } else {
          this.sortLogForm.dataList = res.data[0].sortRateList || []
        }
      }).catch(() => {}).finally(() => resolve())
    },
    // 清空并重新获取比价渠道
    cleanAndReSortRate () {
      this.getSortChannelUrl = `/co/order/cleanAndReSortRate`
      this.sortRate()
    },
    getSortList () {
      this.dataListLoading = true
      this.$http.get(
        `/ba/sortratelog/list/`,
        {
          params: {
            ...this.dataForm
          }
        }
      ).then(({ data: res }) => {
        this.dataListLoading = false
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        } else {
          this.sortLogForm.dataList = res.data || []
        }
      }).catch(() => {})
    },
    /**
     * 选择并预报
     */
    usedAndForecasting (logId) {
      this.dataListLoading = true
      this.$http.post(`/co/order/forecastingBySortRateLog/${logId}`).then(({ data: res }) => {
        this.dataListLoading = false
        if (res.code !== 0) {
          this.getSortList()
          return this.$message.error(res.msg)
        } else if (res.data.status !== 3) {
          this.getSortList()
          return this.$message.error(res.data.message)
        } else {
          this.cancelFn()
        }
      }).catch(() => {})
    },
    // 重新询价
    reRate (logObject) {
      logObject.status = 4
      this.reRateByObject(logObject)
    },
    reRateByObject (logObject) {
      this.requestCount++
      this.$http.post(
        `/co/order/reRate`,
        logObject
      ).then(({ data: res }) => {
        this.requestCount--
        if (res.code !== 0) {
          this.$set(logObject, 'status', 1)
          this.$set(logObject, 'message', res.msg)
        } else {
          // 复制属性
          Object.keys(logObject).forEach(key => {
            if (res.data[key] !== undefined) {
              this.$set(logObject, key, res.data[key])
            }
          })
          this.$refs.sortTable.sort('sortByReceivableSum', 'ascending')
        }
      }).catch(() => {})
    },
    sleep (ms) {
      return new Promise((resolve) => setTimeout(resolve, ms))
    },
    /**
     * 批量比价
     */
    async batchReRate (sortRateList) {
      // 最大请求数
      let requestMax = 10
      for (let i = 0; i < sortRateList.length && this.visible;) {
        if (this.requestCount < requestMax) {
          let thisSortItem = sortRateList[i]
          if (thisSortItem.status === 4) {
            this.reRateByObject(thisSortItem)
          }
          i++
        } else {
          // 当前请求数达到最大请求数时的休眠时间
          await this.sleep(500)
        }
      }
    },
    async getBaseData () {
      this.logisticsChannelList = await baseData(api.listByCurrent).catch((res) => {
        const msg = res.msg ? res.msg : res
        this.$message.error(msg)
      })
    },
    async getDict () {
      // 获得使用状态数据字典.
      this.statusList = await this.getDictTypeList('baSortRateStatus')
      this.quotationSourceList = await this.getDictTypeList('channelQuotationSource') // 渠道报价来源
      this.getDictTypeList('weightUnit').then(res => {
        this.weightUnitList = res
      })
    },
    // 取消
    cancelFn () {
      this.visible = false
      this.$emit('sortRateAfterHandle')
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'logisticsChannelCode':
          value = formatterCodeName(scope.row.logisticsChannelCode, this.logisticsChannelList)
          break
        case 'status':
          value = formatterType(scope.row.status, this.statusList)
          break
        case 'balanceWeightUnit':
          value = formatterType(scope.row.balanceWeightUnit, this.weightUnitList)
          break
        case 'type':
          value = formatterType(scope.row.type, this.quotationSourceList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    addRedStar (h, { column }) {
      return [h('span', { style: 'color: red' }, '*'), h('span', ' ' + column.label)]
    },
    notAddRedStar (h, { column }) {
      return [h('span', ' ' + column.label)]
    },
    indexMethod (index) {
      if (!this.dataList || this.dataList.length <= 0) {
        return index
      }
      return (this.page - 1) * this.limit + index + 1
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat,
    formatterCodeName,
    formatterName
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep #declareTable .el-form-item  {
    margin-bottom: 0px !important;
  }
  ::v-deep  div.el-loading-spinner {
    margin-top: -7% !important;
  }
</style>
