<template>
  <div>
    <batchImportOrderExcel ref='batchImportOrderExcel'
                           title="fba.batchOrderImport"
                           bodyClass='panel_body'
                           :selectTemplate="true"
                           :defaultSelect="true"
                           :importExcelUrl="batchOrderImportUrl"
                           @hasDeclareInfoChange='hasDeclareInfoChange'
                           @backImportExcel="$closeFn"
                           return-btn-name='close'
    ></batchImportOrderExcel>
  </div>
</template>

<script>
import closeMixins from '@/mixins/closeMixins'
import batchImportOrderExcel from '@/components/importExcel/fba-multiple-mode-import'
import Cookies from 'js-cookie'
import baseData from '@/api/baseData'
import api from '@/api'
export default {
  mixins: [closeMixins],
  data () {
    return {
      hasDeclareInfoVal: 1,
      importOrderTemplateList: [],
      templateList: [
        { 'hasDeclareInfo': true, 'permission': true, 'name': 'FBA-多票批量导入-按箱号', 'downloadUrl': 'xls/FBA-MultiOrdersBatchImport-byBoxNo.xlsx', 'value': 1 },
        { 'hasDeclareInfo': true, 'permission': true, 'name': 'FBA-多票批量导入-按总箱数', 'downloadUrl': 'xls/FBA-MultiOrdersBatchImport-byTotalBoxCount.xlsx', 'value': 2 },
        { 'hasDeclareInfo': true, 'permission': true, 'name': 'FBA-单票导入', 'downloadUrl': 'xls/FBA-SingleOrderImport.xlsx', 'value': 3 },
        { 'hasDeclareInfo': false, 'permission': true, 'name': 'FBA-多票批量导入-按箱号-无申报信息', 'downloadUrl': 'xls/FBA-MultiOrdersBatchImport-byBoxNo-NoDeclare.xlsx', 'value': 4 },
        { 'hasDeclareInfo': false, 'permission': true, 'name': 'FBA-多票批量导入-按总箱数-无申报信息', 'downloadUrl': 'xls/FBA-MultiOrdersBatchImport-byTotalBoxCount-NoDeclare.xlsx', 'value': 5 },
        { 'hasDeclareInfo': false, 'permission': true, 'name': 'FBA-单票导入-无申报信息', 'downloadUrl': 'xls/FBA-MultiOrdersBatchImport-SingleOrderImport-NoDeclare.xlsx', 'value': 6 },
        { 'hasDeclareInfo': false, 'permission': true, 'name': 'FBA-多票批量导入-无箱号-无申报信息', 'downloadUrl': 'xls/FBA-meiyida.xlsx', 'value': 14 }
      ]
    }
  },
  filters: {
  },
  computed: {
    // 赋值导入URL
    batchOrderImportUrl() {
      return `${this.$baseUrl}/fba/order/batchOrderImport?node=11`
    },
    panelShow() {
      let ret = false
      if (this.importExcelVisible) {
        ret = true
      }
      return ret
    },
    getToken() {
      return {
        token: Cookies.get('token')
      }
    }
  },
  created () {
    this.$nextTick(() => {
      Promise.all([
        this.getImportOrderTemplateList()
      ]).then(() => {
        this.$refs.batchImportOrderExcel.visible = true
        this.$refs.batchImportOrderExcel.templateList = this.allTemplateList()
        this.$refs.batchImportOrderExcel.init()
      })
    })
  },
  methods: {
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        this.$refs['dataForm'].resetFields()
      })
    },
    hasDeclareInfoChange(val) {
      this.hasDeclareInfoVal = val
      this.$nextTick(() => {
        this.$refs.batchImportOrderExcel.templateList = this.allTemplateList()
      })
    },
    allTemplateList () {
      let appendTemplateList = []
      if (this.importOrderTemplateList && this.hasDeclareInfoVal) {
        this.importOrderTemplateList.forEach(item => {
          appendTemplateList.push({
            'name': item.name,
            'downloadUrl': item.demoFileUrl,
            'value': item.id,
            'id': item.id,
            'type': item.templateType
          })
        })
      }
      let items = this.templateList.filter(item => item.hasDeclareInfo === Boolean(this.hasDeclareInfoVal) && item.permission)
      return [].concat(appendTemplateList, items)
    },
    async getImportOrderTemplateList () {
      // 所有分区模板列表
      this.importOrderTemplateList = await baseData(api.importOrderTemplateList)
    }
  },
  components: {
    batchImportOrderExcel
  }
}
</script>
<style lang="scss" scoped>

</style>
