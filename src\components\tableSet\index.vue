<template>
  <el-dialog :title="$t('tableConfigTooltip')" :visible.sync="dialogVisible" width="680px" :before-close="handleClose">
    <el-form :inline="true" @keyup.enter.native="dataFormSubmitHandle()">
      <el-checkbox v-model="item.isShow" v-for="(item, index) in tablelist" :disabled="item.disabled" :key="index" v-show="item.label">{{item.label}}</el-checkbox>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">{{$t('cancel')}}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{$t('save')}}</el-button>
    </span>
  </el-dialog>
</template>
<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      dialogVisible: false,
      tablelist: [],
      tableName: '',
      dataForm: ''
    }
  },
  methods: {
    init (tableName, tableColumns) {
      this.tablelist = tableColumns
      this.dialogVisible = true
      this.tableName = tableName
    },
    handleClose () {
      this.dialogVisible = false
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      // let strTableList = JSON.stringify(this.tablelist)
      // const params = { tableName: this.tableName, setting: JSON.stringify(this.tablelist) }
      // let key = this.tableName + '-' + this.$store.state.user.id
      // 保存到前端缓存
      // window.localStorage.removeItem(key)
      // window.localStorage.setItem(key, strTableList)
      // this.$http['post']('/user/modifyTableConfig', params).then(({ data: res }) => {
      //   if (res.code !== 0) {
      //     return this.$message.error(res.msg)
      //   }
      //   this.$message({
      //     message: this.$t('prompt.success'),
      //     type: 'success',
      //     duration: 500,
      //     onClose: () => {
      //       this.visible = false
      //       this.$emit('refreshDataList')
      //     }
      //   })
      //   this.handleClose()
      // }).catch(() => {
      //   console.error('save table config error...')
      //   this.handleClose()
      // })
      this.handleClose()
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
