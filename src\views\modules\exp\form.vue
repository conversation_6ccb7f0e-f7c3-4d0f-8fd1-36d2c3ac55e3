<template>
  <div class="aui-card--fill">
    <div class="mod-order-list flex_wrap" v-show="!panelShow" ref="listPanel">
      <el-card class="margin_bottom15 search_box" shadow="never">
        <el-form ref="searchForm" class="form_no_margin" :model="dataForm" @keyup.enter.native="queryPageByParam()" label-width="80px">
          <el-row :gutter="10" type="flex">
           <el-col >
             <el-row :gutter="10">
               <el-col :sm="24" :md="8">
                 <el-form-item :label="$t('入库订单号')" prop="id">
                   <el-input v-model="dataForm.id" placeholder="请输入入库订单号" clearable ></el-input>
                 </el-form-item>
               </el-col>
               <el-col :sm="24" :md="8">
                 <el-form-item :label="$t('客户单号')" >
                   <el-input v-model="dataForm.customerVoucherNo" placeholder="" clearable></el-input>
                 </el-form-item>
               </el-col>
               <el-col :sm="24" :md="8">
                 <el-form-item :label="$t('仓库')">
                   <el-select v-model="dataForm.warehouseId" filterable placeholder="" clearable>
                     <el-option v-for="item in startWarehouseList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                   </el-select>
                 </el-form-item>
               </el-col>
               <template v-if="!searchBoxShow">
                 <el-col :sm="24" :md="8">
                   <el-form-item :label="$t('订单类型')" prop="orderType">
                     <el-select v-model="dataForm.orderType" filterable clearable>
                       <el-option v-for="item in orderTypeList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                     </el-select>
                   </el-form-item>
                 </el-col>
                 <el-col :sm="24" :md="8">
                   <el-form-item :label="$t('创建时间')">
                     <el-date-picker v-model="createDataArray" type="datetimerange" :start-placeholder="$t('开始时间')" :end-placeholder="$t('结束时间')" :default-time="['00:00:00', '23:59:59']">
                     </el-date-picker>
                   </el-form-item>
                 </el-col>
               </template>
             </el-row>
           </el-col>
            <div class="search_box_btn">
              <el-button type="primary" @click="queryPageByParam()" icon="el-icon-search">查询</el-button>
              <el-button @click.native="_resetForm('searchForm')" icon="el-icon-refresh-right">重置</el-button>
              <el-button type="text" @click="searchBoxShowFn">{{searchBoxShow? '展开':'收起'}}<i :class="searchBoxShow ? 'el-icon-arrow-down': 'el-icon-arrow-up'"></i></el-button>
            </div>
          </el-row>
        </el-form>
      </el-card>

      <el-tabs class="flex_tab no_shadow" type="border-card" v-model="activeName" @tab-click="tabsClick" :stretch="false">
        <el-tab-pane v-for="(item, key) in statusArr" :key="key" :name="key">
          <div slot="label" style="min-width: 4em; text-align: center">{{ statusName(item.value) || '&nbsp;' }}
            <el-badge v-if="isBadge(item)" :value="item.num >0 ? item.num:''" :max="999" class="item" type="primary"></el-badge>
          </div>
          <el-row class="optBtn_panel">
            <el-col :md="12" class='optBtn_leftFixed'>
              <el-button size="mini"  >{{ $t('delete') }}</el-button>
              <el-button size="mini" >{{ $t('audit') }}</el-button>
              &nbsp;
              <!--保留空格符-->
            </el-col>
            <el-col :md="12" class="text-right">
              <el-button size="mini" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
            </el-col>
          </el-row>
        </el-tab-pane>
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <el-table v-loading="dataListLoading" :data="dataList" border :max-height="tableHeight">
            <!-- 动态显示表格 -->
            <el-table-column type="selection" width='50' fixed="left"></el-table-column>
            <el-table-column v-for="(item, index) in tableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
              <template slot-scope="scope">
                <div>
                  <div v-if="item.prop === 'createDate'">
                    <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                      <span>{{formatterFn(scope,item.prop)}}</span>
                    </el-tooltip>
                  </div>
                  <div v-else-if="item.prop === 'auditDate'">
                    <el-tooltip :content="scope.row.auditDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                      <span>{{formatterFn(scope,item.prop)}}</span>
                    </el-tooltip>
                  </div>
                  <div v-else-if="item.prop === 'firstReceiptDate'">
                    <el-tooltip :content="scope.row.firstReceiptDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                      <span>{{formatterFn(scope,item.prop)}}</span>
                    </el-tooltip>
                  </div>
                  <div v-else-if="item.prop === 'cutStatus'">
                    <el-tooltip v-if="scope.row.cutStatus === 3" :content="scope.row.errorMessage" effect="light" placement="top">
                      <span >{{scope.row.cutStatus | formatterType(cutStatusList)}}</span>
                    </el-tooltip>
                    <span v-else>{{scope.row.cutStatus | formatterType(cutStatusList)}}</span>
                  </div>
                  <div v-else>
                    {{formatterFn(scope,item.prop)}}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="150">
              <template slot="header" slot-scope="scope">
                <span>{{$t('handle')}}</span>
                <el-tooltip :content="$t('tableConfigTooltip')" placement="top">
                  <el-button type="text" size="mini" style="margin-left:10px; font-size:16px" class="el-icon-setting" @click="settingCol(tableColumns)"></el-button>
                </el-tooltip>
              </template>
              <template slot-scope="scope">
                <el-button type="text" size="mini" @click="viewHandle(scope.row.id)">{{ $t('view') }}</el-button>
                <el-button type="text" size="mini"  @click="operateViewHandle(scope.row.id)">{{ $t('日志') }}</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination background :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle" @current-change="pageCurrentChangeHandle">
        </el-pagination>
      </el-tabs>
    </div>

    <!-- 其他组件 -->

    <!-- 自定义列显示弹窗 -->
    <table-set ref="tableSet"></table-set>
    <!-- 新增／修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList" @cancelAddOrUpdate="cancelAddOrUpdate"></add-or-update>
    <!-- 详情页面 -->
    <viewDetail v-if="viewVisible" ref="viewDetail" @backView="backView"></viewDetail>
    <!-- 其他组件 -->

  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
// table 自定义显示
import tableSet from '@/components/tableSet'
// 新增页面
import addOrUpdate from './baseForm'
// 页面详情
import viewDetail from './detail'
import listPage from '@/mixins/listPage'
import { formatterType, formatterName, gtmToLtm, timestampFormat } from '@/filters/filters'
const getBeforeMonth = (month) => {
  return new Date(getNowDate() - 3600 * 1000 * 24 * month * 30 + 1)
}
const getNowDate = () => {
  return new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1)
}
export default {
  mixins: [listPage, mixinViewModule],
  data () {
    return {
      tableColumns: [
        { type: '', width: '150', prop: 'id', label: this.$t('入库订单'), align: 'left', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'customerVoucherNo', label: this.$t('客户单号'), align: 'left', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'warehouseId', label: this.$t('仓库'), align: 'left', isShow: true, disabled: false },
        { type: '', width: '180', prop: 'orderType', label: this.$t('订单类型'), align: 'left', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'orderSource', label: this.$t('订单来源'), align: 'left', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'totalBox', label: this.$t('箱数'), align: 'right', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'totalQty', label: this.$t('货品数量'), align: 'right', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'createDate', label: this.$t('创建时间'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'auditDate', label: this.$t('审核时间'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'firstReceiptDate', label: this.$t('到货时间'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '70', prop: 'cutStatus', label: this.$t('截单状态'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '70', prop: 'status', label: this.$t('状态'), align: 'center', isShow: true, disabled: false }
      ],
      mixinViewModuleOptions: {
        getDataListURL: '/woms/co/inorder/page',
        getDataListIsPage: true
      },
      dataForm: {
        id: '',
        status: '',
        pdfType: '',
        createDate1: getBeforeMonth(2),
        createDate2: getNowDate()
      },
      createDataArray: [getBeforeMonth(2), getNowDate()],
      statusList: [],
      warehouseList: [],
      startWarehouseList: [],
      orderSourceList: [],
      orderTypeList: [],
      transportWayList: [],
      cutStatusList: [],
      activeName: 'all',
      tableName: 'client-in-order',
      tabsName: '',
      statusArr: {
        // 全部
        'all': {
          value: 0,
          num: 0
        },
        // 草稿
        'new': {
          value: 10,
          num: 0
        },
        // 审核
        'audit': {
          value: 30,
          num: 0
        },
        // 到仓
        'arrived': {
          value: 35,
          num: 0
        },
        // 部分上架
        'ining': {
          value: 50,
          num: 0
        },
        // 已上架
        'ined': {
          value: 60
          // num: 0
        },
        // 强制关闭
        'forceClose': {
          value: 80
          // num: 0
        },
        // 作废
        'cancel': {
          value: 70,
          num: 0
        }
      },
      setInte: '' // 定时任务
    }
  },
  computed: {
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    panelShow () {
      let ret = false
      if (this.addOrUpdateVisible) {
        ret = true
      } else if (this.viewVisible) {
        ret = true
      }
      return ret
    }
  },
  created () {
    this.getDict()
  },
  methods: {
    async getDict () {
      // 获取相关字典
      this.statusList = await this.getDictTypeList('inOrderStatus') // 获取状态字典
      this.orderSourceList = await this.getDictTypeList('ecPlatform') // 获得状态列表数据字典集.
      this.orderTypeList = await this.getDictTypeList('inOrderType') // 获得出库类型列表数据字典集.
      this.transportWayList = await this.getDictTypeList('transportWay') // 获得货运方式
      this.cutStatusList = await this.getDictTypeList('outOrderCutStatus') // 截单状态.
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'warehouseId':
          value = formatterName(scope.row.warehouseId, this.warehouseList)
          break
        case 'orderType':
          value = formatterType(scope.row.orderType, this.orderTypeList)
          break
        case 'orderSource':
          value = formatterType(scope.row.orderSource, this.orderSourceList)
          break
        case 'createDate':
          value = timestampFormat(gtmToLtm(scope.row.createDate), 'YYYY-MM-DD')
          break
        case 'auditDate':
          value = timestampFormat(gtmToLtm(scope.row.auditDate), 'YYYY-MM-DD')
          break
        case 'firstReceiptDate':
          value = timestampFormat(gtmToLtm(scope.row.firstReceiptDate), 'YYYY-MM-DD')
          break
        case 'cutStatus':
          value = formatterType(scope.row.cutStatus, this.cutStatusList)
          break
        case 'status':
          value = formatterType(scope.row.status, this.statusList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    // 自定义列显示
    settingCol (tableColumns) {
      this.$nextTick(() => {
        this.$refs['tableSet'].init(this.tableName, tableColumns)
      })
    },
    statusName (val) {
      if (val === 0) {
        return '全部'
      }
      let name = ''
      this.statusList.forEach((item) => {
        if (item.dictValue === val) {
          name = item.dictName
        }
      })
      return name
    },
    isBadge (item) {
      let ret = true
      if (item.value === 70) {
        ret = false
        return ret
      }
      return ret
    },
    tabsClick (tab, event) {
      switch (tab.name) {
        case 'all':
          this.dataForm.status = ''
          this.queryPageByParam()
          break
        case 'new':
          this.dataForm.status = 10
          this.queryPageByParam()
          break
        case 'audit':
          this.dataForm.status = 30
          this.queryPageByParam()
          break
        case 'arrived':
          this.dataForm.status = 35
          this.queryPageByParam()
          break
        case 'ining':
          this.dataForm.status = 50
          this.queryPageByParam()
          break
        case 'forceClose':
          this.dataForm.status = 80
          this.queryPageByParam()
          break
        case 'ined':
          this.dataForm.status = 60
          this.queryPageByParam()
          break
        case 'cancel':
          this.dataForm.status = 70
          this.queryPageByParam()
          break
        default:
          break
      }
    }
  },
  filters: {
    formatterType,
    formatterName,
    gtmToLtm,
    timestampFormat,
    filterFileName: function (value) {
      if (value && value.length > 8) {
        value = value.slice((value.lastIndexOf('/') + 1), value.length)
      }
      return value
    }
  },
  components: {
    tableSet,
    viewDetail,
    addOrUpdate
  }
}
</script>
