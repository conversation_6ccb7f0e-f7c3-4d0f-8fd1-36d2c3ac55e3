<template>
  <div class="add-body panel_body">
    <div class="panel-hd">{{!dataForm.id ? $t('add') : $t('update')}}</div>
    <div class="addOrUpdatePanel" >
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" label-width="80px">
        <el-row type="flex" justify="start">
          <el-col :md="{span:13, offset: 2}">
            <el-form-item :label="$t('coOrderShipper.shipperName')" prop="shipperName">
              <el-input v-model="dataForm.shipperName" :placeholder="$t('coOrderShipper.shipperName')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderShipper.shipperCompany')" prop="shipperCompany">
              <el-input v-model="dataForm.shipperCompany" :placeholder="$t('coOrderShipper.shipperCompany')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderShipper.shipperPhone')" prop="shipperPhone">
              <el-input v-model="dataForm.shipperPhone" :placeholder="$t('coOrderShipper.shipperPhone')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderShipper.shipperEmail')" prop="shipperEmail">
              <el-input v-model="dataForm.shipperEmail" :placeholder="$t('coOrderShipper.shipperEmail')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderShipper.shipperCountry')" prop="shipperCountry">
              <el-input v-model="dataForm.shipperCountry" :placeholder="$t('coOrderShipper.shipperCountry')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderShipper.shipperProvince')" prop="shipperProvince">
              <el-input v-model="dataForm.shipperProvince" :placeholder="$t('coOrderShipper.shipperProvince')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderShipper.shipperCity')" prop="shipperCity">
              <el-input v-model="dataForm.shipperCity" :placeholder="$t('coOrderShipper.shipperCity')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderShipper.shipperDistrict')" prop="shipperDistrict">
              <el-input v-model="dataForm.shipperDistrict" :placeholder="$t('coOrderShipper.shipperDistrict')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderShipper.shipperAddress')" prop="shipperAddress">
              <el-input v-model="dataForm.shipperAddress" :placeholder="$t('coOrderShipper.shipperAddress')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderShipper.shipperPostcode')" prop="shipperPostcode">
              <el-input v-model="dataForm.shipperPostcode" :placeholder="$t('coOrderShipper.shipperPostcode')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderShipper.shipperDoorplate')" prop="shipperDoorplate">
              <el-input v-model="dataForm.shipperDoorplate" :placeholder="$t('coOrderShipper.shipperDoorplate')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderShipper.shipperStreet')" prop="shipperStreet">
              <el-input v-model="dataForm.shipperStreet" :placeholder="$t('coOrderShipper.shipperStreet')"></el-input>
            </el-form-item>
          </el-col>

        </el-row>
      </el-form>
      <div id="cs_FormFooter" class="el-form-footer">
        <el-button @click="cancelFn">{{ $t('cancel') }}</el-button>
        <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      dataForm: {
        id: '',
        creator: '',
        createDate: '',
        shipperName: '',
        shipperCompany: '',
        shipperPhone: '',
        shipperEmail: '',
        shipperCountry: '',
        shipperProvince: '',
        shipperCity: '',
        shipperDistrict: '',
        shipperAddress: '',
        shipperPostcode: '',
        shipperDoorplate: '',
        shipperStreet: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        creator: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        createDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        shipperName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        shipperCompany: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        shipperPhone: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        shipperEmail: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        shipperCountry: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        shipperProvince: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        shipperCity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        shipperDistrict: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        shipperAddress: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        shipperPostcode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        shipperDoorplate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        shipperStreet: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/co/ordershipper/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/co/ordershipper/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    // 取消
    cancelFn () {
      this.visible = false
      this.$emit('cancelAddOrUpdate')
    }
  }
}
</script>
