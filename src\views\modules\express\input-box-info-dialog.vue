<template>
  <el-dialog :visible.sync="visible" :title="$t('fba.enterBoxInfo')" :close-on-click-modal="false" width='88%' :close-on-press-escape="false" :show-close='false' :lock-scroll="true" class="location_model" >
    <el-row >
        <el-col :span='8'>
          <span>{{ $t('coOrder.customerOrderNo') }}：<span class='masterColor'>{{customerOrderNo}}</span></span>
        </el-col>
      <el-col :span='16' class='text-right' v-show='false'>
        <span class='margin_right15'>{{ $t('fba.totalBox') }}：{{dataForm.totalBox}}</span>
        <span class='margin_right15'>{{ $t('fba.totalWeight') }}(KG)：{{dataForm.totalWeight}}</span>
        <span class='margin_right15'>{{ $t('fba.totalVol') }}(CBM)：{{dataForm.totalVol}}</span>
      </el-col>
    </el-row>
    <div style='border-top: 2px solid #DCDFE6;margin-top: 10px'>
      <div class="flex_table" ref="tableElm" v-domResize="redraw" id='boxDataFormId'>
        <el-form :model='boxDataForm' ref='boxDataForm'  key='6' :inline-message='true'>
          <el-table  :data='boxDataForm.boxDataList' border :max-height="tableHeight"  class='width100 margin_top10' >
            <el-table-column :label="$t('fba.serialNo')" prop='packageSerialNo' type='index' width='50'>
              <template slot-scope="scope">
                <el-form-item :prop="'boxDataList.' + scope.$index + '.packageSerialNo'" >
                  <span v-if='scope.row.allSerialNo' v-text="scope.row.allSerialNo"></span>
                  <span v-else v-text="scope.row.packageSerialNo"></span>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop='packageLengthD' :label="$t('fba.length')" min-width='100' header-align='center' align='center' :render-header="addRedStar">
              <template slot-scope="scope">
                <el-form-item :prop="'boxDataList.' + scope.$index + '.packageLengthD'" :rules='boxRule.packageLengthD'>
                  <el-input :ref="'boxDataList.' + scope.$index + '.packageLengthD'" v-model="scope.row.packageLengthD"
                            @keyup.enter.native="focusNextInput(scope, 'boxDataList.' + scope.$index + '.packageWidthD')"></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop='packageWidthD' :label="$t('fba.width')" min-width='100' header-align='center' align='center'  :render-header="addRedStar">
              <template slot-scope="scope">
                <el-form-item :prop="'boxDataList.' + scope.$index + '.packageWidthD'" :rules='boxRule.packageWidthD'>
                  <el-input  :ref="'boxDataList.' + scope.$index + '.packageWidthD'" v-model="scope.row.packageWidthD"
                             @keyup.enter.native="focusNextInput(scope, 'boxDataList.' + scope.$index + '.packageHeightD')"></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop='packageHeightD' :label="$t('fba.height')" min-width='100' header-align='center' align='center'  :render-header="addRedStar">
              <template slot-scope="scope">
                <el-form-item :prop="'boxDataList.' + scope.$index + '.packageHeightD'" :rules='boxRule.packageHeightD'>
                  <el-input :ref="'boxDataList.' + scope.$index + '.packageHeightD'" v-model="scope.row.packageHeightD"
                            @keyup.enter.native="focusNextInput(scope, 'boxDataList.' + scope.$index + '.packageWeightD')"></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop='packageWeightD' :label="$t('fba.weight')" min-width='100' header-align='center' align='center' :render-header="addRedStar">
              <template slot-scope="scope">
                <el-form-item :prop="'boxDataList.' + scope.$index + '.packageWeightD'" :rules='boxRule.packageWeightD'>
                  <el-input :ref="'boxDataList.' + scope.$index + '.packageWeightD'" v-model="scope.row.packageWeightD"
                            @keyup.enter.native="focusNextInput(scope, 'boxDataList.' + scope.$index + '.packageQty')"></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop='packageQty' :label="$t('fba.boxCount')" min-width='100' header-align='center' align='center' :render-header="addRedStar">
              <template slot-scope="scope">
                <el-form-item :prop="'boxDataList.' + scope.$index + '.packageQty'" :rules='boxRule.packageQty'>
                  <el-input :ref="'boxDataList.' + scope.$index + '.packageQty'" v-model="scope.row.packageQty"
                            @keyup.enter.native="focusNextInput(scope, 'boxDataList.' + scope.$index + '.packageCustomerNo')"></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop='packageCustomerNo' :label="$t('coOrder.customerBoxNo')" min-width='400' header-align='center' align='center'  >
              <template slot-scope="scope">
                <el-form-item :prop="'boxDataList.' + scope.$index + '.packageCustomerNo'" :rules='boxRule.packageCustomerNo'>
                  <el-input :ref="'boxDataList.' + scope.$index + '.packageCustomerNo'" v-model="scope.row.packageCustomerNo"
                            @keyup.enter.native="focusNextInput(scope, 'boxDataList.' + scope.$index + '.packageLengthD')"></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop='subCustomerOrderNo' :label="$t('fba.subCustomerOrderNo')" min-width='200' header-align='center' align='center' >
              <template slot-scope="scope">
                <el-form-item :prop="'boxDataList.' + scope.$index + '.subCustomerOrderNo'" :rules='boxRule.subCustomerOrderNo'>
                  <el-input v-model="scope.row.subCustomerOrderNo"></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column :label="$t('handle')"  min-width='100' header-align='center' align='center' >
              <template slot='header' slot-scope='scope'>
                <span>{{ $t('handle') }}</span>
              </template>
              <template slot-scope='scope'>
                <el-link :underline='false' @click='copyRow(scope.row)'>{{ $t('copy') }}</el-link>
                <popconfirm i18nOperateValue="delete" @clickHandle='deleteRow(scope.$index)'>{{ $t('delete') }}</popconfirm>
              </template>
            </el-table-column>
          </el-table>
          <el-button class='el-icon-plus width100 margin_top10' size='mini' type='primary' plain @click='addRow'>{{ $t('add') }}</el-button>
        </el-form>
      </div>
    </div>
    <template slot="footer">
      <div style='justify-content: center;display: flex;'>
        <el-button @click='closeHandle'>{{ $t('close') }}</el-button>
        <el-button type='primary' :loading='submitLoading' @click='handleSubmit()'>{{ $t('save') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import areaBox from '@/components/areaBox'
import listPage from '@/mixins/listPage'
import NP from 'number-precision'
import { isDecimal3, isDecimal1, isOverLength, isPlusFloat, isPlusInteger2, serialNumberCheck } from '@/utils/validate'
export default {
  mixins: [listPage],
  data () {
    return {
      visible: false,
      autoAppend: false,
      autoGenerateBox: false,
      submitLoading: false,
      customerOrderNo: null,
      dataForm: {
        orderId: '',
        totalBox: 0,
        totalWeight: 0,
        totalVol: 0
      },
      boxDataForm: {
        packageSerialNo: '',
        packageDeliveryNo: '',
        packageCustomerNo: '',
        packageLengthD: '',
        packageWidthD: '',
        packageHeightD: '',
        packageWeightD: '',
        boxDataList: []
      }
    }
  },
  computed: {
    boxRule () {
      const isInteger = (rule, value, callback) => {
        if (value && !isPlusInteger2(value)) {
          return callback(new Error('请输入大于0的正整数'))
        }
        callback()
      }
      const everyItemIsLength36 = (rule, value, callback) => {
        if (value) {
          let split = value.split(/[,，]/) || []
          for (const item of split) {
            if (!isOverLength(item, 36)) {
              return new Error(this.$t('validate.whoIsOverLength', { who: item, max: 36 }))
            }
          }
        }
        callback && callback.call(true)
      }
      const isFloat1 = (rule, value, callback) => {
        if (value && !(isPlusFloat(value) || isPlusInteger2(value))) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value <= 0) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value && !isDecimal1(value)) {
          return callback(new Error('仅保留1位小数'))
        }
        callback()
      }
      const isFloat3 = (rule, value, callback) => {
        if (value && !(isPlusFloat(value) || isPlusInteger2(value))) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value <= 0) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value && !isDecimal3(value)) {
          return callback(new Error('仅保留3位小数'))
        }
        callback()
      }
      const repeatBoxNo = (rule, value, callback) => {
        if (value && this.boxDataForm.boxDataList && this.boxDataForm.boxDataList.length > 0) {
          let packageCustomerNoArr = this.boxDataForm.boxDataList.map(item => {
            if (item.packageCustomerNo === value) {
              return item.packageCustomerNo
            }
          }).filter(item => typeof item !== 'undefined')
          let packageCustomerNoSet = new Set(this.boxDataForm.boxDataList.map(item => {
            if (item.packageCustomerNo === value) {
              return item.packageCustomerNo
            }
          }).filter(item => typeof item !== 'undefined')
          )
          if (packageCustomerNoSet.size !== packageCustomerNoArr.length) {
            return callback(new Error('箱号不能重复'))
          }
        }
        callback()
      }
      return {
        packageQty: [
          { required: true, message: this.$t('validate.required'), trigger: 'change' },
          { validator: isInteger, trigger: 'blur' }
        ],
        subCustomerOrderNo: [
          { validator: everyItemIsLength36, trigger: 'blur' }
        ],
        packageCustomerNo: [
          { required: false, message: this.$t('validate.required'), trigger: 'change' },
          { validator: everyItemIsLength36, trigger: 'blur' },
          { validator: repeatBoxNo, trigger: 'blur' }
        ],
        packageLengthD: [
          { required: true, message: this.$t('validate.required'), trigger: 'change' },
          { validator: isFloat1, trigger: 'blur' }
        ],
        packageWidthD: [
          { required: true, message: this.$t('validate.required'), trigger: 'change' },
          { validator: isFloat1, trigger: 'blur' }
        ],
        packageHeightD: [
          { required: true, message: this.$t('validate.required'), trigger: 'change' },
          { validator: isFloat1, trigger: 'blur' }
        ],
        packageWeightD: [
          { required: true, message: this.$t('validate.required'), trigger: 'change' },
          { validator: isFloat3, trigger: 'blur' }
        ]
      }
    },
    dataRule () {
      const isFloat1 = (rule, value, callback) => {
        if (value && !(isPlusFloat(value) || isPlusInteger2(value))) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value && value <= 0) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value && !isDecimal1(value)) {
          return callback(new Error('仅保留1位小数'))
        }
        callback()
      }
      const isFloat3 = (rule, value, callback) => {
        if (value && !(isPlusFloat(value) || isPlusInteger2(value))) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value && value <= 0) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value && !isDecimal3(value)) {
          return callback(new Error('仅保留3位小数'))
        }
        callback()
      }
      const isInteger = (rule, value, callback) => {
        if (value && !isPlusInteger2(value)) {
          return callback(new Error('请输入大于0的正整数'))
        }
        callback()
      }
      const checkSerialNumber = (rule, value, callback) => {
        if (value && !serialNumberCheck(value)) {
          return callback(new Error('只能用数字，逗号，中划线组合'))
        }
        callback()
      }
      return {
        boxCount: [
          { validator: isInteger, trigger: 'blur' }
        ],
        packageSerialNo: [
          { validator: checkSerialNumber, trigger: 'blur' }
        ],
        packageWeight: [
          { validator: isFloat3, trigger: 'blur' }
        ],
        packageHeight: [
          { validator: isFloat1, trigger: 'blur' }
        ],
        packageWidth: [
          { validator: isFloat1, trigger: 'blur' }
        ],
        packageLength: [
          { validator: isFloat1, trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init (customerOrderNo, boxDataList, orderId) {
      this.visible = true
      this.submitLoading = false
      this.dataForm.orderId = orderId
      this.customerOrderNo = customerOrderNo
      if (boxDataList && boxDataList.length > 0) {
        this.boxDataForm.boxDataList = boxDataList
      }
    },
    checkBoxNo (boxNo) {
      return new Promise(resolve => {
        if (boxNo && boxNo.length > 0) {
          this.$http.get(`/co/orderpackage/list?packageCustomerNos=` + boxNo).then(({ data: res }) => {
            let boxNos = [...new Set(res.data.map(item => {
              if (item.orderId !== this.dataForm.orderId) {
                return item.packageCustomerNo
              }
            }))]
            boxNos = boxNos.filter(item => item !== undefined)
            resolve(boxNos)
          })
        } else {
          resolve(null)
        }
      })
    },
    focusNextInput(scope, nextRef) {
      // let prop = scope.column.property
      if ((scope.column.property === 'packageQty' && scope.row.packageQty <= 1) || scope.column.property === 'packageCustomerNo') {
        this.$nextTick(() => {
          this.addRow()
        })
        nextRef = 'boxDataList.' + (scope.$index + 1) + '.' + 'packageLengthD'
      }
      let currentRef = 'boxDataList.' + scope.$index + '.' + scope.column.property
      this.$refs.boxDataForm.validateField(currentRef, (error) => {
        if (!error) {
          this.$nextTick(() => {
            this.$refs[nextRef].focus()
          })
        }
      })
    },
    addRow () {
      let obj = {
        packageDeliveryNo: '',
        packageCustomerNo: '',
        packageLengthD: '',
        packageWidthD: '',
        packageHeightD: '',
        packageWeightD: '',
        packageQty: '',
        packageType: ''
      }
      this.boxDataForm.boxDataList.push(obj)
      this.dataForm.boxCount = this.boxDataForm.boxDataList.length || 0
    },
    copyRow (row) {
      let obj = {
        ...row,
        id: null
      }
      delete obj['allSerialNo']
      delete obj['packageSerialNo']
      delete obj['curAllPackageIdSerialNoBoxNoMap']
      this.boxDataForm.boxDataList.push(obj)
      this.dataForm.boxCount = this.boxDataForm.boxDataList.length || 0
    },
    deleteRow (index) {
      this.boxDataForm.boxDataList.splice(index, 1)
      this.dataForm.boxCount = this.boxDataForm.boxDataList.length || null
    },
    addRedStar(h, { column }) {
      return [h('span', { style: 'color: red' }, '*'), h('span', ' ' + column.label)]
    },
    handleSubmit: debounce(function () {
      this.submitLoading = true
      this.$refs['boxDataForm'].validate(async (valid) => {
        if (!valid) {
          this.$message({
            message: '请把箱信息填写完整',
            type: 'warning',
            duration: 2000,
            onClose: () => {
              this.submitLoading = false
            }
          })
          return false
        }
        let boxNos = this.boxDataForm.boxDataList.map(item => item.packageCustomerNo).filter(no => no !== undefined && no !== '')
        await this.checkBoxNo(boxNos).then(repeatBoxNos => {
          if (repeatBoxNos && repeatBoxNos.length > 0) {
            return this.$message({
              message: '箱号/FBA唛头：' + repeatBoxNos + '已被使用',
              type: 'warning',
              duration: 2000,
              onClose: () => {
                this.submitLoading = false
              }
            })
          }
          let totalWeightD = 0
          let totalBoxCount = 0
          let boxNoList = []
          for (let item of this.boxDataForm.boxDataList) {
            totalWeightD = NP.plus(totalWeightD, Number.parseFloat(item.packageWeightD) * Number.parseFloat(item.packageQty))
            totalBoxCount = NP.plus(totalBoxCount, Number.parseFloat(item.packageQty))
            let split = item.packageCustomerNo.split(/[,，]/)
            for (const i of split) {
              if (i) {
                boxNoList.push(i)
              }
            }
            if (item.curAllPackageIdSerialNoBoxNoMap) {
              const entries = Object.entries(item.curAllPackageIdSerialNoBoxNoMap)
              for (let i = 0; i < entries.length; i++) {
                const [, serialNoBoxNoMap] = entries[i]
                const [serialNo] = Object.keys(serialNoBoxNoMap)
                serialNoBoxNoMap[serialNo] = split[i] || ''
              }
            }
          }
          this.$emit('inputBoxInfoEmit', this.boxDataForm.boxDataList, this.$naturalNumberFormat(totalWeightD), totalBoxCount, boxNoList)
          this.closeHandle()
        }).finally(() => { this.submitLoading = false })
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    closeHandle () {
      this.visible = false
      this.$emit('closeInputBoxInfo')
    }
  },
  components: {
    areaBox
  }
}
</script>

<style lang='scss' scoped>
#boxDataFormId{
  ::v-deep .el-form-item {
    margin-bottom: 0!important;
  }
}
.masterColor {
  color: $--color-primary;
  font-weight: bold;
}
</style>
