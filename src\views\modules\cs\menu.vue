<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-sys__menu">
      <el-form class="form_no_margin" :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-row class="optBtn_panel">
          <el-button size="mini" type="primary" v-show="!$store.state.user.companyId"  @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-row>
      </el-form>
      <el-table v-loading="dataListLoading" :data="setDatalist()" row-key="id"  style="width: 100%;">
        <table-tree-column prop="name" :label="$t('menu.name')" header-align="center" width="150"></table-tree-column>
        <el-table-column prop="icon" :label="$t('menu.icon')" header-align="center" align="center">
          <template slot-scope="scope">
            <svg class="icon-svg" aria-hidden="true"><use :xlink:href="`#${scope.row.icon}`"></use></svg>
          </template>
        </el-table-column>
        <el-table-column prop="type" :label="$t('menu.type')" header-align="center" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.type === 0" size="small">{{ $t('menu.type0') }}</el-tag>
            <el-tag v-else size="small" type="info">{{ $t('menu.type1') }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sort" :label="$t('menu.sort')" header-align="center" align="center"></el-table-column>
        <el-table-column prop="url" :label="$t('menu.url')" header-align="center" align="center" width="150" :show-overflow-tooltip="true" ></el-table-column>
        <el-table-column prop="permissions" :label="$t('menu.permissions')" header-align="center" align="center" width="150" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column  :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-link :underline="false" v-if="!$store.state.user.companyId" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-link>
            <popconfirm i18nOperateValue="delete" @clickHandle="deleteHandle(scope.row.id)" :condition="!$store.state.user.companyId"></popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import TableTreeColumn from '@/components/table-tree-column'
import AddOrUpdate from './menu-add-or-update'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/cs/menu/list',
        deleteURL: '/cs/menu'
      },
      supperAdmin: false
    }
  },
  methods: {
    setDatalist () {
      // table row-key是针对支持树类型的数据 Error:if there’s nested data,rowKey is required
      let jsonData
      jsonData = JSON.parse(JSON.stringify(this.dataList).replace(/children/g, 'childrens'))
      return jsonData
    }
  },
  components: {
    TableTreeColumn,
    AddOrUpdate
  }
}
</script>
