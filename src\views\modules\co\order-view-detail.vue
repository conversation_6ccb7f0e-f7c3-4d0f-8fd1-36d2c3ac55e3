<template>
  <div class="add-body panel_body">
    <el-tabs class="no_shadow" type="border-card" v-model="activeName" @tab-click="tabsClick" :stretch="false">
      <el-tab-pane :label="$t('tabPane.waybill')" name="waybill">
        <el-form ref="form" label-width="120px">
          <div class="panel-hd">
            <span v-text="$t('header.waybill')"></span>
          </div>
          <div >
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.customerOrderNo')">
                  <span v-text="dataForm.customerOrderNo"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.subCustomerOrderNo')">
                  <span v-text="dataForm.subCustomerOrderNo"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.waybillNo')">
                  <span v-text="dataForm.waybillNo"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.postalTrackingNo')">
                  <span v-text="dataForm.postalTrackingNo"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.deliveryNo')">
                  <span v-text="dataForm.deliveryNo"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.consigneeCountry')">
                  <span v-text="formatterValue(dataForm.consigneeCountry,'country')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.logisticsProductCode')">
                  <span v-text="formatterValue(dataForm.logisticsProductCode,'product')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.electric')">
                  <span v-text="formatterValue(dataForm.electric,'yesOrNo')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.remote')">
                  <span v-text="formatterValue(dataForm.remote,'yesOrNo')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.parcelType')">
                  <span v-text="formatterValue(dataForm.parcelType,'parcelType')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.goodsCategory')">
                  <span v-text="formatterValue(dataForm.goodsCategory,'goodsCategory')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.packageQty')">
                  <span v-text="dataForm.packageQty"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrderShipper.iossTaxType')" prop="shipper.iossTaxType">
                  <span v-text="formatterValue(dataForm.shipper.iossTaxType,'shipper.iossTaxType')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrderShipper.iossNo')" prop="shipper.iossNo">
                  <span v-text="dataForm.shipper.iossNo"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrderShipper.vatNo')" prop="shipper.vatNo">
                  <span v-text="dataForm.shipper.vatNo"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrderShipper.eoriNo')" prop="shipper.eoriNo">
                  <span v-text="dataForm.shipper.eoriNo"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.forecastWeight')">
                  <span v-text="dataForm.forecastWeightD"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.forecastMoney')">
                  <template v-if="!dataForm.sumList || dataForm.sumList.length===0" >
                         <b>--</b>
                  </template>
                  <span v-else v-for="(item, index) in dataForm.sumList" :key="index" >
                    <span style="font-weight:bold;">{{ item.sum | numberFormat(3)}}</span>
                    <span style="color: green;padding-left: 3px;" class="order-sum">{{item.currency }}</span>
                    <template v-if="index !== dataForm.sumList.length-1"> / </template>
                  </span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.shopName')">
                  <span v-text="dataForm.shopName"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.declareCurrency')">
                  <span v-text="formatterValue(dataForm.declareCurrency,'declareCurrency')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.insuredAmount')">
                  <span v-text="dataForm.insuredAmountD"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.insuredCurrency')">
                  <span v-text="formatterValue(dataForm.insuredCurrency,'insuredCurrency')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.codAmount')">
                  <span v-text="dataForm.codAmountD"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.codCurrency')">
                  <span v-text="formatterValue(dataForm.codCurrency,'codCurrency')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.taxPayMode')">
                  <span v-text="formatterValue(dataForm.taxPayMode,'taxPayMode')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.status')" prop="status">
                  <span v-text="formatterValue(dataForm.status,'status')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.createDate')">
                  <span v-text="dataForm.createDate"></span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="panel-hd">
            <span v-text="$t('header.customer')"></span>
          </div>
          <div >
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.customerName')">
                  <span v-text="dataForm.customerName"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.customerSimpleCode')">
                  <span v-text="dataForm.customerSimpleCode"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.customerRemark')">
                  <span v-text="dataForm.customerRemark"></span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </el-tab-pane>
      <el-tab-pane :label="$t('tabPane.shipperConsignee')" name="shipperConsignee">
        <el-form ref="form" label-width="150px">
          <div class="panel-hd">
            <span v-text="$t('header.consignee')"></span>
          </div>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="$t('coOrderConsignee.consigneeName')" prop="consigneeName">
                <span v-text="dataForm.consignee.consigneeName"></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('coOrderConsignee.consigneeCompany')" prop="consigneeCompany">
                <span v-text="dataForm.consignee.consigneeCompany"></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('coOrderConsignee.consigneePhone')" prop="consigneePhone">
                <span v-text="dataForm.consignee.consigneePhone"></span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="$t('coOrderConsignee.consigneeCountry')" prop="consigneeCountry">
                <span v-text="formatterValue(dataForm.consignee.consigneeCountry,'country')"></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('coOrderConsignee.consigneeProvince')" prop="consigneeProvince">
                <span v-text="dataForm.consignee.consigneeProvince"></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('coOrderConsignee.consigneeCity')" prop="consigneeCity">
                <span v-text="dataForm.consignee.consigneeCity"></span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="16">
              <el-form-item :label="$t('coOrderConsignee.consigneeAddress')" prop="consigneeAddress">
                <span v-text="dataForm.consignee.consigneeAddress"></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('coOrderConsignee.consigneePostcode')" prop="consigneePostcode">
                <span v-text="dataForm.consignee.consigneePostcode"></span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="$t('coOrderConsignee.consigneeEmail')" prop="consigneeEmail">
                <span v-text="dataForm.consignee.consigneeEmail"></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('coOrderConsignee.consigneeDistrict')" prop="consigneeDistrict">
                <span v-text="dataForm.consignee.consigneeDistrict"></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('coOrderConsignee.consigneeDoorplate')" prop="consigneeDoorplate">
                <span v-text="dataForm.consignee.consigneeDoorplate"></span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="$t('coOrderConsignee.consigneeStreet')" prop="consigneeStreet">
                <span v-text="dataForm.consignee.consigneeStreet"></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('coOrderConsignee.consigneeIdcard')" prop="consigneeIdcard">
                <span v-text="dataForm.consignee.consigneeIdcard"></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('coOrderConsignee.consigneeTaxNo')" prop="consigneeTaxNo">
                <span v-text="dataForm.consignee.consigneeTaxNo"></span>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="panel-hd">
            <span v-text="$t('header.shipper')"></span>
          </div>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="$t('coOrderShipper.shipperName')" prop="shipperName">
                <span v-text="dataForm.shipper.shipperName"></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('coOrderShipper.shipperCompany')" prop="shipperCompany">
                <span v-text="dataForm.shipper.shipperCompany"></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('coOrderShipper.shipperPhone')" prop="shipperPhone">
                <span v-text="dataForm.shipper.shipperPhone"></span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="$t('coOrderShipper.shipperCountry')" prop="shipperCountry">
                <span v-text="formatterValue(dataForm.shipper.shipperCountry,'country')"></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('coOrderShipper.shipperProvince')" prop="shipperProvince">
                <span v-text="dataForm.shipper.shipperProvince"></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('coOrderShipper.shipperCity')" prop="shipperCity">
                <span v-text="dataForm.shipper.shipperCity"></span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="$t('coOrderShipper.shipperDistrict')" prop="shipperDistrict">
                <span v-text="dataForm.shipper.shipperDistrict"></span>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item :label="$t('coOrderShipper.shipperAddress')" prop="shipperAddress">
                <span v-text="dataForm.shipper.shipperAddress"></span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="$t('coOrderShipper.shipperPostcode')" prop="shipperPostcode">
                <span v-text="dataForm.shipper.shipperPostcode"></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('coOrderShipper.shipperEmail')" prop="shipperEmail">
                <span v-text="dataForm.shipper.shipperEmail"></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('coOrderShipper.shipperDoorplate')" prop="shipperDoorplate">
                <span v-text="dataForm.shipper.shipperDoorplate"></span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="$t('coOrderShipper.shipperStreet')" prop="shipperStreet">
                <span v-text="dataForm.shipper.shipperStreet"></span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>
      <el-tab-pane :label="$t('tabPane.declare')" name="declare">
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <el-table :key="Math.random()" ref="declareDataList" :data="declareDataList" border>
            <el-table-column :label="$t('system.index')" type="index" width="50"></el-table-column>
            <el-table-column v-for="(item, index) in declareTableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
              <template slot-scope="scope">
                <div>
                  <div v-if="item.prop === 'createDate'">
                    <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                      <span>{{formatterFn(scope,item.prop)}}</span>
                    </el-tooltip>
                  </div>
                  <div v-else>
                    {{formatterFn(scope,item.prop)}}
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
      <el-tab-pane :label="$t('tabPane.package')" name="package">
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <el-table :key="Math.random()" v-loading="dataListLoading" :data="packageDataList" border>
            <!-- 动态显示表格 -->
            <el-table-column :label="$t('system.index')" type="index" width="50"></el-table-column>
            <el-table-column v-for="(item, index) in packageTableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
              <template slot-scope="scope">
                <div>
                  <div v-if="item.prop === 'createDate'">
                    <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                      <span>{{formatterFn(scope,item.prop)}}</span>
                    </el-tooltip>
                  </div>
                  <div v-else>
                    {{formatterFn(scope,item.prop)}}
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>
    <div id="cs_FormFooter" class="el-form-footer" slot="footer">
      <el-button @click="backFn">{{ $t('back') }}</el-button>
    </div>
  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import listPage from '@/mixins/listPage'
import { formatterType, gtmToLtm, timestampFormat, formatterUserName, formatterCodeName, numberFormat } from '@/filters/filters'
import dictTypeMixins from '@/mixins/dictTypeMixins'
import baseDataApi from '@/api'
import baseData from '@/api/baseData'

export default {
  mixins: [listPage, mixinViewModule, dictTypeMixins],
  data () {
    return {
      activeName: 'waybill',
      remark: '',
      dataListLoading: false,
      dataForm: {
        id: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: '',
        status: '',
        version: '',
        orderId: '',
        customerOrderNo: '',
        subCustomerOrderNo: '',
        waybillNo: '',
        deliveryNo: '',
        logisticsProductCode: '',
        forecastWeightD: '',
        weightUnit: '',
        goodsCategory: '',
        parcelType: '',
        electric: '',
        remote: '',
        consigneeCountry: '',
        customerRemark: '',
        serviceId: '',
        salesmanId: '',
        customerId: '',
        customerName: '',
        customerSimpleCode: '',
        platformType: '',
        shopName: '',
        sumList: [],
        packageQty: '',
        shipper: {
          shipperName: '',
          shipperCompany: '',
          shipperPhone: '',
          shipperEmail: '',
          iossTaxType: '',
          shipperCountry: 'CN',
          shipperProvince: '',
          shipperCity: '',
          shipperDistrict: '',
          shipperAddress: '',
          shipperPostcode: '',
          shipperDoorplate: '',
          shipperStreet: '',
          iossNo: '',
          vatNo: '',
          eoriNo: ''
        },
        consignee: {
          consigneeName: '',
          consigneeCompany: '',
          consigneePhone: '',
          consigneeEmail: '',
          consigneeProvince: '',
          consigneeCity: '',
          consigneeDistrict: '',
          consigneeAddress: '',
          consigneePostcode: '',
          consigneeDoorplate: '',
          consigneeStreet: '',
          consigneeIdcard: ''
        }
      },
      declareDataList: [],
      declareTableColumns: [
        { type: '', width: '80', prop: 'chineseName', label: this.$t('coOrderDeclare.chineseName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'englishName', label: this.$t('coOrderDeclare.englishName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'quantity', label: this.$t('coOrderDeclare.quantity'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'unitNetWeightD', label: this.$t('coOrderDeclare.unitNetWeight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'unitDeclarePriceD', label: this.$t('coOrderDeclare.unitDeclarePrice'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'brand', label: this.$t('coOrderDeclare.brand'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'goodsBarcode', label: this.$t('coOrderDeclare.goodsBarcode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'sku', label: this.$t('coOrderDeclare.sku'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'hsCode', label: this.$t('coOrderDeclare.hsCode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'productModel', label: this.$t('coOrderDeclare.productModel'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'material', label: this.$t('coOrderDeclare.material'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'purpose', label: this.$t('coOrderDeclare.purpose'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'origin', label: this.$t('coOrderDeclare.origin'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'pickingRemark', label: this.$t('coOrderDeclare.pickingRemark'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'productUrl', label: this.$t('coOrderDeclare.productUrl'), align: 'center', isShow: true, disabled: false }
      ],
      packageDataList: [],
      packageTableColumns: [
        { type: '', width: '150', prop: 'packageSerialNo', label: this.$t('coOrderPackage.packageSerialNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'packageDeliveryNo', label: this.$t('coOrderPackage.packageDeliveryNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'packageCustomerNo', label: this.$t('coOrderPackage.packageCustomerNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'packageWeightD', label: this.$t('coOrderPackage.packageWeight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'packageLengthD', label: this.$t('coOrderPackage.packageLength'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'packageWidthD', label: this.$t('coOrderPackage.packageWidth'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'packageHeightD', label: this.$t('coOrderPackage.packageHeight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'channelLabelUrl', label: this.$t('coOrderPackage.channelLabelUrl'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '150', prop: 'id', label: this.$t('system.id'), align: 'center', isShow: false, disabled: true }
      ],
      // 数据字典
      comWaybillLogOperateNodeList: [],
      parcelTypeList: [],
      goodsCategoryList: [],
      yesOrNoList: [],
      statusList: [],
      taxPayModeList: [],
      // 基础资料
      logisticsProductByParamsList: [],
      iossTypeList: [],
      userList: [],
      currencyList: [],
      countryList: []
    }
  },
  created () {
    // 获取基础数据
    this.getBaseData()
    // 获取数据字典
    this.getDict()
  },
  methods: {
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        if (this.dataForm.id) {
          this.getOrder(this.getShipper())
        }
      })
    },
    async getBaseData () {
      this.logisticsProductByParamsList = await baseData(baseDataApi.logisticsProductByParamsList).catch(() => {})
      this.countryList = await baseData(baseDataApi.countryList)
      this.userList = await baseData(baseDataApi.userList).catch(() => {})
      this.currencyList = await baseData(baseDataApi.currencyList)
    },
    async getDict () {
      this.comWaybillLogOperateNodeList = await this.getDictTypeList('ComWaybillLogOperateNode')
      // IOSS类型
      this.iossTypeList = await this.getDictTypeList('IossType')
      this.yesOrNoList = await this.getDictTypeList('yesOrNo')
      this.parcelTypeList = await this.getDictTypeList('OrderParcelType')
      this.goodsCategoryList = await this.getDictTypeList('OrderGoodsCategory')
      this.statusList = await this.getDictTypeList('OrderStatus')
      this.taxPayModeList = await this.getDictTypeList('OrderTaxPayMode') // 税制模式
      this.objectTypeList = await this.getDictTypeList('coOrderObjectType')
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'createDate':
          value = timestampFormat(gtmToLtm(scope.row.createDate), 'YYYY-MM-DD hh:mm:ss')
          break
        case 'status':
          value = formatterType(scope.row.status, this.statusList)
          break
        case 'creator':
          value = formatterUserName(scope.row.creator, this.userList)
          break
        case 'operateNode':
          value = formatterType(scope.row.operateNode, this.comWaybillLogOperateNodeList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    formatterValue (value, field) {
      if (value !== undefined && value !== null && value !== '') {
        switch (field) {
          case 'product':
            value = formatterCodeName(value, this.logisticsProductByParamsList)
            break
          case 'shipper.iossTaxType':
            value = formatterType(value, this.iossTypeList)
            break
          case 'creator':
            value = formatterUserName(value, this.userList)
            break
          case 'parcelType':
            value = formatterType(value, this.parcelTypeList)
            break
          case 'goodsCategory':
            value = formatterType(value, this.goodsCategoryList)
            break
          case 'yesOrNo':
            value = formatterType(value, this.yesOrNoList)
            break
          case 'country':
            value = formatterCodeName(value, this.countryList)
            break
          case 'insuredCurrency':
            value = formatterCodeName(value, this.currencyList)
            break
          case 'declareCurrency':
            value = formatterCodeName(value, this.currencyList)
            break
          case 'codCurrency':
            value = formatterCodeName(value, this.currencyList)
            break
          case 'taxPayMode':
            value = formatterType(value, this.taxPayModeList)
            break
          case 'status':
            value = formatterType(value, this.statusList)
            break
        }
      }
      return value
    },
    // 获取信息
    getOrder (callback) {
      this.$http.get(`/co/order/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        if (!res.data.shipper) {
          delete res.data['shipper']
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
        this.dataForm.subCustomerOrderNo = res.data.packageList[0].subCustomerOrderNo
        callback && callback()
      }).catch(() => {})
    },
    tabsClick (tab, event) {
      // let url
      this.dataList = []
      switch (tab.name) {
        case 'shipperConsignee':
          this.getShipper()
          this.getConsignee()
          break
        case 'declare':
          this.getDeclare()
          break
        case 'package':
          this.getPackage()
          break
        default:
          break
      }
    },
    // 返回
    backFn () {
      this.visible = false
      this.$emit('backView')
    },
    getShipper () {
      this.$http.get('/co/ordershipper/' + this.dataForm.id).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        if (res.data) {
          this.dataForm.shipper = res.data
        }
      }).catch(() => { })
    },
    getConsignee () {
      this.$http.get('/co/orderconsignee/' + this.dataForm.id).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm.consignee = res.data
      }).catch(() => { })
    },
    getDeclare () {
      this.$http.get('/co/orderdeclare/orderid', { params: { orderId: this.dataForm.id } }).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.declareDataList = res.data
      }).catch(() => { })
    },
    getPackage () {
      this.$http.get('/co/orderpackage/list', { params: { orderId: this.dataForm.id } }).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.packageDataList = res.data
      }).catch(() => { })
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    numberFormat,
    timestampFormat
  },
  computed: {
    packageTableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.packageTableColumns).map((key) => this.packageTableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    declareTableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.declareTableColumns).map((key) => this.declareTableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    }
  }
}
</script>
