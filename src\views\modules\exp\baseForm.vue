<template>
  <div class="panel_body">
    <div class="panel-hd">
      {{!dataForm.id ? $t('add') : $t('update')}}
    </div>
    <div class="addOrUpdatePanel" >
      <el-form ref="form" :model="dataForm" label-width="80px">
        <el-row type="flex" justify="start">
          <el-col :md="{span:13, offset: 2}">
            <el-form-item label="活动名称">
              <el-input v-model="dataForm.name"></el-input>
            </el-form-item>
            <el-form-item label="活动区域">
              <el-select v-model="dataForm.region" placeholder="请选择活动区域">
                <el-option label="区域一" value="shanghai"></el-option>
                <el-option label="区域二" value="beijing"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="活动时间">
              <el-date-picker
                class="width100"
                v-model="dataForm.date1"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="即时配送">
              <el-switch v-model="dataForm.delivery"></el-switch>
            </el-form-item>
            <el-form-item label="活动性质">
              <el-checkbox-group v-model="dataForm.type">
                <el-checkbox label="美食/餐厅线上活动" name="type"></el-checkbox>
                <el-checkbox label="地推活动" name="type"></el-checkbox>
                <el-checkbox label="线下主题活动" name="type"></el-checkbox>
                <el-checkbox label="单纯品牌曝光" name="type"></el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="特殊资源">
              <el-radio-group v-model="dataForm.resource">
                <el-radio label="线上品牌商赞助"></el-radio>
                <el-radio label="线下场地免费"></el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="活动形式">
              <el-input type="textarea" v-model="dataForm.desc"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSubmit">立即创建</el-button>
              <el-button @click.native="_resetForm('searchForm')" icon="el-icon-refresh-right">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'add',
  data () {
    return {
      dataForm: {
        id: '',
        name: '',
        region: '',
        date1: '',
        delivery: false,
        type: [],
        resource: '',
        desc: ''
      }
    }
  },
  computed: {
    dataRule () {
      return []
    }
  },
  methods: {
    init (id) {
      // 如果菜单浮动位置 需要初始化
      this.$footerScroll()
    },
    // 返回
    backFn () {
      this.$emit('cancelAddOrUpdate')
    },
    // 提交表单
    onSubmit () {
    },
    // 重置表单
    _resetForm (formName) {
      this.$nextTick(() => {
        this.$refs[formName].resetFields()
      })
    }
  }
}
</script>

<style scoped>

</style>
