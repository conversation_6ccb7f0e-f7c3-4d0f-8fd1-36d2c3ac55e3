<template>
  <div class="aui-card--fill">
    <div class="mod-co__inorder flex_wrap" v-show="!panelShow" ref="listPanel">
      <el-card class="search_box" shadow="never">
        <el-form ref="searchForm" class="form_no_margin" :model="dataForm" @keyup.enter.native="queryPageByParam()" label-width="130px">
          <el-row :gutter="10" type="flex">
            <el-col >
              <el-row :gutter="10">
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coSetDiyimportModule.name')" prop="name">
                    <el-input v-model="dataForm.name" :placeholder="$t('coSetDiyimportModule.name')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coSetDiyimportModule.mainObjectName')" prop="mainObjectName">
                    <el-select filterable v-model="dataForm.mainObjectName"
                               :placeholder="$t('coSetDiyimportModule.mainObjectName')" clearable>
                      <el-option v-for="item in excelMasterList" v-if="item.className === 'CoOrderParamDTO'" :key="item.className" :label="item.name" :value="item.className" :disabled="item.disabled"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <div class="search_box_btn no_more">
              <el-button type="primary" @click="queryPageByParam()" icon="el-icon-search">查询</el-button>
              <el-button @click.native="_resetForm('searchForm')" icon="el-icon-refresh-right">重置</el-button>
            </div>
          </el-row>
        </el-form>
      </el-card>

      <el-card class="flex_tab no_shadow" type="border-card" >
        <el-row class="optBtn_panel">
          <el-col :md="12" class='optBtn_leftFixed'>
            <el-button size="mini" type="primary" plain @click="deleteHandle()">{{ $t('delete') }}</el-button>
            <el-button size="mini" type="primary" plain @click="changeStatusHandle(1)">{{ $t('enable') }}</el-button>
            <el-button size="mini" type="primary" plain @click="changeStatusHandle(0)">{{ $t('disable') }}</el-button>

            <!--保留空格符-->
          </el-col>
          <el-col :md="12" class="text-right">
            <el-button size="mini" type="primary" plain v-if="$hasPermission('co:setdiyimportmodule:update')" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
          </el-col>
        </el-row>
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <el-table v-loading="dataListLoading" :data="dataList"  @selection-change="dataListSelectionChangeHandle" :max-height="tableHeight">
            <!-- 动态显示表格 -->
            <el-table-column type="selection" width='50' fixed="left"></el-table-column>
            <el-table-column v-for="(item, index) in tableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
              <template slot-scope="scope">
                <div>
                  <div v-if="item.prop === 'createDate'">
                    <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                      <span>{{formatterFn(scope,item.prop)}}</span>
                    </el-tooltip>
                  </div>
                  <div v-else-if="item.prop === 'status'">
                    <el-badge is-dot class="badge_status" v-if="scope.row.status === 0" type="info"></el-badge>
                    <el-badge is-dot class="badge_status" v-else-if="scope.row.status === 1" type="success"></el-badge>
                    <span>{{ statusFm(scope.row.status) }}</span>
                  </div>
                  <div v-else>
                    {{formatterFn(scope,item.prop)}}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="150">
              <template slot="header" slot-scope="scope">
                <span>{{$t('handle')}}</span>
                <el-tooltip :content="$t('tableConfigTooltip')" placement="top">
                  <el-link style="margin-left:10px; font-size:16px" class="el-icon-setting" @click="settingCol(tableColumns)"></el-link>
                </el-tooltip>
              </template>
              <template slot-scope="scope">
                <el-button type="text" size="mini"  v-if="$hasPermission('co:setdiyimportmodule:update') && scope.row.status===0" @click="changeStatusHandle(1, scope.row.id)">{{ $t('enable') }}</el-button>
                <el-button type="text" size="mini"  v-if="$hasPermission('co:setdiyimportmodule:update') && scope.row.status===1" @click="changeStatusHandle(0, scope.row.id)">{{ $t('disable') }}</el-button>
                <el-button type="text" size="mini"  v-if="$hasPermission('co:setdiyimportmodule:update')" @click="addOrUpdateHandle(scope.row.id,scope.row.mainObjectName)">{{ $t('update') }}</el-button>
                <el-button type="text" size="mini"  v-if="$hasPermission('co:setdiyimportmodule:update')" @click="editDetailHandle(scope.row.id,scope.row.mainObjectName)">{{ $t('template.editdetail') }}</el-button>
                <el-button type="text" size="mini"  v-if="$hasPermission('co:setdiyimportmodule:view')" @click="loadOrderTemplateByDiy(scope.row.id)">{{ $t('template.download') }}</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination background :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle" @current-change="pageCurrentChangeHandle">
        </el-pagination>
      </el-card>
    </div>
    <!-- 自定义列显示弹窗 -->
    <table-set ref="tableSet"></table-set>
    <!-- 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList" @cancelAddOrUpdate="cancelAddOrUpdate"></add-or-update>
    <!-- 详情页面 -->
    <detailEdit v-if="detailVisible" ref="detailEdit" @backView="backView"></detailEdit>
  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import listPage from '@/mixins/listPage'
import api from '@/api'
import baseData from '@/api/baseData'
import { formatterType, gtmToLtm, timestampFormat, formatterClassName } from '@/filters/filters'
import dictTypeMixins from '@/mixins/dictTypeMixins'
// table 自定义显示
import tableSet from '@/components/tableSet'
import AddOrUpdate from './set-diyimport-module-add-or-update'
import DetailEdit from './set-diyimport-detail-edit-dialog'
// import DetailEdit from './set-diyimport-module-detail'
import qs from 'qs'
import Cookies from 'js-cookie'
// import ViewDetail from './set-diyimport-module-view-detail'
export default {
  mixins: [listPage, mixinViewModule, dictTypeMixins],
  data () {
    return {
      tableColumns: [
        { type: '', width: '150', prop: 'name', label: this.$t('coSetDiyimportModule.name'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'description', label: this.$t('coSetDiyimportModule.description'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'mainObjectName', label: this.$t('coSetDiyimportModule.mainObjectName'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '150', prop: 'customerId', label: this.$t('coSetDiyimportModule.customerId'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '100', prop: 'status', label: this.$t('system.status'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'id', label: this.$t('system.id'), align: 'center', isShow: false, disabled: false }
      ],
      mixinViewModuleOptions: {
        getDataListURL: '/co/setdiyimportmodule/page',
        getDataListIsPage: true,
        deleteURL: '/co/setdiyimportmodule',
        deleteIsBatch: true
      },
      dataForm: {
        id: '',
        name: '',
        mainObjectName: ''
      },
      changeStatusForm: {
        ids: [],
        status: ''
      },
      excelMasterList: [],
      activeName: 'all',
      detailVisible: false,
      exportURL: '/co/setdiyimportmoduledetail/export',
      tableName: 'co-setdiyimportmodule'
    }
  },
  created () {
    this.getBaseData()
  },
  methods: {
    async getBaseData () {
      // urlDTO对应的字段
      baseData(api.getExcelImportMaster).then(res => {
        this.excelMasterList = res
      })
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'mainObjectName':
          value = formatterClassName(scope.row.mainObjectName, this.excelMasterList)
          break
        case 'createDate':
          value = timestampFormat(gtmToLtm(scope.row.createDate), 'YYYY-MM-DD')
          break
        case 'status':
          value = formatterType(scope.row.status, this.statusList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    statusFm (currentState) {
      let noUseStateValue = 0
      let enableStateValue = 1
      let defaultDisplayStr = this.$t('disable')
      switch (currentState) {
        case noUseStateValue:
          return this.$t('disable')
        case enableStateValue:
          return this.$t('enable')
        default:
          return defaultDisplayStr
      }
    },
    // 返回
    backFn () {
      this.detailVisible = false
      this.$emit('cancelAddOrUpdate')
    },
    // 新增 / 修改
    addOrUpdateHandle (id, mainObjectName) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.dataForm.id = id
        this.$refs.addOrUpdate.dataForm.objectName = mainObjectName
        this.$refs.addOrUpdate.init()
      })
    },
    // 新增 / 修改
    editDetailHandle (id, mainObjectName) {
      this.detailVisible = true
      this.$nextTick(() => {
        this.$refs.detailEdit.dataForm.moduleId = id
        this.$refs.detailEdit.dataForm.mainObjectName = mainObjectName
        this.$refs.detailEdit.init()
      })
    },
    loadOrderTemplateByDiy (moduleId) {
      var params = qs.stringify({
        'token': Cookies.get('cs_token'),
        'moduleId': moduleId
      })
      window.location.href = `${this.$baseUrl}${this.exportURL}?${params}`
    },
    // 启用停用
    changeStatusHandle (status, id) {
      if (!id) {
        if (this.dataListSelections.length <= 0) {
          return this.$message({
            message: this.$t('prompt.actionStatusBatch'),
            type: 'warning',
            duration: 500
          })
        }
      }
      this.changeStatusForm.ids = id ? [id] : this.dataListSelections.map(item => item.id)
      this.changeStatusForm.status = status
      this.$http.post('/co/setdiyimportmodule/updateStatus', this.changeStatusForm).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 500,
          onClose: () => {
            this.getDataList()
          }
        })
      }).catch(() => {})
    },
    // 自定义列显示
    settingCol (tableColumns) {
      this.$nextTick(() => {
        this.$refs['tableSet'].init(this.tableName, tableColumns)
      })
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat
  },
  computed: {
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    panelShow () {
      let ret = false
      if (this.addOrUpdateVisible) {
        ret = true
      }
      return ret
    }
  },
  components: {
    AddOrUpdate,
    DetailEdit,
    tableSet
  }
}
</script>
