<template>
  <div class="aui-card--fill">
    <div class="mod-co__inorder flex_wrap" v-show="!panelShow" ref="listPanel">
      <el-card class="search_box" shadow="never">
        <el-form ref="searchForm" class="form_no_margin" :model="dataForm" @keyup.enter.native="queryPageByParam()" label-width="80px">
          <el-row :gutter="10" type="flex">
            <el-col >
              <el-row :gutter="10">
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrder.customerOrderNo')" prop="customerOrderNo">
                    <el-input v-model="dataForm.customerOrderNo" :placeholder="$t('coOrder.customerOrderNo')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrder.waybillNo')" prop="waybillNo">
                    <el-input v-model="dataForm.waybillNo" :placeholder="$t('coOrder.waybillNo')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrder.deliveryNo')" prop="deliveryNo">
                    <el-input v-model="dataForm.deliveryNo" :placeholder="$t('coOrder.deliveryNo')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrder.logisticsProductCode')" prop="logisticsProductCode">
                    <el-input v-model="dataForm.logisticsProductCode" :placeholder="$t('coOrder.logisticsProductCode')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrder.taxPayMode')" prop="taxPayMode">
                    <el-input v-model="dataForm.taxPayMode" :placeholder="$t('coOrder.taxPayMode')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrder.forecastWeight')" prop="forecastWeight">
                    <el-input v-model="dataForm.forecastWeight" :placeholder="$t('coOrder.forecastWeight')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrder.parcelType')" prop="parcelType">
                    <el-input v-model="dataForm.parcelType" :placeholder="$t('coOrder.parcelType')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrder.goodsCategory')" prop="goodsCategory">
                    <el-input v-model="dataForm.goodsCategory" :placeholder="$t('coOrder.goodsCategory')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrder.electric')" prop="electric">
                    <el-input v-model="dataForm.electric" :placeholder="$t('coOrder.electric')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrder.remote')" prop="remote">
                    <el-input v-model="dataForm.remote" :placeholder="$t('coOrder.remote')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrder.insuredAmount')" prop="insuredAmount">
                    <el-input v-model="dataForm.insuredAmount" :placeholder="$t('coOrder.insuredAmount')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrder.insuredCurrency')" prop="insuredCurrency">
                    <el-input v-model="dataForm.insuredCurrency" :placeholder="$t('coOrder.insuredCurrency')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrder.codAmount')" prop="codAmount">
                    <el-input v-model="dataForm.codAmount" :placeholder="$t('coOrder.codAmount')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrder.codCurrency')" prop="codCurrency">
                    <el-input v-model="dataForm.codCurrency" :placeholder="$t('coOrder.codCurrency')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrder.declareCurrency')" prop="declareCurrency">
                    <el-input v-model="dataForm.declareCurrency" :placeholder="$t('coOrder.declareCurrency')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrder.consigneeCountry')" prop="consigneeCountry">
                    <el-input v-model="dataForm.consigneeCountry" :placeholder="$t('coOrder.consigneeCountry')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrder.customerRemark')" prop="customerRemark">
                    <el-input v-model="dataForm.customerRemark" :placeholder="$t('coOrder.customerRemark')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrder.serviceId')" prop="serviceId">
                    <el-input v-model="dataForm.serviceId" :placeholder="$t('coOrder.serviceId')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrder.salesmanId')" prop="salesmanId">
                    <el-input v-model="dataForm.salesmanId" :placeholder="$t('coOrder.salesmanId')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrder.customerId')" prop="customerId">
                    <el-input v-model="dataForm.customerId" :placeholder="$t('coOrder.customerId')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrder.customerName')" prop="customerName">
                    <el-input v-model="dataForm.customerName" :placeholder="$t('coOrder.customerName')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrder.customerSimpleCode')" prop="customerSimpleCode">
                    <el-input v-model="dataForm.customerSimpleCode" :placeholder="$t('coOrder.customerSimpleCode')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrder.franchiseeId')" prop="franchiseeId">
                    <el-input v-model="dataForm.franchiseeId" :placeholder="$t('coOrder.franchiseeId')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrder.warehouseId')" prop="warehouseId">
                    <el-input v-model="dataForm.warehouseId" :placeholder="$t('coOrder.warehouseId')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrder.platformType')" prop="platformType">
                    <el-input v-model="dataForm.platformType" :placeholder="$t('coOrder.platformType')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrder.salesUrl')" prop="salesUrl">
                    <el-input v-model="dataForm.salesUrl" :placeholder="$t('coOrder.salesUrl')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrder.packageQty')" prop="packageQty">
                    <el-input v-model="dataForm.packageQty" :placeholder="$t('coOrder.packageQty')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrder.print')" prop="print">
                    <el-input v-model="dataForm.print" :placeholder="$t('coOrder.print')" clearable ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <div class="search_box_btn no_more">
              <el-button type="primary" @click="queryPageByParam()" icon="el-icon-search">查询</el-button>
              <el-button @click.native="_resetForm('searchForm')" icon="el-icon-refresh-right">重置</el-button>
            </div>
          </el-row>
        </el-form>
      </el-card>

      <el-card class="flex_tab no_shadow" type="border-card" >
        <el-row class="optBtn_panel">
          <el-col :md="12" class='optBtn_leftFixed'>
            <el-button size="mini" @click="deleteHandle()">{{ $t('delete') }}</el-button>
            <el-button size="mini" >{{ $t('audit') }}</el-button>
            <!--保留空格符-->
          </el-col>
          <el-col :md="12" class="text-right">
            <el-button size="mini"  v-if="$hasPermission('co:order:save')" plain @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
          </el-col>
        </el-row>
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <el-table v-loading="dataListLoading" :data="dataList"  @selection-change="dataListSelectionChangeHandle" :max-height="tableHeight">
            <!-- 动态显示表格 -->
            <el-table-column type="selection" width='50' fixed="left"></el-table-column>
            <el-table-column v-for="(item, index) in tableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
              <template slot-scope="scope">
                <div>
                  <div v-if="item.prop === 'createDate'">
                    <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                      <span>{{formatterFn(scope,item.prop)}}</span>
                    </el-tooltip>
                  </div>
                  <div v-else>
                    {{formatterFn(scope,item.prop)}}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="150">
              <template slot="header" slot-scope="scope">
                <span>{{$t('handle')}}</span>
                <el-tooltip :content="$t('tableConfigTooltip')" placement="top">
                  <el-link style="margin-left:10px; font-size:16px" class="el-icon-setting" @click="settingCol(tableColumns)"></el-link>
                </el-tooltip>
              </template>
              <template slot-scope="scope">
                <el-link :underline="false"  @click="viewHandle(scope.row.id)">{{ $t('view') }}</el-link>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination background :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle" @current-change="pageCurrentChangeHandle">
        </el-pagination>
      </el-card>
    </div>
    <!-- 自定义列显示弹窗 -->
    <table-set ref="tableSet"></table-set>
    <!-- 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList" @cancelAddOrUpdate="cancelAddOrUpdate"></add-or-update>
    <!-- 详情页面 -->
    <viewDetail v-if="viewVisible" ref="viewDetail" @backView="backView"></viewDetail>
  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import listPage from '@/mixins/listPage'
import { formatterType, gtmToLtm, timestampFormat } from '@/filters/filters'
import dictTypeMixins from '@/mixins/dictTypeMixins'
// table 自定义显示
import tableSet from '@/components/tableSet'
import AddOrUpdate from './order-add-or-update'
import ViewDetail from './order-view-detail'
export default {
  mixins: [listPage, mixinViewModule, dictTypeMixins],
  data () {
    return {
      tableColumns: [
        { type: '', width: '150', prop: 'customerOrderNo', label: this.$t('coOrder.customerOrderNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'waybillNo', label: this.$t('coOrder.waybillNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'deliveryNo', label: this.$t('coOrder.deliveryNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'logisticsProductCode', label: this.$t('coOrder.logisticsProductCode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'taxPayMode', label: this.$t('coOrder.taxPayMode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'forecastWeight', label: this.$t('coOrder.forecastWeight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'parcelType', label: this.$t('coOrder.parcelType'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'goodsCategory', label: this.$t('coOrder.goodsCategory'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'electric', label: this.$t('coOrder.electric'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'remote', label: this.$t('coOrder.remote'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'insuredAmount', label: this.$t('coOrder.insuredAmount'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'insuredCurrency', label: this.$t('coOrder.insuredCurrency'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'codAmount', label: this.$t('coOrder.codAmount'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'codCurrency', label: this.$t('coOrder.codCurrency'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'declareCurrency', label: this.$t('coOrder.declareCurrency'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'consigneeCountry', label: this.$t('coOrder.consigneeCountry'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'customerRemark', label: this.$t('coOrder.customerRemark'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'serviceId', label: this.$t('coOrder.serviceId'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'salesmanId', label: this.$t('coOrder.salesmanId'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'customerId', label: this.$t('coOrder.customerId'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'customerName', label: this.$t('coOrder.customerName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'customerSimpleCode', label: this.$t('coOrder.customerSimpleCode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'franchiseeId', label: this.$t('coOrder.franchiseeId'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'warehouseId', label: this.$t('coOrder.warehouseId'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'platformType', label: this.$t('coOrder.platformType'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'salesUrl', label: this.$t('coOrder.salesUrl'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'packageQty', label: this.$t('coOrder.packageQty'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'print', label: this.$t('coOrder.print'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'id', label: this.$t('system.id'), align: 'center', isShow: true, disabled: false }
      ],
      mixinViewModuleOptions: {
        getDataListURL: '/co/order/page',
        getDataListIsPage: true,
        deleteURL: '/co/order',
        deleteIsBatch: true
      },
      dataForm: {
        id: ''
      },
      activeName: 'all',
      tableName: 'co-order'
    }
  },
  created () {
  },
  methods: {
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'createDate':
          value = timestampFormat(gtmToLtm(scope.row.createDate), 'YYYY-MM-DD')
          break
        case 'status':
          value = formatterType(scope.row.status, this.statusList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    // 自定义列显示
    settingCol (tableColumns) {
      this.$nextTick(() => {
        this.$refs['tableSet'].init(this.tableName, tableColumns)
      })
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat
  },
  computed: {
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    panelShow () {
      let ret = false
      if (this.addOrUpdateVisible) {
        ret = true
      } else if (this.viewVisible) {
        ret = true
      }
      return ret
    }
  },
  components: {
    AddOrUpdate,
    ViewDetail,
    tableSet
  }
}
</script>
