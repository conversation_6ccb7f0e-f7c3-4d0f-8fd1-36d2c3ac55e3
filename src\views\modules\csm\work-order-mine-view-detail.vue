<template>
  <div class="add-body panel_body">
    <div>
      <el-steps :active="active" finish-status="success" simple style="margin-top: 20px">
        <el-step :title="item.title"  v-for="item in stepList" :key="item.value">{{item.title}}</el-step>
      </el-steps>
      <el-form inline ref="form" :model="dataForm" label-width="120px" :rules="dataFormRules" :disabled="dataForm.status===30" >
        <h3>{{$t('header.workOrder')}}:</h3>
        <el-row :gutter="20">
          <el-col :md="{span:8}">
            <el-form-item :label="$t('csmWorkOrder.workOrderNo')">
              <span v-text="dataForm.workOrderNo"></span>
            </el-form-item>
          </el-col>
          <el-col :md="{span:8}">
            <el-form-item :label="$t('csmWorkOrder.waybillNo')">
              <span v-text="dataForm.waybillNo"></span>
            </el-form-item>
          </el-col>
          <el-col :md="{span:8}">
            <el-form-item :label="$t('csmWorkOrder.workOrderTypeParentId')">
              <span v-text="formatterValue(dataForm.workOrderTypeParentId,'workOrderType')"></span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
        <el-col :md="{span:8}">
          <el-form-item :label="$t('csmWorkOrder.workOrderTypeId')">
            <span v-text="formatterValue(dataForm.workOrderTypeId,'workOrderType')"></span>
          </el-form-item>
        </el-col>
        <el-col :md="{span:8}">
          <el-form-item :label="$t('csmWorkOrder.acceptServiceId')">
            <span v-text="formatterValue(dataForm.acceptServiceId,'user')"></span>
          </el-form-item>
        </el-col>
        <el-col :md="{span:8}">
          <el-form-item :label="$t('csmWorkOrder.createDate')">
            <span v-text="dataForm.createDate"></span>
          </el-form-item>
        </el-col>
        <el-col :md="{span:8}">
          <el-form-item :label="$t('csmWorkOrder.contactPerson')">
            <span v-text="dataForm.contactPerson"></span>
          </el-form-item>
        </el-col>
        <el-col :md="{span:8}">
          <el-form-item :label="$t('csmWorkOrder.phone')" >
            <span v-text="dataForm.phone" ></span>
          </el-form-item>
        </el-col>
        <el-col :md="{span:20}">
          <el-form-item :label="$t('csmWorkOrder.problemStatement')">
            <span v-text="dataForm.problemStatement"></span>
          </el-form-item>
        </el-col>
        <el-col :md="{span:20}">
          <el-form-item :label="$t('label.attachment')">
            <div v-if="attachmentList.length > 0 && otherList.length > 0">
              <el-image
                style="width: 100px; height: 100px"
                v-if="attachment"
                :src="attachment"
                :preview-src-list="attachmentList">
              </el-image>
              <el-tag class="otherAttach" type="info" v-if="otherList.length > 0">
                <a :href="item.attachmentUrl"  v-for="(item, index) in otherList" :key="index" download> {{item.attachmentName}}</a>
              </el-tag>
            </div>
            <span v-else>
              无
            </span>
          </el-form-item>
          </el-col>
        </el-row>
        <h3>{{$t('header.feedbackRecord')}}:</h3>
        <div class="feedback_wrapper" style="overflow:auto">
          <el-timeline>
            <el-timeline-item placement="top" v-for="item in communicationList" :key="item.id">
              <el-card class="no_shadow" :body-style="{ padding: '0 10px' }">
                <div class="avatar_box block">
                  <!--<el-avatar shape="square" :size="50" :src="squareUrl"></el-avatar>-->
                  <img class="avatar_img" src="~@/assets/img/avatar.png">
                </div>
                <p>{{item.communicationContent}}</p>
                <p><span><b>{{$t('label.time')}}:</b>&nbsp;{{item.createDate}}</span> <span style="margin-left:50px"><b>{{$t('label.ip')}}:</b>&nbsp;{{item.ipAddress}}</span></p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>

        <h3 v-if="dataForm.status!==25 && dataForm.status!==30">{{$t('header.answerService')}}：</h3>
        <div class="clearfix" v-if="dataForm.status!==25 && dataForm.status!==30" style="margin-bottom: 50px">
          <el-input :disabled="dataForm.status===25 || dataForm.status===30"  v-model="dataForm.communicationContent" type="textarea" :rows="6" autocomplete="off" :maxlength="512"></el-input>
          <btn-upload-attachment ref="btnUploadAttachment" :attachmentFiles.sync="fileList"></btn-upload-attachment>
          <el-row class="margin_top15" type="flex" justify="center">
            <el-col class="text-right">
              <el-button type="primary" @click="replayAndFinishHandle">{{$t('btn.replayAndFinish')}}</el-button>
              <el-button type="primary" @click="onlySubmitReplayHandle">{{$t('btn.onlySubmitReplay')}}</el-button>
            </el-col>
          </el-row>
        </div>

        <h3 v-if="dataForm.status===25">{{$t('header.customerEvaluation')}}:</h3>
        <div v-if="dataForm.status===25" class="feedback_wrapper" style="overflow:auto">
          <el-row :gutter="24" class="margin_bottom22">
            <el-col :span="24">
              <el-form-item :label="$t('csmWorkOrderEvaluation.score')" prop="evaluation.score">
                <el-radio-group v-model="dataForm.evaluation.score">
                  <el-radio v-for="item in dict.scoreList" :key="item.dictValue" :label="item.dictValue">{{item.dictName}}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item :label="$t('csmWorkOrderEvaluation.resolved')" prop="evaluation.resolved">
                <el-radio-group v-model="dataForm.evaluation.resolved">
                  <el-radio v-for="item in dict.resolvedList" :key="item.dictValue" :label="item.dictValue">{{item.dictName}}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <!--<el-form-item :label="$t('csmWorkOrderEvaluation.evaluationContent')" prop="evaluationContent" class="evaluationContentClass"></el-form-item>-->
              <el-input v-model="dataForm.evaluation.evaluationContent" :placeholder="$t('placeholder.evaluationContent')"  type="textarea" :rows="6" autocomplete="off" :maxlength="512" show-word-limit></el-input>
            </el-col>
            <el-col :span="24" style="padding-top: 20px">
              <el-button type="primary" @click="submitEvaluationHandle">{{$t('submit')}}</el-button>
            </el-col>
          </el-row>
        </div>

        <h3 v-if="dataForm.status===30">{{$t('header.customerEvaluation')}}:</h3>
        <div v-if="dataForm.status===30" class="feedback_wrapper" style="overflow:auto">
          <el-row :gutter="24" class="margin_bottom22">
            <el-col :span="8">
              <el-form-item :label="$t('csmWorkOrderEvaluation.score')" prop="evaluation.score">
                <span v-text="formatterValue(dataForm.evaluation.score,'score')"></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('csmWorkOrderEvaluation.createDate')" prop="evaluation.createDate">
                <span v-text="dataForm.evaluation.createDate"></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('csmWorkOrderEvaluation.resolved')" prop="evaluation.resolved">
                <span v-text="formatterValue(dataForm.evaluation.resolved,'resolved')"></span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item :label="$t('csmWorkOrderEvaluation.evaluationContent')" prop="evaluation.evaluationContent">
                <span v-text="dataForm.evaluation.evaluationContent"></span>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </div>
    <div id="cs_FormFooter" class="el-form-footer" slot="footer">
      <el-button @click="backFn">{{ $t('back') }}</el-button>
    </div>
  </div>
</template>

<script>
import { formatterType, gtmToLtm, timestampFormat, formatterName, formatterUser } from '@/filters/filters'
import dictTypeMixins from '@/mixins/dictTypeMixins'
import baseDataApi from '@/api'
import baseData from '@/api/baseData'
import btnUploadAttachment from '@/components/upload/btn-upload-attachment'
import { isImgFile } from '@/utils/validate'
export default {
  mixins: [dictTypeMixins],
  data () {
    return {
      squareUrl: `~@/assets/img/avatar.png`,
      active: 1,
      stepList: [
        { title: '等受理', value: 0, active: 1 },
        { title: '已受理', value: 5, active: 2 },
        { title: '已处理', value: 25, active: 3 },
        { title: '已关闭', value: 30, active: 4 }
      ],
      dataForm: {
        id: '',
        workOrderNo: '',
        workOrderTypeId: '',
        waybillNo: '',
        customerVoucherNo: '',
        problemStatement: '',
        contactPerson: '',
        phone: '',
        email: '',
        status: '',
        urgentLevel: '',
        assignee: '',
        assignTime: '',
        acceptServiceId: '',
        startProcessingTime: '',
        firstReplyTime: '',
        lastReplyTime: '',
        lastConsultationTime: '',
        completionTime: '',
        completionType: '',
        closeTime: '',
        closeType: '',
        newNote: '',
        newConsultation: '',
        creatorType: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: '',
        version: '',
        communicationContent: '',
        evaluation: {
          workOrderId: '',
          workOrderNo: '',
          score: '',
          resolved: '',
          evaluationContent: ''
        }
      },
      logDataListLoading: false,
      logDataList: [],
      logTableColumns: [
        { type: '', width: '150', prop: 'createDate', label: this.$t('csmWorkOrderLog.createDate'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'logCode', label: this.$t('csmWorkOrderLog.logCode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'creator', label: this.$t('csmWorkOrderLog.creator'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'logDescription', label: this.$t('csmWorkOrderLog.logDescription'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'id', label: this.$t('system.id'), align: 'center', isShow: false, disabled: true }
      ],
      communicationList: [],
      attachment: '',
      attachmentList: [],
      otherList: [],
      dict: {
        statusList: [],
        creatorTypeList: [],
        logCodeList: [],
        scoreList: [],
        resolvedList: []
      },
      baseData: {
        workOrderTypeList: [],
        userList: []
      },
      fileList: []
    }
  },
  created () {
    // 基础资料
    this.getBaseData()
    // 数据字典
    this.getDict()
  },
  methods: {
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        if (this.dataForm.id) {
          this.getInfo().then(() => {
            if (this.dataForm.id) {
              this.getLog()
              this.getCommunication()
              this.getAttachment()
              this.getStep()
              this.getEvaluation()
            }
          })
        }
      })
    },
    async getDict () {
      this.dict.statusList = await this.getDictTypeList('CsmWorkOrderStatus')
      this.dict.creatorTypeList = await this.getDictTypeList('CsmWorkOrderCreatorType')
      this.dict.logCodeList = await this.getDictTypeList('CsmWorkOrderLogLogCode')
      this.dict.scoreList = await this.getDictTypeList('CsmWorkOrderEvaluationScore')
      this.dict.resolvedList = await this.getDictTypeList('CsmWorkOrderEvaluationResolved')
    },
    async getBaseData () {
      this.baseData.workOrderTypeList = await baseData(baseDataApi.workOrderTypeList).catch(() => {})
      // 用户信息
      this.baseData.userList = await baseData(baseDataApi.userList).catch(() => {})
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'createDate':
          value = timestampFormat(gtmToLtm(scope.row.createDate), 'YYYY-MM-DD')
          break
        case 'status':
          value = formatterType(scope.row.status, this.dict.statusList)
          break
        case 'creatorType':
          value = formatterType(scope.row.creatorType, this.dict.creatorTypeList)
          break
        case 'workOrderTypeId':
          value = formatterName(scope.row.workOrderTypeId, this.baseData.workOrderTypeList)
          break
        case 'acceptServiceId':
          value = formatterUser(scope.row.acceptServiceId, this.baseData.userList)
          break
        case 'creator':
          value = formatterUser(scope.row.creator, this.baseData.userList)
          break
        case 'logCode':
          value = formatterType(scope.row.logCode, this.dict.logCodeList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    formatterValue (value, prop) {
      if (value !== undefined && value !== '' && value !== null) {
        switch (prop) {
          case 'creatorType':
            value = formatterType(value, this.dict.creatorTypeList)
            break
          case 'creator':
            value = formatterUser(value, this.baseData.userList)
            break
          case 'workOrderType':
            value = formatterName(value, this.baseData.workOrderTypeList)
            break
          case 'status':
            value = formatterType(value, this.dict.statusList)
            break
          case 'user':
            value = formatterUser(value, this.baseData.userList)
            break
          case 'score':
            value = formatterType(value, this.dict.scoreList)
            break
          case 'resolved':
            value = formatterType(value, this.dict.resolvedList)
            break
          default:
            break
        }
      }
      return value
    },
    getStep () {
      if (this.dataForm.status === 0) {
        this.active = 1
      } else if (this.dataForm.status >= 5 && this.dataForm.status < 25) {
        this.active = 2
      } else if (this.dataForm.status >= 25 && this.dataForm.status < 30) {
        this.active = 3
      } else if (this.dataForm.status === 30) {
        this.active = 4
      }
    },
    // 获取信息
    getInfo () {
      return this.$http.get(`/csm/workorder/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    getLog () {
      this.$http.get(`/csm/workorderlog/list`, { params: { workOrderId: this.dataForm.id } }).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.logDataList = res.data
      }).catch(() => {})
    },
    getCommunication () {
      this.$http.get(`/csm/workordercommunication/list`, { params: { workOrderId: this.dataForm.id } }).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.communicationList = res.data
      }).catch(() => {})
    },
    getAttachment () {
      this.$http.get(`/csm/workorderattachment/list`, { params: { workOrderId: this.dataForm.id } }).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.attachmentList = []
        for (let i = 0; i < res.data.length; i++) {
          if (isImgFile(res.data[i].attachmentUrl)) {
            this.attachmentList.push(res.data[i].attachmentUrl)
          } else {
            this.otherList.push(res.data[i].attachmentUrl)
          }
        }
        this.attachment = this.attachmentList[0]
      }).catch(() => {})
    },
    getEvaluation () {
      this.$http.get(`/csm/workorderevaluation/list`, { params: { workOrderId: this.dataForm.id } }).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        if (res.data.length >= 1) {
          this.dataForm.evaluation = res.data[0]
        }
      }).catch(() => {})
    },
    replayAndFinishHandle () {
      /* if (this.dataForm.communicationContent.trim() === '') {
        return this.$message.error('请输入答复内容')
      } */
      let formData = new FormData()
      let isBreak = this.$refs.btnUploadAttachment.validFiles()
      if (isBreak) {
        return ''
      }
      this.fileList.forEach(file => {
        // 此处一定是append file.raw 上传文件只需维护fileList file.raw.name要加上
        formData.append('files', file.file)
      })
      formData.append('workOrderId', this.dataForm.id)
      formData.append('workOrderNo', this.dataForm.workOrderNo)
      formData.append('communicationContent', this.dataForm.communicationContent)
      formData.append('status', 25)
      formData.append('completionType', 2)
      formData.append('communicationType', 1)
      this.$http.post('/csm/workorder/replayAndFinish', formData).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 500,
          onClose: () => {
            this.visible = false
            this.$emit('backView')
          }
        })
      }).catch(() => {})
    },
    onlySubmitReplayHandle () {
      if (this.dataForm.communicationContent.trim() === '') {
        return this.$message.error('请输入答复内容')
      }
      let formData = new FormData()
      let isBreak = this.$refs.btnUploadAttachment.validFiles()
      if (isBreak) {
        return ''
      }
      this.fileList.forEach(file => {
        // 此处一定是append file.raw 上传文件只需维护fileList file.raw.name要加上
        formData.append('files', file.file)
      })
      formData.append('workOrderId', this.dataForm.id)
      formData.append('workOrderNo', this.dataForm.workOrderNo)
      formData.append('communicationContent', this.dataForm.communicationContent)
      formData.append('communicationType', 1)
      this.$http.post('/csm/workorder/onlyReplay', formData).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 500,
          onClose: () => {
            this.visible = false
            this.$emit('backView')
          }
        })
      }).catch(() => {})
    },
    // 返回
    backFn () {
      this.visible = false
      this.$emit('backView')
    },
    submitEvaluationHandle () {
      this.dataForm.evaluation.workOrderNo = this.dataForm.workOrderNo
      this.dataForm.evaluation.workOrderId = this.dataForm.id
      this.$refs.form.validate((valid, object) => {
        if (!valid) {
          return false
        }
        this.$http.post('/csm/workorder/assess', this.dataForm.evaluation).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('backView')
            }
          })
        }).catch(() => {})
      })
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat,
    formatterName,
    formatterUser
  },
  computed: {
    logTableColumnsArr () {
      let arr = []
      let logTableColumns = Object.keys(this.logTableColumns).map((key) => this.logTableColumns[key])
      arr = logTableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    dataFormRules () {
      return {
        'evaluation.score': [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        'evaluation.resolved': [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  components: {
    btnUploadAttachment
  }
}
</script>

<style lang="scss" scoped>
  .avatar_box{
    position: absolute;
    top: 0;
    left: -23px;
    z-index: 2;
  }
  .feedback_wrapper{
    max-height: 350px;
    padding: 20px;
    margin-bottom: 50px;
  }
  .avatar_box{
    width: 50px;
    height: 50px;
    overflow: hidden;
    border-radius: 10px;
    .avatar_img{
      width: 50px;
      height: 50px;
    }
  }
  .otherAttach + .otherAttach {
    margin-left: 10px;
  }
</style>
