<template>
  <div class="add-body">
    <div class="panel-hd">{{$t('import')}}</div>
    <div class="importExcelPanel" v-show="visible">
      <el-row :gutter="20">
        <el-col :md="{span:10}">
          <el-upload
            :action="url"
            :data="param"
            drag
            :disabled="uploadDisable"
            :before-upload="beforeUploadHandle"
            :on-success="successHandle"
            :on-error="errorHandle"
            :headers="getToken"
            class="text-center">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text" v-html="$t('upload.text')"></div>
            <div class="el-upload__tip" slot="tip" v-show="showTip" v-if="showMsg">{{ showMsg }}</div>
            <div class="el-upload__tip" slot="tip" v-else>{{ $t('upload.tip', { 'format': 'xls、xlsx' }) }}
              <a :href="fileUrl" target="_blank">{{$t('import_module')}}<i class="el-icon-download"></i></a>
              &nbsp;&nbsp;
              <a :href="amazonUrl" v-if="this.param" target="_blank">{{$t('import_amazon_module')}}<i class="el-icon-download"></i></a>
              <a :href="verticalUrl" v-if="this.isOut" target="_blank">{{$t('import_vertical_module')}}<i class="el-icon-download"></i></a>
            </div>
          </el-upload>
        </el-col>
        <el-col :md="{span:14}">
          <el-card class="box-card" shadow="never" >
            <div slot="header" class="clearfix">
              <span>{{$t('import_log_info')}}</span>
            </div>
            <div style="height:560px; overflow:hidden; overflow-y:auto; position: relative; z-index: 2;" v-loading="uploadDisable" element-loading-text="正在读取数据…" element-loading-spinner="el-icon-loading"
                 >
              <div class="info" style="white-space: pre-wrap;" v-html="message" ></div>
              <!-- <el-input type="textarea" disabled v-model="" :autosize="{ minRows: 30, maxRows: 4}" ></el-input> -->
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    <div id="cs_FormFooter" class="el-form-footer" slot="footer">
      <el-button @click="backFn">{{ $t('back') }}</el-button>
    </div>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
import { clearLoginInfo } from '@/utils'
export default {
  props: {
    importExcelUrl: '',
    downLoadUrl: '',
    downLoadAmazonUrl: '',
    downVerticalUrl: '',
    isOut: { // 判断是否是出库
      type: Boolean,
      default: false
    },
    fileType: {
      // 配置限制上传文件
      type: Array,
      default: () => {
        return []
      }
    },
    // 是否展示提示语
    showTip: {
      type: Boolean,
      default: true
    },
    // 展示提示语内容
    showMsg: {
      type: String,
      default: ''
    },
    // 上传文件大小限制
    sizeLimit: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      visible: false,
      uploadDisable: false, // 避免重复上传
      url: '',
      fileUrl: '',
      amazonUrl: '',
      verticalUrl: '',
      message: '',
      token: '',
      lang: '',
      param: {}
    }
  },
  computed: {
    getToken () {
      return {
        token: this.token,
        'Accept-Language': this.lang
      }
    }
  },
  methods: {
    init (param) {
      // console.log(this.showMsg)
      this.token = Cookies.get('cs_token')
      this.visible = true
      this.url = this.importExcelUrl
      this.lang = Cookies.get('language') || 'zh-CN'
      if (param) {
        this.param = param
      } else {
        this.param = null
      }
      let url = process.env.NODE_ENV === 'production' ? location.origin : (location.origin || this.$baseUrl)
      this.fileUrl = `${url}/static/` + this.downLoadUrl
      this.amazonUrl = `${url}/static/` + this.downLoadAmazonUrl
      this.verticalUrl = `${url}/static/` + this.downVerticalUrl
    },
    // 上传之前
    beforeUploadHandle (file) {
      this.uploadDisable = true
      this.message = ''
      let regStr = ''
      let formatTip = ''
      if (this.fileType.length) {
        let fileTypeStr = []
        this.fileType.forEach((item, index) => {
          formatTip += `${index > 0 ? '、' : ''}${item}`
          fileTypeStr.push(`(.${item})$`)
        })
        fileTypeStr = fileTypeStr.join('|')
        regStr = fileTypeStr
      } else {
        regStr = '(.xlsx)$|(.xls)$'
        formatTip = '.xls、.xlsx'
      }
      let regx = new RegExp(regStr)
      if (!regx.test(file.name)) {
        this.$message.error(this.$t('upload.tip', { 'format': `${formatTip}` }))
        this.uploadDisable = false
        return false
      }
      // if (file.type !== 'application/vnd.ms-excel' && file.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
      //   this.$message.error(this.$t('upload.tip', { 'format': 'xls、xlsx' }))
      //   return false
      // }
      const sizeLimit = file.size / 1024 / 1024 < this.sizeLimit
      if (this.sizeLimit > 0 && !sizeLimit) {
        this.$message.error(this.$t('upload.sizeMsg', { 'size': `${this.sizeLimit}` }))
        return false // 必须返回false
      }
    },
    // 上传失败
    errorHandle (error) {
      console.log('error', error)
      this.uploadDisable = false
      let myError = error.toString()
      myError = myError.replace('Error: ', '')
      console.log('myError')
      return this.$message({
        dangerouslyUseHTMLString: true,
        message: myError || '发生错误',
        type: 'error',
        duration: 2500
      })
    },
    // 上传成功
    successHandle (res, file) {
      if (res.code !== 0) {
        this.uploadDisable = false
        this.message = res.msg && res.msg.replace(/<br>/g, '\r\n')
        if (this.message === '未授权') {
          clearLoginInfo()
          this.$router.push({ name: 'login' })
        }
      } else {
        this.uploadDisable = false
        this.message = this.$t('prompt.success')
        this.$emit('refreshDataList')
      }
    },
    // 返回
    backFn () {
      this.visible = false
      this.$emit('backImportExcel')
    }
  }
}
</script>
