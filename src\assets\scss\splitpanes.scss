.red-bg{
  background-color: red !important;
}
.green-bg{
  background-color: #1890ff !important;
}
.white-bg{
  background-color: white !important;
}
.showData_text{
  font-size: 24px;
  color: #fff;
}
.data-table{
  .data-title{
    padding:0 10px;
    color: #fff;
  }
}
.tip-bg{
  position: relative;
  width: 100%;
  height: 100%;
  display: table;
  .tip-text{
    font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
    font-size: 40px;
    display: table-cell;
    vertical-align: middle;
    text-align: center;
    color: #FFF;
  }
}

//  以下是split-pans样式
.splitpanes {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  height: 100%
}

.splitpanes--vertical {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row
}

.splitpanes--horizontal {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column
}

.splitpanes--dragging * {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none
}

.splitpanes__pane {
  width: 100%;
  height: 100%;
  overflow: hidden;
  -webkit-transition: width .2s ease-out,height .2s ease-out;
  transition: width .2s ease-out,height .2s ease-out
}

.splitpanes--dragging .splitpanes__pane {
  -webkit-transition: none;
  transition: none
}

.splitpanes__splitter {
  -ms-touch-action: none;
  touch-action: none
}

.splitpanes--vertical>.splitpanes__splitter {
  min-width: 1px;
  cursor: col-resize
}

.splitpanes--horizontal>.splitpanes__splitter {
  min-height: 1px;
  cursor: row-resize
}
.splitpanes.default-theme{
  height:calc(calc(100vh - 38px - 30px - 48px) - 2px);
}
.splitpanes.default-theme .splitpanes__pane {
  background-color: transparent;
}

.splitpanes.default-theme .splitpanes__splitter {
  background-color: #000 !important;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  position: relative;
}

.splitpanes.default-theme .splitpanes__splitter:after,.splitpanes.default-theme .splitpanes__splitter:before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  background-color: rgba(0,0,0,.15);
  -webkit-transition: background-color .3s;
  transition: background-color .3s
}

.splitpanes.default-theme .splitpanes__splitter:hover:after,.splitpanes.default-theme .splitpanes__splitter:hover:before {
  background-color: rgba(0,0,0,.25)
}

.splitpanes.default-theme .splitpanes__splitter:first-child {
  cursor: auto
}

.default-theme.splitpanes .splitpanes .splitpanes__splitter {
  z-index: 1
}

.default-theme.splitpanes--vertical>.splitpanes__splitter,.default-theme .splitpanes--vertical>.splitpanes__splitter {
  width: 9px;
  border-left: 1px solid #000;
  margin-left: -1px
}

.default-theme.splitpanes--vertical>.splitpanes__splitter:after,.default-theme .splitpanes--vertical>.splitpanes__splitter:after,.default-theme.splitpanes--vertical>.splitpanes__splitter:before,.default-theme .splitpanes--vertical>.splitpanes__splitter:before {
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  width: 1px;
  height: 30px
}

.default-theme.splitpanes--vertical>.splitpanes__splitter:before,.default-theme .splitpanes--vertical>.splitpanes__splitter:before {
  margin-left: -2px
}

.default-theme.splitpanes--vertical>.splitpanes__splitter:after,.default-theme .splitpanes--vertical>.splitpanes__splitter:after {
  margin-left: 1px
}

.default-theme.splitpanes--horizontal>.splitpanes__splitter,.default-theme .splitpanes--horizontal>.splitpanes__splitter {
  height: 9px;
  border-top: 1px solid #000;
  margin-top: -1px
}

.default-theme.splitpanes--horizontal>.splitpanes__splitter:after,.default-theme .splitpanes--horizontal>.splitpanes__splitter:after,.default-theme.splitpanes--horizontal>.splitpanes__splitter:before,.default-theme .splitpanes--horizontal>.splitpanes__splitter:before {
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  width: 30px;
  height: 1px
}

.default-theme.splitpanes--horizontal>.splitpanes__splitter:before,.default-theme .splitpanes--horizontal>.splitpanes__splitter:before {
  margin-top: -2px
}

.default-theme.splitpanes--horizontal>.splitpanes__splitter:after,.default-theme .splitpanes--horizontal>.splitpanes__splitter:after {
  margin-top: 1px
}


