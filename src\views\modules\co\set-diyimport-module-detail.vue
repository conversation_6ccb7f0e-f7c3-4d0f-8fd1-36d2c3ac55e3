<template>
  <el-dialog :visible.sync="visible" :title="$t('template.detail')" width="60%" :close-on-click-modal="false" :close-on-press-escape="false" :append-to-body="true">
    <div v-if="!pageShow">
      <div class="table">
        <el-table
          v-loading="dataListLoading" :data="tableData"
          ref="table"
          tooltip-effect="dark"
          border
          stripe
          style="width: 93%"
          @selection-change='selectRow'>
          <el-table-column :label="$t('coSetDiyimportModuleDetail.serialNo')" type="index" width="60" align="center"></el-table-column>
          <el-table-column  :label="$t('coSetDiyimportModuleDetail.fieldValue')" align="center">
            <template slot-scope="scope">
              <el-select v-model="scope.row.fieldValue" filterable :placeholder="$t('coSetDiyimportModuleDetail.fieldValue')" clearable @change="setTableValue(scope.row, scope.$index)">
                <el-option v-for="item in excelFieldList" :key="item.fullFieldName" :label="item.fullName" :value="item.fullFieldName" :disabled="item.disabled"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column :label="$t('coSetDiyimportModuleDetail.fieldName')">
            <template slot-scope="scope">
              <el-input class="require_des" v-model="scope.row.fieldName" maxlength="32"></el-input>
            </template>
          </el-table-column>
          <el-table-column v-if="hasFixValues" :label="$t('coSetDiyimportModuleDetail.fixValue')">
            <template slot-scope="scope">
              <el-input v-if="scope.row.type ===3" class="require_des" v-model="scope.row.parameterType" maxlength="64"></el-input>
            </template>
          </el-table-column>
          <el-table-column :label="$t('handle')" fixed="right" min-width="60">
            <template slot="header" slot-scope="scope">
              <span>{{ $t('handle') }}</span>
            </template>
            <template slot-scope="scope">
              <el-button class="el-icon-plus" @click.prevent="addRow(scope.$index+1)"></el-button>
              <el-button class="el-icon-minus" @click.prevent="delData(scope.$index)"></el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div style="text-align: right; padding-top:15px;padding-right: 15%;">
        <el-button size="mini" plain :loading="buttonLoading" @click="cancelFn()">{{ $t('cancel') }}</el-button>
        <el-button size="mini" type="primary" :loading="buttonLoading" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
      </div>
      <!-- 新增 / 修改 -->
      <template slot="footer">
        <el-button @click="backFn">{{ $t('back') }}</el-button>
      </template>
    </div>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import { formatterType, gtmToLtm, timestampFormat } from '@/filters/filters'
import dictTypeMixins from '@/mixins/dictTypeMixins'
import api from '@/api'
import baseData from '@/api/baseData'
import mixinViewModule from '@/mixins/view-module'

// table 自定义显示
export default {
  mixins: [dictTypeMixins, mixinViewModule],
  data () {
    return {
      tableData: [],
      selectlistRow: [],
      rowNum: 1,
      visible: false,
      getCompIndexMap: new Map(),
      importFieldMap: new Map(),
      mixinViewModuleOptions: {
        getDataListURL: '/co/setdiyimportmodule/page',
        getDataListIsPage: true,
        deleteURL: '/co/setdiyimportmodule',
        deleteIsBatch: true,
        auditIsBatch: true
      },
      dataForm: {
        id: null,
        url: '',
        moduleId: '',
        masterDTOName: ''
      },
      visibleAddFee: false,
      isAddFee: false,
      isSave: false,
      dataListLoading: false,
      buttonLoading: false,
      total: 0,
      limit: 10,
      page: 1,
      activeName: 'all',
      tableName: 'co-setdiyimportmodule',
      excelFieldList: [],
      seaTypeList: []
    }
  },
  created () {
    this.getBaseData()
  },
  methods: {
    async getBaseData () {
      // urlDTO对应的字段
      baseData(api.getExcelImportField + this.dataForm.masterDTOName).then(res => {
        this.excelFieldList = res
        this.excelFieldList.forEach((v, i) => {
          this.importFieldMap.set(v.fullFieldName, v)
        })
      })
    },
    init () {
      this.visible = true
      this.getBaseData()
      this.tableData = []
      this.getInfo()
    },
    // 获取信息
    getInfo () {
      this.dataListLoading = true
      this.$http.get(`/co/setdiyimportmoduledetail/${this.dataForm.moduleId}`).then(({ data: res }) => {
        this.dataListLoading = false
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        console.log(res.data)
        if (res.data.length > 0) {
          this.tableData = res.data
          this.tableData.forEach((v, i) => {
            v.fieldValue = v.objectType + '.' + v.fieldValue
          })
          this.setOptionDisabled()
        } else {
          this.addRow(0)
        }
      }).catch(() => {})
    },
    // 返回
    backFn () {
      this.visible = false
      this.$emit('backView')
    },
    // 获取表格选中时的数据
    selectRow (val) {
      this.selectlistRow = val
    },
    // 增加行
    addRow (index) {
      let list = {
        moduleId: this.dataForm.moduleId,
        fieldName: '',
        mainObjectName: '',
        objectType: '',
        serviceName: '',
        repeatableColumns: '',
        digital: '',
        allowEmpty: '',
        parameterType: '',
        type: 0
      }
      this.tableData.splice(index, 0, list)
      // // 列序号重排
      this.tableData.forEach((v, i) => {
        this.tableData[i].serialNo = i
      })
      this.rowNum += 1
    },
    // 设置表单值
    setTableValue (row, index) {
      this.setOptionDisabled()
      // 将表头名设置为字段名
      let exportFieldObj = this.importFieldMap.get(row.fieldValue)
      this.tableData[index].fieldName = exportFieldObj.fullName.split('-')[1]
      this.tableData[index].type = exportFieldObj.typeEnumValue
    },
    // 设置选项禁用
    setOptionDisabled () {
      this.tablaDataToMap()
      // 将选中的设置为禁选，未选中的设置为可选
      this.excelFieldList.forEach((v, i) => {
        if (!v.disabled && this.getCompIndexMap.get(v.fullFieldName)) {
          // i 为选中的索引
          v.disabled = true
        } else if (v.disabled && !this.getCompIndexMap.get(v.fullFieldName)) {
          v.disabled = false
        }
      })
    },
    // 获取已有值的数据，转为map
    tablaDataToMap () {
      this.getCompIndexMap = new Map()
      this.tableData.forEach((v, i) => {
        this.getCompIndexMap.set(v.fieldName, true)
      })
    },
    // 删除方法
    // 删除选中行
    delData (index) {
      // i 为选中的索引
      this.tableData.splice(index, 1)
      // // 列序号重排
      this.tableData.forEach((v, i) => {
        this.tableData[i].serialNo = i
      })
      this.setOptionDisabled()
      // 删除完数据之后清除勾选框
      this.$refs.table.clearSelection()
      if (this.tableData.length === 0) {
        this.addRow(0)
      }
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.buttonLoading = true
      this.$http['post']('/co/setdiyimportmoduledetail', this.tableData).then(({ data: res }) => {
        this.buttonLoading = false
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 500,
          onClose: () => {
            this.visible = false
            this.$emit('refreshDataList')
          }
        })
      }).catch(() => {
        this.buttonLoading = false
      })
    }, 1000, { 'leading': true, 'trailing': false }),

    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'createDate':
          value = timestampFormat(gtmToLtm(scope.row.createDate), 'YYYY-MM-DD')
          break
        case 'status':
          value = formatterType(scope.row.status, this.statusList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    // 取消
    cancelFn () {
      this.visible = false
      this.$emit('cancelAddOrUpdate')
    }
  },
  computed: {
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map(key => this.tableColumns[key])
      arr = tableColumns.filter(item => {
        return item.isShow
      })
      return arr
    },
    hasFixValues () {
      let hasFixValue = false
      if (this.tableData.length === 0) {
        hasFixValue = false
      } else {
        this.tableData.forEach((v, i) => {
          if (v.type === 3) {
            hasFixValue = true
          }
        })
      }
      // console.log('hasFixValue:' + hasFixValue)
      return hasFixValue
    },
    pageShow () {
      let ret = false
      if (this.viewVisible) {
        ret = true
      } else if (this.addOrUpdateVisible) {
        ret = true
      }
      return ret
    }
  }
}
</script>
