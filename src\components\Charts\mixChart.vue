<template>
  <div :id="id" :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
import 'echarts/theme/macarons.js'
import resize from './mixins/resize'
export default {
  mixins: [resize],
  props: {
    title: {
      type: String,
      default: 'statistics'
    },
    className: {
      type: String,
      default: 'chart'
    },
    id: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '200px'
    },
    height: {
      type: String,
      default: '200px'
    },
    data1: {
      type: Array,
      default: () => {
        return []
      }
    },
    data2: {
      type: Array,
      default: () => {
        return []
      }
    },
    dataArray: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data () {
    return {
      chart: null
    }
  },
  mounted () {
    this.initChart()
  },
  beforeDestroy () {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart () {
      this.chart = echarts.init(document.getElementById(this.id), 'macarons')
      const xData = (() => {
        return this.dataArray
      })()
      const count = (() => {
        const data = []
        for (let i = 0; i < this.data1.length; i++) {
          data.push((Number(this.data1[i]) + Number(this.data2[i])))
        }
        return data
      })()
      this.chart.setOption({
        title: {
          text: this.title || '',
          x: '20',
          top: '0',
          textStyle: {
            color: '#90979c',
            fontSize: '22'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            textStyle: {
              color: '#90979c'
            }
          }
        },
        grid: {
          left: 10,
          right: 10,
          borderWidth: 0,
          top: 50,
          bottom: 10,
          containLabel: true,
          textStyle: {
            color: '#90979c'
          }
        },
        calculable: true,
        legend: {
          x: '30',
          top: '10',
          textStyle: {
            color: '#90979c'
          },
          data: ['入库', '出库', '曲线']
        },
        // dataZoom: [
        //   {
        //     show: true,
        //     realtime: true,
        //     start: 0,
        //     end: 100
        //   },
        //   {
        //     type: 'inside',
        //     realtime: true,
        //     start: 0,
        //     end: 100
        //   }
        // ],
        xAxis: [
          {
            type: 'category',
            barWidth: 50,
            axisLine: {
              lineStyle: {
                color: '#90979c'
              }
            },
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitArea: {
              show: false
            },
            axisLabel: {
              interval: 0
            },
            data: xData
          }
        ],
        yAxis: [
          {
            type: 'value',
            splitLine: {
              show: true
            },
            axisLine: {
              lineStyle: {
                color: '#90979c'
              }
            },
            axisTick: {
              show: true
            },
            axisLabel: {
              interval: 0
            },
            splitArea: {
              show: false
            }
          }
        ],
        series: [
          {
            name: '入库',
            type: 'bar',
            itemStyle: {
              normal: {
                color: 'rgba(255,144,128,1)',
                label: {
                  show: true,
                  textStyle: {
                    color: '#fff'
                  },
                  position: 'insideTop',
                  formatter (p) {
                    return p.value > 0 ? p.value : ''
                  }
                }
              }
            },
            data: this.data1
          },
          {
            name: '出库',
            type: 'bar',
            itemStyle: {
              normal: {
                color: 'rgba(0,191,183,1)',
                barBorderRadius: 0,
                label: {
                  show: true,
                  position: 'top',
                  formatter (p) {
                    return p.value > 0 ? p.value : ''
                  }
                }
              }
            },
            data: this.data2
          },
          {
            name: '曲线',
            type: 'line',
            stack: 'total',
            symbolSize: 12,
            // symbol: 'circle',
            itemStyle: {
              normal: {
                color: '#EB6709',
                barBorderRadius: 0,
                label: {
                  show: false,
                  position: 'top',
                  formatter (p) {
                    return p.value > 0 ? p.value : ''
                  }
                }
              }
            },
            data: count
          }
        ]
      })
    }
  }
}
</script>
