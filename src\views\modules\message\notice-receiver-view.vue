<template>
  <div class="add-body panel_body">
    <div class="panel-hd">
      <span v-text="$t('detail')"></span>
    </div>
    <div class="detail-desc">
      <el-form ref="form" label-width="80px">
        <el-row :gutter="24">
          <el-col :span="12">
              <h2 v-text="dataForm.title" style="color: #46A0FC;"></h2>
          </el-col>
          <el-col :span="12" class="text-right">
              <!-- 发布时间 -->
              <span style="margin-left:10px;">{{ dataForm.publishDate|gtmToLtm }}</span>
<!--              &lt;!&ndash; 发布者 &ndash;&gt;-->
<!--              <span style="margin-left:10px;">{{ dataForm.creatorName }}</span>-->
              <!-- 类型 -->
              <el-tag style="margin-left:10px;" type="info">{{ dataForm.type|formatterType(noticeTypeList) }}</el-tag>
          </el-col>
        </el-row>
        <el-divider></el-divider>
        <el-row :gutter="24">
          <el-col :span="24">
            <div v-html="dataForm.content"></div>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div id="cs_FormFooter" class="el-form-footer" slot="footer">
      <el-button @click="backFn">{{ $t('back') }}</el-button>
    </div>
  </div>
</template>

<script>
import { formatterType, gtmToLtm, timestampFormat } from '@/filters/filters'
export default {
  data () {
    return {
      dataForm: {
        id: '',
        title: '',
        type: '',
        receiverType: '',
        publishDate: '',
        content: '',
        companyId: '',
        creatorName: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: '',
        status: '',
        delFlag: '',
        version: ''
      },
      noticeTypeList: []
    }
  },
  methods: {
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        if (this.dataForm.id) {
          Promise.all([
            this.getInfo(),
            this.readNotice()
          ])
        }
      })
    },
    // 获取信息
    async getInfo () {
      await this.$http.get(`/message/notice/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 读取信息
    async readNotice () {
      await this.$http.get(`/message/notice/read/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
      }).catch(() => {})
    },
    // 返回
    backFn () {
      this.visible = false
      this.$emit('backView')
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat
  }
}
</script>
