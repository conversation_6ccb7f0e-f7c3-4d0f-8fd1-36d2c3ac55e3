<template>
  <div class="add-body panel_body">
    <div class="panel-hd">
      <span>{{$t('detail')}}</span>
    </div>
    <el-tabs class="no_shadow" type="border-card" v-model="activeName" @tab-click="tabsClick" :stretch="false">
      <el-tab-pane :label="$t('tabPane.waybill')" name="waybill">
        <el-form ref="form" label-width="120px">
          <div class="panel-hd">
            <span v-text="$t('header.waybill')"></span>
          </div>
          <div >
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.customerOrderNo')">
                  <span v-text="dataForm.customerOrderNo"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.waybillNo')">
                  <span v-text="dataForm.waybillNo"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.deliveryNo')">
                  <span v-text="dataForm.deliveryNo"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.consigneeCountry')">
                  <span v-text="formatterValue(dataForm.consigneeCountry,'country')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('wsComWaybill.logisticsProductCode')">
                  <span v-text="formatterValue(dataForm.logisticsProductCode,'product')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.electric')">
                  <span v-text="formatterValue(dataForm.electric,'yesOrNo')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.magnetized')">
                  <span v-text="formatterValue(dataForm.magnetized,'yesOrNo')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.liquid')">
                  <span v-text="formatterValue(dataForm.liquid,'yesOrNo')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.powder')">
                  <span v-text="formatterValue(dataForm.powder,'yesOrNo')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.remote')">
                  <span v-text="formatterValue(dataForm.remote,'yesOrNo')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.parcelType')">
                  <span v-text="formatterValue(dataForm.parcelType,'parcelType')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.goodsCategory')">
                  <span v-text="formatterValue(dataForm.goodsCategory,'goodsCategory')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.packageQty')">
                  <span v-text="dataForm.packageQty"></span>
                </el-form-item>
              </el-col>
              <!--<el-col :span="8">
                <el-form-item :label="$t('coOrder.platformType')">
                  <span v-text="dataForm.platformType"></span>
                </el-form-item>
              </el-col>-->
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.forecastWeight')">
                  <span v-text="dataForm.forecastWeightD + (dataForm.standardUnit === 0 ? ' 千克 (KG)' : ' 磅 (LB)')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.forecastMoney')">
                  <template v-if="!dataForm.sumList || dataForm.sumList.length===0" >
                    <b>--</b>
                  </template>
                  <span v-else v-for="(item, index) in dataForm.sumList" :key="index" >
                    <span style="font-weight:bold;">{{ item.sum | numberFormat(3)}}</span>
                    <span style="color: green;padding-left: 3px;" class="order-sum">{{item.currency }}</span>
                    <template v-if="index !== dataForm.sumList.length-1"> / </template>
                  </span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.status')" prop="status">
                  <span v-text="formatterValue(dataForm.status,'status')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrderShipper.vatNo')" prop="shipper.vatNo">
                  <span v-text="dataForm.shipper.vatNo"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrderShipper.eoriNo')" prop="shipper.eoriNo">
                  <span v-text="dataForm.shipper.eoriNo"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrderShipper.vatCompanyEnName')" prop="shipper.vatCompanyEnName">
                  <span v-text="dataForm.shipper.vatCompanyEnName"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrderShipper.vatRegisterCountry')" prop="shipper.vatRegisterCountry">
                  <span v-text="formatterValue(dataForm.shipper.vatRegisterCountry,'country')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrderShipper.vatRegisterAddress')" prop="shipper.vatRegisterAddress">
                  <span v-text="dataForm.shipper.vatRegisterAddress"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.customsMethod')">
                  <span v-text="formatterValue(dataForm.customsMethod,'customsMethod')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.taxPayMode')">
                  <span v-text="formatterValue(dataForm.taxPayMode,'taxPayMode')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8" v-if="dataForm.taxPayMode !== 11">
                <el-form-item :label="$t('coOrder.taxPayAccount')">
                  <span v-text="dataForm.taxPayAccount"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.declareCurrency')" prop="declareCurrency">
                  <span>{{ dataForm.declareCurrency | formatterCurrency(this.currencyList)}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.shopName')" prop="shopName">
                  <span>{{ dataForm.shopName}}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.createDate')">
                  <span>{{dataForm.createDate  | gtmToLtm}}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="panel-hd">
            <span v-text="$t('header.customer')"></span>
          </div>
          <div >
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.customerName')">
                  <span v-text="dataForm.customerName"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.customerSimpleCode')">
                  <span v-text="dataForm.customerSimpleCode"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrder.customerRemark')">
                  <span v-text="dataForm.customerRemark"></span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </el-tab-pane>
      <el-tab-pane :label="$t('tabPane.consignee')" name="shipperConsignee">
        <el-form ref="form" label-width="120px">
          <div class="panel-hd">
            <span v-text="$t('header.consignee')"></span>
          </div>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="$t('coOrderConsignee.consigneeName')" prop="consigneeName">
                <span v-text="dataForm.consignee.consigneeName"></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('coOrderConsignee.consigneeCompany')" prop="consigneeCompany">
                <span v-text="dataForm.consignee.consigneeCompany"></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('fba.warehouseCode')" prop="consigneeCompany">
                <span v-text="dataForm.consignee.fbaWarehouseCode"></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('coOrderConsignee.consigneePhone')" prop="consigneePhone">
                <span v-text="dataForm.consignee.consigneePhone"></span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="$t('coOrderConsignee.consigneeCountry')" prop="consigneeCountry">
                <span v-text="formatterValue(dataForm.consignee.consigneeCountry,'country')"></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('coOrderConsignee.consigneeProvince')" prop="consigneeProvince">
                <span v-text="dataForm.consignee.consigneeProvince"></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('coOrderConsignee.consigneeCity')" prop="consigneeCity">
                <span v-text="dataForm.consignee.consigneeCity"></span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="16">
              <el-form-item :label="$t('coOrderConsignee.consigneeAddress')" prop="consigneeAddress">
                <span v-text="dataForm.consignee.consigneeAddress"></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('coOrderConsignee.consigneePostcode')" prop="consigneePostcode">
                <span v-text="dataForm.consignee.consigneePostcode"></span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="$t('coOrderConsignee.consigneeEmail')" prop="consigneeEmail">
                <span v-text="dataForm.consignee.consigneeEmail"></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('coOrderConsignee.consigneeDistrict')" prop="consigneeDistrict">
                <span v-text="dataForm.consignee.consigneeDistrict"></span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('coOrderConsignee.consigneeDoorplate')" prop="consigneeDoorplate">
                <span v-text="dataForm.consignee.consigneeDoorplate"></span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="$t('coOrderConsignee.consigneeStreet')" prop="consigneeStreet">
                <span v-text="dataForm.consignee.consigneeStreet"></span>
              </el-form-item>
            </el-col>
          </el-row>
          <div v-if='false'>
            <div class="panel-hd">
              <span v-text="$t('header.shipper')"></span>
            </div>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item :label="$t('coOrderShipper.shipperName')" prop="shipperName">
                  <span v-text="dataForm.shipper.shipperName"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrderShipper.shipperCompany')" prop="shipperCompany">
                  <span v-text="dataForm.shipper.shipperCompany"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrderShipper.shipperPhone')" prop="shipperPhone">
                  <span v-text="dataForm.shipper.shipperPhone"></span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item :label="$t('coOrderShipper.shipperCountry')" prop="shipperCountry">
                  <span v-text="formatterValue(dataForm.shipper.shipperCountry,'country')"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrderShipper.shipperProvince')" prop="shipperProvince">
                  <span v-text="dataForm.shipper.shipperProvince"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrderShipper.shipperCity')" prop="shipperCity">
                  <span v-text="dataForm.shipper.shipperCity"></span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item :label="$t('coOrderShipper.shipperDistrict')" prop="shipperDistrict">
                  <span v-text="dataForm.shipper.shipperDistrict"></span>
                </el-form-item>
              </el-col>
              <el-col :span="16">
                <el-form-item :label="$t('coOrderShipper.shipperAddress')" prop="shipperAddress">
                  <span v-text="dataForm.shipper.shipperAddress"></span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item :label="$t('coOrderShipper.shipperPostcode')" prop="shipperPostcode">
                  <span v-text="dataForm.shipper.shipperPostcode"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrderShipper.shipperEmail')" prop="shipperEmail">
                  <span v-text="dataForm.shipper.shipperEmail"></span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('coOrderShipper.shipperDoorplate')" prop="shipperDoorplate">
                  <span v-text="dataForm.shipper.shipperDoorplate"></span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item :label="$t('coOrderShipper.shipperStreet')" prop="shipperStreet">
                  <span v-text="dataForm.shipper.shipperStreet"></span>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </el-tab-pane>
      <el-tab-pane :label="$t('tabPane.declare')" name="declare">
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <el-table :key="Math.random()" ref="declareDataList" v-loading='declareTableLoading' :data="declareDataList" border>
            <el-table-column :label="$t('system.index')" type="index" width="50"></el-table-column>
            <el-table-column v-for="(item, index) in declareTableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
              <template slot-scope="scope">
                <div>
                  <div v-if="item.prop === 'createDate'">
                    <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                      <span>{{formatterFn(scope,item.prop)}}</span>
                    </el-tooltip>
                  </div>
                  <div v-else-if="item.prop === 'picUrl'">
                    <el-image v-if='scope.row.picUrl' style="width: 5vw; height: 5vh;" fit="scale-down"  :src="scope.row.picUrl" :preview-src-list="[scope.row.picUrl]" >
                      <div slot="placeholder" class="image-slot">
                        加载中<span class="dot">...</span>
                      </div>
                      <div slot="error" class="image-slot">
                        <i class="el-icon-picture-outline"></i>
                      </div>
                    </el-image>
                  </div>
                  <div v-else>
                    {{formatterFn(scope,item.prop)}}
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination background :current-page="declarePage" :page-sizes="[10, 20, 50, 100]" :page-size="declareLimit" :total="declareTotal"
                       layout="total, sizes, prev, pager, next, jumper" @size-change="declarePageSizeChangeHandle" @current-change="declarePageCurrentChangeHandle">
        </el-pagination>
      </el-tab-pane>
      <el-tab-pane :label="$t('tabPane.package')" name="package">
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <el-table :key="Math.random()" v-loading="packageTableLoading" :data="packageDataList" border>
            <!-- 动态显示表格 -->
            <el-table-column :label="$t('system.index')" type="index" width="50"></el-table-column>
            <el-table-column v-for="(item, index) in packageTableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
              <template slot-scope="scope">
                <div>
                  <div v-if="item.prop === 'createDate'">
                    <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                      <span>{{formatterFn(scope,item.prop)}}</span>
                    </el-tooltip>
                  </div>
                  <div v-else>
                    {{formatterFn(scope,item.prop)}}
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination background :current-page="packagePage" :page-sizes="[10, 20, 50, 100]" :page-size="packageLimit" :total="packageTotal"
                       layout="total, sizes, prev, pager, next, jumper" @size-change="packagePageSizeChangeHandle" @current-change="packagePageCurrentChangeHandle">
        </el-pagination>
      </el-tab-pane>
      <el-tab-pane :label="$t('upload.attachment')" name="attachments">
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <el-table :key="Math.random()" v-loading="attachmentsTableLoading" :data="attachmentsList" border>
            <!-- 动态显示表格 -->
            <el-table-column :label="$t('system.index')" type="index" width="50"></el-table-column>
            <el-table-column v-for="(item, index) in attachmentsTableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
              <template slot-scope="scope">
                <div>
                  <div v-if="item.prop === 'name'">
                    <a :href='scope.row.attachmentUrl'>{{scope.row.name}}</a>
                  </div>
                  <div v-else>
                    {{formatterFn(scope,item.prop)}}
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination background :current-page="attachmentsPage" :page-sizes="[10, 20, 50, 100]" :page-size="attachmentsLimit" :total="attachmentsTotal"
                       layout="total, sizes, prev, pager, next, jumper" @size-change="attachmentsPageSizeChangeHandle" @current-change="attachmentsPageCurrentChangeHandle">
        </el-pagination>
      </el-tab-pane>
    </el-tabs>
    <div id="cs_FormFooter" class="el-form-footer" slot="footer">
      <el-button @click="backFn">{{ $t('back') }}</el-button>
    </div>
  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import listPage from '@/mixins/listPage'
import { formatterType, gtmToLtm, timestampFormat, formatterUserName, formatterCodeName, formatterCurrency, numberFormat } from '@/filters/filters'
import dictTypeMixins from '@/mixins/dictTypeMixins'
import baseDataApi from '@/api'
import baseData from '@/api/baseData'

export default {
  mixins: [listPage, mixinViewModule, dictTypeMixins],
  data () {
    return {
      declarePage: 1,
      declareLimit: 10,
      declareTotal: 0,
      declareTableLoading: false,
      packagePage: 1,
      packageLimit: 10,
      packageTotal: 0,
      packageTableLoading: false,
      attachmentsPage: 1,
      attachmentsLimit: 10,
      attachmentsTotal: 0,
      attachmentsTableLoading: false,
      activeName: 'waybill',
      remark: '',
      dataListLoading: false,
      dataForm: {
        id: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: '',
        status: '',
        shopName: '',
        version: '',
        orderId: '',
        customerOrderNo: '',
        waybillNo: '',
        deliveryNo: '',
        logisticsProductCode: '',
        forecastWeightD: '',
        weightUnit: '',
        goodsCategory: '',
        customsMethod: '',
        taxPayMode: '',
        parcelType: '',
        declareCurrency: '',
        electric: '',
        magnetized: '',
        liquid: '',
        powder: '',
        remote: '',
        consigneeCountry: '',
        customerRemark: '',
        serviceId: '',
        salesmanId: '',
        sumList: [],
        customerId: '',
        customerName: '',
        customerSimpleCode: '',
        platformType: '',
        packageQty: '',
        standardUnit: '',
        lengthUnit: '',
        shipper: {
          shipperName: '',
          shipperCompany: '',
          shipperPhone: '',
          shipperEmail: '',
          shipperCountry: 'CN',
          shipperProvince: '',
          shipperCity: '',
          shipperDistrict: '',
          shipperAddress: '',
          shipperPostcode: '',
          shipperDoorplate: '',
          shipperStreet: ''
        },
        consignee: {
          consigneeName: '',
          consigneeCompany: '',
          consigneePhone: '',
          consigneeEmail: '',
          fbaWarehouseCode: '',
          consigneeProvince: '',
          consigneeCity: '',
          consigneeDistrict: '',
          consigneeAddress: '',
          consigneePostcode: '',
          consigneeDoorplate: '',
          consigneeStreet: '',
          consigneeIdcard: ''
        }
      },
      declareDataList: [],
      declareTableColumns: [
        { type: '', width: '80', prop: 'chineseName', label: this.$t('coOrderDeclare.chineseName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'englishName', label: this.$t('coOrderDeclare.englishName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'quantity', label: this.$t('coOrderDeclare.quantity'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'unitNetWeightD', label: this.$t('fba.unitNetWeight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '90', prop: 'unitDeclarePriceD', label: this.$t('coOrderDeclare.unitDeclarePrice'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'declareUnit', label: this.$t('fba.declareUnit'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'brand', label: this.$t('coOrderDeclare.brand'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'sku', label: this.$t('coOrderDeclare.sku'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'hsCode', label: this.$t('coOrderDeclare.hsCode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'productModel', label: this.$t('coOrderDeclare.productModel'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'material', label: this.$t('coOrderDeclare.material'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'purpose', label: this.$t('coOrderDeclare.purpose'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'origin', label: this.$t('coOrderDeclare.origin'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'pickingRemark', label: this.$t('coOrderDeclare.pickingRemark'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'productUrl', label: this.$t('coOrderDeclare.productUrl'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'picUrl', label: this.$t('fba.picUrl'), align: 'center', isShow: true, disabled: false }
      ],
      packageDataList: [],
      packageTableColumns: [
        { type: '', width: '150', prop: 'packageSerialNo', label: this.$t('coOrderPackage.packageSerialNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'packageDeliveryNo', label: this.$t('coOrderPackage.packageDeliveryNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'subCustomerOrderNo', label: this.$t('coOrderPackage.subCustomerOrderNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'packageCustomerNo', label: this.$t('coOrderPackage.packageCustomerNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'packageWeightD', label: this.$t('fba.weight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'packageLengthD', label: this.$t('fba.length'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'packageWidthD', label: this.$t('fba.width'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'packageHeightD', label: this.$t('fba.height'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'channelLabelUrl', label: this.$t('coOrderPackage.channelLabelUrl'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'id', label: this.$t('system.id'), align: 'center', isShow: false, disabled: true }
      ],
      attachmentsList: [],
      attachmentsTableColumns: [
        { type: '', width: '150', prop: 'name', label: this.$t('bdAttachments.file'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'attachmentUrl', label: this.$t('bdAttachments.attachmentUrl'), align: 'center', isShow: false, disabled: false }
      ],
      // 数据字典
      comWaybillLogOperateNodeList: [],
      parcelTypeList: [],
      goodsCategoryList: [],
      taxPayModeList: [],
      customsMethodList: [],
      yesOrNoList: [],
      statusList: [],
      // 基础资料
      logisticsProductByParamsList: [],
      userList: [],
      currencyList: [],
      lengthUnitList: [],
      weightUnitList: [],
      countryList: []
    }
  },
  created () {
    this.declarePage = 1
    this.declareLimit = 10
    this.declareTotal = 0
    this.packagePage = 1
    this.packageLimit = 10
    this.packageTotal = 0
    this.attachmentsPage = 1
    this.attachmentsLimit = 10
    this.attachmentsTotal = 0
    // 获取基础数据
    this.getBaseData()
    // 获取数据字典
    this.getDict()
  },
  methods: {
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        if (this.dataForm.id) {
          this.getOrder(this.getShipper())
        }
      })
    },
    async getBaseData () {
      this.logisticsProductByParamsList = await baseData(baseDataApi.logisticsProductByParamsList).catch(() => {})
      this.countryList = await baseData(baseDataApi.countryList)
      this.userList = await baseData(baseDataApi.userList).catch(() => {})
      this.currencyList = await baseData(baseDataApi.currencyList)
      this.declareUnitList = await this.getDictTypeList('declareUnit')
    },
    async getDict () {
      this.comWaybillLogOperateNodeList = await this.getDictTypeList('ComWaybillLogOperateNode')
      this.yesOrNoList = await this.getDictTypeList('yesOrNo')
      this.parcelTypeList = await this.getDictTypeList('OrderParcelType')
      this.goodsCategoryList = await this.getDictTypeList('OrderGoodsCategory')
      this.taxPayModeList = await this.getDictTypeList('OrderTaxPayMode')
      this.customsMethodList = await this.getDictTypeList('OrderCustomsMethod')
      this.statusList = await this.getDictTypeList('OrderStatus')
      this.objectTypeList = await this.getDictTypeList('coOrderObjectType')
      await this.getDictTypeList('lengthUnit').then((res) => {
        this.lengthUnitList = res.filter(item => item.dictValue === 10 || item.dictValue === 30) // 10=厘米 30=英寸
      })
      await this.getDictTypeList('weightUnit').then((res) => {
        this.weightUnitList = res.filter(item => item.dictValue === 20 || item.dictValue === 40) // 20=千克 40=磅
      })
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'createDate':
          value = timestampFormat(gtmToLtm(scope.row.createDate), 'YYYY-MM-DD hh:mm:ss')
          break
        case 'status':
          value = formatterType(scope.row.status, this.statusList)
          break
        case 'creator':
          value = formatterUserName(scope.row.creator, this.userList)
          break
        case 'operateNode':
          value = formatterType(scope.row.operateNode, this.comWaybillLogOperateNodeList)
          break
        case 'declareUnit':
          value = formatterType(scope.row.declareUnit, this.declareUnitList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    formatterValue (value, field) {
      if (value !== undefined && value !== null && value !== '') {
        switch (field) {
          case 'product':
            value = formatterCodeName(value, this.logisticsProductByParamsList)
            break
          case 'creator':
            value = formatterUserName(value, this.userList)
            break
          case 'parcelType':
            value = formatterType(value, this.parcelTypeList)
            break
          case 'goodsCategory':
            value = formatterType(value, this.goodsCategoryList)
            break
          case 'taxPayMode':
            value = formatterType(value, this.taxPayModeList)
            break
          case 'customsMethod':
            value = formatterType(value, this.customsMethodList)
            break
          case 'yesOrNo':
            value = formatterType(value, this.yesOrNoList)
            break
          case 'country':
            value = formatterCodeName(value, this.countryList)
            break
          case 'status':
            value = formatterType(value, this.statusList)
            break
        }
      }
      return value
    },
    standardUnitChange (value) {
      this.dataForm.standardUnit = value
      if (this.dataForm.standardUnit === 0) {
        this.dataForm.lengthUnit = 10
        this.dataForm.weightUnit = 20
      } else {
        this.dataForm.lengthUnit = 30
        this.dataForm.weightUnit = 40
      }
    },
    declarePageSizeChangeHandle (val) {
      this.declarePage = 1
      this.declareLimit = val
      this.getDeclare()
    },
    // 分页, 当前页
    declarePageCurrentChangeHandle (val) {
      this.declarePage = val
      this.getDeclare()
    },
    packagePageSizeChangeHandle (val) {
      this.packagePage = 1
      this.packageLimit = val
      this.getPackage()
    },
    // 分页, 当前页
    packagePageCurrentChangeHandle (val) {
      this.packagePage = val
      this.getPackage()
    },
    attachmentsPageSizeChangeHandle (val) {
      this.attachmentsPage = 1
      this.attachmentsLimit = val
      this.getAttachments()
    },
    // 分页, 当前页
    attachmentsPageCurrentChangeHandle (val) {
      this.attachmentsPage = val
      this.getAttachments()
    },
    // 获取信息
    getOrder (callback) {
      this.$http.get(`/co/order/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        if (!res.data.shipper) {
          delete res.data['shipper']
        }
        if (!res.data.consignee) {
          delete res.data['consignee']
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
        this.standardUnitChange(res.data.standardUnit)
        callback && callback()
      }).catch(() => {})
    },
    tabsClick (tab, event) {
      // let url
      this.dataList = []
      switch (tab.name) {
        case 'shipperConsignee':
          this.getShipper()
          this.getConsignee()
          break
        case 'declare':
          this.getDeclare()
          break
        case 'package':
          this.getPackage()
          break
        case 'attachments':
          this.getAttachments()
          break
        default:
          break
      }
    },
    // 返回
    backFn () {
      this.visible = false
      this.$emit('backView')
    },
    getShipper () {
      this.$http.get('/co/ordershipper/' + this.dataForm.id).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        if (res.data) {
          this.dataForm.shipper = res.data
        } else {
          for (let key in this.dataForm.shipper) {
            this.dataForm.shipper[key] = ''
          }
        }
      }).catch(() => { })
    },
    getConsignee () {
      this.$http.get('/co/orderconsignee/' + this.dataForm.id).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm.consignee = res.data
      }).catch(() => { })
    },
    getDeclare () {
      this.declareTableLoading = true
      this.$http.get('/co/orderdeclare/page', { params: { orderId: this.dataForm.id, page: this.declarePage, limit: this.declareLimit } }).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.declareDataList = res.data.list
        this.declareTotal = res.data.total || 0
      }).catch(() => { }).finally(() => { this.declareTableLoading = false })
    },
    getAttachments () {
      this.attachmentsTableLoading = true
      this.$http.get('/bd/attachments/page', { params: { relationId: this.dataForm.id, page: this.attachmentsPage, limit: this.attachmentsLimit } }).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.attachmentsList = res.data.list
        this.attachmentsTotal = res.data.total || 0
      }).catch(() => { }).finally(() => { this.attachmentsTableLoading = false })
    },
    getPackage () {
      this.packageTableLoading = true
      this.$http.get('/co/orderpackage/page', { params: { orderId: this.dataForm.id, page: this.packagePage, limit: this.packageLimit } }).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.packageDataList = res.data.list
        this.packageTotal = res.data.total || 0
      }).catch(() => { }).finally(() => { this.packageTableLoading = false })
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat,
    numberFormat,
    formatterCodeName,
    formatterCurrency
  },
  computed: {
    packageTableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.packageTableColumns).map((key) => this.packageTableColumns[key])
      arr = tableColumns.filter((item) => {
        if (item.prop === 'packageWeightD') {
          item.label = this.$t('fba.weight') + (this.dataForm.standardUnit === 0 ? '(KG)' : '(LB)')
        }
        if (item.prop === 'packageLengthD') {
          item.label = this.$t('fba.length') + (this.dataForm.standardUnit === 0 ? '(CM)' : '(IN)')
        }
        if (item.prop === 'packageWidthD') {
          item.label = this.$t('fba.width') + (this.dataForm.standardUnit === 0 ? '(CM)' : '(IN)')
        }
        if (item.prop === 'packageHeightD') {
          item.label = this.$t('fba.height') + (this.dataForm.standardUnit === 0 ? '(CM)' : '(IN)')
        }
        return item.isShow
      })
      return arr
    },
    attachmentsTableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.attachmentsTableColumns).map((key) => this.attachmentsTableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    declareTableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.declareTableColumns).map((key) => this.declareTableColumns[key])
      arr = tableColumns.filter((item) => {
        if (item.prop === 'unitDeclarePriceD') {
          item.label = this.$t('coOrderDeclare.unitDeclarePrice') + '(' + this.dataForm.declareCurrency + ')'
        }
        if (item.prop === 'unitNetWeightD') {
          item.label = this.$t('fba.unitNetWeight') + (this.dataForm.standardUnit === 0 ? '(KG)' : '(LB)')
        }
        return item.isShow
      })
      return arr
    }
  }
}
</script>
