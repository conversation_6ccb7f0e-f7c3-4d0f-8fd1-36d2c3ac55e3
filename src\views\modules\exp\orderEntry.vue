<template>
  <div class="add-body panel_body">
    <div class="panel-hd">
      <span>订单录入</span>
    </div>
    <div class="addOrUpdatePanel orderAdd" >
      <h2>主信息</h2>
      <div class="tab_box">
        <div class="tab_panel">
          <div class="tab_item" :class="active === 0? 'active': ''" @click="active = 0">
            运单信息
          </div>
          <div class="tab_item" :class="active === 1? 'active': ''" @click="active = 1">
            收件人信息
          </div>
          <div class="tab_item" :class="active === 2? 'active': ''" @click="active = 2">
            发件人信息
          </div>
        </div>
      </div>

      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" :disabled="this.dataForm.status==10 ? false: true">
        <!-- 第一步 -->
        <el-row :gutter="20" type="flex" justify="center" v-show="active === 0">
          <el-col :span="22">
            <el-row :gutter="20">
              <el-col :md="12">
                <el-form-item :label="$t('coOrder.waybillNo')" prop="waybillNo">
                  <el-input v-model="dataForm.waybillNo" :placeholder="$t('coOrder.waybillNo')" disabled></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="12">
                <el-form-item :label="$t('coOrder.deliveryNo')" prop="deliveryNo">
                  <el-input v-model="dataForm.deliveryNo" :placeholder="$t('coOrder.deliveryNo')" disabled></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :md="12">
                <el-form-item :label="$t('coOrder.status')" prop="status">
                  <el-select v-model="dataForm.status" :placeholder="$t('coOrder.status')" disabled>
                    <el-option v-for="item in orderStatusList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :md="12">
                <el-form-item :label="$t('coOrder.customerOrderNo')" prop="customerOrderNo">
                  <el-input v-model="dataForm.customerOrderNo" :placeholder="$t('coOrder.customerOrderNo')" :disabled="this.dataForm.id?true:false"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :md="12">
                <el-form-item :label="$t('coOrder.logisticsProductCode')" prop="logisticsProductCode">
                  <el-select v-model="dataForm.logisticsProductCode" :placeholder="$t('coOrder.logisticsProductCode')" filterable clearable>
                    <el-option v-for="item in logisticsProductByParamsList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col class="borderLeft">
            <el-row :gutter="20">
              <el-col :md="12">
                <el-form-item :label="$t('coOrder.taxPayMode')" prop="taxPayMode">
                  <el-select v-model="dataForm.taxPayMode" :placeholder="$t('coOrder.taxPayMode')">
                    <el-option v-for="item in taxPayModeList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :md="12">
                <el-form-item :label="$t('coOrder.electric')" prop="electric">
                  <el-radio v-model="dataForm.electric" label=0>否</el-radio>
                  <el-radio v-model="dataForm.electric" label=1>是</el-radio>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :md="12">
                <el-form-item :label="$t('coOrder.parcelType')" prop="parcelType">
                  <el-select v-model="dataForm.parcelType" :placeholder="$t('coOrder.parcelType')">
                    <el-option v-for="item in parcelTypeList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :md="12">
                <el-form-item :label="$t('coOrder.goodsCategory')" prop="goodsCategory">
                  <el-select v-model="dataForm.goodsCategory" :placeholder="$t('coOrder.goodsCategory')">
                    <el-option v-for="item in goodsCategoryList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :md="12">
                <el-form-item :label="$t('coOrder.declareCurrency')" prop="declareCurrency">
                  <el-select v-model="dataForm.declareCurrency" :placeholder="$t('coOrder.declareCurrency')" filterable clearable>
                    <el-option v-for="item in currencyList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :md="12">
                <el-form-item :label="$t('coOrder.insuredAmount')" prop="insuredAmount">
                  <el-input v-model="dataForm.insuredAmount" :placeholder="$t('coOrder.insuredAmount')"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :md="12">
                <el-form-item :label="$t('coOrder.insuredCurrency')" prop="insuredCurrency">
                  <el-select v-model="dataForm.insuredCurrency" :placeholder="$t('coOrder.insuredCurrency')" filterable clearable>
                    <el-option v-for="item in currencyList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :md="12">
                <el-form-item :label="$t('coOrder.codAmount')" prop="codAmount">
                  <el-input v-model="dataForm.codAmount" :placeholder="$t('coOrder.codAmount')"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :md="12">
                <el-form-item :label="$t('coOrder.codCurrency')" prop="codCurrency">
                  <el-select v-model="dataForm.codCurrency" :placeholder="$t('coOrder.codCurrency')" filterable clearable>
                    <el-option v-for="item in currencyList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :md="12">
                <el-form-item :label="$t('coOrder.customerRemark')" prop="customerRemark">
                  <el-input v-model="dataForm.customerRemark" :placeholder="$t('coOrder.customerRemark')"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :md="12">
                <el-form-item :label="$t('coOrder.salesUrl')" prop="salesUrl">
                  <el-input v-model="dataForm.salesUrl" :placeholder="$t('coOrder.salesUrl')"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
        </el-row>

        <!-- 第二步 -->
        <!--收件人-->
        <el-row :gutter="20" type="flex" justify="center" v-show="active === 1">
          <el-col >
            <el-row :gutter="10">
              <el-col :md="6">
                <el-form-item :label="$t('coOrderConsignee.consigneeName')" prop="consignee.consigneeName">
                  <el-input v-model="dataForm.consignee.consigneeName" :placeholder="$t('coOrderConsignee.consigneeName')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="6">
                <el-form-item :label="$t('coOrderConsignee.consigneePhone')" prop="consignee.consigneePhone">
                  <el-input v-model="dataForm.consignee.consigneePhone" :placeholder="$t('coOrderConsignee.consigneePhone')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="6">
                <el-form-item :label="$t('coOrderConsignee.consigneeCountry')" prop="consignee.consigneeCountry">
                  <el-select v-model="dataForm.consignee.consigneeCountry" :placeholder="$t('coOrderConsignee.consigneeCountry')" filterable clearable>
                    <el-option v-for="item in countryList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :md="6">
                <el-form-item :label="$t('coOrderConsignee.consigneeProvince')" prop="consignee.consigneeProvince">
                  <el-input v-model="dataForm.consignee.consigneeProvince" :placeholder="$t('coOrderConsignee.consigneeProvince')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="6">
                <el-form-item :label="$t('coOrderConsignee.consigneeCity')" prop="consignee.consigneeCity">
                  <el-input v-model="dataForm.consignee.consigneeCity" :placeholder="$t('coOrderConsignee.consigneeCity')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="6" >
                <el-form-item :label="$t('coOrderConsignee.consigneeDistrict')" prop="consignee.consigneeDistrict">
                  <el-input v-model="dataForm.consignee.consigneeDistrict" :placeholder="$t('coOrderConsignee.consigneeDistrict')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="6" >
                <el-form-item :label="$t('coOrderConsignee.consigneeStreet')" prop="consignee.consigneeStreet">
                  <el-input v-model="dataForm.consignee.consigneeStreet" :placeholder="$t('coOrderConsignee.consigneeStreet')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="6" >
                <el-form-item :label="$t('coOrderConsignee.consigneeDoorplate')" prop="consignee.consigneeDoorplate">
                  <el-input v-model="dataForm.consignee.consigneeDoorplate" :placeholder="$t('coOrderConsignee.consigneeDoorplate')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="6">
                <el-form-item :label="$t('coOrderConsignee.consigneeAddress')" prop="consignee.consigneeAddress">
                  <el-input v-model="dataForm.consignee.consigneeAddress" :placeholder="$t('coOrderConsignee.consigneeAddress')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="6">
                <el-form-item :label="$t('coOrderConsignee.consigneePostcode')" prop="consignee.consigneePostcode">
                  <el-input v-model="dataForm.consignee.consigneePostcode" :placeholder="$t('coOrderConsignee.consigneePostcode')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="6" >
                <el-form-item :label="$t('coOrderConsignee.consigneeCompany')" prop="consignee.consigneeCompany">
                  <el-input v-model="dataForm.consignee.consigneeCompany" :placeholder="$t('coOrderConsignee.consigneeCompany')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="6" >
                <el-form-item :label="$t('coOrderConsignee.consigneeEmail')" prop="consignee.consigneeEmail">
                  <el-input v-model="dataForm.consignee.consigneeEmail" :placeholder="$t('coOrderConsignee.consigneeEmail')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="6" >
                <el-form-item :label="$t('coOrderConsignee.consigneeIdcard')" prop="consignee.consigneeIdcard">
                  <el-input v-model="dataForm.consignee.consigneeIdcard" :placeholder="$t('coOrderConsignee.consigneeIdcard')"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
        </el-row>

        <!--发件人-->
        <el-row :gutter="20" type="flex" justify="center" v-show="active === 2">
          <el-col>
            <el-row :gutter="20">
              <el-col :md="6">
                <el-form-item :label="$t('coOrderShipper.shipperName')" prop="shipper.shipperName">
                  <el-input v-model="dataForm.shipper.shipperName" :placeholder="$t('coOrderShipper.shipperName')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="6" >
                <el-form-item :label="$t('coSetCustomerShipper.shortName')">
                  <el-select v-model="dataForm.setShipper" :placeholder="$t('coSetCustomerShipper.shortName')" filterable clearable @change="selectSetShipper">
                    <el-option v-for="item in setShipperByCustomerCodeList" :key="item.id" :label="item.shortName" :value="item.id"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :md="6" >
                <el-form-item :label="$t('coOrderShipper.shipperCompany')" prop="shipper.shipperCompany">
                  <el-input v-model="dataForm.shipper.shipperCompany" :placeholder="$t('coOrderShipper.shipperCompany')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="6">
                <el-form-item :label="$t('coOrderShipper.shipperPhone')" prop="shipper.shipperPhone">
                  <el-input v-model="dataForm.shipper.shipperPhone" :placeholder="$t('coOrderShipper.shipperPhone')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="6">
                <el-form-item :label="$t('coOrderShipper.shipperCountry')" prop="shipper.shipperCountry">
                  <el-select v-model="dataForm.shipper.shipperCountry" :placeholder="$t('coOrderShipper.shipperCountry')" filterable clearable>
                    <el-option v-for="item in countryList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :md="6">
                <el-form-item :label="$t('coOrderShipper.shipperProvince')" prop="shipper.shipperProvince">
                  <el-input v-model="dataForm.shipper.shipperProvince" :placeholder="$t('coOrderShipper.shipperProvince')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="6">
                <el-form-item :label="$t('coOrderShipper.shipperCity')" prop="shipper.shipperCity">
                  <el-input v-model="dataForm.shipper.shipperCity" :placeholder="$t('coOrderShipper.shipperCity')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="6">
                <el-form-item :label="$t('coOrderShipper.shipperDistrict')" prop="shipper.shipperDistrict">
                  <el-input v-model="dataForm.shipper.shipperDistrict" :placeholder="$t('coOrderShipper.shipperDistrict')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="6" >
                <el-form-item :label="$t('coOrderShipper.shipperDoorplate')" prop="shipper.shipperDoorplate">
                  <el-input v-model="dataForm.shipper.shipperDoorplate" :placeholder="$t('coOrderShipper.shipperDoorplate')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="6" >
                <el-form-item :label="$t('coOrderShipper.shipperStreet')" prop="shipper.shipperStreet">
                  <el-input v-model="dataForm.shipper.shipperStreet" :placeholder="$t('coOrderShipper.shipperStreet')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="6">
                <el-form-item :label="$t('coOrderShipper.shipperAddress')" prop="shipper.shipperAddress">
                  <el-input v-model="dataForm.shipper.shipperAddress" :placeholder="$t('coOrderShipper.shipperAddress')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="6">
                <el-form-item :label="$t('coOrderShipper.shipperPostcode')" prop="shipper.shipperPostcode">
                  <el-input v-model="dataForm.shipper.shipperPostcode" :placeholder="$t('coOrderShipper.shipperPostcode')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="6" >
                <el-form-item :label="$t('coOrderShipper.shipperEmail')" prop="shipper.shipperEmail">
                  <el-input v-model="dataForm.shipper.shipperEmail" :placeholder="$t('coOrderShipper.shipperEmail')"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
        </el-row>

        <!-- 第三部 -->
        <h2 class="packDetail">包裹尺寸及申报信息</h2>
        <el-tabs class="no_shadow" type="border-card" v-model="activeSecondName" :stretch="false"  >
          <el-tab-pane label="报关明细" name="declare">
              <el-row class="optBtn_panel">
                <el-col :md="12" >
                  <el-button size="mini" plain @click="declareAddClick">{{ $t('add') }}</el-button>
                </el-col>
              </el-row>
              <div class="list_panel_500" >
                <el-table :key="Math.random()" ref="declareDataList" v-loading="declareDataListLoading" :data="declareDataList" border max-height="500">
                  <!-- 动态显示表格 -->
                  <!--<el-table-column type="selection" width='50' fixed="left"></el-table-column>-->
                  <el-table-column label="序号" type="index" width="50"></el-table-column>
                  <el-table-column v-for="(item, index) in declareTableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
                    <template slot-scope="scope">
                      <div>
                        <div v-if="item.prop === 'createDate'">
                          <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                            <span>{{formatterFn(scope,item.prop)}}</span>
                          </el-tooltip>
                        </div>
                        <div v-else>
                          {{formatterFn(scope,item.prop)}}
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="150">
                    <template slot-scope="scope">
                      <el-link :underline="false"  @click="declareViewHandle(scope)" :disabled="dataForm.status==10 ? false: true">{{ $t('view') }}</el-link>
                      <el-link :underline="false"  @click="declareDeleteHandle(scope)" :disabled="dataForm.status==10 ? false: true">{{ $t('delete') }}</el-link>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
          </el-tab-pane>
          <el-tab-pane label="包裹明细" name="parcel">
            <el-row class="optBtn_panel">
              <el-col :md="6">
                <el-button size="mini" plain @click="packageAddClick">{{ $t('add') }}</el-button>
              </el-col>
            </el-row>
            <div class="list_panel_600" >
              <el-table :key="Math.random()" ref="packageDataList" v-loading="declareDataListLoading" :data="packageDataList" border max-height="600">
                <!--&lt;!&ndash; 动态显示表格 &ndash;&gt;-->
                <!--<el-table-column type="selection" width='50' fixed="left"></el-table-column>-->
                <el-table-column label="序号" type="index" width="50"></el-table-column>
                <el-table-column v-for="(item, index) in packageTableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
                  <template slot-scope="scope">
                    <div>
                      <div v-if="item.prop === 'createDate'">
                        <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                          <span>{{formatterFn(scope,item.prop)}}</span>
                        </el-tooltip>
                      </div>
                      <div v-else>
                        {{formatterFn(scope,item.prop)}}
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="150">
                  <template slot-scope="scope">
                    <el-link :underline="false"  @click="packageViewHandle(scope)" :disabled="dataForm.status==10 ? false: true">{{ $t('view') }}</el-link>
                    <el-link :underline="false"  @click="packageDeleteHandle(scope)" :disabled="dataForm.status==10 ? false: true">{{ $t('delete') }}</el-link>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-form >
    </div>

    <div id="cs_FormFooter" class="el-form-footer" slot="footer">
      <el-button size="medium" @click="draftOrder" :disabled="this.dataForm.status==10 ? false: true" >{{ $t('draft') }}</el-button>
      <el-button type="primary" size="medium" @click="forecastOrder" :disabled="this.dataForm.status==10 ? false: true" >{{ $t('forecast') }}</el-button>
    </div>
    <!-- 报关明细弹出层 -->
    <el-dialog title="报关明细" :visible.sync="declareDialogVisible" width="70%" style="min-width: 900px"  :before-close="declareHandleClose">
      <el-form :model="declareDataForm" :rules="declareDataRules" ref="declareDataForm" >
        <el-row :gutter="20" type="flex" justify="center" >
          <el-col>
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item :label="$t('coOrderDeclare.chineseName')" prop="chineseName">
                  <el-input v-model="declareDataForm.chineseName" :placeholder="$t('coOrderDeclare.chineseName')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('coOrderDeclare.englishName')" prop="englishName">
                  <el-input v-model="declareDataForm.englishName" :placeholder="$t('coOrderDeclare.englishName')"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item :label="$t('coOrderDeclare.quantity')" prop="quantity">
                  <el-input v-model="declareDataForm.quantity" :placeholder="$t('coOrderDeclare.quantity')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('coOrderDeclare.unitNetWeight')" prop="unitNetWeight">
                  <el-input v-model="declareDataForm.unitNetWeight" :placeholder="$t('coOrderDeclare.unitNetWeight')"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="24">
                <el-form-item :label="$t('coOrderDeclare.unitDeclarePrice')" prop="unitDeclarePrice">
                  <el-input v-model="declareDataForm.unitDeclarePrice" :placeholder="$t('coOrderDeclare.unitDeclarePrice')"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col class="borderLeft">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('coSetCustomerDeclare.chineseName')">
                  <el-select v-model="declareDataForm.setCustomerDeclare" :placeholder="$t('coSetCustomerDeclare.chineseName')" filterable clearable @change="selectSetDeclare">
                    <el-option v-for="item in setDeclareByCustomerCodeList" :key="item.id" :label="item.chineseName" :value="item.id"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('coOrderDeclare.brand')" prop="brand">
                  <el-input v-model="declareDataForm.brand" :placeholder="$t('coOrderDeclare.brand')"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item :label="$t('coOrderDeclare.goodsBarcode')" prop="goodsBarcode">
                  <el-input v-model="declareDataForm.goodsBarcode" :placeholder="$t('coOrderDeclare.goodsBarcode')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('coOrderDeclare.sku')" prop="sku">
                  <el-input v-model="declareDataForm.sku" :placeholder="$t('coOrderDeclare.sku')"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item :label="$t('coOrderDeclare.hsCode')" prop="hsCode">
                  <el-input v-model="declareDataForm.hsCode" :placeholder="$t('coOrderDeclare.hsCode')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('coOrderDeclare.productModel')" prop="productModel">
                  <el-input v-model="declareDataForm.productModel" :placeholder="$t('coOrderDeclare.productModel')"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item :label="$t('coOrderDeclare.material')" prop="material">
                  <el-input v-model="declareDataForm.material" :placeholder="$t('coOrderDeclare.material')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('coOrderDeclare.purpose')" prop="purpose">
                  <el-input v-model="declareDataForm.purpose" :placeholder="$t('coOrderDeclare.purpose')"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item :label="$t('coOrderDeclare.origin')" prop="origin">
                  <el-input v-model="declareDataForm.origin" :placeholder="$t('coOrderDeclare.origin')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('coOrderDeclare.pickingRemark')" prop="pickingRemark">
                  <el-input v-model="declareDataForm.pickingRemark" :placeholder="$t('coOrderDeclare.pickingRemark')"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item :label="$t('coOrderDeclare.productUrl')" prop="productUrl">
                  <el-input v-model="declareDataForm.productUrl" :placeholder="$t('coOrderDeclare.productUrl')"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="declareHandleClose">取 消</el-button>
        <el-button type="primary" @click="declareHandleConfirm">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 包裹明细 -->
    <el-dialog title="包裹明细" :visible.sync="packageDialogVisible" width="70%" style="min-width: 900px; " :before-close="packageHandleClose">
      <el-form :model="packageDataForm" :rules="packageDataRule" ref="packageDataForm" >
        <el-row :gutter="20" type="flex" justify="center">
          <el-col>
           <el-row :gutter="10">
             <el-col :span="12">
               <el-form-item :label="$t('coOrderPackage.channelLabelUrl')" prop="channelLabelUrl">
                 <el-input v-model="packageDataForm.channelLabelUrl" :placeholder="$t('coOrderPackage.channelLabelUrl')" disabled></el-input>
               </el-form-item>
             </el-col>
             <el-col :span="12">
               <el-form-item :label="$t('coOrderPackage.packageWeight')" prop="packageWeight">
                 <el-input v-model="packageDataForm.packageWeight" :placeholder="$t('coOrderPackage.packageWeight')"></el-input>
               </el-form-item>
             </el-col>
           </el-row>
         </el-col>
          <el-col class="borderLeft">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="$t('coOrderPackage.packageSerialNo')" prop="packageSerialNo">
                  <el-input v-model="packageDataForm.packageSerialNo" :placeholder="$t('coOrderPackage.packageSerialNo')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('coOrderPackage.packageDeliveryNo')" prop="packageDeliveryNo">
                  <el-input v-model="packageDataForm.packageDeliveryNo" :placeholder="$t('coOrderPackage.packageDeliveryNo')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('coOrderPackage.packageCustomerNo')" prop="packageCustomerNo">
                  <el-input v-model="packageDataForm.packageCustomerNo" :placeholder="$t('coOrderPackage.packageCustomerNo')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('coOrderPackage.packageLength')" prop="packageLength">
                  <el-input v-model="packageDataForm.packageLength" :placeholder="$t('coOrderPackage.packageLength')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('coOrderPackage.packageWidth')" prop="packageWidth">
                  <el-input v-model="packageDataForm.packageWidth" :placeholder="$t('coOrderPackage.packageWidth')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$t('coOrderPackage.packageHeight')" prop="packageHeight">
                  <el-input v-model="packageDataForm.packageHeight" :placeholder="$t('coOrderPackage.packageHeight')"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
          <el-button @click="packageHandleClose">取 消</el-button>
          <el-button type="primary" @click="packageHandleConfirm">确 定</el-button>
        </span>
    </el-dialog>
  </div>
</template>

<script>
import debounce from 'lodash/debounce'
import cloneDeep from 'lodash/cloneDeep'
import dictTypeMixins from '@/mixins/dictTypeMixins'
import areaBox from '@/components/areaBox'
import { formatterType, gtmToLtm, timestampFormat } from '@/filters/filters'
import { isDecimal3, isDecimal2, isPlusInteger2, letterAndNumber, isEmail } from '@/utils/validate'
import baseDataApi from '@/api'
import baseData from '@/api/baseData'

export default {
  mixins: [dictTypeMixins],
  data () {
    return {
      active: 0,
      orderStatusList: [],
      logisticsProductByParamsList: [],
      countryList: [],
      currencyList: [],
      taxPayModeList: [],
      parcelTypeList: [],
      goodsCategoryList: [],
      setShipper: null,
      setShipperByCustomerCodeList: [],
      setCustomerDeclare: null,
      setDeclareByCustomerCodeList: [],
      activeFirstName: 'consignee',
      activeSecondName: 'declare',
      dataForm: {
        id: null,
        customerOrderNo: null,
        waybillNo: null,
        deliveryNo: null,
        status: 10,
        logisticsProductCode: null,
        taxPayMode: 11,
        parcelType: 10,
        goodsCategory: 15,
        electric: 0,
        insuredAmount: null,
        insuredCurrency: 'USD',
        codAmount: null,
        codCurrency: 'USD',
        declareCurrency: 'USD',
        customerRemark: null,
        salesUrl: null,
        consignee: {
          consigneeName: null,
          consigneeCompany: null,
          consigneePhone: null,
          consigneeEmail: null,
          consigneeCountry: null,
          consigneeProvince: null,
          consigneeCity: null,
          consigneeDistrict: null,
          consigneeAddress: null,
          consigneePostcode: null,
          consigneeDoorplate: null,
          consigneeStreet: null,
          consigneeIdcard: null
        },
        shipper: {
          shipperName: null,
          shipperCompany: null,
          shipperPhone: null,
          shipperEmail: null,
          shipperCountry: 'CN',
          shipperProvince: null,
          shipperCity: null,
          shipperDistrict: null,
          shipperAddress: null,
          shipperPostcode: null,
          shipperDoorplate: null,
          shipperStreet: null
        },
        orderDeclareList: [],
        packageList: [],
        setShipper: null
      },
      declareTableColumns: [
        { type: '', width: '150', prop: 'orderId', label: this.$t('coOrderDeclare.orderId'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '150', prop: 'chineseName', label: this.$t('coSetCustomerDeclare.chineseName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'englishName', label: this.$t('coSetCustomerDeclare.englishName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'quantity', label: this.$t('coSetCustomerDeclare.quantity'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'unitNetWeight', label: this.$t('coSetCustomerDeclare.unitNetWeight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'unitDeclarePrice', label: this.$t('coSetCustomerDeclare.unitDeclarePrice'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'id', label: this.$t('system.id'), align: 'center', isShow: false, disabled: false }
      ],
      declareDataList: [],
      declareDataListLoading: false,
      declareDialogVisible: false,
      declareDataForm: {
        id: '',
        creator: '',
        createDate: '',
        orderId: '',
        chineseName: '',
        englishName: '',
        quantity: '',
        unitNetWeight: '',
        unitDeclarePrice: '',
        brand: '',
        goodsBarcode: '',
        sku: '',
        hsCode: '',
        productModel: '',
        material: '',
        purpose: '',
        origin: '',
        pickingRemark: '',
        productUrl: '',
        setCustomerDeclare: ''
      },
      packageTableColumns: [
        { type: '', width: '150', prop: 'orderId', label: this.$t('coOrderPackage.orderId'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '150', prop: 'packageSerialNo', label: this.$t('coOrderPackage.packageSerialNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'packageWeight', label: this.$t('coOrderPackage.packageWeight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'packageLength', label: this.$t('coOrderPackage.packageLength'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'packageWidth', label: this.$t('coOrderPackage.packageWidth'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'packageHeight', label: this.$t('coOrderPackage.packageHeight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'id', label: this.$t('system.id'), align: 'center', isShow: false, disabled: false }
      ],
      packageDataList: [],
      packageDataListLoading: false,
      packageDialogVisible: false,
      packageDataForm: {
        id: '',
        creator: '',
        createDate: '',
        orderId: '',
        packageSerialNo: '',
        packageDeliveryNo: '',
        packageCustomerNo: '',
        packageWeight: '',
        packageLength: '',
        packageWidth: '',
        packageHeight: '',
        channelLabelUrl: ''
      },
      declareViewIndex: 0,
      // 0 新增 1 修改
      declareAddOrUpdate: 0,
      packageViewIndex: 0,
      // 0 新增 1 修改
      packageAddOrUpdate: 0
    }
  },
  props: {
    // excl导入的产品编号
    action: {
      type: String,
      required: false
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat
  },
  computed: {
    dataRule () {
      const validateIsEmail = (rule, value, callback) => {
        if (value !== null && value !== '' && value !== undefined && !isEmail(value)) {
          return callback(new Error(this.$t('validate.format', { 'attr': this.$t('coOrderDeclare.isEmail') })))
        }
        callback()
      }
      const validateDecimal2 = (rule, value, callback) => {
        if (value !== null && value !== '' && value !== undefined && !isDecimal2(value)) {
          return callback(new Error(this.$t('validate.format', { 'attr': this.$t('coOrderDeclare.isDecimal2') })))
        }
        callback()
      }
      return {
        customerOrderNo: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        logisticsProductCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        'consignee.consigneeName': [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        'consignee.consigneePhone': [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        'consignee.consigneeCountry': [
          { required: true, message: this.$t('validate.required'), trigger: ['blur', 'change'] }
        ],
        'consignee.consigneeProvince': [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        'consignee.consigneeCity': [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        'consignee.consigneeAddress': [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        'consignee.consigneePostcode': [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        consigneeEmail: [
          { validator: validateIsEmail, trigger: 'blur' }
        ],
        'shipper.shipperName': [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        'shipper.shipperPhone': [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        'shipper.shipperCountry': [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        'shipper.shipperProvince': [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        'shipper.shipperCity': [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        'shipper.shipperDistrict': [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        'shipper.shipperAddress': [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        'shipper.shipperPostcode': [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        shipperEmail: [
          { validator: validateIsEmail, trigger: 'blur' }
        ],
        codAmount: [
          { validator: validateDecimal2, trigger: 'blur' }
        ],
        insuredAmount: [
          { validator: validateDecimal2, trigger: 'blur' }
        ]
      }
    },
    declareDataRules () {
      const validateDecimal3 = (rule, value, callback) => {
        if (!isDecimal3(value)) {
          return callback(new Error(this.$t('validate.format', { 'attr': this.$t('coOrderDeclare.isDecimal3') })))
        }
        callback()
      }
      const validateDecimal2 = (rule, value, callback) => {
        if (!isDecimal2(value)) {
          return callback(new Error(this.$t('validate.format', { 'attr': this.$t('coOrderDeclare.isDecimal2') })))
        }
        callback()
      }
      const validateIsPlusInteger2 = (rule, value, callback) => {
        if (!isPlusInteger2(value)) {
          return callback(new Error(this.$t('validate.format', { 'attr': this.$t('coOrderDeclare.isPlusInteger2') })))
        }
        callback()
      }
      const validateLetterAndNumber = (rule, value, callback) => {
        if (!letterAndNumber(value)) {
          return callback(new Error(this.$t('validate.format', { 'attr': this.$t('coOrderDeclare.letterAndNumber') })))
        }
        callback()
      }
      return {
        chineseName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        englishName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: validateLetterAndNumber, trigger: 'blur' }
        ],
        quantity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: validateIsPlusInteger2, trigger: 'blur' }
        ],
        unitNetWeight: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: validateDecimal3, trigger: 'blur' }
        ],
        unitDeclarePrice: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: validateDecimal2, trigger: 'blur' }
        ]
      }
    },
    declareTableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.declareTableColumns).map((key) => this.declareTableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    packageTableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.packageTableColumns).map((key) => this.packageTableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    packageDataRule () {
      const validateDecimal3 = (rule, value, callback) => {
        if (!isDecimal3(value)) {
          return callback(new Error(this.$t('validate.format', { 'attr': this.$t('coOrderDeclare.isDecimal3') })))
        }
        callback()
      }
      const validateIsPlusInteger2 = (rule, value, callback) => {
        if (value !== null && value !== '' && value !== undefined && !isPlusInteger2(value)) {
          return callback(new Error(this.$t('validate.format', { 'attr': this.$t('coOrderDeclare.isPlusInteger2') })))
        }
        callback()
      }
      return {
        packageWeight: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: validateDecimal3, trigger: 'blur' }
        ],
        packageLength: [
          { validator: validateIsPlusInteger2, trigger: 'blur' }
        ],
        packageWidth: [
          { validator: validateIsPlusInteger2, trigger: 'blur' }
        ],
        packageHeight: [
          { validator: validateIsPlusInteger2, trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    this.getDetail()
  },
  activated () {
    this.$nextTick(() => {
      // 如果菜单浮动位置 需要初始化
      this.$footerScroll()
    })
    this.getDetail()
    // 验证
    this.$refs['dataForm'].validate((valid) => {
      if (!valid) {
        return false
      }
    })
  },
  methods: {
    prev () {
      if (this.active > 0) this.active--
    },
    next () {
      if (this.active++ > 2) this.active = 0
    },
    getDetail () {
      this.getDict()
      this.getBaseData()
      // 查询订单详情
      if (this.$route.query.orderId !== undefined && this.$route.query.orderId !== null) {
        this.getOrderInfo(this.$route.query.orderId)
      }
      // 复制订单
      if (this.$route.query.orderId !== undefined && this.$route.query.orderId !== null && this.$route.query.opType !== undefined && this.$route.query.opType === 'copy') {
        this.getOrderInfo(this.$route.query.orderId)
      }
    },
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        this.$refs['dataForm'].resetFields()
      })
    },
    async getDict () {
      // 获取相关字典
      this.orderStatusList = await this.getDictTypeList('OrderStatus') // 订单状态
      this.taxPayModeList = await this.getDictTypeList('OrderTaxPayMode') // 订单状态
      this.parcelTypeList = await this.getDictTypeList('OrderParcelType') // 订单状态
      this.goodsCategoryList = await this.getDictTypeList('OrderGoodsCategory') // 订单状态
    },
    async getBaseData () {
      this.logisticsProductByParamsList = await await baseData(baseDataApi.logisticsProductByParamsList)
      this.countryList = await await baseData(baseDataApi.countryList)
      this.currencyList = await await baseData(baseDataApi.currencyList)
      this.setShipperByCustomerCodeList = await await baseData(baseDataApi.setShipperByCustomerCodeList + this.$store.state.user.customerId)
      this.setDeclareByCustomerCodeList = await await baseData(baseDataApi.setDeclareByCustomerCodeList + this.$store.state.user.customerId)
    },
    getOrderInfo (id) {
      this.$http.get('/co/order/' + id).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = res.data
        this.declareDataList = res.data.orderDeclareList
        this.packageDataList = res.data.packageList
        if (this.$route.query.opType === 'copy') {
          this.dataForm.id = null
          this.dataForm.customerOrderNo = null
          this.dataForm.waybillNo = null
          this.dataForm.deliveryNo = null
          this.dataForm.status = 10
        }
      }).catch(() => {})
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'createDate':
          value = timestampFormat(gtmToLtm(scope.row.createDate), 'YYYY-MM-DD')
          break
        case 'status':
          value = formatterType(scope.row.status, this.statusList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    declareAddClick () {
      this.declareDialogVisible = true
      this.declareViewIndex = 0
      this.declareAddOrUpdate = 0
    },
    declareViewHandle (data) {
      this.declareViewIndex = data.$index
      this.declareDialogVisible = true
      this.declareAddOrUpdate = 1
      this.declareDataForm = cloneDeep(data.row)
    },
    declareDeleteHandle (data) {
      this.declareViewIndex = data.$index
      this.declareDataList.splice(this.declareViewIndex, 1)
    },
    declareHandleClose () {
      this.$refs.declareDataForm.resetFields()
      this.declareDialogVisible = false
    },
    declareHandleConfirm () {
      this.$refs.declareDataForm.validate((valid) => {
        if (!valid) {
          return false
        }
        if (this.declareAddOrUpdate === 1) {
          this.declareDataList.splice(this.declareViewIndex, 1, cloneDeep(this.declareDataForm))
        } else {
          this.declareDataList.push(cloneDeep(this.declareDataForm))
        }
        this.$refs.declareDataForm.resetFields()
        this.declareDialogVisible = false
      })
    },
    packageAddClick () {
      this.packageDialogVisible = true
      this.packageViewIndex = 0
      this.packageAddOrUpdate = 0
    },
    packageViewHandle (data) {
      this.packageViewIndex = data.$index
      this.packageDialogVisible = true
      this.packageAddOrUpdate = 1
      this.packageDataForm = cloneDeep(data.row)
    },
    packageDeleteHandle (data) {
      this.packageViewIndex = data.$index
      this.packageDataList.splice(this.packageViewIndex, 1)
    },
    packageHandleClose () {
      this.$refs.packageDataForm.resetFields()
      this.packageDialogVisible = false
    },
    packageHandleConfirm () {
      this.$refs.packageDataForm.validate((valid) => {
        if (!valid) {
          return false
        }
        if (this.packageAddOrUpdate === 1) {
          this.packageDataList.splice(this.packageViewIndex, 1, cloneDeep(this.packageDataForm))
        } else {
          this.packageDataList.push(cloneDeep(this.packageDataForm))
        }
        this.$refs.packageDataForm.resetFields()
        this.packageDialogVisible = false
      })
    },
    // 表单提交
    forecastOrder: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        // 预报
        this.dataForm.status = 11
        this.placeOrder('forecast')
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    draftOrder: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        // 草稿
        this.dataForm.status = 10
        this.placeOrder('draft')
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    placeOrder (data) {
      this.dataForm.orderDeclareList = this.declareDataList
      this.dataForm.packageList = this.packageDataList
      if (this.declareDataList.length <= 0) {
        this.$message({
          message: this.$t('coOrderDeclare.atLeastOne'),
          type: 'warning'
        })
        return false
      }
      this.$http[!this.dataForm.id ? 'post' : 'put']('/co/order', { 'orders': [this.dataForm] }).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$router.push({ name: 'co-orderList' })
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 500,
          onClose: () => {
            this.$router.push({ name: 'co-orderList', params: { activeName: data } })
          }
        })
      }).catch(() => {})
    },
    selectSetShipper (data) {
      let shipperArray = this.setShipperByCustomerCodeList
      for (let i = 0; i < shipperArray.length; i++) {
        if (data === shipperArray[i].id) {
          this.dataForm.shipper.shipperName = shipperArray[i].shipperName
          this.dataForm.shipper.shipperCompany = shipperArray[i].shipperCompany
          this.dataForm.shipper.shipperPhone = shipperArray[i].shipperPhone
          this.dataForm.shipper.shipperEmail = shipperArray[i].shipperEmail
          this.dataForm.shipper.shipperCountry = shipperArray[i].shipperCountryCode
          this.dataForm.shipper.shipperProvince = shipperArray[i].shipperProvince
          this.dataForm.shipper.shipperCity = shipperArray[i].shipperCity
          this.dataForm.shipper.shipperDistrict = shipperArray[i].shipperDistrict
          this.dataForm.shipper.shipperAddress = shipperArray[i].shipperAddress
          this.dataForm.shipper.shipperPostcode = shipperArray[i].shipperPostcode
          this.dataForm.shipper.shipperDoorplate = shipperArray[i].shipperDoorplate
          this.dataForm.shipper.shipperStreet = shipperArray[i].shipperStreet
          return
        }
      }
    },
    selectSetDeclare (data) {
      let declareArray = this.setDeclareByCustomerCodeList
      for (let i = 0; i < declareArray.length; i++) {
        if (data === declareArray[i].id) {
          this.declareDataForm.chineseName = declareArray[i].chineseName
          this.declareDataForm.englishName = declareArray[i].englishName
          this.declareDataForm.quantity = declareArray[i].quantity
          this.declareDataForm.unitNetWeight = declareArray[i].unitNetWeight
          this.declareDataForm.unitDeclarePrice = declareArray[i].unitDeclarePrice
          this.declareDataForm.brand = declareArray[i].brand
          this.declareDataForm.goodsBarcode = declareArray[i].goodsBarcode
          this.declareDataForm.sku = declareArray[i].sku
          this.declareDataForm.hsCode = declareArray[i].hsCode
          this.declareDataForm.productModel = declareArray[i].productModel
          this.declareDataForm.material = declareArray[i].material
          this.declareDataForm.purpose = declareArray[i].purpose
          this.declareDataForm.origin = declareArray[i].origin
          this.declareDataForm.pickingRemark = declareArray[i].pickingRemark
          this.declareDataForm.productUrl = declareArray[i].productUrl
          return
        }
      }
    }
  },
  components: {
    areaBox
  }
}
</script>
<style lang="scss" scoped>
.orderAdd{
  .el-divider--vertical{
    height: auto;
  }
  .consigneeArea{
    .title{
      color: #2378be;
    }
  }
  .shipperArea{
    .title{
      color: #9cc82b;
    }
  }
  .packDetail {
    margin-top: 50px;
  }
}

.borderLeft{
  margin-lelft:1px;
  border-left: 1px solid #dcdfe6
}
</style>
