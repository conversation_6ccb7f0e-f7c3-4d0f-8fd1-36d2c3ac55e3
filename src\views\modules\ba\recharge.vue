<template>
  <div class="aui-card--fill">
    <div class="mod-co__inorder flex_wrap" v-show="!viewVisible" ref="listPanel">
      <el-card class="search_box" shadow="never">
        <el-form ref="searchForm" class="form_no_margin" :model="dataForm" @keyup.enter.native="queryByParam()" label-width="120px">
          <el-row :gutter="10" type="flex">
            <el-col >
              <el-row :gutter="10">
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('baRecharge.bankAccountId')" prop="bankAccountId">
                    <el-select v-model="dataForm.bankAccountId" clearable :placeholder="$t('baRecharge.bankAccountId')">
                      <el-option v-for="item in getEnableCompanyBanAccount" :key="item.id" :label="item.name" :value="item.id"/>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('baRecharge.currency')" prop="currency">
                    <el-select v-model="dataForm.currency" clearable :placeholder="$t('baRecharge.currency')">
                      <el-option v-for="item in currencyList" :key="item.code" :label="item.name" :value="item.code"/>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('system.status')" prop="status">
                    <el-select v-model="dataForm.status" clearable :placeholder="$t('select')">
                      <el-option v-for="item in rechargeStatusList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"/>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('baRecharge.payerContacts')" prop="payerContacts">
                    <el-input v-model="dataForm.payerContacts" :placeholder="$t('baRecharge.payerContacts')" clearable />
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('baRecharge.payerPhone')" prop="payerPhone">
                    <el-input v-model="dataForm.payerPhone" :placeholder="$t('baRecharge.payerPhone')" clearable />
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('baRecharge.rechargeDate')">
                    <el-date-picker class="w-percent-100" v-model="rechargeDateArr" type="datetimerange"
                                    :start-placeholder="$t('datePicker.start')"
                                    :end-placeholder="$t('datePicker.end')"
                                    :default-time="['00:00:00', '23:59:59']">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <div class="search_box_btn no_more">
              <el-button type="primary" @click="queryByParam()" icon="el-icon-search">查询</el-button>
              <el-button @click.native="_resetForm('searchForm')" icon="el-icon-refresh-right">重置</el-button>
            </div>
          </el-row>
        </el-form>
      </el-card>

      <el-card class="flex_tab no_shadow" type="border-card" >
        <el-row class="optBtn_panel">
          <el-col :md="12" class='optBtn_leftFixed'>
            <el-button size="mini" type="primary" v-if="$hasPermission('ba:recharge:batchDelete')" plain :disabled="deleted" @click="deleteHandle()">{{ $t('delete') }}</el-button>
            <!--保留空格符-->
            <span style="margin-left: 10px;"> 可用金额: </span>
            <span v-for="(item, index) in currencyUsableList" :key="index" >
              <span style="margin-left: 10px; color: red; font-size: 15px;">{{ item.usableSum | numberFormat(3)}}</span>
              <span style="margin-left: 10px; color: red; font-size: 15px;" class="order-sum">{{item.currency }}</span>
            </span>
          </el-col>
          <el-col :md="12" class="text-right">
            <el-button size="mini" type="primary"  v-if="$hasPermission('ba:recharge:save')" plain @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
          </el-col>
        </el-row>
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <el-table v-loading="dataListLoading" @selection-change="controlButton" :data="dataList"  :max-height="tableHeight">
            <!-- 动态显示表格 -->
            <el-table-column type="selection" width='50' fixed="left"/>
            <el-table-column v-for="(item, index) in tableColumnsArr" :show-overflow-tooltip="item.prop === 'customerRemark' || item.prop === 'receivableRemark' || item.prop === 'invalidRemark'" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
              <template slot-scope="scope">
                <div>
                  <div v-if="item.prop === 'createDate'">
                    <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                      <span>{{formatterFn(scope,item.prop)}}</span>
                    </el-tooltip>
                  </div>
                  <div v-else>
                    <span class="text-overflow">
                      {{formatterFn(scope,item.prop)}}
                    </span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="150">
              <template slot="header" slot-scope="scope">
                <span>{{$t('handle')}}</span>
                <el-tooltip :content="$t('tableConfigTooltip')" placement="top">
                  <el-link style="margin-left:10px; font-size:16px" class="el-icon-setting" @click="settingCol(tableColumns)"/>
                </el-tooltip>
              </template>
              <template slot-scope="scope">
                <el-link :underline="false" v-if="$hasPermission('ba:recharge:view')" @click="viewHandle(scope.row.id)">{{ $t('view') }}</el-link>
                <el-link :underline="false" v-if="$hasPermission('ba:recharge:update') && scope.row.status === 10 && scope.row.creator === $store.state.user.id" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-link>
                <el-link :underline="false" v-if="$hasPermission('ba:recharge:delete') && scope.row.status === 10 && scope.row.creator === $store.state.user.id" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-link>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination background :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle" @current-change="pageCurrentChangeHandle">
        </el-pagination>
      </el-card>
    </div>
    <!-- 自定义列显示弹窗 -->
    <table-set ref="tableSet"/>
    <!-- 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList" @cancelAddOrUpdate="cancelAddOrUpdate"/>
    <!-- 详情页面 -->
    <viewDetail v-if="viewVisible" ref="viewDetail" @backView="backView"/>
  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import listPage from '@/mixins/listPage'
import { formatterType, gtmToLtm, timestampFormat, formatterCodeName, formatterName, formatterUserName, numberFormat } from '@/filters/filters'
import dictTypeMixins from '@/mixins/dictTypeMixins'
import api from '@/api'
import baseData from '@/api/baseData'
// table 自定义显示
import tableSet from '@/components/tableSet'
import AddOrUpdate from './recharge-add-or-update'
import ViewDetail from './recharge-view-detail'
export default {
  mixins: [listPage, mixinViewModule, dictTypeMixins],
  data () {
    return {
      tableColumns: [
        { type: '', width: '150', prop: 'id', label: this.$t('baRecharge.id'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'payerContacts', label: this.$t('baRecharge.payerContacts'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'payerPhone', label: this.$t('baRecharge.payerPhone'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'sumD', label: this.$t('baRecharge.sum'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'currency', label: this.$t('baRecharge.currency'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'serialNumber', label: this.$t('baRecharge.serialNumber'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'bankAccountId', label: this.$t('baRecharge.bankAccountId'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'status', label: this.$t('system.status'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'createDate', label: this.$t('baRecharge.rechargeDate'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'bookkeepingTime', label: this.$t('baRecharge.bookkeepingTime'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'bookkeeper', label: this.$t('baRecharge.bookkeeper'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'customerRemark', label: this.$t('baRecharge.customerRemark'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'receivableRemark', label: this.$t('baRecharge.receivableRemark'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'invalidRemark', label: this.$t('baRecharge.invalidRemark'), align: 'center', isShow: true, disabled: false }
      ],
      mixinViewModuleOptions: {
        getDataListURL: '/ba/recharge/page',
        getDataListIsPage: true,
        deleteURL: '/ba/recharge',
        deleteIsBatch: true
      },
      dataForm: {
        id: '',
        payerPhone: '',
        payerContacts: '',
        bankAccountId: '',
        currency: '',
        status: '',
        rechargeBeginDate: '',
        rechargeEndDate: ''
      },
      activeName: 'all',
      tableName: 'ba-recharge',
      rechargeDateArr: [],
      rechargeStatusList: [],
      deleted: false,
      getEnableCompanyBanAccount: [],
      getCompanyBanAccount: [],
      bankAccountList: [],
      currencyList: [],
      userList: [],
      currencyUsableList: []
    }
  },
  created () {
    this.getDict()
    this.getBaseData()
  },
  methods: {
    getDict () {
      this.getDictTypeList('rechargeType').then(res => {
        this.rechargeStatusList = res
      }) // 充值状态
    },
    getBaseData () {
      // 币种
      baseData(api.currencyList).then(res => {
        this.currencyList = res
      })
      // 币种
      baseData(api.userList).then(res => {
        this.userList = res
      })
      // 银行卡号
      baseData(api.getEnableCompanyBanAccount).then(res => {
        this.getEnableCompanyBanAccount = res
      })
      // 银行卡号
      baseData(api.getCompanyBanAccount).then(res => {
        this.getCompanyBanAccount = res
      })
      // 银行卡号
      baseData(api.bankAccountList).then(res => {
        this.bankAccountList = res
      })
      // 客户余额
      baseData(api.currencyUsableList).then(res => {
        this.currencyUsableList = res
      })
    },
    queryByParam () {
      this.getDataList()
      // 客户余额
      baseData(api.currencyUsableList).then(res => {
        this.currencyUsableList = res
      })
    },
    controlButton (selection) {
      this.dataListSelections = selection
      this.statusArray = selection.map(item => item.status)
      if (this.statusArray.indexOf(30) > -1 || this.statusArray.indexOf(20) > -1) {
        this.deleted = true
      } else {
        this.deleted = false
      }
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'createDate':
          value = timestampFormat(gtmToLtm(scope.row.createDate), 'YYYY-MM-DD')
          break
        case 'currency':
          value = formatterCodeName(scope.row.currency, this.currencyList)
          break
        case 'bankAccountId':
          value = formatterName(scope.row.bankAccountId, this.getCompanyBanAccount)
          break
        case 'bookkeeper':
          value = formatterUserName(scope.row.bookkeeper, this.userList)
          break
        case 'status':
          value = formatterType(scope.row.status, this.rechargeStatusList)
          break
        case 'sumD':
          value = numberFormat(scope.row.sumD, 2)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    // 自定义列显示
    settingCol (tableColumns) {
      this.$nextTick(() => {
        this.$refs['tableSet'].init(this.tableName, tableColumns)
      })
    }
  },
  watch: {
    rechargeDateArr: {
      handler (newVal, oldVal) {
        if (newVal !== null) {
          this.dataForm.rechargeBeginDate = newVal[0]
          this.dataForm.rechargeEndDate = newVal[1]
          return
        }
        this.dataForm.rechargeBeginDate = this.dataForm.rechargeEndDate = ''
      },
      deep: true
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat,
    formatterCodeName,
    formatterName,
    formatterUserName,
    numberFormat
  },
  computed: {
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    panelShow () {
      let ret = false
      if (this.addOrUpdateVisible) {
        ret = true
      } else if (this.viewVisible) {
        ret = true
      }
      return ret
    }
  },
  components: {
    AddOrUpdate,
    ViewDetail,
    tableSet
  }
}
</script>
<style lang="scss">
  .order-sum {
    font-size:8px;
    transform: translateY(2px) scale(0.8);
    display: inline-block;
  }
</style>
