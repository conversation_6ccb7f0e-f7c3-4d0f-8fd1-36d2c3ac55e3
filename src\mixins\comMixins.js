/**
 * Created by <PERSON><PERSON> on 2019/3/6.
 */
export default {
  data () {
    return {
      comMixins: {
        isGetVendorList: false,
        isGetOrderTypeList: false,
        isGetLogisticsChannelList: false,
        isGetEnableLogisticsChannelList: false,
        isGetAllLogisticsProviderListIgnoreCom: false,
        isGetOptUserList: false,
        isGetLocationList: false,
        isGetAreaCodeList: false,
        isGetCountryList: false,
        isGetCountryAreaList: false,
        isGetWarehouseList: false,
        isGetEnableWarehouseList: false,
        isGetStartWarehouseList: false,
        isGetPayAccountNameList: false, // 获取付款账户名列表
        isGetPayAccountNameEnableList: false, // 获取可用付款账户名列表
        isUsingGetFeeTypeList: false, // 获取可用费用项
        isGetAllUserList: false, // 获取所有用户
        isGetAllAdminUserList: false, // 获取所有用户
        isGetCurrencyList: false, // 获取币种列表
        isGetLogisticsChannelTypeList: false, // 获取物流渠道类型
        isGetCompanyList: false, // 获取所有公司
        isGetShippingCompanyList: false, // 获取所有船公司
        isGetSeaportList: false, // 获取所有港口列表
        isGetLogisticsProductByParamsList: false, // 根据自带参数获得物流产品列表
        isGetLogisticsChannelByParamsList: false, // 根据自带参数获得尾程派送渠道列表
        isGetSetShipperByCustomerCodeList: false, // 根据客户编码获取设置的发货人
        isGetSetDeclareByCustomerCodeList: false // 根据客户编码获取设置的报信息
      }
    }
  },
  created () {
    if (this.comMixins.isGetVendorList) {
      this.getVendorList()
    }
    if (this.comMixins.isGetOrderTypeList) {
      this.getOrderTypeList()
    }
    if (this.comMixins.isGetLogisticsChannelList) {
      this.getLogisticsChannelList()
    }
    if (this.comMixins.isGetEnableLogisticsChannelList) {
      this.getEnableLogisticsChannelList()
    }
    if (this.comMixins.isGetAllLogisticsProviderListIgnoreCom) {
      this.getAllLogisticsProviderListIgnoreCom()
    }
    if (this.comMixins.isGetOptUserList) {
      this.getOptUserList()
    }
    if (this.comMixins.isGetLocationList) {
      this.getLocationList()
    }
    if (this.comMixins.isGetAreaCodeList) {
      this.getAreaCodeList()
    }
    if (this.comMixins.isGetCountryList) {
      this.getCountryList()
    }
    if (this.comMixins.isGetCountryAreaList) {
      this.getCountryByPcode()
    }
    if (this.comMixins.isGetWarehouseList) {
      this.getWarehouseList()
    }
    if (this.comMixins.isGetEnableWarehouseList) {
      this.getEnableWarehouseList()
    }
    if (this.comMixins.isGetStartWarehouseList) {
      this.getStartWarehouseList()
    }
    if (this.comMixins.isGetPayAccountNameList) {
      this.getPayAccountNameList()
    }
    if (this.comMixins.isGetPayAccountNameEnableList) {
      this.getPayAccountNameEnableList()
    }
    if (this.comMixins.isUsingGetFeeTypeList) {
      this.getEnableFeeTypeList()
    }
    if (this.comMixins.isGetAllUserList) {
      this.getAllUserList()
    }
    if (this.comMixins.isGetAllAdminUserList) {
      this.getAllAdminUserList()
    }
    if (this.comMixins.isGetCurrencyList) {
      this.getCurrencyList()
    }
    if (this.comMixins.isGetLogisticsChannelTypeList) {
      this.getLogisticsChannelTypeList()
    }
    if (this.comMixins.isGetCompanyList) {
      this.getCompanyList()
    }
    if (this.comMixins.isGetShippingCompanyList) {
      this.getShippingCompanyList()
    }
    if (this.comMixins.isGetSeaportList) {
      this.getSeaportList()
    }
    if (this.comMixins.isGetLogisticsProductByParamsList) {
      this.getLogisticsProductByParamsList()
    }
    if (this.comMixins.isGetLogisticsChannelByParamsList) {
      this.getLogisticsChannelByParamsList()
    }
    if (this.comMixins.isGetSetShipperByCustomerCodeList) {
      this.getSetShipperByCustomerCodeList()
    }
    if (this.comMixins.isGetSetDeclareByCustomerCodeList) {
      this.getSetDeclareByCustomerCodeList()
    }
  },
  methods: {
    // 获取启用的货主列表
    getVendorList () {
      return this.$http.get('/bd/vendor/getVendors').then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.vendorList = res.data
      }).catch(() => { })
    },
    // 从数据字典获取出库类型列表
    getOrderTypeList () {
      return this.$http.get('/dict/dictList', { params: { dictType: 'outType' } }).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.orderTypeList = res.data
      }).catch(() => { })
    },
    // 获取物流产品列表
    getLogisticsChannelList () {
      return this.$http.get('/bd/logisticschannel/list', { params: { type: '10' } }).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.logisticsChannelList = res.data
      }).catch(() => { })
    },
    // 获取可用的物流渠道列表
    getEnableLogisticsChannelList () {
      return this.$http.get('/bd/logisticschannel/enableList').then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.enableLogisticsChannelList = res.data
      }).catch(() => { })
    },
    getOptUserList (callback) {
      return this.$http.get('/bd/customer/list').then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        callback && callback.call(this, res)
        this.optUserList = res.data
      }).catch(() => { })
    },
    // 获取库位信息集合
    getLocationList (query) {
      if (query !== '') {
        this.loading = true
        setTimeout(() => {
          this.loading = false
          this.$http.get(`/bd/warehouselocation/listByCode?locationCode=` + query).then(({ data: res }) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.locationList = res.data
          }).catch(() => { })
        }, 200)
      } else {
        this.locationList = []
      }
    },
    // 获取收货库位信息集合
    getReceiveLocationList (query) {
      if (query !== '') {
        this.loading = true
        setTimeout(() => {
          this.loading = false
          this.$http.get(`/bd/warehouselocation/ListByCodeAndType?type=0&locationCode=` + query).then(({ data: res }) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.locationList = res.data
          }).catch(() => { })
        }, 200)
      } else {
        this.locationList = []
      }
    },
    // 获取物流渠道类型
    getLogisticsChannelTypeList () {
      return this.$http.get('/bd/logisticschanneltype/list').then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.logisticsChannelTypeList = res.data
      }).catch(() => { })
    },
    // 获取币种列表
    getCurrencyList () {
      return this.$http.get('/bd/currency/list').then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.currencyList = res.data
      }).catch(() => { })
    },
    // 获取库区列表
    getAreaCodeList () {
      this.$http.get(`/bd/warehousearea/getList`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.areaCodeList = res.data
      }).catch(() => { })
    },
    // 获取国家列表
    getCountryList () {
      return this.$http.get('/bd/region/listCountry').then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.countryList = res.data
      }).catch(() => { })
    },
    // 根据二字简码获取国家列表
    getCountryListByCodeOrName (query) {
      // console.log(query)
      if (query !== '') {
        this.loading = true
        setTimeout(() => {
          this.loading = false
          this.$http.get(`/bd/region/listByCodeOrName?codeOrName=` + query).then(({ data: res }) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.newCountryList = res.data
          }).catch(() => { })
        }, 200)
      } else {
        this.newCountryList = []
      }
    },
    // 根据国家父节code获取子节点值
    getCountryByPcode (query) {
      if (query !== '') {
        this.loading = true
        setTimeout(() => {
          this.loading = false
          this.$http.get(`/bd/region/listCountryByPcode?pcode=` + query).then(({ data: res }) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.countryAreaList = res.data
          }).catch(() => { })
        }, 200)
      } else {
        this.countryAreaList = []
      }
    },
    // 获取启用的客户列表
    getCustomerList () {
      return this.$http.get('/bd/customer/findUseList').then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.customerList = res.data
      }).catch(() => { })
    },
    // 获取仓库列表
    getWarehouseList () {
      return this.$http.get('/bd/warehouse/list').then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.warehouseList = res.data
      }).catch(() => { })
    },
    // 获取可用的仓库列表
    getEnableWarehouseList () {
      return this.$http.get('/bd/warehouse/enableList').then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.enableWarehouseList = res.data
      }).catch(() => { })
    },
    // 获取仓库列表(启用)
    getStartWarehouseList () {
      return this.$http.get(
        '/bd/warehouse/list',
        {
          params: {
            status: 1
          }
        }).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.startWarehouseList = res.data
      }).catch(() => { })
    },
    // 获取供应商银行账号可用列表
    getReceivableAccountNameEnableList () {
      this.$http.get(`/bd/bankaccount/enableListByType`, { params: { accountType: '0' } }).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.receivableAccountNameEnableList = res.data
      }).catch(() => { })
    },
    // 获取公司银行账号可用列表
    getPayAccountNameEnableList () {
      this.$http.get(`/bd/bankaccount/enableListByType`, { params: { accountType: '1' } }).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.payAccountNameEnableList = res.data
      }).catch(() => { })
    },
    // 获取供应商银行账号所有列表
    getReceivableAccountNameList () {
      this.$http.get(`/bd/bankaccount/listByType`, { params: { accountType: '0' } }).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.receivableAccountNameList = res.data
      }).catch(() => { })
    },
    getAllUserList (callback) {
      return this.$http.get('/user/showList').then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        callback && callback.call(this, res)
        this.allUserList = res.data
      }).catch(() => { })
    },
    getAllAdminUserList (callback) {
      return this.$http.get('/user/showList').then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        callback && callback.call(this, res)
        this.allAdminUserList = res.data
      }).catch(() => { })
    },
    // 获取公司银行账号所有列表
    getPayAccountNameList () {
      this.$http.get(`/bd/bankaccount/listByType`, { params: { accountType: '1' } }).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.payAccountNameList = res.data
      }).catch(() => { })
    },
    // 获取费用项
    getEnableFeeTypeList () {
      return this.$http.get('/bd/feetype/enableList').then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.usingFeeTypeList = res.data
      }).catch(() => { })
    },
    // 获取所有物流供应商列表（不过滤公司）
    getAllLogisticsProviderListIgnoreCom () {
      return this.$http.get('/bd/provider/listIgnoreCom').then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.logisticsProviderList = res.data
      }).catch(() => { })
    },
    // 获取公司列表
    getCompanyList () {
      return this.$http.get('/company/list').then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.companyList = res.data
      }).catch(() => { })
    },
    // 获取船公司列表
    getShippingCompanyList () {
      return this.$http.get('/bd/shippingcompany/listShippingCompany').then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.shippingCompanyList = res.data
      }).catch(() => { })
    },
    // 根据二字简码code/或名称获取船公司列表
    getShippingCompanyListByCodeOrName (query) {
      if (!query) {
        this.newShippingCompanyList = []
      } else {
        this.loading = true
        setTimeout(() => {
          this.loading = false
          this.$http('/bd/shippingcompany/listShippingCompanyByCodeOrName?codeOrName=' + query).then(({ data: res }) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.newShippingCompanyList = res.data
          }).catch(() => {})
        }, 200)
      }
    },
    // 获取港口列表
    getSeaportList () {
      return this.$http('/bd/seaport/listSeaport').then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.seaportList = res.data
      }).catch(() => {})
    },
    // 根据code或name获取起始港口列表
    getDeparturePortListByCodeOrName (query) {
      if (!query) {
        this.newSeaportList = []
      } else {
        this.loading = true
        setTimeout(() => {
          this.loading = false
          this.$http('/bd/seaport/listSeaportByCodeOrName?codeOrName=' + query).then(({ data: res }) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.departureSeaportList = res.data
          }).catch(() => {})
        }, 200)
      }
    },
    // 根据code或name获取目的港口列表
    getArrivalSeaportListByCodeOrName (query) {
      if (!query) {
        this.newSeaportList = []
      } else {
        this.loading = true
        setTimeout(() => {
          this.loading = false
          this.$http('/bd/seaport/listSeaportByCodeOrName?codeOrName=' + query).then(({ data: res }) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.arrivalSeaportList = res.data
          }).catch(() => {})
        }, 200)
      }
    },
    // 自带参数物流产品列表
    getLogisticsProductByParamsList () {
      return this.$http('/bd/logisticsproduct/listByParams').then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.logisticsProductByParamsList = res.data
      }).catch(() => {})
    },
    // 自带参数尾程派送渠道列表
    getLogisticsChannelByParamsList () {
      return this.$http('/bd/logisticschannel/listByParams').then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.logisticsChannelByParamsList = res.data
      }).catch(() => {})
    },
    getSetShipperByCustomerCodeList () {
      return this.$http('/co/setcustomershipper/listByCustomerId/' + this.$store.state.user.customerId).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.setShipperByCustomerCodeList = res.data
      }).catch(() => {})
    },
    getSetDeclareByCustomerCodeList () {
      return this.$http('/co/setcustomerdeclare/listByCustomerId/' + this.$store.state.user.customerId).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.setDeclareByCustomerCodeList = res.data
      }).catch(() => {})
    }
  }
}
