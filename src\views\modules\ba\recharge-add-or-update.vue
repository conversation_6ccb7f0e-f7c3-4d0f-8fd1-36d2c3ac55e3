<template>
  <el-dialog :visible.sync="visible" width="780px" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
      <el-container>
        <el-main>
          <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" label-width="120px">
                <el-form-item :label="$t('baRecharge.payerContacts')" prop="payerContacts">
                  <el-input v-model="dataForm.payerContacts" :placeholder="$t('baRecharge.payerContacts')" :maxlength="64" show-word-limit/>
                </el-form-item>
                <el-form-item :label="$t('baRecharge.payerPhone')" prop="payerPhone">
                  <el-input v-model="dataForm.payerPhone" :placeholder="$t('baRecharge.payerPhone')" :maxlength="13" show-word-limit/>
                </el-form-item>
                <el-form-item :label="$t('baRecharge.currency')" prop="currency">
                  <el-select v-model="dataForm.currency" :placeholder="$t('baRecharge.currency')" filterable clearable>
                    <el-option v-for="item in currencyList" :key="item.code" :label="item.name" :value="item.code"/>
                  </el-select>
                </el-form-item>
                <el-form-item :label="$t('baRecharge.sum')" prop="sumD">
                  <el-input v-model="dataForm.sumD" :placeholder="$t('baRecharge.sum')" :maxlength="15" show-word-limit/>
                </el-form-item>
                <el-form-item :label="$t('baRecharge.serialNumber')" prop="serialNumber">
                  <el-input v-model="dataForm.serialNumber" :placeholder="$t('baRecharge.serialNumber')" :maxlength="36" show-word-limit/>
                </el-form-item>
                <el-form-item :label="$t('baRecharge.bankAccountId')" prop="bankAccountId">
                  <el-select v-model="dataForm.bankAccountId" :placeholder="$t('baRecharge.bankAccountId')" filterable clearable @change="setAccountName()">
                    <el-option v-for="item in getEnableCompanyBanAccount" :key="item.id" :label="item.name" :value="item.id"/>
                  </el-select>
                </el-form-item>
                <el-form-item :label="$t('baRecharge.bankName')" prop="bankName">
                  <el-input :disabled="true" v-model="dataForm.bankName" :placeholder="$t('baRecharge.bankName')"/>
                </el-form-item>
                <el-form-item :label="$t('baRecharge.accountNumber')" prop="accountNumber">
                  <el-input :disabled="true" v-model="dataForm.accountNumber" :placeholder="$t('baRecharge.accountNumber')"/>
                </el-form-item>
                <el-form-item :label="$t('baRecharge.customerRemark')" prop="customerRemark">
                  <el-input type="textarea" v-model="dataForm.customerRemark" :placeholder="$t('baRecharge.customerRemark')" :maxlength="512" show-word-limit />
                </el-form-item>
          </el-form>
        </el-main>
        <el-aside  style="width:328px; margin-top:20px">
          <el-upload
            :multiple="false"
            class="avatar-uploader"
            :auto-upload="false"
            action=""
            :show-file-list="false"
            :on-change="handleAvatarSuccess"
            :on-error="errorHandle"
            accept="image/jpg,image/png,image/jpeg">
            <img v-if="dataForm.attachmentUrl" :src="dataForm.attachmentUrl" class="avatar" fit="cover" lazy>
            <i v-else class="el-icon-plus avatar-uploader-icon" style="line-height:400px;"/>
          </el-upload>
        </el-aside>
      </el-container>
      <template slot="footer">
        <el-button @click="cancelFn">{{ $t('cancel') }}</el-button>
        <el-button type="primary" :loading="isSave" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
      </template>
  </el-dialog>
</template>

<script>
import { formatterType } from '@/filters/filters'
import dictTypeMixins from '@/mixins/dictTypeMixins'
import debounce from 'lodash/debounce'
import api from '@/api'
import baseData from '@/api/baseData'
import { isSignDecimal3, isMobile, letterAndNumber } from '@/utils/validate'
export default {
  mixins: [dictTypeMixins],
  data () {
    return {
      dataForm: {
        id: '',
        payerContacts: '',
        payerPhone: '',
        currency: '',
        sumD: '',
        serialNumber: '',
        bankAccountId: '',
        attachmentUrl: '',
        customerRemark: '',
        bankName: '',
        accountNumber: ''
      },
      currencyList: [],
      getEnableCompanyBanAccount: [],
      bankNameList: [],
      visible: false,
      isSave: false,
      file: []
    }
  },
  computed: {
    dataRule () {
      const sumDSign3Validate = (rule, value, callback) => {
        if (!isSignDecimal3(value)) {
          return callback(new Error(this.$t('validate.decimal3', { 'number': 3 })))
        }
        callback()
      }
      const validateMobile = (rule, value, callback) => {
        if (!isMobile(value)) {
          return callback(new Error(this.$t('validate.format', { 'attr': this.$t('baRecharge.payerPhone') })))
        }
        callback()
      }
      const letterAndNumberValidator = (rule, value, callback) => {
        if (!letterAndNumber(value)) {
          return callback(new Error(this.$t('validate.letterAndNumber')))
        }
        callback()
      }
      return {
        payerContacts: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        payerPhone: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: validateMobile, trigger: 'blur' }
        ],
        currency: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        sumD: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: sumDSign3Validate, trigger: 'blur' }
        ],
        serialNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: letterAndNumberValidator, trigger: 'blur' }
        ],
        bankAccountId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    // this.getDict()
    // this.getBaseData()
  },
  methods: {
    async getDict () {
      this.bankNameList = await this.getDictTypeList('BANK_TYPE')
    },
    async getBaseData () {
      // 币种
      this.currencyList = await baseData(api.currencyList).catch((res) => {
        const msg = res.msg ? res.msg : res
        return this.$message.error(msg)
      })
      // 银行卡号
      this.getEnableCompanyBanAccount = await baseData(api.getEnableCompanyBanAccount).catch((res) => {
        const msg = res.msg ? res.msg : res
        return this.$message.error(msg)
      })
    },
    init () {
      this.visible = true
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        this.$refs['dataForm'].resetFields()
        Promise.all([this.getDict(), this.getBaseData()]).then((res) => {
          if (this.dataForm.id) {
            this.getInfo()
          }
        })
      })
    },
    // 上传失败
    errorHandle (error) {
      console.log('error', error)
      let myError = error.toString()
      myError = myError.replace('Error: ', '')
      console.log('myError')
      return this.$message({
        dangerouslyUseHTMLString: true,
        message: myError || '发生错误',
        type: 'error',
        duration: 2500
      })
    },
    handleAvatarSuccess (file, fileList) {
      this.file = file.raw
      this.dataForm.attachmentUrl = URL.createObjectURL(file.raw)
    },
    setAccountName () {
      this.dataForm.bankName = ''
      this.dataForm.accountNumber = ''
      if (this.getEnableCompanyBanAccount !== null && this.getEnableCompanyBanAccount !== undefined) {
        this.getEnableCompanyBanAccount.forEach(payAccount => {
          if (this.dataForm.bankAccountId === payAccount.id) {
            let bankCode = payAccount.bankCode
            this.dataForm.bankName = formatterType(bankCode, this.bankNameList)
            this.dataForm.accountNumber = payAccount.accountNumber
            return true
          }
        })
      }
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/ba/recharge/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
        this.setAccountName()
        this.setAccountName()
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.dataForm.status = 10
        let formData = new FormData()
        formData.append('file', this.file)
        formData.append('payerContacts', this.dataForm.payerContacts)
        formData.append('payerPhone', this.dataForm.payerPhone)
        formData.append('currency', this.dataForm.currency)
        formData.append('sumD', this.dataForm.sumD)
        formData.append('serialNumber', this.dataForm.serialNumber)
        formData.append('bankAccountId', this.dataForm.bankAccountId)
        formData.append('memo', this.dataForm.memo)
        formData.append('customerRemark', this.dataForm.customerRemark)
        if (this.dataForm.id) {
          formData.append('id', this.dataForm.id)
        }
        if (this.file.length === 0 && !this.dataForm.id) {
          return this.$message({
            message: this.$t('prompt.uploadEmpty'),
            type: 'warning',
            duration: 1000
          })
        }
        if (this.isSave) {
          return false
        }
        this.isSave = true
        this.$http[!this.dataForm.id ? 'post' : 'put']('/ba/recharge', formData).then(({ data: res }) => {
          this.isSave = false
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {
          this.isSave = false
        })
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    // 取消
    cancelFn () {
      this.visible = false
      this.$emit('cancelAddOrUpdate')
    }
  },
  filters: {
    formatterType
  }
}
</script>
<style lang="scss">
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 325px;
    height: 440px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 325px;
    height: 440px;
    display: block;
    overflow: hidden;
  }
  .mod-sys__user {
    .dept-list {
      .el-input__inner,
      .el-input__suffix {
        cursor: pointer;
      }
    }
    .role-list {
      .el-select {
        width: 100%;
      }
    }
  }
</style>
