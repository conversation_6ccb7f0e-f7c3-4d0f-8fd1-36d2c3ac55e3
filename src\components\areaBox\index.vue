<template>
  <div class="area-box" :style="this.top ? {'margin-top':  setTop} : ''" >
    <h3 :class="title? 'title':''">
      <span  :class="className? className:''" v-html="title"></span>
      <i class="el-icon" v-show="displayPullBack" :class="searchBoxShow ? 'el-icon-arrow-down' : 'el-icon-arrow-up'" style="margin-left: 10px" @click="searchBoxShowFn">
      </i>
    </h3>
    <div v-show="searchBoxShow">
      <slot ></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    top: {
      type: String,
      default: ''
    },
    className: {
      type: String,
      default: null
    },
    displayPullBack: false
  },
  data () {
    return {
      searchBoxShow: true
    }
  },
  computed: {
    setTop () {
      let ret = 25
      ret = Number(this.top) + ret + 'px'
      return ret
    }
  },
  methods: {
    searchBoxShowFn() {
      this.searchBoxShow = !this.searchBoxShow
    }
  }
}
</script>

<style  lang="scss" scoped>
.area-box{
  position: relative;
  margin-top:25px;
  padding:20px 10px 10px;
  border:1px solid #DCDFE6;
  .title{
    position: absolute;
    top:-20px;
    left: 20px;
    display: inline-block;
    margin:0;
    padding: 10px;
    background-color: #fff;
    font-size: 14px;
    color: #606266;
  }
  i.el-icon {
    cursor: pointer
  }
}
.search_box {
  position: relative;
  padding-left: 15px;
  margin-left: 10px;
  min-width: 230px;
  font-size: smaller;
  &:before {
    content: '';
    border-left: 1px solid #eee;
    position: absolute;
    top: 0;
    left: -1px;
    bottom: 15px;
  }
  &.no_more {
    min-width: 190px;
  }
}
</style>
