const DateUtil = {}

DateUtil.gmtToLt = function (gmt) {
  const offset = new Date().getTimezoneOffset()
  let t = gmt
  if (!(gmt instanceof Date)) {
    t = new Date(gmt)
  }
  return new Date(t.getTime() - offset * 60000)
}

DateUtil.padLeftZero = function (str) {
  return ('00' + str).substr(str.length)
}

DateUtil.format = function (date, fmt) {
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
  }
  let o = {
    'M+': date.getMonth() + 1,
    'd+': date.getDate(),
    'h+': date.getHours(),
    'm+': date.getMinutes(),
    's+': date.getSeconds()
  }
  for (let k in o) {
    let str = o[k] + ''
    if (new RegExp(`(${k})`).test(fmt)) {
      fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? str : this.padLeftZero(str))
    }
  }
  return fmt
}

DateUtil.dateFormat = function (date) {
  return DateUtil.format(date, 'yyyy-MM-dd hh:mm:ss')
}

DateUtil.gmtToLtFormat = function (date) {
  return DateUtil.format(DateUtil.gmtToLt(date), 'yyyy-MM-dd hh:mm:ss')
}

DateUtil.timestampToDate = function (time) {
  if (time && time !== '0') {
    var date = new Date(parseInt(time))
    var Y = date.getFullYear() + '-'
    var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
    var D = date.getDate() < 10 ? '0' + date.getDate() + ' ' : date.getDate() + ' '
    var h = date.getHours() < 10 ? '0' + date.getHours() + ':' : date.getHours() + ':'
    var m = date.getMinutes() < 10 ? '0' + date.getMinutes() + ':' : date.getMinutes() + ':'
    var s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
    return Y + M + D + h + m + s
  }
}

DateUtil.getTimezoneOffsetHour = function () {
  return new Date().getTimezoneOffset() / (-60)
}

DateUtil.getDifferHour = function (nowDate, oldDate) {
  var d1 = new Date(nowDate)
  var d2 = new Date(oldDate)
  return Number(parseInt(d1 - d2) / 1000 / 60 / 60).toFixed(1)
}

export default DateUtil
