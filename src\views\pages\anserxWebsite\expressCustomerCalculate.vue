<template>
  <div class="aui-card--fill">
    <div class="mod-co__inorder flex_wrap" ref="listPanel">
      <el-card class="search_box" shadow="never">
        <el-form ref="dataForm"  :model="dataForm" :rules="dataRule" label-width="120px">
          <el-row :gutter="10" type="flex">
            <el-col >
              <el-row :gutter="10">
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('psCalculateExpense.logisticsProduct')" prop="logisticsChannel">
                    <el-select  filterable v-model="dataForm.logisticsChannel"
                                :placeholder="$t('psCalculateExpense.logisticsProduct')" clearable>
                      <el-option v-for="(item, index) in logisticsProductList" :key="index" :label="item.name"
                                 :value="item.code"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('psCalculateExpense.productType')" prop="productType">
                    <el-select  filterable v-model="dataForm.productType"
                                :placeholder="$t('psCalculateExpense.productType')" clearable>
                      <el-option v-for="(item, index) in parcelTypeList" :key="index" :label="item.dictName"
                                 :value="item.dictValue"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('psCalculateExpense.country')" prop="country">
                    <el-select  filterable v-model="dataForm.country"
                                :placeholder="$t('psCalculateExpense.country')" clearable>
                      <el-option v-for="(item, index) in countryList" :key="index" :label="`${item.name} ${item.code}`"
                                 :value="item.code"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('psCalculateExpense.postcode')" prop="postcode">
                    <el-input v-model="dataForm.postcode" :placeholder="$t('psCalculateExpense.postcode')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('psCalculateExpense.weightD')" prop="weightD">
                    <el-input v-model="dataForm.weight" :placeholder="$t('psCalculateExpense.weight')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('psCalculateExpense.lengthD')" prop="lengthD">
                    <el-input v-model="dataForm.length" :placeholder="$t('psCalculateExpense.length')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('psCalculateExpense.widthD')" prop="widthD">
                    <el-input v-model="dataForm.widthD" :placeholder="$t('psCalculateExpense.width')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('psCalculateExpense.height')" prop="heightD">
                    <el-input v-model="dataForm.heightD" :placeholder="$t('psCalculateExpense.height')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('expressCustomerCalculate.deliveryWarehouse')" prop="warehouseId">
                    <el-select v-model="dataForm.warehouseId" clearable filterable :placeholder="$t('expressCustomerCalculate.deliveryWarehouse')" @change="dataFormCompanyIdChange">
                      <el-option v-for="item in warehouseList" :key="item.id" :label="item.warehouseName" :value="item.id"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <div class="search_box_btn no_more">
              <el-button type="primary" :loading="loadingFlag" @click="getTransSum()" icon="el-icon-search">计算</el-button>
            </div>
          </el-row>
        </el-form>
      </el-card>
      <el-card class="flex_tab no_shadow" type="border-card" >
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <el-table v-loading="dataListLoading" :data="dataList" border :max-height="tableHeight">
            <el-table-column type="expand" fixed="left">
              <template slot-scope="scope">
                <el-table :data="scope.row.resultDTOList" border :max-height="tableHeight" >
                  <!-- 动态显示表格 -->
                  <el-table-column v-for="(item, index) in tableItemColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
                    <template slot-scope="scope">
                      <div>
                        {{formatterFn(scope,item.prop)}}
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </el-table-column>
            <!-- 动态显示表格 -->
            <el-table-column v-for="(item, index) in tableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
              <template slot-scope="scope">
                <div>
                    {{formatterFn(scope,item.prop)}}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
<!--        <el-pagination background :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle" @current-change="pageCurrentChangeHandle">-->
<!--        </el-pagination>-->
      </el-card>
    </div>
    <!-- 自定义列显示弹窗 -->
  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import listPage from '@/mixins/listPage'
import { formatterType, gtmToLtm, timestampFormat, formatterName, formatterCodeName } from '@/filters/filters'
import dictTypeMixins from '@/mixins/dictTypeMixins'
import baseApi from '@/api'
import baseData from '@/api/baseData'
import { isLength32 } from '@/utils/validate'
export default {
  mixins: [listPage, mixinViewModule, dictTypeMixins],
  data () {
    return {
      tableColumns: [
        { type: '', width: '150', prop: 'logisticsChannelId', label: this.$t('psCalculateExpense.logisticsProduct'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'aging', label: this.$t('psCalculateExpense.aging'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'sum', label: this.$t('psCalculateExpense.sum'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'balanceWeight', label: this.$t('psCalculateExpense.balanceWeight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'sysSum', label: this.$t('psCalculateExpense.sysSum'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'sysBalanceWeight', label: this.$t('psCalculateExpense.sysBalanceWeight'), align: 'center', isShow: true, disabled: false }
      ],
      tableItemColumns: [
        { type: '', width: '100', prop: 'feeType', label: this.$t('psCalculateExpense.feeType'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'sum', label: this.$t('psCalculateExpense.sum'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'balanceWeight', label: this.$t('psCalculateExpense.balanceWeight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'sysSum', label: this.$t('psCalculateExpense.sysSum'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'sysBalanceWeight', label: this.$t('psCalculateExpense.sysBalanceWeight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'formula', label: this.$t('psCalculateExpense.formula'), align: 'center', isShow: true, disabled: false }
      ],
      mixinViewModuleOptions: {
        activatedIsNeed: false
      },
      dataForm: {
        id: '',
        objectType: 0,
        objectId: 0,
        productType: 10,
        payable: 0,
        client: 1,
        psCalculateExpenseDetailList: [],
        companyId: '',
        warehouseId: ''
      },
      changeStatusForm: {
        ids: '',
        status: ''
      },
      parcelTypeList: [],
      countryList: [],
      weightUnitList: [],
      currencyList: [],
      logisticsProductList: [],
      baseQuotationAllList: [],
      usingFeeTypeList: [],
      logisticsProductTableList: [],
      warehouseList: [],
      loadingFlag: false
    }
  },
  created () {
    this.getDict()
    this.getBaseData()
    this.dataForm.objectId = this.$store.state.user.customerId
  },
  mounted () {
    // 根据请求url--接收来自官网的运费查询跳转
    this.acceptCustomerCal()
  },
  methods: {
    async getDict () {
      this.weightUnitList = await this.getDictTypeListByUrl('weightUnit', '/cs/anserxWebsite/dictList')
      this.parcelTypeList = await this.getDictTypeListByUrl('OrderParcelType', '/cs/anserxWebsite/dictList')
    },
    // 同步通过接口获取基础数据
    getBaseData () {
      baseData(baseApi.anserxWebsiteCurrencyList).then((res) => {
        this.currencyList = res
      })
      baseData(baseApi.anserxWebsiteEnableLogisticsProductByCurrent).then((res) => {
        this.logisticsProductList = res
      })
      baseData(baseApi.anserxWebsiteCountryList, { type: 0 }).then((res) => {
        this.countryList = res
      })
      baseData(baseApi.anserxWebsiteUsingFeeTypeList).then((res) => {
        this.usingFeeTypeList = res
      })
      baseData(baseApi.anserxWebsiteLogisticsProductByParamsList).then((res) => {
        this.logisticsProductTableList = res
      })
      baseData(baseApi.anserxWebsiteWarehouseInfoNotIsolationList).then((res) => {
        this.warehouseList = res
      }).catch(() => {})
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'logisticsChannelId':
          value = formatterName(scope.row.logisticsChannelId, this.logisticsProductTableList)
          break
        case 'feeType':
          value = formatterName(scope.row.feeType, this.usingFeeTypeList)
          break
        case 'sum':
          value = `${scope.row.sum} (${formatterCodeName(scope.row.currency, this.currencyList)})`
          break
        case 'balanceWeight':
          value = `${scope.row.balanceWeight} (${formatterType(scope.row.weightUnit, this.weightUnitList)})`
          break
        case 'sysSum':
          value = `${scope.row.sysSum} (${formatterCodeName(scope.row.sysCurrency, this.currencyList)})`
          break
        case 'sysBalanceWeight':
          value = `${scope.row.sysBalanceWeight} (${formatterType(scope.row.sysWeightUnit, this.weightUnitList)})`
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    getTransSum () {
      this.$refs['dataForm'].validate((valid) => {
        this.loadingFlag = true
        if (!valid) {
          this.loadingFlag = false
          return false
        }
        this.dataForm.psCalculateExpenseDetailList[0] = {
          lengthD: this.dataForm.lengthD,
          widthD: this.dataForm.widthD,
          heightD: this.dataForm.heightD,
          weightD: this.dataForm.weightD
        }
        this.$http['post']('/cs/anserxWebsite/expresscommonsquotation/getTransSum', this.dataForm).then(({ data: res }) => {
          this.loadingFlag = false
          if (res.code !== 0) {
            this.$alert(res.msg, '错误信息', {
              confirmButtonText: '确定',
              dangerouslyUseHTMLString: true
            })
            this.dataList = []
            return false
          } else {
            this.dataList = res.data
          }
        }).catch(() => {})
      })
    },
    dataFormCompanyIdChange (data) {
      this.dataForm.companyId = this.warehouseList.filter(item => item.id === data)[0].companyId
    },
    // 根据请求url--接收来自官网的运费查询跳转
    acceptCustomerCal () {
      let requestUrl = window.location.href

      let requestUrlArr = requestUrl.split('?')
      if (requestUrlArr.length !== 2) {
        return
      }
      let paramsArr = requestUrlArr[1].split('&')
      if (paramsArr.length !== 5) {
        return
      }
      let weightIndex = paramsArr[0].indexOf('weight')
      let countryIndex = paramsArr[1].indexOf('country')
      let warehouseIdIndex = paramsArr[2].indexOf('warehouseId')
      let companyIdIndex = paramsArr[3].indexOf('companyId')
      let productTypeIndex = paramsArr[4].indexOf('productType')
      if (weightIndex === -1 || countryIndex === -1 || warehouseIdIndex === -1 || productTypeIndex === -1 || companyIdIndex === -1) {
        return
      }
      let weight = paramsArr[0].substring(weightIndex + 'weight'.length + 1)
      let country = paramsArr[1].substring(countryIndex + 'country'.length + 1)
      let warehouseId = paramsArr[2].substring(warehouseIdIndex + 'warehouseId'.length + 1)
      let companyId = paramsArr[3].substring(companyIdIndex + 'companyId'.length + 1)
      let productType = paramsArr[4].substring(productTypeIndex + 'productType'.length + 1)

      this.dataForm = {
        ...this.dataForm,
        'weight': weight,
        'country': country,
        'warehouseId': warehouseId,
        'companyId': companyId,
        'productType': productType
      }
      this.$nextTick(() => {
        this.getTransSum()
      })
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat,
    formatterName,
    formatterCodeName
  },
  computed: {
    dataRule () {
      const postCodeValidate = (rule, value, callback) => {
        if (value && !isLength32(value)) {
          return callback(new Error(this.$t('validate.postCodeValidate', { 'attr': this.$t('psCalculateExpense.postcode') })))
        }
        callback()
      }
      const lengthValidate = (rule, value, callback) => {
        let pattrn = /^[0-9]+(.[0-9]{0,1})?$/
        if (value && !(pattrn.test(value) && value > 0)) {
          return callback(new Error(this.$t('validate.lengthValidate', { 'attr': this.$t('psCalculateExpense.length') })))
        }
        if (value > 99999) {
          return callback(new Error(this.$t('validate.max', { 'attr': this.$t('validate.max'), 'max': '99999' })))
        }
        callback()
      }
      const widthValidate = (rule, value, callback) => {
        let pattrn = /^[0-9]+(.[0-9]{0,1})?$/
        if (value && !(pattrn.test(value) && value > 0)) {
          return callback(new Error(this.$t('validate.lengthValidate', { 'attr': this.$t('psCalculateExpense.width') })))
        }
        if (value > 99999) {
          return callback(new Error(this.$t('validate.max', { 'attr': this.$t('validate.max'), 'max': '99999' })))
        }
        callback()
      }
      const heightValidate = (rule, value, callback) => {
        let pattrn = /^[0-9]+(.[0-9]{0,1})?$/
        if (value && !(pattrn.test(value) && value > 0)) {
          return callback(new Error(this.$t('validate.lengthValidate', { 'attr': this.$t('psCalculateExpense.high') })))
        }
        if (value > 99999) {
          return callback(new Error(this.$t('validate.max', { 'attr': this.$t('validate.max'), 'max': '99999' })))
        }
        callback()
      }
      const weightValidate = (rule, value, callback) => {
        let pattrn = /^[0-9]+(.[0-9]{0,3})?$/
        if (value && !(pattrn.test(value) && value > 0)) {
          return callback(new Error(this.$t('validate.weight', { 'attr': this.$t('psCalculateExpense.weight') })))
        }
        if (value > 99999) {
          return callback(new Error(this.$t('validate.max', { 'attr': this.$t('validate.max'), 'max': '99999' })))
        }
        callback()
      }
      return {
        productType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        country: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        postcode: [
          { validator: postCodeValidate, trigger: 'blur' }
        ],
        lengthD: [
          { validator: lengthValidate, trigger: 'blur' }
        ],
        widthD: [
          { validator: widthValidate, trigger: 'blur' }
        ],
        heightD: [
          { validator: heightValidate, trigger: 'blur' }
        ],
        weightD: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: weightValidate, trigger: 'blur' }
        ],
        warehouseId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    },
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    tableItemColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableItemColumns).map((key) => this.tableItemColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    }
  },
  components: {
  }
}
</script>
