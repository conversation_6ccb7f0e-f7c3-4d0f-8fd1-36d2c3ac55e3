<template>
  <div class="add-body panel_body">
    <div class="panel-hd">
      <span v-text="$t('detail')"></span>
    </div>
    <div class="detail-desc">
      <el-form ref="form" label-width="80px">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item :label="$t('coOrderDeclare.orderId')">
              <span v-text="dataForm.orderId"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderDeclare.chineseName')">
              <span v-text="dataForm.chineseName"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderDeclare.englishName')">
              <span v-text="dataForm.englishName"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderDeclare.quantity')">
              <span v-text="dataForm.quantity"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderDeclare.unitNetWeight')">
              <span v-text="dataForm.unitNetWeight"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderDeclare.unitDeclarePrice')">
              <span v-text="dataForm.unitDeclarePrice"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderDeclare.brand')">
              <span v-text="dataForm.brand"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderDeclare.goodsBarcode')">
              <span v-text="dataForm.goodsBarcode"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderDeclare.sku')">
              <span v-text="dataForm.sku"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderDeclare.hsCode')">
              <span v-text="dataForm.hsCode"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderDeclare.productModel')">
              <span v-text="dataForm.productModel"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderDeclare.material')">
              <span v-text="dataForm.material"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderDeclare.purpose')">
              <span v-text="dataForm.purpose"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderDeclare.origin')">
              <span v-text="dataForm.origin"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderDeclare.pickingRemark')">
              <span v-text="dataForm.pickingRemark"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderDeclare.productUrl')">
              <span v-text="dataForm.productUrl"></span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div id="cs_FormFooter" class="el-form-footer" slot="footer">
      <el-button @click="backFn">{{ $t('back') }}</el-button>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      dataForm: {
        id: '',
        creator: '',
        createDate: '',
        orderId: '',
        chineseName: '',
        englishName: '',
        quantity: '',
        unitNetWeight: '',
        unitDeclarePrice: '',
        brand: '',
        goodsBarcode: '',
        sku: '',
        hsCode: '',
        productModel: '',
        material: '',
        purpose: '',
        origin: '',
        pickingRemark: '',
        productUrl: ''
      }
    }
  },
  methods: {
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/co/orderdeclare/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 返回
    backFn () {
      this.visible = false
      this.$emit('backView')
    }
  }
}
</script>
