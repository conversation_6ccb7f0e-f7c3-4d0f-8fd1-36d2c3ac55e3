<template>
  <div class="add-body panel_body">
    <div class="panel-hd">
      {{!dataForm.id ? $t('add') : $t('update')}}
    </div>
    <div class="addOrUpdatePanel" >
      <!-- 仓库管理 -->
      <h3 class="margin_top20">仓库管理</h3>
      <el-form ref="form" >
        <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="仓库名称">
                <el-input placeholder="placeholder"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="仓库域名">
                <el-input placeholder="placeholder">
                  <template slot="prepend">Http://</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="仓库管理员">
                <el-select v-model="dataForm.opt" placeholder="placeholder">
                  <el-option
                    v-for="item in optionsList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="审批人">
                <el-input placeholder="placeholder"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="有效时间">
                <el-date-picker
                  class="width100"
                  v-model="dataForm.date"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="仓库类型">
                <el-select v-model="dataForm.opt" placeholder="placeholder">
                  <el-option
                    v-for="item in optionsList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
      </el-form>

      <!--任务管理-->
      <h3 class="margin_top20">任务管理</h3>
      <el-form ref="form" >
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="任务名称">
                <el-input placeholder="placeholder"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="执行人">
                <el-input placeholder="placeholder"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="完成时间">
                <el-date-picker
                  class="width100"
                  v-model="dataForm.time"
                  type="datetime"
                  placeholder="选择日期时间">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="任务描述">
                <el-input
                  type="textarea"
                  :autosize="{ minRows: 2, maxRows: 4}"
                  placeholder="请输入内容">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

      <!--商品信息-->
      <h3 class="margin_top20" >商品信息</h3>
      <el-table
        :data="tableData"
        border
        >
        <el-table-column
          fixed
          prop="date"
          label="日期"
          width="150">
        </el-table-column>
        <el-table-column
          prop="name"
          label="姓名"
          width="120">
        </el-table-column>
        <el-table-column
          prop="province"
          label="省份"
          width="120">
        </el-table-column>
        <el-table-column
          prop="city"
          label="市区"
          width="120">
        </el-table-column>
        <el-table-column
          prop="address"
          label="地址"
          width="300">
        </el-table-column>
        <el-table-column
          prop="zip"
          label="邮编"
          width="120">
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
          min-width="100">
          <template slot-scope="scope">
            <el-button type="text" size="small">查看</el-button>
            <el-button type="text" size="small">编辑</el-button>
          </template>
        </el-table-column>
        </el-table>
    </div>
    <div id="cs_FormFooter" class="el-form-footer" slot="footer">
      <el-button type="primary" >{{ $t('confirm') }}</el-button>
      <el-button >{{ $t('cancel') }}</el-button>
    </div>
  </div>
</template>

<script>
import areaBox from '@/components/areaBox'
export default {
  name: 'add',
  data () {
    return {
      dataForm: {
        date: [],
        time: '',
        opt: ''
      },
      optionsList: [
        {
          label: '张三',
          value: 1
        }
      ],
      tableData: [{
        date: '2016-05-02',
        name: '王小虎',
        province: '上海',
        city: '普陀区',
        address: '上海市普陀区金沙江路 1518 弄',
        zip: 200333
      }, {
        date: '2016-05-04',
        name: '王小虎',
        province: '上海',
        city: '普陀区',
        address: '上海市普陀区金沙江路 1517 弄',
        zip: 200333
      }, {
        date: '2016-05-01',
        name: '王小虎',
        province: '上海',
        city: '普陀区',
        address: '上海市普陀区金沙江路 1519 弄',
        zip: 200333
      }, {
        date: '2016-05-03',
        name: '王小虎',
        province: '上海',
        city: '普陀区',
        address: '上海市普陀区金沙江路 1516 弄',
        zip: 200333
      }]
    }
  },
  created () {
    this.$nextTick(() => {
      // 如果菜单浮动位置 需要初始化
      this.$footerScroll()
    })
  },
  methods: {
    handleClick (row) {
      console.log(row)
    },
    // 返回
    backFn () {
      this.visible = false
      this.$emit('backView')
    }
  },
  components: {
    areaBox
  }
}
</script>

<style scoped>

</style>
