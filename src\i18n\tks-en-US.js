const tks = {}

/* 客服管理 - 轨迹查询-tksTracking */
tks.tksTracking = {}
tks.tksTracking.waybillNo = 'Please enter 1 or no more than 10 waybill numbers, separated by commas or newlines'
tks.tksTracking.cnSearch = 'View CN track'
tks.tksTracking.enSearch = 'View EN track'
tks.tksTracking.result = 'Result'
tks.tksTracking.normal = 'Normal'
tks.tksTracking.abnormal = 'Abnormal'
tks.tksTracking.queryNO = 'Query No'
tks.tksTracking.timeZone = 'Time Zone'
tks.tksTracking.btyNo = 'Logistics Company No'
tks.tksTracking.trackingNO = 'Tracking No'
tks.tksTracking.postalTrackingNo = 'Postal Tracking No'
tks.tksTracking.logisticsCompanyTrackNo = 'Logistics Company track No'
tks.tksTracking.trackingNumber = 'Tracking No'
tks.tksTracking.destinationCountry = 'Destination country'
tks.tksTracking.collectPackageTimeliness = 'Collect timeliness'
tks.tksTracking.deliveryPackageTimeliness = 'Delivery timeliness'
tks.tksTracking.day = 'Days'
tks.tksTracking.back = 'Back'
tks.tksTracking.state = 'State'
tks.tksTracking.content = 'Content'
tks.tksTracking.location = 'Location'
tks.tksTracking.timestamp = 'Time'
tks.tksTracking.save = 'Save'
tks.tksTracking.delete = 'Delete'
tks.tksTracking.edit = 'Edit'
tks.tksTracking.add = 'Add'
tks.tksTracking.eventCountry = 'Country'
tks.tksTracking.eventProvince = 'State'
tks.tksTracking.eventCity = 'City'
tks.tksTracking.zipCode = 'Zipcode'
tks.tksTracking.flightNo = 'Flight No.'
tks.tksTracking.itemflightNo = 'Flight No.'
tks.tksTracking.trackDescription = 'Track Description: the time when all tracks occur is the time in the time zone where they occur'

export default tks
