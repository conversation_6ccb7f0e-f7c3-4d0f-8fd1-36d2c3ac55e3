<template>
  <div class="aui-card--fill">
    <div class="mod-co__upload flex_wrap" >
    <el-card class="box-card" shadow="never">
    <el-form ref="searchForm" class="form_no_margin" :model="dataForm" label-width='100px'>
      <el-row :gutter="10" type="flex" justify="center">
        <el-col :span="12">
          <el-form-item :label="$t('coOrder.noType')" prop="noType">
            <el-select v-model="dataForm.noType" filterable>
              <el-option v-for="item in masterNoTypeList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10" type="flex" justify="center">
        <el-col :span="24">
          <el-upload
            :action="url"
            :data="dataForm"
            drag
            :before-upload="beforeUploadHandle"
            :on-success="successHandle"
            :on-error="errorHandle"
            :headers="getToken"
            accept=".xlsx,.xls"
            class="text-center margin_bottom15">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text" v-html="$t('upload.text')"></div>
            <div class="el-upload__tip" slot="tip">只能上传xlsx/xls文件，且不超过2M
              <a :href="fileUrl" target="_blank">{{$t('import_module')}}<i class="el-icon-download"></i></a>
            </div>
          </el-upload>
        </el-col>
      </el-row>
    </el-form>
    </el-card>
    <el-card class="box-card" shadow="never">
      <div slot="header" class="clearfix">
        <span>导入结果</span>
        <!--<el-button class="fr" size="small" type="primary">下单结果导出</el-button>-->
      </div>
      <el-container>
        <el-main style="padding: 0px">
          <el-table :data="resultDataList" border :height="244" >
            <el-table-column v-for="(item, index) in resultTableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
            </el-table-column>
          </el-table>
        </el-main>
      </el-container>
    </el-card>
    </div>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
import dictTypeMixins from '@/mixins/dictTypeMixins'

/**
 * 批量导入入库
 */
export default {
  mixins: [dictTypeMixins],
  data () {
    return {
      token: Cookies.get('cs_token'),
      lang: Cookies.get('language') || 'zh-CN',
      url: `${this.$baseUrl}/co/order/import/customerRealWeight`,
      param: null,
      activeSecondName: 'template',
      resultTableColumns: [
        { type: '', width: '120', prop: 'no', label: this.$t('threeNoInput.no'), align: 'left', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'customerRealWeightD', label: this.$t('coOrderOther.customerRealWeight'), align: 'left', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'message', label: '消息提示', align: 'center', isShow: true, disabled: false }
      ],
      resultDataList: [],
      dataForm: {
        noType: 20,
        customerId: ''
      },
      masterNoTypeList: [],
      fileUrl: '',
      downLoadUrl: '/csm/customerWeightUpdate.xlsx',
      loading: false
    }
  },
  computed: {
    getToken () {
      return {
        token: this.token,
        'Accept-Language': this.lang
      }
    },
    resultTableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.resultTableColumns).map((key) => this.resultTableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    }
  },
  created () {
    let url = process.env.NODE_ENV === 'production' ? location.origin : (location.origin || this.$baseUrl)
    this.fileUrl = `${url}/static/` + this.downLoadUrl
    this.getDict()
  },
  methods: {
    async getDict () {
      this.masterNoTypeList = await this.getDictTypeList('masterNoType')
    },
    beforeUploadHandle (file) {
      let regStr = '(.xlsx)$|(.xls)$'
      let regx = new RegExp(regStr)
      if (!regx.test(file.name)) {
        this.$message.error(this.$t('upload.tip', { 'format': `xlsx、xls` }))
        return false
      }
      const sizeLimit = file.size / 1024 / 1024 < this.sizeLimit
      if (this.sizeLimit > 0 && !sizeLimit) {
        this.$message.error(this.$t('upload.sizeMsg', { 'size': `${this.sizeLimit}` }))
        return false // 必须返回false
      }
    },
    // 上传失败
    errorHandle (error) {
      console.log('error', error)
      let myError = error.toString()
      myError = myError.replace('Error: ', '')
      console.log('myError')
      return this.$message({
        dangerouslyUseHTMLString: true,
        message: myError || '发生错误',
        type: 'error',
        duration: 2500
      })
    },
    successHandle (res, file) {
      if (res.code !== 0) {
        return this.$message.error(res.msg)
      }
      this.resultDataList = res.data
    }
  }
}
</script>
