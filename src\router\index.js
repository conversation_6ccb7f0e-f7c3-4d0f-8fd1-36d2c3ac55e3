import Vue from 'vue'
import Router from 'vue-router'
import http from '@/utils/request'
import axios from 'axios'
import { isURL } from '@/utils/validate'
import addRoute from './newRoute'
import Cookies from 'js-cookie'
import debounce from 'lodash/debounce'

Vue.use(Router)

// hack router push callback
const routerMethods = ['push', 'replace']
routerMethods.forEach(method => {
  const originalCall = Router.prototype[method]
  Router.prototype[method] = function(location, onResolve, onReject) {
    if (onResolve || onReject) {
      return originalCall.call(this, location, onResolve, onReject)
    }
    return originalCall.call(this, location).catch(err => err)
  }
})

// 页面路由(独立页面)
export const pageRoutes = [
  {
    path: '/404',
    component: () => import(/* webpackChunkName: "404" */ `@/views/pages/404.vue`),
    name: '404',
    meta: { title: '404未找到' },
    beforeEnter (to, from, next) {
      // 拦截处理特殊业务场景
      // 如果, 重定向路由包含__双下划线, 为临时添加路由
      if (/__.*/.test(to.redirectedFrom)) {
        return next(to.redirectedFrom.replace(/__.*/, ''))
      }
      next()
    }
  },
  {
    path: '/login',
    component: () => import(/* webpackChunkName: "login" */ `@/views/pages/login.vue`),
    name: 'login',
    meta: { title: '登录' }
  },
  {
    path: '/register',
    component: () => import(/* webpackChunkName: "register" */ `@/views/pages/register.vue`),
    name: 'register',
    meta: { title: '会员注册' }
  },
  {
    path: '/forgetPassword',
    component: () => import(/* webpackChunkName: "register" */ `@/views/pages/forgetPassword.vue`),
    name: 'forgetPassword',
    meta: { title: '找回密码' }
  },
  {
    path: '/guest-track',
    component: () => import(/* webpackChunkName: "guest-track" */ `@/views/modules/guest/track.vue`),
    name: 'guest-track',
    meta: { title: '运单轨迹查询' }
  },
  {
    path: '/guest-price-calculate',
    component: () => import(/* webpackChunkName: "guest-price-calculate" */ `@/views/modules/guest/price-calculate.vue`),
    name: 'guest-price-calculate',
    meta: { title: '运费试算' }
  },
  {
    path: '/co-orderRouteEntry',
    component: () => import(/* webpackChunkName: "guest-price-calculate" */ `@/views/modules/co/orderRouteEntry.vue`),
    name: 'co-orderRouteEntry',
    meta: { title: '运费试算' }
  }
]

// 模块路由(基于主入口布局页面)
export const moduleRoutes = {
  path: '/',
  component: () => import(/* webpackChunkName: "main" */ `@/views/main.vue`),
  name: 'main',
  redirect: { name: 'home' },
  meta: { title: '主入口布局' },
  children: [
    {
      path: '/home',
      component: () => import(/* webpackChunkName: "home" */ `@/views/modules/home.vue`),
      name: 'home',
      meta: { title: '首页', isTab: true }
    }
  ]
}

const router = new Router({
  mode: 'history',
  scrollBehavior: () => ({ y: 0 }),
  routes: pageRoutes.concat(moduleRoutes)
})

router.beforeEach((to, from, next) => {
  // 用于企业端直接进入客户端，通过url中传递cs_token
  if (to && to.query && to.query.cs_token) {
    Cookies.set('cs_token', to.query.cs_token, { expires: 7 })
  }
  // 添加动态(菜单)路由
  // 已添加或者当前路由为页面路由, 可直接访问
  if (to.params.metaTitle) {
    to.meta.title = to.params.metaTitle
  }
  const reloadedData = localStorage.getItem('itsClientReloadedAfterGoPath')
  if (reloadedData) {
    console.log('页面因版本更新刷新，恢复路由跳转')
    localStorage.removeItem('itsClientReloadedAfterGoPath')
    const parsedData = JSON.parse(reloadedData)
    return next({ path: parsedData.to })
  }else {
    if (from.path === '/' || from.path === '/login' || to.path === '/login') {
      if (window.SITE_CONFIG['dynamicMenuRoutesHasAdded'] || fnCurrentRouteIsPageRoute(to, pageRoutes)) {
        return next()
      }
    } else {
      checkAppNewVersion().then(() => {
        if (window.SITE_CONFIG['dynamicMenuRoutesHasAdded'] || fnCurrentRouteIsPageRoute(to, pageRoutes)) {
          return next()
        }
      })
    }
  }
  getMenuNav(next, to)
})
const getMenuNav = debounce(function (next,to) {
  // 获取菜单列表, 添加并全局变量保存
  http.get('/cs/menu/nav').then(({ data: res }) => {
    if (!res) {
      return next({ name: 'login' })
    } else if (res.code !== 0) {
      if (to.path !== '/login') {
        Vue.prototype.$message.error(res.msg)
      }
      return next({ name: 'login' })
    }
    window.SITE_CONFIG['menuList'] = res.data
    fnAddDynamicMenuRoutes(window.SITE_CONFIG['menuList'])
    next({ ...to, replace: true })
  }).catch((e) => {
    console.log(e)
    next({ name: 'login' })
  })
}, 1000, { 'leading': true, 'trailing': false })
// 检查服务端是否已经更新，如果更新刷新页面
async function checkAppNewVersion(to) {
  const url = '/version.json?t=' + Date.now()
  let res = null
  const { log: printLog } = console
  try {
    res = await axios.get(url)
  } catch (err) {
    printLog('error Object => ', err)
    printLog('error response => ', res)
  }
  if ((res && res.data && res.data.code === 401) || (res && res.data === '' && !res.data.code)) {
    return
  }
  if (!res) {
    // 如果没有版本信息，直接刷新
    // alert('检测到新版本, 请点击确定获取最新页面！')
    localStorage.setItem('itsClientReloadedAfterGoPath', JSON.stringify({ to: to.path }))
    window.location.reload(true)
    return
  }
  const version = res.data.version
  const localVersion = localStorage.getItem('itsClientVersion')
  if (localVersion !== version) {
    printLog('new version => ', version)
    printLog('localVersion => ', localVersion)
    // alert('检测到新版本, 请点击确定获取最新页面！')
    localStorage.setItem('itsClientVersion', version)
    localStorage.setItem('itsClientReloadedAfterGoPath', JSON.stringify({ to: to.path }))
    window.location.reload(true)
  }
  localStorage.setItem('itsClientVersion', version)
}

/**
 * 判断当前路由是否为页面路由
 * @param {*} route 当前路由
 * @param {*} pageRoutes 页面路由
 */
function fnCurrentRouteIsPageRoute (route, pageRoutes = []) {
  var temp = []
  for (var i = 0; i < pageRoutes.length; i++) {
    if (route.path === pageRoutes[i].path) {
      return true
    }
    if (pageRoutes[i].children && pageRoutes[i].children.length >= 1) {
      temp = temp.concat(pageRoutes[i].children)
    }
  }
  return temp.length >= 1 ? fnCurrentRouteIsPageRoute(route, temp) : false
}

/**
 * 添加动态(菜单)路由
 * @param {*} menuList 菜单列表
 * @param {*} routes 递归创建的动态(菜单)路由
 */
function fnAddDynamicMenuRoutes (menuList = [], routes = []) {
  var temp = []
  for (var i = 0; i < menuList.length; i++) {
    if (menuList[i].children && menuList[i].children.length >= 1) {
      temp = temp.concat(menuList[i].children)
      continue
    }
    // 组装路由
    let route = {
      path: '',
      component: null,
      name: '',
      meta: {
        ...window.SITE_CONFIG['contentTabDefault'],
        menuId: menuList[i].id,
        title: menuList[i].name
      }
    }
    // eslint-disable-next-line
    let URL = (menuList[i].url || '').replace(/{{([^}}]+)?}}/g, (s1, s2) => eval(s2)) // URL支持{{ window.xxx }}占位符变量
    if (isURL(URL)) {
      route['path'] = route['name'] = `i-${menuList[i].id}`
      route['meta']['iframeURL'] = URL
    } else {
      URL = URL.replace(/^\//, '').replace(/_/g, '-')
      route['path'] = route['name'] = URL.replace(/\//g, '-')
      // eslint-disable-next-line
      route['component'] = () => import(/* webpackChunkName: "chunk-[request][index]" */ `@/views/modules/${URL}`)
    }
    routes.push(route)
  }
  if (temp.length >= 1) {
    return fnAddDynamicMenuRoutes(temp, routes)
  }
  // 添加路由
  routes = routes.concat(addRoute)
  router.addRoutes([
    {
      ...moduleRoutes,
      name: 'main-dynamic-menu',
      children: routes
    },
    { path: '*', redirect: { name: '404' } }
  ])
  window.SITE_CONFIG['dynamicMenuRoutes'] = routes
  window.SITE_CONFIG['dynamicMenuRoutesHasAdded'] = true
}

export default router
