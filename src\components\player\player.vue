<template>
  <div>
    <audio ref="audio" :src="audioUrl || mp3Url"></audio>
  </div>
</template>

<script>
export default {
  props: {
    audioUrl: {
      type: String,
      default: ''
    },
    data: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      Ready: false,
      codecsType: {}
    }
  },
  watch: {
    audioUrl (newUrl, oldUrl) {
      clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        // 先判断音乐文件是否加载状态
        this.play()
      }, 1000)
    }
  },
  computed: {
    mp3Url () {
      return this.getMp3Url(this.data)
    }
  },
  methods: {
    play () {
      if (this.$refs.audio.readyState === 4) {
        this.$refs.audio.play()
      }
    },
    getMp3Url: function (date) {
      let aidemo = 'https://ai.baidu.com/aidemo'
      let text = {
        tex: date,
        per: 2,
        lan: 'zh',
        spd: 5,
        vol: 5
      }
      return aidemo + '?type=tns2' + '&idx=1' + '&tex=' + encodeURIComponent(encodeURIComponent(text.tex)) + '&cuid=baidu_speech_demo' + '&cod=2' + '&lan=' + text.lan + '&ctp=1' + '&pdt=1' + '&spd=' + text.spd + '&per=' + text.per + '&vol=' + text.vol + '&pit=5'
    },
    codecs () {
      let audioElm = this.$refs.audio
      let n = audioElm.canPlayType('audio/mpeg;').replace(/^no$/, '')
      let navigator = window.navigator && window.navigator.userAgent.match(/OPR\/([0-6].)/g)
      let r = navigator && parseInt(navigator[0].split('/')[1], 10) < 33
      this.codecsType = {
        mp3: !((r || !n) && !audioElm.canPlayType('audio/mp3;').replace(/^no$/, '')),
        mpeg: !!n,
        opus: !!audioElm.canPlayType('audio/ogg; codecs="opus"').replace(/^no$/, ''),
        ogg: !!audioElm.canPlayType('audio/ogg; codecs="vorbis"').replace(/^no$/, ''),
        oga: !!audioElm.canPlayType('audio/ogg; codecs="vorbis"').replace(/^no$/, ''),
        wav: !!audioElm.canPlayType('audio/wav; codecs="1"').replace(/^no$/, ''),
        aac: !!audioElm.canPlayType('audio/aac;').replace(/^no$/, ''),
        caf: !!audioElm.canPlayType('audio/x-caf;').replace(/^no$/, ''),
        m4a: !!(audioElm.canPlayType('audio/x-m4a;') || audioElm.canPlayType('audio/m4a;') || audioElm.canPlayType('audio/aac;')).replace(/^no$/, ''),
        mp4: !!(audioElm.canPlayType('audio/x-mp4;') || audioElm.canPlayType('audio/mp4;') || audioElm.canPlayType('audio/aac;')).replace(/^no$/, ''),
        weba: !!audioElm.canPlayType('audio/webm; codecs="vorbis"').replace(/^no$/, ''),
        webm: !!audioElm.canPlayType('audio/webm; codecs="vorbis"').replace(/^no$/, ''),
        dolby: !!audioElm.canPlayType('audio/mp4; codecs="ec-3"').replace(/^no$/, ''),
        flac: !!(audioElm.canPlayType('audio/x-flac;') || audioElm.canPlayType('audio/flac;')).replace(/^no$/, '')
      }
    }
  },
  components: {

  }
}
</script>

<style scoped lang="scss">

</style>
