<template>
  <div class="add-body panel_body" v-if="visible">
    <div class="detail-desc" v-loading="loadingFlag">
      <el-row :gutter="10">
        <el-col :span="16" :offset="8">
        <div class="margin_bottom22">
          <h3 class="font-size18">
            <span v-show='clickItem'>{{$t(lang.tksTracking.logisticsCompanyTrackNo)}}:{{clickItem ? clickItem.waybillNo : ''}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{$t(lang.tksTracking.trackingNumber)}}:{{clickItem ? clickItem.subDeliveryNo || clickItem.deliveryNo : ''}}</span>
            <span style='float: right;margin-right: 10%;' v-show='clickItem'>{{$t(lang.tksTracking.destinationCountry)}}：{{clickItem && clickItem.destinationCountry | formatterCodeName(countryList, cnEnFlag)}}</span>
          </h3>
          <p v-show='clickItem'>
            <span class="font-size14 danger">{{$t(lang.tksTracking.trackDescription)}}</span>
          </p>

        </div>
          <div class="timeline-box block">
            <el-timeline>
              <el-timeline-item
                v-for="(activity, index) in activities"
                :key="index"
                :icon="activity.icon"
                :type="activity.type"
                :color="activity.color"
                :size="activity.size"
                :timestamp="edit ? null:activity.timestamp"
                >
                <div v-if="edit">
                  <el-card shadow="hover" class="cardMinWidth">
                    <div v-show="activity.update">
                      <el-form :model='activity' ref="activityForm" :key="'activityFormKey-'+index" label-width="120px" :inline-message='true'>
                        <el-row>
                          <p>
                            <el-form-item  prop="content" :label="$t(lang.tksTracking.content)" :rules='activityRule.content'>
                              <el-input v-model="activity.content" clearable></el-input>
                            </el-form-item>
                          </p>
                          <p class="text-time">
                            <el-form-item  prop="timestamp" :label="$t(lang.tksTracking.timestamp)" :rules='activityRule.timestamp'>
                              <el-date-picker
                                :clearable="false"
                                v-model="activity.timestamp"
                                type="datetime"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                align="right"
                              >
                              </el-date-picker>
                            </el-form-item>
                          </p>
                          <p>
                            <el-form-item  prop="timeZone" :label="$t(lang.tksTracking.timeZone)" :rules='activityRule.timeZone'>
                              <el-input v-model="activity.timeZone" clearable></el-input>
                            </el-form-item>
                          </p>
                          <p>
                            <el-form-item  prop="location" :label="$t(lang.tksTracking.location)"  :rules='activityRule.location'>
                              <el-input v-model="activity.location" clearable></el-input>
                            </el-form-item>
                          </p>
                          <p>
                            <el-form-item  prop="eventCountry" :label="$t(lang.tksTracking.eventCountry)"  :rules='activityRule.eventCountry'>
                              <el-select  filterable v-model="activity.eventCountry" auto-complete="new-password"
                                          :placeholder="$t('tksTrackingDetail.eventCountry')" clearable>
                                <el-option v-for="(item, index) in countryList" :key="index" :label="`${item.name} ${item.code}`"
                                           :value="item.code"></el-option>
                              </el-select>
                            </el-form-item>
                          </p>
                          <p>
                            <el-form-item  prop="eventProvince" :label="$t(lang.tksTracking.eventProvince)"  :rules='activityRule.eventProvince'>
                              <el-input v-model="activity.eventProvince" clearable></el-input>
                            </el-form-item>
                          </p>
                          <p>
                            <el-form-item  prop="eventCity" :label="$t(lang.tksTracking.eventCity)"  :rules='activityRule.eventCity'>
                              <el-input v-model="activity.eventCity" clearable></el-input>
                            </el-form-item>
                          </p>
                          <p>
                            <el-form-item  prop="zipCode" :label="$t(lang.tksTracking.zipCode)"  :rules='activityRule.zipCode'>
                              <el-input v-model="activity.zipCode" clearable></el-input>
                            </el-form-item>
                          </p>
                          <p>
                            <el-form-item  prop="flightNo" :label="$t(lang.tksTracking.flightNo)"  :rules='activityRule.flightNo'>
                              <el-input v-model="activity.flightNo" clearable></el-input>
                            </el-form-item>
                          </p>
                        </el-row>
                      </el-form>
                    </div>
                    <div v-show="!activity.update">
                      <p v-show="activity.location"><el-tag  class="margin_right10" size="mini" effect="plain">{{activity.location | formatterAirPortOrSeaport(airportList, seaportList, cnEnFlag)}}</el-tag></p>
                      <p>{{activity.content}}</p>
                      <p>{{activity.zipCode}}&nbsp;&nbsp;{{activity.eventCity}}&nbsp;&nbsp;{{activity.eventProvince}}&nbsp;&nbsp;{{activity.eventCountry | formatterCodeName(countryList, cnEnFlag)}}&nbsp;&nbsp;<span v-show='activity.flightNo' style='color: lightgrey;float: right;'>{{$t(lang.tksTracking.itemflightNo)}}:{{activity.flightNo}}</span></p>
                      <p class="text-time">{{activity.timestamp}} <span  style='color: lightgrey;float: right;'>{{activity.timeZone}}</span></p>
                    </div>
                    <div class="floatRightInline margin_bottom10">
                      <el-popconfirm
                        :confirm-button-text="$t('confirm')"
                        :cancel-button-text="$t('cancel')"
                        confirm-button-type="primary"
                        icon="el-icon-question"
                        placement="top-start"
                        @confirm="delDetail(activity.id, index)"
                        :width="$i18n.locale === 'en-US' ? 190 : 160"
                        :title="$t('prompt.info', { 'handle': $t( 'delete')})"
                      >
                        <el-button type="text" v-if="activities.length > 1" class="margin_right15 defaultGreyColor hoverBlue" slot="reference">{{ $t(lang.tksTracking.delete) }}</el-button>
                      </el-popconfirm>
                      <el-link type="text" size="mini" v-if="activity.update" :underline="false" @click="editDetail(activity, index)" plain round>{{$t(lang.tksTracking.save)}}</el-link>
                      <el-link type="text" size="mini" v-if="!activity.update" :underline="false" @click="activateEditDetail(index)" plain round>{{$t(lang.tksTracking.edit)}}</el-link>
                    </div>
                  </el-card>
                </div>
                <div v-else >
                    <div>
                      <p v-show="activity.location"><el-tag  class="margin_right10" size="mini" effect="plain">{{activity.location | formatterAirPortOrSeaport(airportList, seaportList, cnEnFlag)}}</el-tag></p>
                      <p> {{activity.content}}</p>
                      <p>{{activity.zipCode}}&nbsp;&nbsp;{{activity.eventCity}}&nbsp;&nbsp;{{activity.eventProvince}}&nbsp;&nbsp;{{activity.eventCountry | formatterCodeName(countryList, cnEnFlag)}}&nbsp;&nbsp;<span v-show='activity.flightNo' style='color: lightgrey;float: right;'>{{$t(lang.tksTracking.itemflightNo)}}:{{activity.flightNo}}</span></p>
                    </div>
                    <div class='timeZoneWhenView'>{{activity.timeZone}}</div>
                </div>

              </el-timeline-item>
            </el-timeline>
          </div>
        </el-col>
      </el-row>
      <div id="cs_FormFooter" class="el-form-footer left51" slot="footer">
        <el-button type="primary" :loading="loadingFlag"  @click="getTrackInfo('tksCN')">{{$t(lang.tksTracking.cnSearch)}}</el-button>
        <el-button type="primary" :loading="loadingFlag"  @click="getTrackInfo('tksEN')">{{$t(lang.tksTracking.enSearch)}}</el-button>
        <el-button @click="backFn">{{ $t(lang.tksTracking.back) }}</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import tksCN from '@/i18n/tks-CN'
import tksEN from '@/i18n/tks-en-US'
import comMixins from '@/mixins/comMixins'
import dictTypeMixins from '@/mixins/dictTypeMixins'
import baseData from '@/api/baseData'
import api from '@/api'
import { gmtTimeZoneCheck, hasChinese } from '@/utils/validate'
export default {
  name: 'tks-tracking-timeline-detail.vue',
  mixins: [ comMixins, dictTypeMixins ],
  data () {
    return {
      comMixins: {
        isGetCountryList: true
      },
      nos: '',
      id: '',
      visible: false,
      loadingFlag: false,
      trackMainTainDialogConfirmLoading: false,
      edit: true,
      // 查询单号个数限制
      noSizeInner: 10,
      // 查询结果
      result: {
        // 查询正常条数
        normalCount: 0,
        // 查询异常条数
        abnormalCount: 0,
        // 轨迹信息
        list: []
      },
      lang: tksCN,
      trackActive: 0,
      // 当前点击的item
      curItem: null,
      // 中英文标识--便于数据转换 tksCN:中文,tksEN:英文
      cnEnFlag: 'tksCN',
      countryList: [],
      airportList: [],
      seaportList: [],
      activities: []
    }
  },
  created () {
    this.baseData()
  },
  filters: {
    formatterCodeName: (value, list, cnEnFlag) => {
      if (cnEnFlag === 'tksEN') {
        return value
      }
      let name = ''
      if (!list) {
        console.warn('formatterName filtes must be a list in the parameter')
        return
      }
      list.forEach(object => {
        if (object.code === value) {
          name = object.name
          return false
        }
      })
      return name
    },
    formatterStatus: (value, list, cnEnFlag) => {
      let name = ''
      if (!list) {
        console.warn('formatterName filtes must be a list in the parameter')
        return
      }
      list.forEach(object => {
        if (object.dictValue === value) {
          name = cnEnFlag === 'tksCN' ? object.dictName : object.dictNameEn
          return false
        }
      })
      return name
    },
    formatterAirPortOrSeaport: (value, airportList, seaportList, cnEnFlag) => {
      let successConvert = false
      let convertVar = value
      airportList.forEach(item => {
        if (value === item.name || value === item.englishName) {
          console.log('airportList value =' + value + ';item.name=' + item.name + ';item.englishName=' + item.englishName + ';result=' + (value === item.name || value === item.englishName))
          convertVar = cnEnFlag === 'tksCN' ? item.name : item.englishName
          successConvert = true
          return false
        }
      })
      if (successConvert) {
        return convertVar
      }
      seaportList.forEach(item => {
        if (value === item.name || value === item.englishName) {
          convertVar = cnEnFlag === 'tksCN' ? item.name : item.englishName
          return false
        }
      })
      return convertVar
    }
  },
  computed: {
    activityRule () {
      const isTimeZone = (rule, value, callback) => {
        if (value && !gmtTimeZoneCheck(value)) {
          return callback(new Error('请正确输入GMT格式的时区，如：GMT+08:00或-06:00'))
        }
        callback()
      }
      const notChineseValidator = (rule, value, callback) => {
        if (value && hasChinese(value)) {
          return callback(new Error('不允许中文'))
        }
        callback()
      }
      return {
        content: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { max: 512, message: this.$t('validate.isOverLength', { max: 512 }), trigger: ['blur', 'change'] }
        ],
        timestamp: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        timeZone: [
          { validator: isTimeZone, trigger: ['blur', 'change'] },
          { max: 12, message: this.$t('validate.isOverLength', { max: 12 }), trigger: ['blur', 'change'] }
        ],
        eventCountry: [
          { max: 2, message: this.$t('validate.isOverLength', { max: 2 }), trigger: ['blur', 'change'] }
        ],
        eventProvince: [
          { max: 32, message: this.$t('validate.isOverLength', { max: 32 }), trigger: ['blur', 'change'] }
        ],
        eventCity: [
          { max: 64, message: this.$t('validate.isOverLength', { max: 64 }), trigger: ['blur', 'change'] }
        ],
        zipCode: [
          { validator: notChineseValidator, trigger: ['blur', 'change'] },
          { max: 32, message: this.$t('validate.isOverLength', { max: 32 }), trigger: ['blur', 'change'] }
        ],
        flightNo: [
          { validator: notChineseValidator, trigger: ['blur', 'change'] },
          { max: 32, message: this.$t('validate.isOverLength', { max: 32 }), trigger: ['blur', 'change'] }
        ]
      }
    },
    // 点击查询结果的当前item
    clickItem: {
      get () {
        return this.curItem
      },
      set (val) {
        this.activities = []
        // 加载明细
        if (!val || !val.tksTrackingDetailDTOList) {
          return this.activities
        } else {
          for (var i = 0; i < val.tksTrackingDetailDTOList.length; i++) {
            let data
            // 轨迹节点中英文切换
            let content = this.cnEnFlag === 'tksCN' ? val.tksTrackingDetailDTOList[i].trackingContentCn : val.tksTrackingDetailDTOList[i].trackingContentEn
            // 轨迹时间
            let trackingTime = val.tksTrackingDetailDTOList[i].trackingTime
            // 时区
            let timeZone = val.tksTrackingDetailDTOList[i].timeZone
            let eventCountry = val.tksTrackingDetailDTOList[i].eventCountry
            let eventProvince = val.tksTrackingDetailDTOList[i].eventProvince
            let zipCode = val.tksTrackingDetailDTOList[i].zipCode
            let eventCity = val.tksTrackingDetailDTOList[i].eventCity
            let flightNo = val.tksTrackingDetailDTOList[i].flightNo
            // detail ID
            let id = val.tksTrackingDetailDTOList[i].id
            let trackingLocation = val.tksTrackingDetailDTOList[i].trackingLocation
            switch (i) {
              case 0 :
                data = {
                  id: id,
                  content: content,
                  timeZone: timeZone,
                  eventCountry: eventCountry,
                  eventProvince: eventProvince,
                  zipCode: zipCode,
                  eventCity: eventCity,
                  flightNo: flightNo,
                  update: false,
                  location: trackingLocation,
                  timestamp: trackingTime,
                  size: 'large',
                  type: 'primary',
                  icon: 'el-icon-more'
                }
                break
              case 1 :
                data = {
                  id: id,
                  update: false,
                  timeZone: timeZone,
                  eventCountry: eventCountry,
                  eventProvince: eventProvince,
                  zipCode: zipCode,
                  eventCity: eventCity,
                  flightNo: flightNo,
                  location: trackingLocation,
                  content: content,
                  timestamp: trackingTime,
                  color: '#0bbd87'
                }
                break
              case 2 :
                data = {
                  id: id,
                  update: false,
                  timeZone: timeZone,
                  eventCountry: eventCountry,
                  eventProvince: eventProvince,
                  zipCode: zipCode,
                  eventCity: eventCity,
                  flightNo: flightNo,
                  location: trackingLocation,
                  content: content,
                  timestamp: trackingTime,
                  size: 'large'
                }
                break
              default :
                data = {
                  id: id,
                  update: false,
                  timeZone: timeZone,
                  eventCountry: eventCountry,
                  eventProvince: eventProvince,
                  zipCode: zipCode,
                  eventCity: eventCity,
                  flightNo: flightNo,
                  location: trackingLocation,
                  content: content,
                  timestamp: trackingTime
                }
                break
            }
            this.activities.push(data)
          }
        }
      }
    }
  },
  methods: {
    init (no, id) {
      this.id = id
      this.visible = true
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        if (no) {
          this.nos = no
          this.getTrackInfo('tksCN')
        }
      })
    },
    backFn () {
      this.visible = false
      this.$emit('backView')
    },
    async baseData () {
      // 机场
      this.airportList = await baseData(api.airportList).catch((res) => {
        const msg = res.msg ? res.msg : res
        return this.$message.error(msg)
      })
      // 港口
      this.seaportList = await baseData(api.seaportList).catch((res) => {
        const msg = res.msg ? res.msg : res
        return this.$message.error(msg)
      })
    },
    trackMaintainDialogClose () {
      this.$nextTick(() => {
        this.$refs.trackMaintainDialog.cleanData()
      })
    },
    trackMaintainDialogConfirm (data) {
      this.trackMaintainForm = data
      console.log('data', data)
      let reqObj = {
        subWaybillId: this.result.list[0].subWaybillId || '',
        waybillNo: this.result.list[0].waybillNo || '',
        internalFlag: this.result.list[0].internalFlag || 0,
        trackingType: this.result.list[0].trackingType || 0,
        trackingContentCn: data.trackingContentZh,
        ...data
      }
      this.$http.post('tks/trackingdetail', reqObj).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 500,
          onClose: () => {
            this.$nextTick(() => {
              this.$refs.trackMaintainDialog.cleanData()
              this.getTrackInfo(this.cnEnFlag)
            })
          }
        })
      }).catch(() => {
      })
    },
    delDetail (id, index) {
      this.loadingFlag = true
      this.$http.delete('/tks/trackingdetail', { 'data': [id] }).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        } else {
          this.activities.splice(index, 1)
          return this.$message.success(this.$t('prompt.success'))
        }
      }).catch(() => {}).finally(() => { this.loadingFlag = false })
    },
    activateEditDetail (index) {
      this.$set(this.activities[index], 'update', true)
    },
    editDetail (activity, index) {
      this.$set(this.activities[index], 'update', true)
      this.$refs.activityForm[index].validate((valid, object) => {
        if (!valid) {
          return false
        }
        let contentKey = this.cnEnFlag === 'tksCN' ? 'trackingContentCn' : 'trackingContentEn'
        let reqObj = { 'trackingTime': activity.timestamp,
          id: activity.id,
          'trackingLocation': activity.location,
          'timeZone': activity.timeZone,
          'eventCountry': activity.eventCountry,
          'eventProvince': activity.eventProvince,
          'eventCity': activity.eventCity,
          'zipCode': activity.zipCode,
          'flightNo': activity.flightNo
        }
        reqObj[contentKey] = activity.content
        this.$http.put(`tks/trackingdetail`, reqObj).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.$nextTick(() => {
                this.$set(this.activities[index], 'update', false)
                this.getTrackInfo(this.cnEnFlag)
              })
            }
          })
        }).catch(() => {
        })
      })
    },
    // 增加轨迹
    addTrackInfo () {
      this.$nextTick(() => {
        this.$refs.trackMaintainDialog.init()
      })
    },
    // 获取轨迹信息
    getTrackInfo (val) {
      this.cnEnFlag = val
      this.lang = val === 'tksCN' ? tksCN : tksEN
      this.loadingFlag = true
      if (!this.nos) {
        console.log('nono')
        this.loadingFlag = false
        this.result.list = []
        this.result.normalCount = 0
        this.result.abnormalCount = 0
        this.clickItem = []
        this.curItem = ''
        return
      }
      if (this.nos.split(',').length > this.noSizeInner) {
        this.$message.warning(this.$t('threeNoInput.outMessage', { 'size': `${this.noSizeInner}` }))
        this.loadingFlag = false
        return
      }
      let paramsObj
      if (this.id) {
        paramsObj = { nos: this.nos, id: this.id }
      } else {
        paramsObj = { nos: this.nos }
      }
      this.$http.get('/tks/trackingwaybill/getTrackInfo', { params: paramsObj }).then(({ data: res }) => {
        if (res.code === 0 && res.data) {
          this.result.normalCount = res.data.normalCount
          this.result.abnormalCount = res.data.abnormalCount
          this.result.list = res.data.list
          // 默认第一条
          this.clickItem = this.curItem = !res.data.list || res.data.list.length < 1 ? null : res.data.list[0]
        }
        this.loadingFlag = false
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.el-timeline-item__content{
  padding-top: 4px;
}
.el-timeline-item__timestamp {
  transform: translate(-170px, -22px);
}
.left51 {
  left: 51px;
}
.margin_left20 {
  margin-left: 20px;
}
.floatRightInline{
  float: right;
  display: inline-flex;
}
.defaultGreyColor {
  color: #979ead !important;
}
.cardMinWidth {
  min-width: 300px;
}
.hoverBlue:hover {
  color: #46a6ff !important;
}
::v-deep .el-card.is-hover-shadow:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .65) !important;
}
.timeZoneWhenView {
  display: inline;
  padding-left: 100%;
  position: absolute;
  color: darkgrey;
}
</style>
