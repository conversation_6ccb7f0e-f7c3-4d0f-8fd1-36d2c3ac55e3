const newRoute = [
  {
    path: 'message-notice-receiver',
    component: () => import(/* webpackChunkName: "message-notice-receiver" */ `@/views/modules/message/notice-receiver.vue`),
    // component: () => import(`@/views/modules/message/notice-receiver`),
    name: 'message-notice-receiver',
    meta: { title: '我收到的消息', isTab: true }
  // },
  // {
  //   path: 'exp-form',
  //   component: () => import(/* webpackChunkName: "exp-form" */ `@/views/modules/exp/form`),
  //   name: 'exp-form',
  //   meta: {
  //     ...window.SITE_CONFIG['contentTabDefault'],
  //     menuId: '',
  //     title: `高级列表`,
  //     isTab: true
  //   }
  // },
  // {
  //   path: 'exp-list',
  //   component: () => import(/* webpackChunkName: "exp-form" */ `@/views/modules/exp/list`),
  //   name: 'exp-list',
  //   meta: {
  //     ...window.SITE_CONFIG['contentTabDefault'],
  //     menuId: '',
  //     title: `列表`,
  //     isTab: true
  //   }
  // },
  // {
  //   path: 'exp-baseForm',
  //   component: () => import(/* webpackChunkName: "exp-form" */ `@/views/modules/exp/baseForm`),
  //   name: 'exp-baseForm',
  //   meta: {
  //     ...window.SITE_CONFIG['contentTabDefault'],
  //     menuId: '',
  //     title: `基础表单`,
  //     isTab: true
  //   }
  // },
  // {
  //   path: 'exp-advanced',
  //   component: () => import(/* webpackChunkName: "exp-form" */ `@/views/modules/exp/advanced-form`),
  //   name: 'exp-advanced',
  //   meta: {
  //     ...window.SITE_CONFIG['contentTabDefault'],
  //     menuId: '',
  //     title: `高级表单`,
  //     isTab: true
  //   }
  // },
  // {
  //   path: 'exp-detail',
  //   component: () => import(/* webpackChunkName: "exp-form" */ `@/views/modules/exp/detail`),
  //   name: 'exp-detail',
  //   meta: {
  //     ...window.SITE_CONFIG['contentTabDefault'],
  //     menuId: '',
  //     title: `详情`,
  //     isTab: true
  //   }
  // },
  // {
  //   path: 'notice-list',
  //   component: () => import(/* webpackChunkName: "notice-list" */ `@/views/modules/notice/list`),
  //   name: 'notice-list',
  //   meta: {
  //     ...window.SITE_CONFIG['contentTabDefault'],
  //     menuId: '',
  //     title: `公告列表`,
  //     isTab: true
  //   }
  // },
  // {
  //   path: 'notice-detail',
  //   component: () => import(/* webpackChunkName: "notice-detail" */ `@/views/modules/notice/detail`),
  //   name: 'notice-detail',
  //   meta: {
  //     ...window.SITE_CONFIG['contentTabDefault'],
  //     menuId: '',
  //     title: `公告详情`,
  //     isTab: true
  //   }
  // },
  // {
  //   path: 'order',
  //   component: () => import(/* webpackChunkName: "order-Entry" */ `@/views/modules/exp/order`),
  //   name: 'order',
  //   meta: {
  //     ...window.SITE_CONFIG['contentTabDefault'],
  //     menuId: '',
  //     title: `订单录入`,
  //     isTab: true
  //   }
  // },
  // {
  //   path: 'co-in-order',
  //   component: () => import(/* webpackChunkName: "order-Entry" */ `@/views/modules/co/orderList`),
  //   name: 'co-in-order',
  //   meta: {
  //     ...window.SITE_CONFIG['contentTabDefault'],
  //     menuId: '',
  //     title: `订单列表`,
  //     isTab: true
  //   }
  // },
  // {
  //   path: 'mOption',
  //   component: () => import(/* webpackChunkName: "order-Entry" */ `@/views/modules/exp/mOption`),
  //   name: 'mOption',
  //   meta: {
  //     ...window.SITE_CONFIG['contentTabDefault'],
  //     menuId: '',
  //     title: `订单录入`,
  //     isTab: true
  //   }
  }
]

export default newRoute
