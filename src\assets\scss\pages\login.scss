.aui-page__login {
  &::before,
  &::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: -1;
    content: "";
  }
  &::before {
    background-image: url(~@/assets/img/login_bg.jpg);
    background-size: cover;
  }
  //&::after {
  //  background-color: rgba(7, 81, 241, 0.3);
  //}
  .aui-content {
    display: flex;
    flex-flow: column wrap;
    justify-content: flex-start;
    align-items: flex-end;
    min-height: 100vh;
    padding: 1% 20px 150px;
    text-align: center;
    margin-right: 5%;
    &__wrapper {
      height: 100vh;
      background-color: transparent;
      overflow-x: hidden;
      overflow-y: auto;
    }
  }
  .login-header {
    padding: 20px;
    width: 450px;
    color: #fff;
    .login-brand {
      margin: 0 0 15px;
      font-size: 40px;
      font-weight: 400;
      letter-spacing: 2px;
      text-transform: uppercase;
    }
    .login-intro {
      padding: 0;
      margin: 0;
      list-style: none;
      > li {
        font-size: 16px;
        line-height: 1.5;
        color: rgba(255, 255, 255, .6);
        & + li {
          margin-top: 5px;
        }
      }
    }
  }
  .login-body,
  .login-footer {
    width: 450px;
  }
  .login-body {
    padding: 20px 30px;
    background-color: rgba(255, 255, 255, 0.2);
    border:1px solid #28a8e5;
    margin-bottom: 5px;
    .login-title {
      font-size: 18px;
      font-weight: 400;
      color: #fff;
    }
    .el-input__prefix .el-input__icon {
      font-size: 16px;
    }
    .login-captcha {
      height: $--input-height;
      line-height: $--input-height -2px;
      > img {
        max-width: 100%;
        cursor: pointer;
      }
    }
    .login-shortcut {
      margin-bottom: 20px;
      &__title {
        position: relative;
        margin: 0 0 15px;
        font-weight: 400;

        &::before {
          position: absolute;
          top: 50%;
          right: 0;
          left: 0;
          z-index: 1;
          content: "";
          height: 1px;
          margin-top: -.5px;
          background-color: $--border-color-base;
          overflow: hidden;
        }
        > span {
          position: relative;
          z-index: 2;
          padding: 0 20px;
          color: rgba(0, 0, 0, .3);
          background-color: #fff;
        }
      }
      &__list {
        padding: 0;
        margin: 0;
        list-style: none;
        font-size: 0;
        > li {
          display: inline-block;
          vertical-align: middle;
          margin: 0 10px;
          font-size: 28px;
        }
      }
    }
    .login-guide {
      color: rgba(0, 0, 0, .3);
    }
    .el-link--inner{
      color: #e2e2e2;
    }
  }
  .login-footer {
    position: absolute;
    left: 50%;
    margin-left: -230px;
    bottom: 0;
    padding: 20px;
    color: rgba(255, 255, 255, .6);
    p {
      margin: 10px 0;
    }
    a {
      padding: 0 5px;
      color: rgba(255, 255, 255, .6);
      &:focus,
      &:hover {
        color: #fff;
      }
    }
  }
  .login_tips{
    width: 450px;
    padding: 10px;
    color: #fff;
    font-size:14px;
  }
  // 右侧垂直风格
  &--right-vertical {
    .aui-content {
      flex-flow: row nowrap;
      justify-content: flex-start;
      align-items: stretch;
      padding: 0;
    }
    .login-header {
      flex: 1;
      display: flex;
      flex-flow: column wrap;
      justify-content: center;
      padding: 30px 120px;
      text-align: left;
    }
    .login-body {
      position: relative;
      display: flex;
      flex-flow: column wrap;
      justify-content: center;
      padding: 120px 30px 150px;
      text-align: center;
      .login-guide {
        margin-top: 0;
      }
    }
    .login-footer {
      right: 0;
      color: $--color-text-regular;
      a {
        color: $--color-text-regular;
        &:focus,
        &:hover {
          color: $--color-primary;
        }
      }
    }
  }
}
@media (max-width: 991px) {
  .aui-page__login {
    &--right-vertical {
      .login-header {
        padding: 30px;
      }
    }
  }
}
@media (max-width: 767px) {
  .aui-page__login {
    &--right-vertical {
      .login-header {
        .login-brand,
        .login-intro {
          display: none;
        }
      }
    }
  }
}
@media (max-width: 575px) {
  .aui-page__login {
    .login-body,
    .login-footer {
      width: 100%;
    }
    .login-captcha {
      text-align: left;
      > img {
        width: 136px;
      }
    }
    &--right-vertical {
      .login-header {
        display: none;
      }
    }
  }
}
