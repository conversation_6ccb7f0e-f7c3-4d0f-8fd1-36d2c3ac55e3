import DateUtil from '@/utils/date'
import NP from 'number-precision'
// import { isDateTime } from '@/utils/validate'
/**
 * 通用的code转name
 */
export function formatterCodeShowName (value, list, showName) {
  let name = ''
  if (!list) {
    console.warn('formatterName filtes must be a list in the parameter')
    return
  }
  list.forEach(object => {
    if (object.code === value) {
      name = object[showName]
      return false
    }
  })
  return name
}
/**
 * GMT时间转本地时间
 * @param date
 * @returns {*
 */
export function gtmToLtm (date) {
  return date
  // 暂时不需要考虑时区。调用这个方法需要数据库存储的时间为0时区
  // if (date && isDateTime(date)) {
  //   return DateUtil.gmtToLtFormat(date)
  // }
}

/**
 * 文本中中文；分号换行显示
 * @param date
 * @returns {*}
 */
export function lineFeed (date) {
  date = date.replace(/;/g, '\n')
  date = date.replace(/；/g, '\n')
  return date
}

/**
 * 将时间戳转换为时间格式,默认yyyy-mm-dd hh:mm:ss
 * @param time
 * @returns {*}
 */
export function timestampToDate (time) {
  if (time && time !== '0') {
    return DateUtil.timestampToDate(time)
  }
}

/**
 * 字典类型转换
 * @param {*} s
 */
export function formatterType (value, list) {
  let dictName = ''
  if (!list) {
    console.warn('formatterType filtes must be a list in the parameter')
    return
  }
  list.forEach(dict => {
    if (String(dict.dictValue) === String(value)) {
      dictName = dict.dictName
      return false
    }
  })
  return dictName
}

/**
 * 用户名ID转realName
 */
export function formatterUser (value, list) {
  let realName = ''
  if (!list) {
    console.warn('formatterUser filtes must be a list in the parameter')
    return
  }
  list.forEach(user => {
    if (user.id === String(value)) {
      realName = user.realName
      return false
    }
  })
  return realName
}

/**
 * 通用的ID转name
 */
export function formatterName (value, list) {
  let name = ''
  if (!list) {
    console.warn('formatterName filtes must be a list in the parameter')
    return
  }
  list.forEach(object => {
    if (object.id === String(value)) {
      name = object.name
      return false
    }
  })
  return name
}

/**
 * 通用的ID转code
 */
export function formatterCode (value, list) {
  let code = ''
  if (!list) {
    console.warn('formatterCode filtes must be a list in the parameter')
    return
  }
  list.forEach(object => {
    if (object.id === String(value)) {
      code = object.code
      return false
    }
  })
  return code
}

/**
 * 通用的code转name
 */
export function formatterCodeName (value, list) {
  let name = ''
  if (!list) {
    console.warn('formatterName filtes must be a list in the parameter')
    return
  }
  list.forEach(object => {
    if (object.code === value) {
      name = object.name
      return false
    }
  })
  return name
}

/**
 * 通用的className转name
 */
export function formatterClassName (value, list) {
  let name = ''
  if (!list) {
    console.warn(' formatterClassName filters must be a list in the parameter')
    return
  }
  list.forEach(object => {
    if (object.className === value) {
      name = object.name
      return false
    }
  })
  return name
}

/**
 * 通用的code转name
 */
export function formatterCurrency (value, list) {
  let name = ''
  if (!list) {
    console.warn('formatterName filtes must be a list in the parameter')
    return
  }
  list.forEach(object => {
    if (object.code === value) {
      name = object.name + ' (' + object.code + ')'
      return false
    }
  })
  return name
}
/**
 * 字母转大写
 * @param {*} s
 */
export function toUpperCase (s) {
  return s.toUpperCase()
}

/**
 * 字母转小写
 * @param {*} s
 */
export function toLowerCase (s) {
  return s.toLowerCase()
}

/**
 * 时间格式化
 * @param times
 * @param pattern
 * @returns {string|*}
 */
export function timestampFormat (timestamp, format) {
  let result = ''
  let zeroFill = val => val < 10 ? '0' + val : val
  timestamp = new Date(timestamp) / 1000
  timestamp = parseInt(timestamp) * 1000
  format = format || 'YYYY-MM-DD hh:mm:ss'

  if (!timestamp) {
    result = '-'
  } else {
    let sFormat = format
    format = format.split(' ')
    let dateFormat = format[0]
    let timeFormat = format[format.length - 1]
    let dateSymbol = dateFormat.match(/[^Y,^M,^D]/)[0]
    let timeSymbol = timeFormat.match(/[^h,^m,^s]/)[0]
    let clearance = sFormat.replace(dateFormat, '').replace(timeFormat, '')
    let datetime = new Date(timestamp)
    let year = datetime.getFullYear()
    let month = zeroFill(datetime.getMonth() + 1)
    let day = zeroFill(datetime.getDate())
    let hours = zeroFill(datetime.getHours())
    let minute = zeroFill(datetime.getMinutes())
    let second = zeroFill(datetime.getSeconds())
    if (dateFormat.match(/Y/)) {
      result += year
    }
    if (dateFormat.match(/M/)) {
      result += dateSymbol + month
    }
    if (dateFormat.match(/D/)) {
      result += dateSymbol + day
    }
    if (timeFormat.match(/h/)) {
      result += clearance + hours
    }
    if (timeFormat.match(/m/)) {
      result += timeSymbol + minute
    }
    if (timeFormat.match(/s/)) {
      result += timeSymbol + second
    }
  }

  return result.replace(/^[^\d]+/, '')
}

/**
 * 银行通用的id转BankAcNoName
 */
export function formatterBankAcNoName (value, list) {
  let bankAccountName = ''
  if (!list) {
    console.warn('formatterName filtes must be a list in the parameter')
    return
  }
  list.forEach(object => {
    if (object.id === String(value)) {
      bankAccountName = object.bankAccountName
      return false
    }
  })
  return bankAccountName
}

/**
 * 银行通用的id转BankNam
 */
export function formatterBankName (value, list) {
  let bankName = ''
  if (!list) {
    console.warn('formatterName filtes must be a list in the parameter')
    return
  }
  list.forEach(object => {
    if (object.id === String(value)) {
      bankName = object.bankName
      return false
    }
  })
  return bankName
}

/**
 * 银行通用的id转BankNam
 */
export function formatterBankNameNew (value, list, list1) {
  let bankName = ''
  if (!list) {
    console.warn('formatterName filtes must be a list in the parameter')
    return
  }
  list.forEach(object => {
    if (object.id === String(value)) {
      let bankId = object.bankName
      list1.forEach(obj => {
        if (bankId === String(obj.dictValue)) {
          bankName = obj.dictName
          return false
        }
      })
      return false
    }
  })
  return bankName
}

/**
 * 银行通用的id转bankAccount
 */
export function formatterBankAccount (value, list) {
  let bankAccount = ''
  if (!list) {
    console.warn('formatterName filtes must be a list in the parameter')
    return
  }
  list.forEach(object => {
    if (object.id === String(value)) {
      bankAccount = object.bankAccount
      return false
    }
  })
  return bankAccount
}

/**
 * 数字转保留两位小数（不足位数，则补0）
 */
export function NumberToDecimal2 (num) {
  var result = parseFloat(num)
  if (isNaN(result)) {
    return false
  }
  result = Math.round(num * 100) / 100
  var sx = result.toString() // 将数字转换为字符串

  var posDecimal = sx.indexOf('.') // 小数点的索引值

  // 当整数时，posDecimal=-1 自动补0
  if (posDecimal < 0) {
    posDecimal = sx.length
    sx += '.'
  }

  // 当数字的长度< 小数点索引+2时，补0
  while (sx.length <= posDecimal + 2) {
    sx += '0'
  }
  return sx
}

// 乘法
export function numberMul (arg1, arg2) {
  let m = 0
  let s1 = arg1 + ''
  let s2 = arg2 + ''
  try {
    m += s1.split('.')[1].length
  } catch (e) {
  }
  try {
    m += s2.split('.')[1].length // m是累加结果
  } catch (e) {
  }
  return Number(s1.replace('.', '')) * Number(s2.replace('.', '')) / Math.pow(10, m)
}

// 除数，被除数， 保留的小数点后的位数
export function numberDiv (arg1, arg2, digit) {
  let t1 = 0
  let t2 = 0
  let r1
  let r2
  try {
    t1 = arg1.toString().split('.')[1].length
  } catch (e) {

  }
  try {
    t2 = arg2.toString().split('.')[1].length
  } catch (e) {

  }
  r1 = Number(arg1.toString().replace('.', ''))
  r2 = Number(arg2.toString().replace('.', ''))
  // 获取小数点后的计算值
  var result = ((r1 / r2) * Math.pow(10, t2 - t1)).toString()
  var result2 = result.split('.')[1]
  if (result2 === undefined) {
    result2 = '00'
  } else {
    result2 = result2.substring(0, digit > result2.length ? result2.length : digit)
  }
  return Number(result.split('.')[0] + '.' + result2)
}

/*
  * 参数说明：
  * number：要格式化的数字
  * decimals：保留几位小数
  * dec_point：小数点符号
  * thousands_sep：千分位符号
*/
export function numberFormat (number, decimals, decPoint, thousandsSep) {
  number = (number + '').replace(/[^0-9+-Ee.]/g, '')
  let n = !isFinite(+number) ? 0 : +number
  let prec = !isFinite(+decimals) ? 2 : Math.abs(decimals)
  let sep = (typeof thousandsSep === 'undefined') ? ',' : thousandsSep
  let dec = (typeof decPoint === 'undefined') ? '.' : decPoint
  let s = ''
  let toFixedFix = function (n, prec) {
    var k = Math.pow(10, prec)
    return '' + numberDiv(numberMul(n, k), k)
  }
  s = (prec ? toFixedFix(n, prec) : '' + Math.round(n)).split('.')
  var re = /(-?\d+)(\d{3})/
  while (re.test(s[0])) {
    s[0] = s[0].replace(re, '$1' + sep + '$2')
  }
  if ((s[1] || '').length < prec) {
    s[1] = s[1] || ''
    s[1] += new Array(prec - s[1].length + 1).join('0')
  }
  return s.join(dec)
}

/**
 * 通用的ID转name
 */
export function formatterShowName (value, list, showName) {
  let name = ''
  if (!list) {
    console.warn('formatterName filtes must be a list in the parameter')
    return
  }
  list.forEach(object => {
    if (object.id === String(value)) {
      name = object[showName]
      return false
    }
  })
  return name
}

/**
 * 通用的Code转name
 */
export function formatterShowNameForCode (value, list, showName) {
  let name = ''
  if (!list) {
    console.warn('formatterName filtes must be a list in the parameter')
    return
  }
  list.forEach(object => {
    if (object.code === String(value)) {
      name = object[showName]
      return false
    }
  })
  return name
}

/**
 * 通用的ID转name
 */
export function formatterUserName (value, list) {
  let name = ''
  if (!list) {
    console.warn('formatterName filtes must be a list in the parameter')
    return
  }
  list.forEach(object => {
    if (object.id === String(value)) {
      name = object.username
      return false
    }
  })
  return name
}

/**
 * 通用的code转name
 */
export function formatterCodeNativeName (value, list) {
  let name = ''
  if (!list) {
    console.warn('formatterName filtes must be a list in the parameter')
    return
  }
  list.forEach(object => {
    if (object.code === value) {
      name = object.name
      return false
    }
  })
  return name
}
// 两数相乘，不支持负数 例子： 233*1.122=261.42600000000004 => 233*1.122=261.426
export function multiply(num1, num2, ...others) {
  if (!num1 || !num2) {
    return ''
  }
  if (others.length > 0) {
    return NP.times(NP.times(num1, num2), others[0], ...others.slice(1)).toLocaleString('fullwide', { useGrouping: false })
  }
  return NP.times(num1, num2).toLocaleString('fullwide', { useGrouping: false })
}
