<template>
  <div class="add-body panel_body">
    <div class="panel-hd">
      <span v-text="$t('detail')"></span>
    </div>
    <div class="detail-desc">
      <el-form ref="form" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item :label="$t('baReceivableFee.businessId')">
              <span v-text="dataForm.businessId"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('baReceivableFee.feeTypeId')">
              <template>{{ dataForm.feeTypeId | formatterName(this.usingFeeTypeList)}}</template>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('baReceivableFee.currency')">
              <template>{{ dataForm.currency | formatterCodeName(this.currencyList)}}</template>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('baReceivableFee.sum')">
              <template v-text="dataForm.sumD">{{ dataForm.sumD | numberFormat(2)}}</template>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('baReceivableFee.optDate')">
              <span v-text="dataForm.optDate"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('baReceivableFee.billingDate')">
              <span v-text="dataForm.billingDate"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('baReceivableFee.createType')">
              <template>{{ dataForm.createType | formatterType(this.createTypeList)}}</template>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('baReceivableFee.businessType')">
              <template>{{ dataForm.businessType | formatterType(this.businessTypeList)}}</template>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('baReceivableFee.waybillNo')">
              <span v-text="dataForm.waybillNo"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('baReceivableFee.customerVoucherNo')">
              <span v-text="dataForm.customerVoucherNo"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('baReceivableFee.memo')">
              <span v-text="dataForm.memo"></span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div id="cs_FormFooter" class="el-form-footer" slot="footer">
      <el-button @click="backFn">{{ $t('back') }}</el-button>
    </div>
  </div>
</template>

<script>
import dictTypeMixins from '@/mixins/dictTypeMixins'
import { formatterType, gtmToLtm, timestampFormat, formatterCodeName, formatterName, numberFormat } from '@/filters/filters'
import api from '@/api'
import baseData from '@/api/baseData'
export default {
  mixins: [dictTypeMixins],
  data () {
    return {
      dataForm: {
        id: '',
        receivableBizOrderId: '',
        createType: '',
        settlementObjectType: '',
        settlementObjectId: '',
        settlementObjectName: '',
        businessType: '',
        orderType: '',
        orderId: '',
        waybillNo: '',
        customerVoucherNo: '',
        receivableBillId: '',
        feeTypeId: '',
        currency: '',
        sum: '',
        formula: '',
        optDate: '',
        billingDate: '',
        billingStatus: '',
        auditor: '',
        auditDate: '',
        memo: ''
      },
      currencyList: [],
      usingFeeTypeList: [],
      optDateArr: [],
      businessTypeList: [],
      createTypeList: []
    }
  },
  created () {
    this.getBaseData()
    this.getDict()
  },
  methods: {
    async getBaseData () {
      // 币种
      this.currencyList = await baseData(api.currencyList).catch((res) => {
        const msg = res.msg ? res.msg : res
        return this.$message.error(msg)
      })
      // 币种
      this.usingFeeTypeList = await baseData(api.usingFeeTypeList).catch((res) => {
        const msg = res.msg ? res.msg : res
        return this.$message.error(msg)
      })
    },
    async getDict () {
      this.businessTypeList = await this.getDictTypeList('feeBusinessType')
      this.createTypeList = await this.getDictTypeList('createType')
    },
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/ba/receivablefee/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 返回
    backFn () {
      this.visible = false
      this.$emit('backView')
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat,
    formatterCodeName,
    formatterName,
    numberFormat
  }
}
</script>
