<template>
  <div class="add-body panel_body">
    <el-form ref="labelPrintDataForm" class="print-box" :model="labelPrintDataForm" :rules="rules" @submit.native.prevent @keydown.native.enter="labelPrintHandle()" >
      <el-form-item :label="$t('单号')" prop="no">
        <el-input size="medium" ref="labelPrintDataForm_no" type="text" v-model="labelPrintDataForm.no" :placeholder="$t('扫描/输入单号')"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button size="medium" v-if="$hasPermission('co:orderLabelPrint:print')" type="primary" @click="labelPrintHandle()" icon="el-icon-printer">打印</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import debounce from 'lodash/debounce'
import { printFn } from '@/utils/print'

export default {
  mixins: [],
  data () {
    return {
      labelPrintDataForm: {
        no: ''
      }
    }
  },
  computed: {
    rules () {
      return {
        no: [
          { required: true, message: this.$t('validate.required'), trigger: ['blur', 'change'] }
        ]
      }
    }
  },
  activated () {
    this.$nextTick(function () {
      this.$refs.labelPrintDataForm_no.focus()
    })
  },
  methods: {
    labelPrintHandle: debounce(function () {
      this.$refs.labelPrintDataForm.validate((valid) => {
        if (!valid) {
          return false
        }
        printFn('/co/order/customer/label?nos=' + this.labelPrintDataForm.no + '&orderByMode=11')
        this.labelPrintDataForm.no = ''
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>

<style lang="scss">
  .print-box{
    .el-input.el-input--medium .el-input__inner{
      height: 60px !important;
      line-height: 60px !important;
      font-size: 32px;
    }
  }
</style>

<style scoped>
  .print-box {
    padding: 20px 0 0 0;
    max-width: 560px;
    margin: 0 auto;

  }
</style>
