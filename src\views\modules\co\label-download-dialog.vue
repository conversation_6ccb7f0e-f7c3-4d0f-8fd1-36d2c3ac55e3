<template>
  <div>
    <!-- 导出类型选择 -->
    <el-dialog :visible.sync="exportDialogVisible" width="450px" :title="$t('batchDownloadLabel')" :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :model="dataForm" :inline="true">
        <div style="padding-bottom: 10px;font-size: 16px;">文件名包含：</div>
        <el-form-item :label="$t('guidPrefix')">
          <el-radio-group v-model="dataForm.guidPrefix" style="width: 100%">
            <el-radio
              v-for="item in yesOrNoList"
              :key="item.dictValue"
              :label="item.dictValue">
              {{item.dictName}}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('noType')">
          <!-- <el-checkbox-group class="width100" v-model="dataForm.noTypeArray"  style="width: 100%">
            <el-checkbox v-for="item in noTypeList" :label="item.dictValue" :key="item.dictValue">{{item.dictName}}</el-checkbox>
          </el-checkbox-group> -->
          <el-radio-group class="width100" v-model="dataForm.noType"  style="width: 100%">
            <el-radio v-for="item in noTypeList" :label="item.dictValue" :key="item.dictValue">{{item.dictName}}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="exportDialogVisible = false">{{ this.$t('cancel') }}</el-button>
        <el-button type="primary" @click="downloadHandle()" :loading="downloadFileButtonLoading" :disabled="false">{{this.$t('batchDownloadLabel')}}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import dictTypeMixins from '@/mixins/dictTypeMixins'
import mixinViewModule from '@/mixins/view-module'

// table 自定义显示
export default {
  mixins: [dictTypeMixins, mixinViewModule],
  data () {
    return {
      dataForm: {
        guidPrefix: 0,
        noType: 1
      },
      dataList: [],
      // 允许下载操作的状态值
      enableStatusArray: [11, 12, 13],
      // 非法状态值的错误提示
      enableErrorTips: '已预报、已入库、已出库',
      // 允许下载操作的状态字段名
      statusField: 'status',
      exportListURL: '/bd/excelexporttemplate/listByUrlClient',
      exportURL: '/bd/excelexporttemplate/export',
      noTypeList: [],
      yesOrNoList: [],
      exportVisible: false,
      buttonLoading: false,
      exportDialogVisible: false
    }
  },
  methods: {
    init () {
      this.exportDialogVisible = true
      this.getDict()
    },
    async getDict () {
      this.yesOrNoList = await this.getDictTypeList('yesOrNo')
      this.noTypeList = await this.getDictTypeList('fileNameNoType')
    },
    // 返回
    backFn () {
      this.exportDialogVisible = false
      this.downloadFileButtonLoading = false
      this.$emit('backView')
    },
    downloadHandle () {
      if (this.dataList.length <= 0) {
        return this.$message({
          message: this.$t('prompt.batchPrint'),
          type: 'warning',
          duration: 1000
        })
      }
      if (this.dataListSelections.length > 200) {
        return this.$message.warning('下载面单包裹数不能超过200个')
      }
      let ids = ''
      for (let i = 0; i < this.dataList.length; i++) {
        if (!this.enableStatusArray.includes(this.dataList[i][this.statusField])) {
          return this.$message.warning('请选择' + this.enableErrorTips + '订单进行下载')
        } else {
          if (ids !== '') {
            ids += ','
          }
          ids += this.dataList[i].id
        }
      }
      // let noType = 0
      // // if (this.dataForm.noTypeArray.length < 1) {
      // //   return this.$message.warning('单号类型最少选择一项')
      // // }
      // for (let i = 0; i < this.dataForm.noTypeArray.length; i++) {
      //   noType += this.dataForm.noTypeArray[i]
      // }
      this.downloadData.nos = ids
      this.downloadData.noType = this.dataForm.noType
      this.downloadData.guidPrefix = this.dataForm.guidPrefix
      this.downloadFileByMsgHandle('/co/order/customer/downloadLabel')
    }
  }
}
</script>
