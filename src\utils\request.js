import axios from 'axios'
import Cookies from 'js-cookie'
import router from '@/router'
import qs from 'qs'
import { clearLoginInfo } from '@/utils'
import isPlainObject from 'lodash/isPlainObject'
import config from './config'
import { Message } from 'element-ui'

let localtionUrl = (() => {
  return process.env.NODE_ENV === 'production' ? config.apiURL : ''
})()
const http = axios.create({
  baseURL: localtionUrl,
  timeout: 1000 * 180,
  withCredentials: true
})

/**
 * 请求拦截
 */
http.interceptors.request.use(config => {
  config.headers['Accept-Language'] = Cookies.get('language') || 'zh-CN'
  config.headers['token'] = Cookies.get('cs_token') || ''
  // 默认参数
  var defaults = {}
  // 防止缓存，GET请求默认带_t参数
  if (config.method === 'get') {
    config.params = {
      ...config.params,
      ...{ '_t': new Date().getTime() }
    }
  }
  if (isPlainObject(config.params)) {
    config.params = {
      ...defaults,
      ...config.params
    }
  }
  if (isPlainObject(config.data)) {
    config.data = {
      ...defaults,
      ...config.data
    }
    if (/^application\/x-www-form-urlencoded/.test(config.headers['content-type'])) {
      config.data = qs.stringify(config.data)
    }
  }
  return config
}, error => {
  return Promise.reject(error)
})

/**
 * 响应拦截
 */
http.interceptors.response.use(response => {
  if (response.data.code === 401) {
    clearLoginInfo()
    router.replace({ name: 'login' })
    return Promise.reject(response.data.msg)
  }
  return response
}, error => {
  const { response } = error
  const { error: printErrorLog } = console
  printErrorLog('error Object => ', error)
  printErrorLog('error response => ', response)
  if (response) {
    if (response.status === 414) {
      // 处理 "414 Request-URI Too Large" 错误
      Message({
        dangerouslyUseHTMLString: true,
        message: '请求的URL过长，服务器无法处理!' + '(' + response.statusText + ')',
        type: 'error',
        duration: 4000
      })
    } else {
      Message({
        dangerouslyUseHTMLString: true,
        message: response.statusText || '发生错误',
        type: 'error',
        duration: 4000
      })
    }
  } else if (!window.navigator.onLine) {
    Message({
      dangerouslyUseHTMLString: true,
      message: '无法访问网络，请检查网络设置！',
      type: 'error',
      duration: 4000
    })
  }
  return Promise.reject(error)
})

export default http
