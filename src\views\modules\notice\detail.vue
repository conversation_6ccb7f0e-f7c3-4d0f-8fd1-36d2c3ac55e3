<template>
  <div class="flex_wrap notice_detail">
    <div class="add-body">
      <div class="notice_bd clearfix" v-loading="isShow">
        <h1 class="notice_title text-overflow">{{dataForm.title}}</h1>
        <div class="notice_info">
          <span>发布时间: <strong>{{dataForm.createDate  | gtmToLtm}}</strong></span>
        </div>
        <div class="notice_content clearfix">
          <div v-html="dataForm.content"></div>
        </div>
      </div>
      <div id="cs_FormFooter" class="el-form-footer" slot="footer">
        <el-button @click="backFn">{{ $t('backList') }}</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import comMixins from '@/mixins/comMixins'
import { formatterType, gtmToLtm } from '@/filters/filters'
import dictTypeMixins from '@/mixins/dictTypeMixins'
import 'quill/dist/quill.snow.css'
export default {
  name: 'detail',
  mixins: [comMixins, dictTypeMixins],
  data () {
    return {
      allUserList: [],
      dataForm: {},
      isShow: true
    }
  },
  created () {
    this.init()
  },
  watch: {
    '$route' (to, from) {
      this.init()
    }
  },
  methods: {
    init () {
      let id = this.$route.query.id
      if (id) {
        Promise.all([this.getDict, this.getDetail(id)]).then(() => {})
      }
    },
    getDetail (id) {
      this.isShow = true
      this.$http.get(`/woms/ns/notice/${id}`).then(({ data: res }) => {
        this.isShow = false
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = res.data
      }).catch(() => {})
    },
    // 状态字典
    getDict () {
      this.getDictTypeList('nsNoticePubStatus', function () {
        this.nsNoticePubStatus = this.dictTypeList
      })
      this.getDictTypeList('nsNoticeReadStatus', function () {
        this.nsNoticeReadStatus = this.dictTypeList
      })
      this.getDictTypeList('noticeType', function () {
        this.noticeType = this.dictTypeList
      })
      this.getDictTypeList('noticePubType', function () {
        this.noticePubType = this.dictTypeList
      })
    },
    // 返回
    backFn () {
      this.visible = false
      this.$router.push({ name: 'notice-list' })
    }
  },
  filters: {
    formatterType,
    gtmToLtm
  }
}
</script>

<style lang="scss" scoped>
  .notice_detail{
    position: relative;
    background-color: #fff;
    .notice_title{
      font-size: 24px;
      text-align: center;
    }
    .notice_info{
      padding: 15px;
      margin: 0 auto;
      text-align: center;
      color: #909399;
      border-top: 1px solid #E4E7ED;
      & > span {
        margin:0 10px;
      }
    }
    .notice_content{
      position: relative;
      padding: 20px 30px;
      margin: 0 5%;
      overflow: hidden;
    }
  }
</style>
