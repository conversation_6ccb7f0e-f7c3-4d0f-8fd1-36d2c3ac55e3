
export default {
  // 币种集合
  currencyList: '/bd/currency/list',
  // 获取轨迹分组
  tksTrackingGroupList: '/tks/trackinggroup/list',
  // 获取物流渠道
  logisticsChannelByParamsList: '/bd/logisticschannel/listByParams',
  // 获取系统所有用户信息-不过滤公司
  allUserList: '/sys/user/listAllByNoFilter',
  // 获取仓库信息
  warehouseInfoList: '/bd/warehouseinfo/listWidthCompany',
  // 港口
  seaportList: '/bd/seaport/listSeaport',
  // 机场
  airportList: '/bd/airport/list',
  // 国家
  countryList: '/bd/region/listCountry',
  // 根据客户编码获取设置的发货人
  setShipperByCustomerCodeList: '/co/setcustomershipper/listByCustomerId/',
  // 根据客户ID获取设置的收货人
  setConsigneeByCustomerCodeList: templateStr`/co/setcustomerconsignee/listByCustomerId/${0}`,
  // 根据客户编码获取设置的报信息
  setDeclareByCustomerCodeList: '/co/setcustomerdeclare/listByCustomerId/',
  // 获得当前用户所有的尾程渠道
  listByCurrent: '/bd/logisticschannel/listByCurrent',
  // 获取物流渠道链路
  logisticschannelrouteList: '/bd/logisticschannelroute/listByParams',
  // 获取物流产品
  logisticsProductByParamsList: '/bd/logisticsproduct/listByParams',
  // 获得当前公司能使用的所有启用的物流产品列表
  listEnableAllByCurrent: '/bd/logisticsproduct/listEnableAllByCurrent',
  // 获得当前公司能使用的所有的物流产品列表
  listAllByCurrent: '/bd/logisticsproduct/listAllByCurrent',
  // 客户获取所有能使用并启用的物流产品
  enableLogisticsProductByCurrent: '/bd/logisticsproduct/listByClient',
  // 运单号列表
  waybillNoList: '/bd/trackingno/list',
  // 启用银行
  bankAccountEnableList: '/bd/bankaccount/enableList',
  // 启用银行
  getEnableCompanyBanAccount: '/bd/bankaccount/getEnableCompanyBanAccount',
  // 根据公司获取类型为公司的银行卡
  getCompanyBanAccount: '/bd/bankaccount/getCompanyBanAccount',
  // 银行卡号
  bankAccountList: '/bd/bankaccount/list',
  // 费用项
  usingFeeTypeList: '/bd/feetype/list',
  // 所有用户信息
  userList: '/sys/user/list',
  // 工单类型
  workOrderTypeList: '/csm/workordertype/list',
  // 获取自定义excel导入模版
  excelModuleList: '/co/setdiyimportmodule/list',
  // 登陆客户可用金额
  currencyUsableList: '/ba/receivableaccount/querySelfUsableSum',
  // 官网-客户获取所有能使用并启用的物流产品
  anserxWebsiteEnableLogisticsProductByCurrent: '/cs/anserxWebsite/logisticsproduct/listByClient',
  // 官网-币种集合
  anserxWebsiteCurrencyList: '/cs/anserxWebsite/currency/list',
  // 官网-国家
  anserxWebsiteCountryList: '/cs/anserxWebsite/region/listCountry',
  // 官网-费用项
  anserxWebsiteUsingFeeTypeList: '/cs/anserxWebsite/feetype/list',
  // 官网-获取物流产品
  anserxWebsiteLogisticsProductByParamsList: '/cs/anserxWebsite/logisticsproduct/listByParams',
  // 官网-获取仓库信息
  anserxWebsiteWarehouseInfoNotIsolationList: '/cs/anserxWebsite/warehouseinfo/listNotIsolation',
  // FBA仓库
  fbaWarehouseList: '/bd/fbawarehouseinfo/list',
  // 获取导出字段
  getExcelExportField: '/bd/excelexporttemplate/getExcelExportField/',
  // 获取导入字段
  getExcelImportField: '/co/setdiyimportmodule/getExcelImportField/',
  // 获取已启用的订单导入模版列表
  importOrderTemplateList: 'bd/orderimporttemplate/list',
  // 获取导出主类名称列表
  getExcelExportMaster: '/bd/excelexporttemplate/getExcelExportMaster',
  // 获取导入主类名称列表
  getExcelImportMaster: '/co/setdiyimportmodule/getExcelImportMaster',
  // 供应商接口
  providerList: '/bd/provider/queryList',
  // 系统参数
  sysParams: '/sys/params/getValueByCode',
  // 获取计费间隔月数的参数
  baBizMonthInterval: '/sys/params/getValueByCode/BA_DATE_INTERVAL'
}
/**
 * var t1Closure = template`${0}${1}${0}!`;
 * t1Closure('Y', 'A');  // "YAY!"
 * var t2Closure = template`${0} ${'foo'}!`;
 * t2Closure('Hello', {foo: 'World'});  // "Hello World!"
 * @param strings
 * @param keys
 * @returns {function(...[*]): string}
 */
function templateStr (strings, ...keys) {
  return function (...values) {
    var dict = values[values.length - 1] || {}
    var result = [strings[0]]
    keys.forEach(function (key, i) {
      var value = Number.isInteger(key) ? values[key] : dict[key]
      result.push(value, strings[i + 1])
    })
    return result.join('')
  }
}
