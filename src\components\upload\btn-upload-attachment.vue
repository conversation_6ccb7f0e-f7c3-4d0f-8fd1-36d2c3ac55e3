<template>
  <div style="padding-top: 10px">
    <!--附件上传-->
    <el-upload
      class="upload-demo"
      ref="upload"
      action="https://jsonplaceholder.typicode.com/posts/"
      :before-upload="beforeUploadHandle"
      :on-remove="handleRemove"
      :on-error="errorHandle"
      :http-request="httpRequestHandle"
      :file-list="fileList"
      :limit="fileNumLimit"
      accept=".jpg,.png,.gif,.xls,.xlsx,.doc,.docx,.bmp"
      :auto-upload="false">
      <el-button slot="trigger" size="small" type="primary">{{$t('btn.uploadAttachment')}}</el-button>
      <span slot="tip" class="el-upload__tip" style="margin-left: 10px;">附件大小不得超过{{sizeLimit}}M且最多{{fileNumLimit}}个。可支持格式：'jpg','png','gif','xls','xlsx','doc','docx','bmp'等格式附件</span>
    </el-upload>
  </div>
</template>

<script>

export default {
  props: {
    attachmentFiles: Array,
    default: () => {
      return []
    },
    attachmentFileNumLimit: {
      type: Number,
      default: 5
    },
    attachmentFileSizeLimit: {
      type: Number,
      default: 2
    }
  },
  data () {
    return {
      visible: false,
      uploadDisable: false,
      sizeLimit: this.attachmentFileSizeLimit,
      fileList: this.attachmentFiles,
      isBreak: false,
      fileNumLimit: this.attachmentFileNumLimit
    }
  },
  methods: {
    validFiles () {
      this.$refs.upload.submit()
      return this.isBreak
    },
    handleRemove (file, fileList) {
      this.uploadDisable = false
      this.isBreak = false
      this.fileList = fileList
      this.$emit('update:attachmentFiles', this.fileList)
    },
    beforeUploadHandle (file) {
      this.uploadDisable = true
      this.isBreak = false
      let regStr = '(.jpg)$|(.png)$|(.gif)$|(.xls)$|(.xlsx)$|(.doc)$|(.docx)$|(.bmp)$'
      let regx = new RegExp(regStr)
      if (!regx.test(file.name)) {
        this.isBreak = true
        this.$message.error(this.$t('upload.tip', { 'format': `jpg、png、gif、xls、xlsx、doc、docx、bmp` }))
        return false
      }
      const sizeLimit = file.size / 1024 / 1024 < this.sizeLimit
      if (this.sizeLimit > 0 && !sizeLimit) {
        this.$message.error(this.$t('upload.sizeMsg', { 'size': `${this.sizeLimit}` }))
        this.isBreak = true
        return false // 必须返回false
      }
    },
    // 上传失败
    errorHandle (error) {
      console.log('error', error)
      this.uploadDisable = false
      let myError = error.toString()
      myError = myError.replace('Error: ', '')
      console.log('myError')
      return this.$message({
        dangerouslyUseHTMLString: true,
        message: myError || '发生错误',
        type: 'error',
        duration: 2500
      })
    },
    httpRequestHandle (file) {
      this.uploadDisable = false
      this.fileList.push({
        'name': file.file.name,
        'file': file.file
      })
      this.$emit('update:attachmentFiles', this.fileList)
    }
  }
}
</script>

<style scoped>

</style>
