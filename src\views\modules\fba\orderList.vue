<template>
  <div class="aui-card--fill">
    <div class="mod-order-list flex_wrap" v-show="!panelShow" ref="listPanel">
      <minSearchCard :class="minSearchCard?'margin_bottom15' : ''" :minSearch="minSearch" :minSearchCard="minSearchCard">
      <el-card class="search_box" shadow="never" v-show="minSearchCard">
        <el-form ref="searchForm" class="form_no_margin" :model="dataForm" @keyup.enter.native="searchHandle()" label-width="100px" >
          <el-row :gutter="10" type="flex">
            <el-col :span="6">
              <three-no-input ref="threeNoInput" :orderAreaList='orderAreaList' :customerOrderNo.sync="dataForm.customerOrderNos" :waybillNo.sync="dataForm.waybillNos" :deliveryNo.sync="dataForm.deliveryNos" :autosize="{ minRows: 7, maxRows: 7}" :noSize="5000"></three-no-input>
            </el-col>
            <el-col :span="16">
              <el-row :gutter="10">
                  <el-col :span="9">
                    <el-form-item :label="$t('coOrder.amazonWarehouse')">
                      <el-select  filterable clearable v-model="dataForm.fbaWarehouseCode"  @change="fbaWarehouseChange">
                        <el-option   v-for="item in fbaWarehouseList" :key="item.id" :label="item.name + (item.name === item.code ? ':  ' : '(' + item.code + '):  ') +  item.street + ' ' + item.city + ((item.district && item.district === item.city) ? '' : (item.district || '' +' '))
                  + ( (item.district && item.province && item.province === item.district ) ? '' : ('，' + item.province|| '' +' ')) + ' '+ item.postcode + '，' + item.country" :value="item.code"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="15">
                    <el-form-item :label="$t('wsComWaybill.logisticsProductCode')" prop="logisticsProductCode">
                      <el-select v-model="dataForm.logisticsProductCode" filterable clearable>
                        <el-option v-for="item in logisticsProductList" :key="item.code" :label="item.name" :value="item.code">
                          <span style="float: left">{{ item.name }}</span>
                          <span style="float: right; color: #DCDFE6; font-size: 13px">{{ item.code }}</span>
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
              </el-row>
              <el-row :gutter="10">
                  <el-col :span="9">
                    <el-form-item :label="$t('coOrder.consigneeCountry')" prop="consigneeCountry">
                      <el-select v-model="dataForm.consigneeCountry" filterable placeholder="" clearable>
                        <el-option v-for="item in countryList" :key="item.code" :label="`${item.name}  ${item.code}`" :value="item.code">
                          <span style="float: left">{{ item.name }}</span>
                          <span style="float: right; color: #DCDFE6; font-size: 13px">{{ item.code }}</span>
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                <el-col :span="15">
                  <el-form-item :label="$t('system.createDate')" prop="createDataArray">
                    <el-date-picker class="width100"  v-model="createDataArray" type="datetimerange" :clearable='false' popper-class='createDateEl'
                                    :start-placeholder="$t('startTime')" :end-placeholder="$t('endTime')"
                                    :default-time="['00:00:00', '23:59:59']" value-format="yyyy-MM-dd HH:mm:ss"></el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col :sm="24" :md="5">
                  <el-form-item :label="$t('coOrder.print')" prop="print">
                    <el-select v-model="dataForm.print" filterable clearable >
                      <el-option v-for="item in printList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="4">
                  <el-form-item label-width="10px" prop="getAsynNoStatus">
                    <el-select v-model="dataForm.getAsynNoStatus" :placeholder="$t('coOrder.getAsynNoStatus')" filterable clearable>
                      <el-option v-for="item in printList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="15">
                  <el-form-item :label="$t('coOrder.orderByMode')" prop="orderByMode">
                    <el-select v-model="dataForm.orderByMode" filterable placeholder="" clearable>
                      <el-option v-for="item in orderByModeList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col :span="9">
                  <el-form-item :label="$t('coOrder.customerOrderNoLike')" prop="customerOrderNoLike">
                    <el-input v-model="dataForm.customerOrderNoLike" :placeholder="$t('coOrder.customerOrderNoLikePlaceHold')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="15">
                  <el-form-item :label="$t('coOrder.splitBatchNo')" prop="splitBatchNo">
                    <el-input v-model="dataForm.splitBatchNo" :placeholder="$t('coOrder.splitBatchNo')" clearable ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <div class="search_box_btn">
              <el-button type="primary" @click="searchHandle()" icon="el-icon-search">查询</el-button>
              <el-button @click.native="resetFormHandle" icon="el-icon-refresh-right">重置</el-button>
              <el-button type="text" @click="minSearch">
                <i class="icon el-icon-caret-top"></i> 隐藏
              </el-button>
            </div>
          </el-row>
        </el-form>
      </el-card>
      </minSearchCard>
      <el-tabs class="flex_tab no_shadow" type="border-card" v-model="activeName" @tab-click="tabsClick" :stretch="false">
        <el-tab-pane v-for="(item, key) in statusArr" :key="key" :name="key">
          <div slot="label" style="min-width: 4em; text-align: center">{{ statusName(item.value) || '&nbsp;' }}<el-badge v-if="isBadge(item)" :value="item.num >0 ? item.num:''" :max="99999" class="item" type="primary"></el-badge>
          </div>
          <!--草稿订单 按钮-->
          <el-row  class="optBtn_panel">
            <el-col :md="12">
              <el-button v-if="item.value==10 && $hasPermission('fba:orderList:forecast')" size="mini" type="primary" plain @click="forecastOrder(item.value,dataListSelections)">{{ $t('forecast') }}</el-button>
              <el-button v-if="showSortPrice === 'Y' && item.value===10 && $hasPermission('fba:orderList:forecast')" size="mini" type="primary" plain @click="batchSortRate(item.value,dataListSelections)">{{ $t('fba.batchSortRate') }}</el-button>
              <el-button v-if="item.value==10 && $hasPermission('fba:orderList:delete')" size="mini" type="primary" plain @click="deleteOrder(item.value,dataListSelections)">{{ $t('delete') }}</el-button>

              <el-button v-if="item.value==11 && $hasPermission('fba:orderList:printLabel')" size="mini" type="primary" plain @click="batchPrintOrder(item.value,dataListSelections)">{{ $t('printLabel') }}</el-button>
              <el-button v-if="item.value==11 && $hasPermission('fba:orderList:cancelForecast')" size="mini" type="primary" plain @click="cancelForecastOrder(item.value,dataListSelections)">{{ $t('cancelForecast') }}</el-button>
              <el-button v-if="item.value==11 && $hasPermission('fba:orderList:printSysBoxMark')" size="mini" type="primary" plain @click="printSysBoxMark">{{ $t('fba.printSysBoxMark') }}</el-button>

              <el-button v-if="item.value==14 && $hasPermission('fba:orderList:printLabel')"  size="mini" type="primary" plain @click="batchPrintOrder(item.value,dataListSelections)">{{ $t('printLabel') }}</el-button>
              <el-button v-if="item.value==15 && $hasPermission('fba:orderList:deletedCancel')" size="mini" type="primary" plain @click="deletedCancelOrder(item.value,dataListSelections)">{{ $t('deletedCancel') }}</el-button>
              <el-button v-if="item.value==15 && $hasPermission('fba:orderList:completeDeleted')" size="mini" type="primary" plain @click="completeDeletedOrder(item.value,dataListSelections)">{{ $t('completeDeleted') }}</el-button>
              <!--保留空格符-->
            </el-col>
            <el-col :md="12" :offset='[10,11,14,15].indexOf(item.value) > -1 ? 0:12' class="text-right">
              <el-button size="mini" type="primary" v-if="$hasPermission('fba:orderList:export')" plain @click="exportHandle()">{{ $t('export') }}</el-button>
<!--              <el-button  size="mini" type="primary" plain @click="batchOrderImport()">{{ $t('fba.batchOrderImport') }}</el-button>-->
            </el-col>
          </el-row>
        </el-tab-pane>
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <ux-grid ref="tableData" v-loading="dataListLoading" :data="dataList"   @selection-change="dataListSelectionChangeHandle"
                   :show-overflow="true" :max-height="tableHeight" size="mini" :widthResize="true" :border="false">
<!--            <ux-table-column label="序号" type="index" width="50"></ux-table-column>-->
<!--             动态显示表格 -->
            <ux-table-column type="checkbox" width='50' fixed="left" ></ux-table-column>
            <ux-table-column v-for="(item) in tableColumnsArr" :key="item.colIndex" show-overflow-tooltip
                             :field="item.prop"  :title="item.label" :resizable="true" :border="false"
                             header-align="center" :align="item.align" :min-width="item.width"
                             :sortable="item.prop === 'createDate'">
              <template slot-scope="scope">
                  <template v-if="item.prop === 'createDate'">
                    <el-tooltip :content="scope.row.createDate | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                      <span>{{formatterFn(scope,item.prop)}}</span>
                    </el-tooltip>
                  </template>
                  <template  v-else-if="item.prop === 'fbaWarehouseCode'">
                    <el-tooltip  effect="light" placement="top">
                      <div slot="content" v-loading='fbaWarehouseCodeLoading'  element-loading-spinner="el-icon-loading">
                        <span class='margin_top10'>{{$t('coOrderConsignee.consigneeCountry')}}：{{scope.row.consigneeCountry || formatterFbaInfo(scope.row.fbaWarehouseCode,'country')}}</span><br/>
                        <span class='margin_top10'>{{$t('coOrderConsignee.consigneePostcode')}}：{{scope.row.consigneePostcode || formatterFbaInfo(scope.row.fbaWarehouseCode,'postcode')}}</span><br/>
                        <span class='margin_top10 margin_right25 '>{{$t('coOrderConsignee.consigneeProvince')}}：{{scope.row.consigneeProvince || formatterFbaInfo(scope.row.fbaWarehouseCode,'province')}}</span>
                        <span class='margin_top10 text-right'>{{$t('coOrderConsignee.consigneeCity')}}：{{scope.row.consigneeCity || formatterFbaInfo(scope.row.fbaWarehouseCode,'city')}}</span><br/>
                        <span class='margin_top10'>{{$t('coOrderConsignee.consigneeAddress')}}：{{scope.row.consigneeAddress || formatterFbaInfo(scope.row.fbaWarehouseCode,'street')}}</span><br/>
                      </div>
                      <a @mouseover="getFbaWarehouseCodeConsigneeInfo(scope.row)">{{scope.row.fbaWarehouseCode}}</a>
                    </el-tooltip>
                  </template>
                  <template v-else-if="item.prop === 'sumList'">
                    <span v-if="!scope.row.sumList || scope.row.sumList.length===0" style='padding-right: 43%;'>
                           <b>--</b>
                    </span>
                    <span v-else v-for="(item, index) in scope.row.sumList" :key="index" >
                      <span style="font-weight:bold;">{{ item.sum | numberFormat(3)}}</span>
                      <span style="color: green;font-size: 8px;padding-left: 3px;" class="order-sum">{{item.currency }}</span>
                    </span>
                  </template>
                <template v-else-if="item.prop === 'asynNoStatus'">
                  <span v-if="scope.row.asynNoStatus.status === 30" class="warning">{{scope.row.asynNoStatus.msg}}</span>
                  <span v-else-if="scope.row.asynNoStatus.status === 0" class="info">{{scope.row.asynNoStatus.msg}}</span>
                  <span v-else-if="scope.row.asynNoStatus.status === 10" class="primary">{{scope.row.asynNoStatus.msg}}</span>
                  <span v-else-if="scope.row.asynNoStatus.status === 20 && scope.row.status !== 10 && scope.row.status !== 15"  class="success">{{scope.row.asynNoStatus.msg}}</span>
                  <span v-else></span>
                </template>
                  <template v-else-if="item.prop === 'print'">
                    <el-badge is-dot class="badge_status" v-if="scope.row.print === 1" type="success" ></el-badge>
                    <el-badge is-dot class="badge_status" v-else type="warning"></el-badge>
                    <span>{{ formatterFn(scope, item.prop) }}</span>
                  </template>
                  <template class="text-overflow" v-else>
                    {{formatterFn(scope,item.prop)}}
                  </template>
              </template>
            </ux-table-column>
            <ux-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="180">
              <template slot="header" slot-scope="scope">
                <span>{{$t('handle')}}</span>
                <el-tooltip :content="$t('tableConfigTooltip')" placement="top">
                  <el-link style="margin-left:10px; font-size:16px" class="el-icon-setting" @click="settingCol(tableColumns)"></el-link>
                </el-tooltip>
              </template>
              <template slot-scope="scope">
                <el-link v-if="showSortPrice === 'Y' && $hasPermission('fba:orderList:forecast') && scope.row.status ===10 && scope.row.orderLogisticsType !== 10"  :underline="false"
                         @click="sortRate(scope.row)" >{{ $t('fba.sortRate') }}</el-link>
                <el-link :underline="false" size="mini"  v-if="$hasPermission('fba:orderList:view')"  @click="viewHandle(scope.row.id)">{{ $t('view') }}</el-link>
                <el-link :underline="false" size="mini" v-if="$hasPermission('fba:orderList:copy')" @click="copyHandle(scope)">{{ $t('copy') }}</el-link>
                <template v-if="scope.row.status <= 10">
                  <el-dropdown size="small" :hide-on-click="true">
                    <a class="el-link el-link--default">
                              <span class="el-dropdown-link el-link--inner">
                                <i class="el-icon-arrow-down el-icon--right"></i>
                              </span>
                    </a>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item  v-if="$hasPermission('fba:orderList:splitOrder') && scope.row.status ===10 && scope.row.orderLogisticsType !== 10">
                        <el-link :underline="false" size="mini"  @click="splitOrder(scope.row)">{{ $t('fba.splitOrder') }}</el-link>
                      </el-dropdown-item>
                      <el-dropdown-item   v-if="scope.row.status===10 && $hasPermission('fba:orderList:update')">
                        <el-link :underline="false" size="mini"  @click="updateHandle(scope)">{{ $t('update') }}</el-link>
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </template>
              </template>
            </ux-table-column>
          </ux-grid>
        </div>
        <!--<span style="float: right;padding-top: 10px;margin-right: 20px;" >已选中 <span style="color: red">{{dataListSelections.length}}</span> 条</span>-->
        <el-pagination background :current-page="page" :page-sizes="[10, 100, 200, 500, 1000]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle" @current-change="pageCurrentChangeHandle"></el-pagination>
      </el-tabs>
    </div>

    <!-- 其他组件 -->

    <!-- 自定义列显示弹窗 -->
    <table-set ref="tableSet"></table-set>
    <el-dialog title="预报进度" :visible.sync="forecastProgressDialogVisible" width="40%" :before-close="forecastProgressDialogCloseHandle" :body-style = "{padding:0}">
      <el-container>
        <el-main>
          <el-row :gutter="20">
            <el-col :span="8" :offset="8">
              <el-progress type="circle" :percentage="forecastProgress" class='fba-forecast-progress-bar'/>
            </el-col>
          </el-row>
        </el-main>
        <el-footer style="padding-top: 10px">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-badge :value="totalCount" class="item" type="warning">
                <el-button size="small">下单总数</el-button>
              </el-badge>
            </el-col>
            <el-col :span="8">
              <el-badge :value="successCount" class="item" type="primary">
                <el-button size="small">成功总数</el-button>
              </el-badge>
            </el-col>
            <el-col :span="8">
              <el-badge :value="failCount" class="item">
                <el-button size="small" @click="showForecastFailInfo">失败总数</el-button>
              </el-badge>
            </el-col>
            <!--<el-col :span="8">-->
              <!--<el-button size="small" type="primary">下单结果导出</el-button>-->
            <!--</el-col>-->
          </el-row>
        </el-footer>
      </el-container>
    </el-dialog>
    <el-dialog title="取消预报进度" :visible.sync="cancelForecastProgressDialogVisible" width="40%" :before-close="unforecastProgressDialogCloseHandle" :body-style = "{padding:0}">
      <el-container>
        <el-main>
          <el-row :gutter="20">
            <el-col :span="8" :offset="8">
              <el-progress type="circle" :percentage="cancelForecastProgress"  class='fba-cancel-forecast-progress-bar'/>
            </el-col>
          </el-row>
        </el-main>
        <el-footer style="padding-top: 10px">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-badge :value="totalCount" class="item" type="warning">
                <el-button size="small">取消总数</el-button>
              </el-badge>
            </el-col>
            <el-col :span="8">
              <el-badge :value="successCount" class="item" type="primary">
                <el-button size="small">成功总数</el-button>
              </el-badge>
            </el-col>
            <el-col :span="8">
              <el-badge :value="failCount" class="item">
                <el-button size="small" @click="showCancelForecastFailInfo">失败总数</el-button>
              </el-badge>
            </el-col>
            <!--<el-col :span="6">
              <el-button size="small" type="primary">取消结果导出</el-button>
            </el-col>-->
          </el-row>
        </el-footer>
      </el-container>
    </el-dialog>
    <el-dialog title="订单拦截" :visible.sync="interceptOrderDialogVisible" width="25%" :body-style = "{padding:0}">
      <el-form ref="interceptOrderDataForm" class="form_no_margin" :model="interceptOrderDataForm" :rules="interceptOrderRules" label-width="80px" >
        <el-row type="flex" justify="center" :gutter="20">
          <el-col :span="20">
            <el-form-item :label="$t('interceptOrderDataForm.remark')" prop="remark">
              <el-input type="textarea" :rows="2" v-model="interceptOrderDataForm.remark" :placeholder="$t('interceptOrderDataForm.remark')"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="interceptOrderHandleClose">取 消</el-button>
        <el-button type="primary" @click="interceptOrderHandleConfirm(dataListSelections)">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 详情页面 -->
    <viewDetail v-if="viewVisible" ref="viewDetail" @backView="backView"></viewDetail>
    <batchImportOrderExcel ref='batchImportOrderExcel'
                         title="fba.batchOrderImport"
                         bodyClass='panel_body'
                         :importExcelUrl="batchOrderImportUrl"
                         :downLoadUrl="downLoadBatchOrderImportTempUrl"
                         v-if='batchImportOrderVisible'
                         @backImportExcel="batchImportOrderExcelBack"
    ></batchImportOrderExcel>
    <exportDetail v-if="exportVisible" ref="exportDetail" @backView="backView"/>
    <!--比价-->
    <co-order-sort-rate-dialog ref="coOrderSortRateDialog" @sortRateAfterHandle="sortRateAfterHandle"></co-order-sort-rate-dialog>
    <!--拆单-->
    <co-order-split-order-dialog ref="coOrderSplitOrderDialog" @splitOrderAfterHandle="splitOrderAfterHandle"></co-order-split-order-dialog>
    <!--比价-->
    <co-order-batch-sort-rate-dialog ref="coOrderBatchSortRateDialog" @sortRateAfterHandle="sortRateAfterHandle"></co-order-batch-sort-rate-dialog>
  </div>
</template>

<script>
// import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
// table 自定义显示
import ExportDetail from '../bd/excel-export-dialog'
import tableSet from '@/components/tableSet'
import threeNoInput from '@/components/three-no-input'
import minSearchCard from '@/components/minSearchCard'
import CoOrderSplitOrderDialog from '@/components/co/co-order-split-order-dialog'
import CoOrderSortRateDialog from '@/components/co/co-order-sortRate-dialog'
import CoOrderBatchSortRateDialog from '@/components/co/co-order-batch-sortRate-dialog'
import listPage from '@/mixins/listPage'
import baseDataApi from '@/api'
import baseData from '@/api/baseData'
import { printFn } from '@/utils/print'
import { formatterType, formatterCodeName, gtmToLtm, formatterShowNameForCode, timestampFormat, numberFormat } from '@/filters/filters'
import ViewDetail from './order-view-detail'
import qs from 'qs'
import Cookies from 'js-cookie'
import DateUtil from '@/utils/date'
import batchImportOrderExcel from '@/components/importExcel/fba-import-template'
import NP from 'number-precision'

const getBeforeMonth = (month) => {
  return new Date(getNowDate() - 3600 * 1000 * 24 * month * 30 + 1)
}
const getNowDate = () => {
  return new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1)
}
export default {
  mixins: [listPage, mixinViewModule],
  data () {
    return {
      searchBoxShow: false,
      fbaWarehouseCodeLoading: false,
      tableColumns: [
        { colIndex: 0, type: '', width: '150', prop: 'customerOrderNo', label: this.$t('coOrder.customerOrderNoOrInWarehouseNo'), align: 'center', isShow: true, disabled: false },
        { colIndex: 1, type: '', width: '140', prop: 'waybillNo', label: this.$t('coOrder.waybillNo'), align: 'center', isShow: true, disabled: false },
        { colIndex: 2, type: '', width: '150', prop: 'deliveryNo', label: this.$t('coOrder.deliveryNo'), align: 'center', isShow: true, disabled: false },
        { colIndex: 12, type: '', width: '150', prop: 'asynNoStatus', label: this.$t('coOrder.asynNoStatus'), align: 'center', isShow: true, disabled: false },
        { colIndex: 3, type: '', width: '120', prop: 'logisticsProductCode', label: this.$t('wsComWaybill.logisticsProductCode'), align: 'center', isShow: true, disabled: false },
        { colIndex: 4, type: '', width: '80', prop: 'fbaWarehouseCode', label: this.$t('fba.fbaWarehouse'), align: 'center', isShow: true, disabled: false },
        { colIndex: 5, type: '', width: '100', prop: 'consigneeCountry', label: this.$t('coOrder.consigneeCountry'), align: 'center', isShow: true, disabled: false },
        { colIndex: 6, type: '', width: '80', prop: 'packageQty', label: this.$t('fba.totalBox'), align: 'center', isShow: true, disabled: false },
        { colIndex: 7, type: '', width: '100', prop: 'forecastWeightD', label: this.$t('coOrder.forecastWeight'), align: 'center', isShow: true, disabled: false },
        { colIndex: 8, type: '', width: '120', prop: 'sumList', label: this.$t('coOrder.forecastMoney'), align: 'right', isShow: true, disabled: false },
        { colIndex: 9, type: '', width: '120', prop: 'splitBatchNo', label: this.$t('coOrder.splitBatchNo'), align: 'center', isShow: true, disabled: false },
        { colIndex: 10, type: '', width: '70', prop: 'status', label: this.$t('coOrder.status'), align: 'center', isShow: true, disabled: false },
        { colIndex: 11, type: '', width: '70', prop: 'print', label: this.$t('coOrder.print'), align: 'center', isShow: true, disabled: false },
        { colIndex: 13, type: '', width: '150', prop: 'createDate', label: this.$t('system.createDate'), align: 'center', isShow: true, disabled: false }
      ],
      mixinViewModuleOptions: {
        getDataListURL: '/co/order/page',
        getDataListURLOfRequestType: 'post',
        exportURL: '/co/order/export',
        getDataListIsPage: true
      },
      dataForm: {
        customerOrderNos: '',
        // 模糊客户单号
        customerOrderNoLike: '',
        waybillNos: '',
        deliveryNos: '',
        consignee: '',
        logisticsProductCode: '',
        consigneeCountry: '',
        createDate1: null,
        createDate2: null,
        customerId: this.$store.state.user.customerId,
        status: 10,
        orderByMode: 11,
        orderLogisticsType: 12,
        orderField: '',
        order: '',
        getAsynNoStatus: '',
        createDate: null
      },
      createDataArray: [getBeforeMonth(2), getNowDate()],
      statusList: [],
      orderStatusList: [],
      orderByModeList: [],
      printList: [],
      countryList: [],
      logisticsProductByAllList: [],
      logisticsProductList: [],
      activeName: 'draft',
      tableName: 'client-in-order',
      tabsName: '',
      statusArr: {
        // 所有订单
        'all': {
          value: 14,
          num: 0
        },
        // 草稿
        'draft': {
          value: 10,
          num: 0
        },
        // 预报
        'forecast': {
          value: 11,
          num: 0
        },
        // 入库中
        'inWarehousing': {
          value: 18,
          num: 0
        },
        // 入库
        'inWarehouse': {
          value: 12,
          num: 0
        },
        // 出库中
        'outWarehousing': {
          value: 19,
          num: 0
        },
        // 出库
        'outWarehouse': {
          value: 13,
          num: 0
        },
        'returned': {
          value: 16
          // num: 0
        },
        // 删除
        'deleted': {
          value: 15
          // num: 0
        }
      },
      setInte: '', // 定时任务
      // 订单查询
      orderAreaList: [
        {
          label: '客户单号',
          value: 1
        },
        {
          label: '运单号',
          value: 2
        },
        {
          label: '派送单号',
          value: 3
        }
      ],
      showSortPrice: 'N',
      orderAreaVal: 1,
      forecastProgressDialogVisible: false,
      exportVisible: false,
      forecastProgress: 0,
      cancelForecastProgressDialogVisible: false,
      cancelForecastProgress: 0,
      totalCount: 0,
      successCount: 0,
      failCount: 0,
      interceptOrderDialogVisible: false,
      batchImportOrderVisible: false,
      interceptOrderDataForm: {
        remark: null,
        ids: []
      },
      forecastFailInfoList: [],
      fbaWarehouseList: [],
      unforecastFailInfoList: []
    }
  },
  watch: {
    createDataArray: {
      handler (value, oldName) {
        if (value !== '' && value !== null && value !== undefined) {
          this.dataForm.createDate1 = timestampFormat(value[0])
          this.dataForm.createDate2 = timestampFormat(value[1])
        } else {
          this.createDataArray = [getBeforeMonth(2), getNowDate()]
        }
      },
      // immediate: true 第一次初始化的时候会被执行 immediate: false 第一次初始化的时候不会被执行
      immediate: true
    }
  },
  computed: {
    interceptOrderRules () {
      return {
        remark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    },
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        if (this.activeName === 'draft' && item.prop === 'sumList') {
          item.isShow = false
        } else if (this.activeName !== 'draft' && item.prop === 'sumList') {
          item.isShow = true
        } else if (item.prop === 'asynNoStatus') {
          if (this.activeName === 'forecast' || this.activeName === 'inWarehouse') {
            this.$set(item, 'isShow', true)
          } else {
            this.$set(item, 'isShow', false)
          }
        }
        return item.isShow
      })
      this.$nextTick(() => {
        this.$refs.tableData.doLayout()
      })
      return arr
    },
    panelShow () {
      let ret = false
      if (this.addOrUpdateVisible) {
        ret = true
      } else if (this.viewVisible) {
        ret = true
      } else if (this.batchImportOrderVisible) {
        ret = true
      }
      return ret
    },
    // 赋值导入URL
    batchOrderImportUrl() {
      return `${this.$baseUrl}/fba/order/batchOrderImport`
    },
    downLoadBatchOrderImportTempUrl() {
      return `xls/FBA-batchOrderImport.xlsx`
    }
  },
  created () {
    // 获取数据字典
    this.getDict()
    // 获取基础数据
    this.getBaseData()
  },
  activated () {
    // 获取汇总数据
    this.searchHandle()
    this.getStatusCount()
    this.getShowSortPrice()
  },
  methods: {
    async getDict () {
      // 获取相关字典
      this.statusList = await this.getDictTypeList('orderListTab') // 获取订单列表tab
      this.orderStatusList = await this.getDictTypeList('OrderStatus') // 订单状态
      this.orderByModeList = await this.getDictTypeList('OrderOrderByMode')// 排序模式
      this.printList = await this.getDictTypeList('yesOrNo')// 排序模式
    },
    async getBaseData () {
      this.logisticsProductByAllList = await baseData(baseDataApi.listAllByCurrent + '?logisticsType=12')
      this.logisticsProductList = await baseData(baseDataApi.enableLogisticsProductByCurrent + '?logisticsType=12')
      this.countryList = await baseData(baseDataApi.countryList)
      this.fbaWarehouseList = await baseData(baseDataApi.fbaWarehouseList, { status: 1 })
    },
    // 显示比价操作
    getShowSortPrice () {
      this.$http.get(`/sys/params/getValueByCode/CLIENT_SHOW_SORT_PRICE`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.showSortPrice = res.data
      }).catch(() => {})
    },
    async getFbaWarehouseCodeConsigneeInfo(row) {
      if (!row.fbaWarehouseCode) {
        return
      }
      this.fbaWarehouseCodeLoading = true
      let consigneeInfo = await baseData(`/co/orderconsignee/${row.id}`)
      this.fbaWarehouseCodeLoading = false
      if (!consigneeInfo) {
        return
      }
      this.$set(row, 'consigneeCountry', consigneeInfo.consigneeCountry)
      this.$set(row, 'consigneeProvince', consigneeInfo.consigneeProvince)
      this.$set(row, 'consigneePostcode', consigneeInfo.consigneePostcode)
      this.$set(row, 'consigneeCity', consigneeInfo.consigneeCity)
      this.$set(row, 'consigneeAddress', consigneeInfo.consigneeAddress)
    },
    // 格式化FBA仓库显示信息
    formatterFbaInfo (code, prop) {
      let value
      switch (prop) {
        case 'country':
          value = formatterShowNameForCode(code, this.fbaWarehouseList, 'country')
          break
        case 'province':
          value = formatterShowNameForCode(code, this.fbaWarehouseList, 'province')
          break
        case 'postcode':
          value = formatterShowNameForCode(code, this.fbaWarehouseList, 'postcode')
          break
        case 'city':
          value = formatterShowNameForCode(code, this.fbaWarehouseList, 'city')
          break
        case 'street':
          value = formatterShowNameForCode(code, this.fbaWarehouseList, 'street')
          break
        default:
          break
      }
      return value
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'createDate':
          // value = timestampFormat(scope.row.createDate, 'YYYY-MM-DD')
          value = timestampFormat(gtmToLtm(scope.row.createDate), 'YYYY-MM-DD')
          break
        case 'consigneeCountry':
          value = formatterCodeName(scope.row.consigneeCountry, this.countryList)
          break
        case 'logisticsProductCode':
          value = formatterCodeName(scope.row.logisticsProductCode, this.logisticsProductByAllList)
          break
        case 'forecastWeightD':
          value = scope.row.forecastWeightD + (scope.row.standardUnit === 0 ? ' 千克' : ' 磅')
          break
        case 'print':
          value = formatterType(scope.row.print, this.printList)
          break
        case 'status':
          value = formatterType(scope.row.status, this.orderStatusList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    // 自定义列显示
    settingCol (tableColumns) {
      this.$nextTick(() => {
        this.$refs['tableSet'].init(this.tableName, tableColumns)
      })
    },
    statusName (val) {
      let name = ''
      this.statusList.forEach((item) => {
        if (item.dictValue === val) {
          name = item.dictName
        }
      })
      return name
    },
    isBadge (item) {
      let ret = true
      if (item.value === 70) {
        ret = false
        return ret
      }
      return ret
    },
    tabsClick (tab, event) {
      switch (tab.name) {
        case 'draft':
          this.dataForm.print = ''
          this.dataForm.status = 10
          this.searchHandle()
          break
        case 'forecast':
          this.dataForm.status = 11
          this.searchHandle()
          break
        case 'inWarehouse':
          this.dataForm.print = ''
          this.dataForm.status = 12
          this.searchHandle()
          break
        case 'inWarehousing':
          this.dataForm.print = ''
          this.dataForm.status = 18
          this.searchHandle()
          break
        case 'outWarehouse':
          this.dataForm.print = ''
          this.dataForm.status = 13
          this.searchHandle()
          break
        case 'outWarehousing':
          this.dataForm.print = ''
          this.dataForm.status = 19
          this.searchHandle()
          break
        case 'all':
          this.dataForm.status = ''
          this.searchHandle()
          break
        case 'returned':
          this.dataForm.print = ''
          this.dataForm.status = 16
          this.searchHandle()
          break
        case 'deleted':
          this.dataForm.print = ''
          this.dataForm.status = 15
          this.searchHandle()
          break
        default:
          break
      }
    },
    fbaWarehouseChange () {
      // todo 查仓库
    },
    // 草稿订单 预报
    async forecastOrder (activeValue, data) {
      if (data.length <= 0) {
        return this.$message({
          message: this.$t('prompt.forecast'),
          type: 'warning',
          duration: 1000
        })
      }
      this.forecastProgress = 0
      this.totalCount = 0
      this.successCount = 0
      this.failCount = 0
      this.forecastProgressDialogVisible = true
      this.totalCount = data.length
      this.forecastFailInfoList = []
      let forecastLoading = null
      // 制造假进度
      let forecastProgressInterval = setInterval(() => {
        this.forecastProgress = NP.plus(this.forecastProgress, 0.01)
      }, 2000)

      for (let i = 1; i < data.length + 1; i++) {
        let currentId = data[i - 1].id
        let currentData = this.dataList.filter(item => item.id === currentId)[0]
        if (!currentData.packageQty) {
          this.forecastProgressDialogVisible = false
          return this.$message.error(currentData.customerOrderNo + '需要导入箱货信息')
        }
        let result = await this.putForecastOrder(currentId)
        if (result.code === 0) {
          if (result.data.isSuccess) {
            this.successCount++
          } else {
            this.failCount++
            this.forecastFailInfoList.push({ customerOrderNo: result.data.customerOrderNo, message: result.data.message })
          }
        } else {
          this.failCount++
          this.forecastFailInfoList.push({ customerOrderNo: null, message: result.data.message })
        }
        this.forecastProgress = Number(Number(i / this.totalCount * 100).toFixed(0))
        // 假进度控制
        forecastLoading = this.fakeProgressControl(this.forecastProgress, forecastLoading, forecastProgressInterval, 'fba-forecast-progress-bar')
      }
      if (forecastLoading) {
        forecastLoading.close()
      }
      // 无失败情况下关闭
      if (this.failCount === 0) {
        setTimeout(() => {
          this.$message.success('预报已完成')
          this.forecastProgressDialogVisible = false
          // 切换tab页面
          this.activeName = 'forecast'
          this.tabsClick({ name: 'forecast' })
        }, 2000)
      } else {
        // 失败情况下
        this.activeName = 'draft'
        this.tabsClick({ name: 'draft' })
      }
    },
    putForecastOrder (id) {
      return new Promise((resolve, reject) => {
        this.$http.put('/co/order/forecast', id, { headers: { 'Content-Type': 'application/json;charset=UTF-8' } }).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          resolve(res)
        }).catch(() => {})
      })
    },
    // 草地订单 删除
    deleteOrder (activeValue, data) {
      if (data.length <= 0) {
        return this.$message({
          message: this.$t('prompt.deleted'),
          type: 'warning',
          duration: 1000
        })
      }
      let idArray = []
      for (let i = 0; i < data.length; i++) {
        idArray.push(data[i].id)
      }
      this.$confirm(this.$t('prompt.info', { 'handle': this.$t('delete') }), this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        this.$http.delete('/co/order', { 'data': idArray }).then(({ data: res }) => {
          if (res.code !== 0) {
            // 切换tab页面
            this.activeName = 'draft'
            this.tabsClick({ name: 'draft' })
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 1000,
            onClose: () => {
              this.activeName = 'draft'
              this.tabsClick({ name: 'draft' })
            }
          })
        }).catch(() => {})
      }).catch(() => {})
    },
    batchOrderImport () {
      this.batchImportOrderVisible = true
      this.$nextTick(() => {
        this.$refs.batchImportOrderExcel.init()
      })
    },
    batchImportOrderExcelBack () {
      this.batchImportOrderVisible = false
      this.queryPageByParam()
    },
    sortRateAfterHandle () {
      // 切换tab页面
      this.activeName = 'forecast'
      this.tabsClick({ name: 'forecast' })
    },
    splitOrderAfterHandle () {
      // 切换tab页面
      this.activeName = 'draft'
      this.tabsClick({ name: 'draft' })
    },
    // 草稿/入库/出库订单 导出
    exportOrder () {
      if (this.getTotal() < 1) {
        // console.log(this.total)
        return this.$message({
          message: this.$t('noData'),
          type: 'warning',
          duration: 1000
        })
      }
      let params = qs.stringify({
        'token': Cookies.get('cs_token'),
        ...this.dataForm
      })
      window.location.href = `${this.$baseUrl}${this.mixinViewModuleOptions.exportURL}?${params}`
    },
    // 导出
    exportHandle () {
      let masterDTOName = 'CoOrderDTO'
      this.exportVisible = true
      this.$nextTick(() => {
        this.$refs.exportDetail.dataForm.masterDTOName = masterDTOName
        this.$refs.exportDetail.queryDataForm = this.dataForm
        this.$refs.exportDetail.init()
      })
    },
    async getTotal () {
      // 查询订单
      await this.getDataList()
      // eslint-disable-next-line no-unmodified-loop-condition
      return this.total
    },
    // 预报/所有订单 批量打印
    batchPrintOrder (activeValue, data) {
      if (data.length <= 0) {
        return this.$message({
          message: this.$t('prompt.batchPrint'),
          type: 'warning',
          duration: 1000
        })
      }
      let ids = []
      for (let i = 0; i < data.length; i++) {
        if (data[i].status !== 11 && data[i].status !== 12 && data[i].status !== 13 && data[i].status !== 19) {
          return this.$message.warning('请选择已预报或者已入库或者出库中或者已出库数据')
        } else {
          ids.push(data[i].id)
        }
      }
      printFn('/co/order/customer/label?nos=' + ids + '&orderByMode=11')
    },
    printSysBoxMark () {
      let idList = this.dataListSelections.map(item => item.id)
      if (idList.length <= 0) {
        return this.$message({
          message: this.$t('prompt.actionStatusBatch'),
          type: 'warning',
          duration: 1000
        })
      }
      let token = Cookies.get('cs_token') || ''
      let printTime = DateUtil.dateFormat(new Date(), 'yyyy-MM-dd HH:mm:ss')
      let url = '/co/order/printShippingMarks?token=' + token + '&ids=' + idList + '&printTime=' + printTime // 有效 服务器配置跨域处理
      url = url + '&.pdf'
      printFn(url)
    },
    // 假进度控制
    fakeProgressControl (progress, loadingObj, intervalId, progressClass) {
      if (progress >= 100 && loadingObj) {
        clearInterval(intervalId)
        this.$nextTick(() => {
          loadingObj.close()
        })
      } else if (progress >= 100 && !loadingObj) {
        clearInterval(intervalId)
      } else if (progress >= 90) {
        clearInterval(intervalId)
        let targetClass = '.' + progressClass + ' .el-progress__text'
        loadingObj = this.$loading({
          target: targetClass,
          fullscreen: false
        })
        let selectors = '.' + progressClass + ' .el-loading-mask'
        this.$nextTick(() => {
          document.querySelector(selectors).style.backgroundColor = 'transparent'
        })
      }
      return loadingObj
    },
    // 预报订单 取消预报
    async cancelForecastOrder (activeValue, data) {
      if (data.length <= 0) {
        return this.$message({
          message: this.$t('prompt.forecast'),
          type: 'warning',
          duration: 1000
        })
      }
      for (let i = 0; i < data.length; i++) {
        if (data[i].status !== 11) {
          return this.$message.warning('请选择已预报数据')
        }
      }
      this.cancelForecastProgress = 0
      this.totalCount = 0
      this.successCount = 0
      this.failCount = 0
      this.cancelForecastProgressDialogVisible = true
      this.totalCount = data.length
      this.unforecastFailInfoList = []
      let cancelForecastLoading = null
      // 制造假进度
      let cancelForecastProgressInterval = setInterval(() => {
        this.cancelForecastProgress = NP.plus(this.cancelForecastProgress, 0.01)
      }, 2000)

      for (let i = 1; i < data.length + 1; i++) {
        let result = await this.putCancelForecastOrder(data[i - 1].id)
        if (result.code === 0) {
          if (result.data.isSuccess) {
            this.successCount++
          } else {
            this.failCount++
            this.unforecastFailInfoList.push({ customerOrderNo: result.data.customerOrderNo, message: result.data.message })
          }
        } else {
          this.failCount++
          this.unforecastFailInfoList.push({ customerOrderNo: null, message: result.data.message })
        }
        this.cancelForecastProgress = Number(Number(i / this.totalCount * 100).toFixed(0))
        // 假进度控制
        cancelForecastLoading = this.fakeProgressControl(this.cancelForecastProgress, cancelForecastLoading, cancelForecastProgressInterval, 'fba-cancel-forecast-progress-bar')
      }
      if (cancelForecastLoading) {
        cancelForecastLoading.close()
      }
      // 无失败情况下关闭
      if (this.failCount === 0) {
        setTimeout(() => {
          this.$message.success('预报已取消')
          this.cancelForecastProgressDialogVisible = false
        }, 2000)
      }
      this.searchHandle()
      // 切换tab页面
      this.activeName = 'forecast'
      this.tabsClick({ name: 'forecast' })
    },
    putCancelForecastOrder (id) {
      return new Promise((resolve, reject) => {
        this.$http.put('/co/order/cancelForecast', id, { headers: { 'Content-Type': 'application/json;charset=UTF-8' } }).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          resolve(res)
        }).catch(() => {})
      })
    },
    // 所有订单 拦截
    interceptOrder (activeValue, data) {
      if (data.length <= 0) {
        return this.$message({
          message: this.$t('prompt.intercept'),
          type: 'warning',
          duration: 1000
        })
      }
      for (let i = 0; i < data.length; i++) {
        if (data[i].status !== 12 && data[i].status !== 13) {
          return this.$message.warning('请选择已入库或者已出库数据')
        }
      }
      this.interceptOrderDataForm.remark = ''
      this.interceptOrderDialogVisible = true
    },
    // 删除订单 撤销
    deletedCancelOrder (activeValue, data) {
      if (data.length <= 0) {
        return this.$message({
          message: this.$t('prompt.deletedCancel'),
          type: 'warning',
          duration: 1000
        })
      }
      let ids = []
      for (let i = 0; i < data.length; i++) {
        ids.push(data[i].id)
      }
      this.$http.put('/co/order/undelete', ids).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 1000,
          onClose: () => {
            this.searchHandle()
          }
        })
      }).catch(() => {})
    },
    // 删除订单 彻底删除
    completeDeletedOrder (activeValue, data) {
      if (data.length <= 0) {
        return this.$message({
          message: this.$t('prompt.completeDeleted'),
          type: 'warning',
          duration: 1000
        })
      }
      let ids = data.map(item => item.id)
      this.$confirm('彻底删除将清除数据，请小心使用！', this.$t('prompt.title'), {
        distinguishCancelAndClose: true,
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        this.$http.post('/co/order/thoroughDel', ids).then(({ data: res }) => {
          if (res.code !== 0) {
            this.queryPageByParam()
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 1000,
            onClose: () => {
              this.queryPageByParam()
            }
          })
        }).catch(() => {})
      }).catch(() => {
        this.$refs.tableData.clearSelection()
      })
    },
    areaTabClick (item) {
      this.orderAreaVal = item.value
    },
    searchHandle () {
      switch (this.dataForm.orderByMode) {
        case 10:
          this.dataForm.order = 'asc'
          this.dataForm.orderField = 'id'
          break
        case 11:
          this.dataForm.order = 'desc'
          this.dataForm.orderField = 'id'
          break
        case 12:
          this.dataForm.order = 'asc'
          this.dataForm.orderField = 'customer_order_no'
          break
        case 13:
          this.dataForm.order = 'desc'
          this.dataForm.orderField = 'customer_order_no'
          break
      }
      let initFlag = this.$refs.threeNoInput.setValue()
      if (initFlag) {
        // 查询
        this.queryPageByParam()
        this.getStatusCount()
      }
    },
    // 分页, 每页条数
    pageSizeChangeHandle (val) {
      switch (this.dataForm.orderByMode) {
        case 10:
          this.dataForm.order = 'asc'
          this.dataForm.orderField = 'id'
          break
        case 11:
          this.dataForm.order = 'desc'
          this.dataForm.orderField = 'id'
          break
        case 12:
          this.dataForm.order = 'asc'
          this.dataForm.orderField = 'customer_order_no'
          break
        case 13:
          this.dataForm.order = 'desc'
          this.dataForm.orderField = 'customer_order_no'
          break
      }
      this.page = 1
      this.limit = val
      let initFlag = this.$refs.threeNoInput.setValue()
      if (initFlag) {
        // 查询条件预缓存到后台
        let params = {
          order: this.order,
          orderField: this.orderField,
          page: this.mixinViewModuleOptions.getDataListIsPage ? this.page : null,
          limit: this.mixinViewModuleOptions.getDataListIsPage ? this.limit : null,
          ...this.dataForm
        }
        this.$http.post('/common/cacheQueryParams', params).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          let tempDataForm = { ...this.dataForm }
          this.dataForm = { 'queryId': res.data }
          // 查询
          this.getDataList()
          this.dataForm = { ...tempDataForm }
        }).catch(() => {})
      }
    },
    // 分页, 当前页
    pageCurrentChangeHandle (val) {
      switch (this.dataForm.orderByMode) {
        case 10:
          this.dataForm.order = 'asc'
          this.dataForm.orderField = 'id'
          break
        case 11:
          this.dataForm.order = 'desc'
          this.dataForm.orderField = 'id'
          break
        case 12:
          this.dataForm.order = 'asc'
          this.dataForm.orderField = 'customer_order_no'
          break
        case 13:
          this.dataForm.order = 'desc'
          this.dataForm.orderField = 'customer_order_no'
          break
      }
      this.page = val
      let initFlag = this.$refs.threeNoInput.setValue()
      if (initFlag) {
        // 查询条件预缓存到后台
        let params = {
          order: this.order,
          orderField: this.orderField,
          page: this.mixinViewModuleOptions.getDataListIsPage ? this.page : null,
          limit: this.mixinViewModuleOptions.getDataListIsPage ? this.limit : null,
          ...this.dataForm
        }
        this.$http.post('/common/cacheQueryParams', params).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          let tempDataForm = { ...this.dataForm }
          this.dataForm = { 'queryId': res.data }
          // 查询
          this.getDataList()
          this.dataForm = { ...tempDataForm }
        }).catch(() => {})
      }
    },
    resetFormHandle () {
      this.createDataArray = [getBeforeMonth(2), getNowDate()]
      this.$refs.threeNoInput.clearValue()
      this._resetForm('searchForm')
    },
    orderViewHandle (data) {
      this.$router.push({
        name: 'co-orderEntry',
        query: {
          orderId: data.row.id,
          t: +new Date()
        }
      })
    },
    sortRate (row) {
      this.$refs.coOrderSortRateDialog.init(row.id)
    },
    splitOrder (row) {
      this.$refs.coOrderSplitOrderDialog.dataForm.id = row.id
      this.$refs.coOrderSplitOrderDialog.orderForm.forecastWeightD = row.forecastWeightD
      this.$refs.coOrderSplitOrderDialog.orderForm.customerOrderNo = row.customerOrderNo
      this.$refs.coOrderSplitOrderDialog.orderForm.standardUnit = row.standardUnit
      this.$refs.coOrderSplitOrderDialog.orderForm.packageQty = row.packageQty
      this.$refs.coOrderSplitOrderDialog.init()
    },
    batchSortRate (activeValue, data) {
      if (data.length <= 0) {
        return this.$message({
          message: this.$t('prompt.batchPrint'),
          type: 'warning',
          duration: 1000
        })
      }
      let ids = []
      for (let i = 0; i < data.length; i++) {
        if (data[i].status !== 10) {
          return this.$message.warning('请选择草稿箱数据')
        } else {
          ids.push(data[i].id)
        }
      }
      this.$refs.coOrderBatchSortRateDialog.init(ids)
    },
    copyHandle (data) {
      this.$router.push({ name: 'fba-orderEntry', query: { orderId: data.row.id, opType: 'fba.header.copy' } })
    },
    updateHandle (data) {
      this.$router.push({ name: 'fba-orderEntry', query: { orderId: data.row.id, opType: 'fba.header.update' } })
    },
    getStatusCount () {
      this.$http.post('/co/order/getStatusCount', this.dataForm).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.statusArr['draft'].num = res.data[this.statusArr['draft'].value]
        this.statusArr['forecast'].num = res.data[this.statusArr['forecast'].value]
        this.statusArr['inWarehouse'].num = res.data[this.statusArr['inWarehouse'].value]
        this.statusArr['inWarehousing'].num = res.data[this.statusArr['inWarehousing'].value]
        this.statusArr['outWarehouse'].num = res.data[this.statusArr['outWarehouse'].value]
        this.statusArr['outWarehousing'].num = res.data[this.statusArr['outWarehousing'].value]
        this.statusArr['all'].num = res.data[0]
        this.statusArr['deleted'].num = res.data[this.statusArr['deleted'].value]
        this.statusArr['returned'].num = res.data[this.statusArr['returned'].value]
      }).catch(() => {})
    },
    forecastProgressDialogCloseHandle () {
      if (this.totalCount !== this.successCount + this.failCount) {
        this.$message.warning('预报未完成请勿关闭')
        return false
      }
      this.forecastProgressDialogVisible = false
    },
    unforecastProgressDialogCloseHandle () {
      if (this.totalCount !== this.successCount + this.failCount) {
        this.$message.warning('预报未完成请勿关闭')
        return false
      }
      this.cancelForecastProgressDialogVisible = false
    },
    interceptOrderHandleClose () {
      this.interceptOrderDialogVisible = false
    },
    // 订单拦截弹出框
    interceptOrderHandleConfirm (data) {
      this.$refs.interceptOrderDataForm.validate((valid) => {
        if (!valid) {
          return false
        }
        let ids = []
        for (let i = 0; i < data.length; i++) {
          ids.push(data[i].id)
        }
        this.interceptOrderDataForm.ids = ids
        alert('待完善后台')
        // this.$http.put('/co/order/forecast', this.interceptOrderDataForm, { headers: { 'Content-Type': 'application/json;charset=UTF-8' } }).then(({ data: res }) => {
        //   if (res.code !== 0) {
        //     return this.$message.error(res.msg)
        //   }
        // }).catch(() => {})
      })
    },
    showForecastFailInfo () {
      let erronInfo = ''
      for (let i = 0; i < this.forecastFailInfoList.length; i++) {
        let str1 = this.$t('coOrder.customerOrderNo') + '[' + this.forecastFailInfoList[i].customerOrderNo + ']:' + this.forecastFailInfoList[i].message + ';<br/>'
        let str2 = this.forecastFailInfoList[i].message + ';<br/>'
        erronInfo += this.forecastFailInfoList[i].customerOrderNo ? str1 : str2
      }
      return this.$message({
        showClose: true,
        dangerouslyUseHTMLString: true,
        message: erronInfo,
        type: 'error'
      })
    },
    showCancelForecastFailInfo () {
      let erronInfo = ''
      for (let i = 0; i < this.unforecastFailInfoList.length; i++) {
        erronInfo += this.$t('coOrder.customerOrderNo') + '[' + this.unforecastFailInfoList[i].customerOrderNo + ']:' + this.unforecastFailInfoList[i].message + ';<br/>'
      }
      return this.$message({
        showClose: true,
        message: erronInfo,
        center: true,
        type: 'error'
      })
    }
  },
  filters: {
    formatterType,
    formatterCodeName,
    gtmToLtm,
    numberFormat,
    timestampFormat,
    filterFileName: function (value) {
      if (value && value.length > 8) {
        value = value.slice((value.lastIndexOf('/') + 1), value.length)
      }
      return value
    }
  },
  components: {
    CoOrderSplitOrderDialog,
    CoOrderSortRateDialog,
    CoOrderBatchSortRateDialog,
    tableSet,
    threeNoInput,
    ViewDetail,
    minSearchCard,
    batchImportOrderExcel,
    ExportDetail
  }
}
</script>
<style lang="scss" scoped>
.search_box {
  .search_box_btn{
    min-width: 100px !important;
  }
}
</style>
<style lang='scss'>
.createDateEl {
  .el-picker-panel__footer{
    .el-button:first-of-type {
      display: none !important;
    }
  }
}
</style>
