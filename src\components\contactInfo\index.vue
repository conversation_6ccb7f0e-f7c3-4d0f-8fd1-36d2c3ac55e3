<template>
  <el-form class="clearfix" :model="dataForm" :rules="contactInfo_rule" ref="contactInfo_Form" :label-width="$i18n.locale === 'en-US' ? '120px' : '120px'">
    <el-row type="flex" justify="center">
      <el-col :md="{span:12}" style="min-width: 200px">
        <el-form-item :label="$t('bdContactInfo.contact')" prop="contact">
          <el-input v-model="dataForm.contact" :maxlength="64" :placeholder="$t('bdContactInfo.contact')"></el-input>
        </el-form-item>
      </el-col>
      <el-col :md="{span:12}">
        <el-form-item :label="$t('bdContactInfo.phone')" prop="phone">
          <el-input v-model="dataForm.phone" :maxlength="32" :placeholder="$t('bdContactInfo.phone')"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :md="{span:12}">
        <el-form-item :label="$t('bdContactInfo.fax')" prop="fax">
          <el-input v-model="dataForm.fax" :maxlength="32" :placeholder="$t('bdContactInfo.fax')"></el-input>
        </el-form-item>
      </el-col>
      <el-col :md="{span:12}">
        <el-form-item :label="$t('bdContactInfo.email')" prop="email">
          <el-input v-model="dataForm.email" :maxlength="64" :placeholder="$t('bdContactInfo.email')"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :md="{span:12}">
        <el-form-item :label="$t('bdContactInfo.country')" prop="country" >
          <el-select class="w-percent-100"  v-model="dataForm.country" clearable :placeholder="$t('select')" filterable
                      remote reserve-keyword :remote-method="getCountryListByCodeOrName" :loading="loading">
            <el-option v-for="item in newCountryList" :key="item.code" :label="item.name" :value="item.code"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :md="{span:12}">
        <el-form-item :label="$t('bdContactInfo.province')"  prop="province">
          <el-input v-model="dataForm.province" :maxlength="32" :placeholder="$t('bdContactInfo.province')" style="min-width: 130px"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :md="{span:12}">
        <el-form-item :label="$t('bdContactInfo.city')" prop="city">
          <el-input v-model="dataForm.city" :maxlength="32" :placeholder="$t('bdContactInfo.city')" style="min-width: 130px"></el-input>
        </el-form-item>
      </el-col>
      <el-col :md="{span:12}">
        <el-form-item :label="$t('bdContactInfo.district')" prop="district">
          <el-input v-model="dataForm.district" :maxlength="32" :placeholder="$t('bdContactInfo.district')" style="min-width: 130px"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :md="{span:12}">
        <el-form-item :label="$t('bdContactInfo.postcode')" prop="postcode">
          <el-input v-model="dataForm.postcode" :maxlength="16" :placeholder="$t('bdContactInfo.postcode')"></el-input>
        </el-form-item>
      </el-col>
      <el-col :md="{span:12}">
        <el-form-item :label="$t('bdContactInfo.houseNo')" prop="houseNo">
          <el-input v-model="dataForm.houseNo" :maxlength="16" :placeholder="$t('bdContactInfo.houseNo')"></el-input>
        </el-form-item>
      </el-col>
      <el-col :md="{span:12}">
        <el-form-item :label="$t('bdContactInfo.address')" prop="address">
          <el-input v-model="dataForm.address" :maxlength="255" :placeholder="$t('bdContactInfo.address')"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
import { isEmail, isBank } from '@/utils/validate'
import comMixins from '@/mixins/comMixins'
export default {
  mixins: [comMixins],
  name: 'contactInfo',
  props: {
    propData: {}
  },
  data () {
    return {
      loading: false,
      dataForm: {
        id: '',
        source: '',
        sourceId: '',
        contact: '',
        phone: '',
        fax: '',
        email: '',
        country: '',
        province: '',
        city: '',
        district: '',
        houseNo: '',
        postcode: '',
        address: ''
      },
      comMixins: {
        // isGetCountryList: true
      },
      newCountryList: []
    }
  },
  created () {
    this.$nextTick(() => {
      this.dataForm = {
        ...this.propData
      }
      this.getCountryListByCodeOrName(this.dataForm.country)
    })
  },
  computed: {
    contactInfo_rule () {
      const validateEmail = (rule, value, callback) => {
        if (!isBank(value) && !isEmail(value)) {
          return callback(new Error(this.$t('validate.format', { 'attr': this.$t('bdContactInfo.email') })))
        }
        callback()
      }
      return {
        contact: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        phone: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        email: [
          { validator: validateEmail, trigger: 'blur' }
        ],
        country: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        province: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        city: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        postcode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        address: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    // 根据二字简码获取国家列表
    getCountryListByCodeOrName (query) {
      if (query !== '') {
        this.loading = true
        setTimeout(() => {
          this.loading = false
          this.$http.get(`/bd/region/listByCodeOrName?codeOrName=` + query).then(({ data: res }) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.newCountryList = res.data
          }).catch(() => { })
        }, 200)
      } else {
        this.newCountryList = []
      }
    },
    validated () {
      let ret = false
      this.$refs['contactInfo_Form'].validate((valid) => {
        if (valid) {
          ret = true
        }
      })
      if (ret) {
        this.$emit('onContactData', this.dataForm)
      }
      return ret
    },
    getData () {
      return this.dataForm
    }
  }
}
</script>

<style scoped>

</style>
