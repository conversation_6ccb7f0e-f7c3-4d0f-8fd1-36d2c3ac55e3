<template>
  <div class="add-body panel_body">
    <div class="addOrUpdatePanel" >
      <el-tabs class="flex_tab no_shadow" type="border-card" v-model="activeName" @tab-click="tabsClick" :stretch="false" style="height: 520px" >
        <el-tab-pane label="物流产品设置" name="setLogisticsProductCode" >
          <el-row :gutter="10" type="flex">
            <el-col :span="11">
              <area-box title='常用列表'>
              <el-form :model="leftDataForm"  :inline-message='true' ref="logisticsProductDataForm" label-width="80px">
                <el-row :gutter="10" type="flex">
                  <el-col :span="18">
                    <el-form-item :label="$t('coOrder.logisticsProductCode')" prop="customerCode">
                      <el-input v-model="leftDataForm.productCode" :placeholder="$t('placeholder.logisticsProductCode')"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="4">
                    <el-button type="primary" @click="leftSearchHandle()" icon="el-icon-search">查询</el-button>
                  </el-col>
                </el-row>
                <el-table :data="leftLogisticsProductDataList" ref='leftTable'  row-key='productCode' :height="300" @selection-change="leftDataListSelectionChangeHandle">
                  <el-table-column type="selection" width="55" :reserve-selection='true'></el-table-column>
                  <el-table-column v-for="(item, index) in leftLogisticsProductTableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
                  </el-table-column>
                </el-table>
                <el-pagination background small :current-page="leftPage" :page-sizes="[10, 20, 50, 100]" :page-size="leftLimit" :total="leftTotal" layout="total, sizes, prev, pager, next, jumper" @size-change="leftPageSizeChangeHandle" @current-change="leftPageCurrentChangeHandle"></el-pagination>
              </el-form>
              </area-box>
            </el-col>
            <el-col :span="2">
              <div style="padding-top: 100px;text-align: center;">
                <el-button type="primary" size="small" class='arrow—btn' :loading='rightTableSelectBtnLoading' icon="el-icon-back" @click="rightTableSelectHandler(rightDataListSelection)"></el-button>
              </div>
              <div style="padding-top: 120px;text-align: center;">
                <el-button type="primary" size="small" class='arrow—btn' :loading='leftTableSelectBtnLoading' icon="el-icon-right" @click="leftTableSelectHandler(leftDataListSelection)"></el-button>
              </div>
            </el-col>
            <el-col :span="11">
              <area-box title='不常用列表'>
              <el-form :model="rightDataForm"  :inline-message='true' ref="rightDataForm" label-width="80px">
                <el-row :gutter="10" type="flex">
                  <el-col :span="18">
                    <el-form-item :label="$t('coOrder.logisticsProductCode')" prop="code">
                      <el-input v-model="rightDataForm.code" :placeholder="$t('placeholder.logisticsProductCode')"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="4">
                    <el-button type="primary" @click="rightSearchHandle()" icon="el-icon-search">查询</el-button>
                  </el-col>
                </el-row>
                <el-table :data="rightLogisticsProductDataList" ref='rightTable' row-key='productCode'  :height="300" @selection-change="rightDataListSelectionChangeHandle">
                  <el-table-column type="selection" width="55" :reserve-selection='true'></el-table-column>
                  <el-table-column v-for="(item, index) in logisticsProductTableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
                  </el-table-column>
                </el-table>
                <el-pagination background small :current-page="rightPage" :page-sizes="[10, 20, 50, 100]" :page-size="rightLimit" :total="rightTotal" layout="total, sizes, prev, pager, next, jumper" @size-change="rightPageSizeChangeHandle" @current-change="rightPageCurrentChangeHandle"></el-pagination>
              </el-form>
              </area-box>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="常用发货地址设置" name="setShipper" >
          <el-row class="optBtn_panel">
            <el-col :md="12" class='optBtn_leftFixed'>
              <el-button size="mini" @click="shipperSetDefaultHandle()">{{ $t('setDefault') }}</el-button>
              &nbsp;
              <!--保留空格符-->
            </el-col>
            <el-col :md="12" class="text-right">
              <el-button size="mini" type="primary" plain @click="shipperAddClick">{{ $t('add') }}</el-button>
            </el-col>
          </el-row>
          <div class="flex_table" ref="tableElm" v-domResize="redraw">
            <el-table :key="Math.random()" ref="shipperDataList" v-loading="shipperDataListLoading" :data="shipperDataList"
                      highlight-current-row @current-change="shipperDataListCurrentChangeHandle" :max-height="tableHeight">
              <el-table-column label="序号" type="index" width="50"></el-table-column>
              <el-table-column v-for="(item, index) in shipperTableColumnsArr" :key="index" :type="item.type" :prop="item.prop"
                               header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
                <template slot-scope="scope">
                  <div>
                    <div v-if="item.prop === 'createDate'">
                      <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                        <span>{{formatterFn(scope,item.prop)}}</span>
                      </el-tooltip>
                    </div>
                    <div v-else-if="item.prop === 'defaulted'">
                      <el-badge is-dot class="badge_status" v-if="scope.row.defaulted === 1" type="success" ></el-badge>
                      <el-badge is-dot class="badge_status" v-else type="warning"></el-badge>
                      <span>{{ formatterFn(scope, item.prop) }}</span>
                    </div>
                    <div v-else>
                      {{formatterFn(scope,item.prop)}}
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="150">
                <template slot-scope="scope">
                  <el-link :underline="false"  @click="shipperViewHandle(scope)">{{ $t('update') }}</el-link>
                  <el-link :underline="false"  @click="shipperDeleteHandle(scope)">{{ $t('delete') }}</el-link>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <el-dialog title="常用发货地址设置" :visible.sync="shipperDialogVisible" width="70%" :before-close="shipperHandleClose" :close-on-click-modal="false">
            <el-row :gutter="10" type="flex" justify="center">
              <el-col :span="24">
                <el-form :model="shipperDataForm" :rules="shipperDataRule"  :inline-message='true' ref="shipperDataForm" label-width="120px">
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item :label="$t('coSetCustomerShipper.shortName')" prop="shortName">
                        <el-input v-model="shipperDataForm.shortName" clearable :disabled="!this.shipperDataForm.id ? false : true"></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item :label="$t('coSetCustomerShipper.shipperName')" prop="shipperName">
                        <el-input v-model="shipperDataForm.shipperName" clearable></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item :label="$t('coSetCustomerShipper.shipperCompany')" prop="shipperCompany">
                        <el-input v-model="shipperDataForm.shipperCompany" clearable></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item :label="$t('coSetCustomerShipper.shipperContact')" prop="shipperContact">
                        <el-input v-model="shipperDataForm.shipperContact" clearable></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item :label="$t('coSetCustomerShipper.shipperCountryCode')" prop="shipperCountryCode">
                        <el-select v-model="shipperDataForm.shipperCountryCode" filterable clearable>
                          <el-option v-for="item in countryList" :key="item.code" :label="`${item.name} ${item.code}`" :value="item.code"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item :label="$t('coSetCustomerShipper.shipperProvince')" prop="shipperProvince">
                        <el-input v-model="shipperDataForm.shipperProvince" clearable></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item :label="$t('coSetCustomerShipper.shipperCity')" prop="shipperCity">
                        <el-input v-model="shipperDataForm.shipperCity" clearable></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item :label="$t('coSetCustomerShipper.shipperDistrict')" prop="shipperDistrict">
                        <el-input v-model="shipperDataForm.shipperDistrict" clearable></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="16">
                      <el-form-item :label="$t('coSetCustomerShipper.shipperAddress')" prop="shipperAddress">
                        <el-input v-model="shipperDataForm.shipperAddress" clearable></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item :label="$t('coSetCustomerShipper.shipperPostcode')" prop="shipperPostcode">
                        <el-input v-model="shipperDataForm.shipperPostcode" clearable></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item :label="$t('coSetCustomerShipper.shipperEmail')" prop="shipperEmail">
                        <el-input v-model="shipperDataForm.shipperEmail" clearable></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item :label="$t('coSetCustomerShipper.shipperDoorplate')" prop="shipperDoorplate">
                        <el-input v-model="shipperDataForm.shipperDoorplate" clearable></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item :label="$t('coSetCustomerShipper.shipperStreet')" prop="shipperStreet">
                        <el-input v-model="shipperDataForm.shipperStreet" clearable></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-col>
            </el-row>
            <span slot="footer" class="dialog-footer">
              <el-button @click="shipperHandleClose">取 消</el-button>
              <el-button type="primary" @click="shipperHandleConfirm">确 定</el-button>
            </span>
          </el-dialog>
        </el-tab-pane>
        <el-tab-pane label="常用收货地址设置" name="setConsignee" >
          <el-card class="search_box" shadow="never">
            <el-form ref="setConsigneeSearchForm" class="form_no_margin" :model="consigneeDataForm" @keyup.enter.native="getSetConsigneeByCustomerCodeList(true)" label-width="120px" >
              <el-row :gutter="10" type="flex">
                <el-col :span="8">
                  <el-form-item :label="$t('coSetCustomerConsignee.consigneeCountry')" prop="consigneeCountry">
                    <el-select v-model="consigneeDataForm.consigneeCountry" filterable placeholder="" clearable>
                      <el-option v-for="item in countryList" :key="item.code" :label="`${item.name} ${item.code}`" :value="item.code"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :label="$t('coSetCustomerConsignee.shortName')" prop="shortName">
                    <el-input v-model="consigneeDataForm.shortName" clearable></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :label="$t('coSetCustomerConsignee.fbaWarehouseCode')" prop="fbaWarehouseCode">
                    <el-input v-model="consigneeDataForm.fbaWarehouseCode" clearable></el-input>
                  </el-form-item>
                </el-col>
                <div class="search_box_btn">
                  <el-button type="primary" @click="getSetConsigneeByCustomerCodeList(true)" icon="el-icon-search">查询</el-button>
                  <el-button @click.native="resetCoSetCustomerConsigneeFormHandle" icon="el-icon-refresh-right">重置</el-button>
                </div>
              </el-row>
            </el-form>
          </el-card>
          <el-row class="optBtn_panel" style="padding-top: 15px;padding-right: 20px">
            <el-col :md="12" class='optBtn_leftFixed'>
              &nbsp;
              <!--保留空格符-->
            </el-col>
            <el-col :md="12" class="text-right">
              <el-button size="mini" type="primary" plain @click="consigneeAddClick">{{ $t('add') }}</el-button>
            </el-col>
          </el-row>
          <div class="flex_table" ref="tableElm" v-domResize="redraw">
            <el-table :key="Math.random()" ref="consigneeDataList" v-loading="consigneeDataListLoading" :data="consigneeDataList"  :height="300"
                      highlight-current-row @current-change="consigneeDataListCurrentChangeHandle">
<!--              <el-table-column label="序号" type="index" width="50"></el-table-column>-->
              <el-table-column v-for="(item, index) in consigneeTableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
                <template slot-scope="scope">
                  <div>
                    <div v-if="item.prop === 'createDate'">
                      <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                        <span>{{formatterFn(scope,item.prop)}}</span>
                      </el-tooltip>
                    </div>
                    <div v-else-if="item.prop === 'defaulted'">
                      <el-badge is-dot class="badge_status" v-if="scope.row.defaulted === 1" type="success"></el-badge>
                      <el-badge is-dot class="badge_status" v-else type="warning"></el-badge>
                      <span>{{ formatterFn(scope, item.prop) }}</span>
                    </div>
                    <div v-else>
                      {{formatterFn(scope,item.prop)}}
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="150">
                <template slot-scope="scope">
                  <el-link :underline="false"  @click="consigneeViewHandle(scope)">{{ $t('update') }}</el-link>
                  <el-link :underline="false"  @click="consigneeDeleteHandle(scope)">{{ $t('delete') }}</el-link>
                </template>
              </el-table-column>
            </el-table>
            <div style="float: right">
              <el-pagination  background  :current-page="consigneePage" :page-sizes="[10, 20, 50, 100]" :page-size="consigneeLimit" :total="consigneeTotal" layout="total, sizes, prev, pager, next, jumper" @size-change="consigneePageSizeChangeHandle" @current-change="consigneePageCurrentChangeHandle"></el-pagination>
            </div>
          </div>
          <el-dialog title="常用收件地址设置" :visible.sync="consigneeDialogVisible" width="70%" :before-close="consigneeHandleClose" :close-on-click-modal="false">
            <el-row :gutter="10" type="flex" justify="center">
              <el-col :span="24">
                <el-form :model="consigneeDataForm" :rules="consigneeDataRule" :inline-message='true' ref="consigneeDataForm" label-width="120px">
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item :label="$t('coSetCustomerConsignee.shortName')" prop="shortName">
                        <el-input v-model="consigneeDataForm.shortName" clearable :disabled="!this.consigneeDataForm.id ? false : true"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item :label="$t('coSetCustomerConsignee.fbaWarehouseCode')" prop="fbaWarehouseCode">
                        <el-input v-model="consigneeDataForm.fbaWarehouseCode" clearable></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item :label="$t('coSetCustomerConsignee.consigneeName')" prop="consigneeName">
                        <el-input v-model="consigneeDataForm.consigneeName" clearable></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item :label="$t('coSetCustomerConsignee.consigneeCompany')" prop="consigneeCompany">
                        <el-input v-model="consigneeDataForm.consigneeCompany" clearable></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item :label="$t('coSetCustomerConsignee.consigneePhone')" prop="consigneePhone">
                        <el-input v-model="consigneeDataForm.consigneePhone" clearable></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item :label="$t('coSetCustomerConsignee.consigneeCountry')" prop="consigneeCountry">
                        <el-select v-model="consigneeDataForm.consigneeCountry" filterable clearable>
                          <el-option v-for="item in countryList" :key="item.code" :label="`${item.name} ${item.code}`" :value="item.code"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item :label="$t('coSetCustomerConsignee.consigneeProvince')" prop="consigneeProvince">
                        <el-input v-model="consigneeDataForm.consigneeProvince" clearable></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item :label="$t('coSetCustomerConsignee.consigneeCity')" prop="consigneeCity">
                        <el-input v-model="consigneeDataForm.consigneeCity" clearable></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item :label="$t('coSetCustomerConsignee.consigneePostcode')" prop="consigneePostcode">
                        <el-input v-model="consigneeDataForm.consigneePostcode"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="16">
                      <el-form-item :label="$t('coSetCustomerConsignee.consigneeAddress')" prop="consigneeAddress">
                        <el-input v-model="consigneeDataForm.consigneeAddress" clearable></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item :label="$t('coSetCustomerConsignee.consigneeDistrict')" prop="consigneeDistrict">
                        <el-input v-model="consigneeDataForm.consigneeDistrict" clearable></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item :label="$t('coSetCustomerConsignee.consigneeEmail')" prop="consigneeEmail">
                        <el-input v-model="consigneeDataForm.consigneeEmail" clearable></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item :label="$t('coSetCustomerConsignee.consigneeDoorplate')" prop="consigneeDoorplate">
                        <el-input v-model="consigneeDataForm.consigneeDoorplate" clearable></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <el-form-item :label="$t('coSetCustomerConsignee.consigneeStreet')" prop="consigneeStreet">
                        <el-input v-model="consigneeDataForm.consigneeStreet" clearable></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-col>
            </el-row>
            <span slot="footer" class="dialog-footer">
              <el-button @click="consigneeHandleClose">取 消</el-button>
              <el-button type="primary" @click="consigneeHandleConfirm">确 定</el-button>
            </span>
          </el-dialog>
        </el-tab-pane>
        <el-tab-pane label="常用报关品名设置" name="setDeclare" >
          <el-row class="optBtn_panel">
            <el-col class="text-right">
              <el-button size="mini" type="primary" plain @click="declareAddClick">{{ $t('add') }}</el-button>
            </el-col>
          </el-row>
          <div class="flex_table" ref="tableElm" v-domResize="redraw">
            <el-table :key="Math.random()" ref="declareDataList" v-loading="declareDataListLoading" :data="declareDataList" >
              <el-table-column label="序号" type="index" width="50"></el-table-column>
              <el-table-column v-for="(item, index) in declareTableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
                <template slot-scope="scope">
                  <div>
                    <div v-if="item.prop === 'createDate'">
                      <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                        <span>{{formatterFn(scope,item.prop)}}</span>
                      </el-tooltip>
                    </div>
                    <div v-else>
                      {{formatterFn(scope,item.prop)}}
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="150">
                <template slot-scope="scope">
                  <el-link :underline="false"  @click="declareViewHandle(scope)">{{ $t('update') }}</el-link>
                  <el-link :underline="false"  @click="declareDeleteHandle(scope)">{{ $t('delete') }}</el-link>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <el-dialog title="报关明细" :visible.sync="declareDialogVisible" width="70%" :before-close="declareHandleClose" :close-on-click-modal="false">
            <el-row :gutter="10" type="flex" justify="center">
              <el-col :span="24">
                <el-form :model="declareDataForm"  :inline-message='true' :rules="declareDataRule" ref="declareDataForm" label-width="120px">
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item :label="$t('coOrderDeclare.chineseName')" prop="chineseName">
                        <el-input v-model="declareDataForm.chineseName" :placeholder="$t('coOrderDeclare.chineseName')"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item :label="$t('coOrderDeclare.englishName')" prop="englishName">
                        <el-input v-model="declareDataForm.englishName" :placeholder="$t('coOrderDeclare.englishName')"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item :label="$t('coOrderDeclare.quantity')" prop="quantity">
                        <el-input v-model="declareDataForm.quantity" :placeholder="$t('coOrderDeclare.quantity')"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item :label="$t('coOrderDeclare.unitNetWeight')" prop="unitNetWeightD">
                        <el-input v-model="declareDataForm.unitNetWeightD" :placeholder="$t('coOrderDeclare.unitNetWeight')"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item :label="$t('coOrderDeclare.unitDeclarePrice')" prop="unitDeclarePriceD">
                        <el-input v-model="declareDataForm.unitDeclarePriceD" :placeholder="$t('coOrderDeclare.unitDeclarePrice')"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item :label="$t('coOrderDeclare.brand')" prop="brand">
                        <el-input v-model="declareDataForm.brand" :placeholder="$t('coOrderDeclare.brand')"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item :label="$t('coOrderDeclare.goodsBarcode')" prop="goodsBarcode">
                        <el-input v-model="declareDataForm.goodsBarcode" :placeholder="$t('coOrderDeclare.goodsBarcode')"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item :label="$t('coOrderDeclare.sku')" prop="sku">
                        <el-input v-model="declareDataForm.sku" :placeholder="$t('coOrderDeclare.sku')"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item :label="$t('coOrderDeclare.hsCode')" prop="hsCode">
                        <el-input v-model="declareDataForm.hsCode" :placeholder="$t('coOrderDeclare.hsCode')"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item :label="$t('coOrderDeclare.productModel')" prop="productModel">
                        <el-input v-model="declareDataForm.productModel" :placeholder="$t('coOrderDeclare.productModel')"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item :label="$t('coOrderDeclare.material')" prop="material">
                        <el-input v-model="declareDataForm.material" :placeholder="$t('coOrderDeclare.material')"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item :label="$t('coOrderDeclare.purpose')" prop="purpose">
                        <el-input v-model="declareDataForm.purpose" :placeholder="$t('coOrderDeclare.purpose')"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item :label="$t('coOrderDeclare.origin')" prop="origin">
                        <el-select v-model="declareDataForm.origin" :placeholder="$t('coOrderDeclare.origin')" filterable clearable>
                          <el-option v-for="item in countryList" :key="item.code" :label="`${item.name} ${item.code}`" :value="item.code"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item :label="$t('coOrderDeclare.pickingRemark')" prop="pickingRemark">
                        <el-input v-model="declareDataForm.pickingRemark" :placeholder="$t('coOrderDeclare.pickingRemark')"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item :label="$t('coOrderDeclare.productUrl')" prop="productUrl">
                        <el-input v-model="declareDataForm.productUrl" :placeholder="$t('coOrderDeclare.productUrl')"></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-col>
            </el-row>
            <span slot="footer" class="dialog-footer">
              <el-button @click="declareHandleClose">取 消</el-button>
              <el-button type="primary" @click="declareHandleConfirm">确 定</el-button>
            </span>
          </el-dialog>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import debounce from 'lodash/debounce'
import cloneDeep from 'lodash/cloneDeep'
import areaBox from '@/components/areaBox'
import dictTypeMixins from '@/mixins/dictTypeMixins'
import { formatterType, gtmToLtm, timestampFormat, formatterCodeName } from '@/filters/filters'
import { isDecimal3, isDecimal2, isPlusInteger2, isEmail } from '@/utils/validate'
import listPage from '@/mixins/listPage'
import baseDataApi from '@/api'
import baseData from '@/api/baseData'
import { Consignee } from '@/utils/fieldLength'
export default {
  mixins: [listPage, dictTypeMixins],
  data () {
    return {
      activeName: 'setLogisticsProductCode',
      leftDataForm: {
        customerId: this.$store.state.user.customerId,
        productCode: '',
        delFlag: 0
      },
      leftPage: 1,
      leftLimit: 10,
      leftTotal: 0,
      leftLogisticsProductTableColumns: [
        { type: '', width: '60', prop: 'productCode', label: this.$t('label.productCode'), align: 'left', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'productName', label: this.$t('label.productName'), align: 'center', isShow: true, disabled: false }
      ],
      logisticsProductTableColumns: [
        { type: '', width: '60', prop: 'productCode', label: this.$t('label.productCode'), align: 'left', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'productName', label: this.$t('label.productName'), align: 'center', isShow: true, disabled: false }
      ],
      leftLogisticsProductDataList: [],
      leftDataListSelection: [],
      rightDataForm: {
        customerId: this.$store.state.user.customerId,
        code: ''
      },
      rightPage: 1,
      rightLimit: 10,
      rightTotal: 0,
      rightLogisticsProductDataList: [],
      rightDataListSelection: [],
      declareTableColumns: [
        { type: '', width: '150', prop: 'orderId', label: this.$t('coOrderDeclare.orderId'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '150', prop: 'chineseName', label: this.$t('coSetCustomerDeclare.chineseName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'englishName', label: this.$t('coSetCustomerDeclare.englishName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'quantity', label: this.$t('coSetCustomerDeclare.quantity'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'unitNetWeightD', label: this.$t('coSetCustomerDeclare.unitNetWeight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'unitDeclarePriceD', label: this.$t('coSetCustomerDeclare.unitDeclarePrice'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'id', label: this.$t('system.id'), align: 'center', isShow: false, disabled: false }
      ],
      declareDataList: [],
      declareDataListLoading: false,
      declareDialogVisible: false,
      rightTableSelectBtnLoading: false,
      leftTableSelectBtnLoading: false,
      declareDataForm: {
        id: '',
        creator: '',
        createDate: '',
        orderId: '',
        chineseName: '',
        englishName: '',
        quantity: '',
        unitNetWeightD: '',
        unitDeclarePriceD: '',
        brand: '',
        goodsBarcode: '',
        sku: '',
        hsCode: '',
        productModel: '',
        material: '',
        purpose: '',
        origin: 'CN',
        pickingRemark: '',
        productUrl: ''
      },
      declareViewIndex: 0,
      // 0 新增 1 修改
      declareAddOrUpdate: 0,
      consigneePage: 1,
      consigneeLimit: 10,
      consigneeTotal: 0,
      consigneeDataForm: {
        shortName: '',
        customerId: this.$store.state.user.customerId,
        fbaWarehouseCode: '',
        consigneeCountry: ''
      },
      consigneeTableColumns: [
        { type: '', width: '150', prop: 'customerId', label: this.$t('coSetCustomerConsignee.customerId'), align: 'center', isShow: false, disabled: true },
        { type: '', width: '80', prop: 'defaulted', label: this.$t('coSetCustomerConsignee.defaulted'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '80', prop: 'shortName', label: this.$t('coSetCustomerConsignee.shortName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'fbaWarehouseCode', label: this.$t('coSetCustomerConsignee.fbaWarehouseCode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '50', prop: 'consigneeCountry', label: this.$t('coSetCustomerConsignee.consigneeCountry'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'consigneeName', label: this.$t('coSetCustomerConsignee.consigneeName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'consigneeCompany', label: this.$t('coSetCustomerConsignee.consigneeCompany'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '70', prop: 'consigneePostcode', label: this.$t('coSetCustomerConsignee.consigneePostcode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'consigneePhone', label: this.$t('coSetCustomerConsignee.consigneePhone'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'consigneeProvince', label: this.$t('coSetCustomerConsignee.consigneeProvince'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'consigneeCity', label: this.$t('coSetCustomerConsignee.consigneeCity'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'consigneeDistrict', label: this.$t('coSetCustomerConsignee.consigneeDistrict'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'consigneeAddress', label: this.$t('coSetCustomerConsignee.consigneeAddress'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'id', label: this.$t('system.id'), align: 'center', isShow: false, disabled: false }
      ],
      consigneeDataList: [],
      consigneeDataListLoading: false,
      consigneeDialogVisible: false,
      shipperTableColumns: [
        { type: '', width: '150', prop: 'customerId', label: this.$t('coSetCustomerShipper.customerId'), align: 'center', isShow: false, disabled: true },
        { type: '', width: '80', prop: 'defaulted', label: this.$t('coSetCustomerShipper.defaulted'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'shortName', label: this.$t('coSetCustomerShipper.shortName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'shipperName', label: this.$t('coSetCustomerShipper.shipperName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'shipperCompany', label: this.$t('coSetCustomerShipper.shipperCompany'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '120', prop: 'shipperContact', label: this.$t('coSetCustomerShipper.shipperContact'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'shipperCountryCode', label: this.$t('coSetCustomerShipper.shipperCountryCode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'shipperProvince', label: this.$t('coSetCustomerShipper.shipperProvince'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'shipperCity', label: this.$t('coSetCustomerShipper.shipperCity'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'shipperDistrict', label: this.$t('coSetCustomerShipper.shipperDistrict'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'shipperAddress', label: this.$t('coSetCustomerShipper.shipperAddress'), align: 'left', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'id', label: this.$t('system.id'), align: 'center', isShow: false, disabled: false }
      ],
      shipperDataList: [],
      shipperDataListLoading: false,
      shipperDialogVisible: false,
      shipperDataForm: {
        id: '',
        creator: '',
        createDate: '',
        customerCode: '',
        shortName: '',
        shipperName: '',
        shipperCompany: '',
        shipperContact: '',
        shipperEmail: '',
        shipperCountryName: '',
        shipperCountryCode: '',
        shipperProvince: '',
        shipperCity: '',
        shipperDistrict: '',
        shipperAddress: '',
        shipperPostcode: '',
        shipperDoorplate: ''
      },
      shipperViewIndex: 0,
      consigneeViewIndex: 0,
      // 0 新增 1 修改
      shipperAddOrUpdate: 0,
      // 0 新增 1 修改
      consigneeAddOrUpdate: 0,
      countryList: [],
      consigneeDataListSelection: {},
      shipperDataListSelection: {},
      yesOrNoList: [],
      baseData: {
        customerList: []
      }
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat
  },
  components: {
    areaBox
  },
  computed: {
    leftLogisticsProductTableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.leftLogisticsProductTableColumns).map((key) => this.leftLogisticsProductTableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    logisticsProductTableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.logisticsProductTableColumns).map((key) => this.logisticsProductTableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    declareDataRule () {
      const validateDecimal3 = (rule, value, callback) => {
        if (!isDecimal3(value)) {
          return callback(new Error(this.$t('validate.format', { 'attr': this.$t('coOrderDeclare.isDecimal3') })))
        }
        callback()
      }
      const validateDecimal2 = (rule, value, callback) => {
        if (!isDecimal2(value)) {
          return callback(new Error(this.$t('validate.format', { 'attr': this.$t('coOrderDeclare.isDecimal2') })))
        }
        callback()
      }
      const validateIsPlusInteger2 = (rule, value, callback) => {
        if (!isPlusInteger2(value)) {
          return callback(new Error(this.$t('validate.format', { 'attr': this.$t('coOrderDeclare.isPlusInteger2') })))
        }
        callback()
      }
      return {
        chineseName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        englishName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        quantity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: validateIsPlusInteger2, trigger: 'blur' }
        ],
        unitNetWeightD: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: validateDecimal3, trigger: 'blur' }
        ],
        unitDeclarePriceD: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: validateDecimal2, trigger: 'blur' }
        ]
      }
    },
    declareTableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.declareTableColumns).map((key) => this.declareTableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    consigneeDataRule () {
      const validateIsEmail = (rule, value, callback) => {
        if (value !== undefined && value !== '' && !isEmail(value) && value !== null) {
          return callback(new Error(this.$t('validate.format', { 'attr': this.$t('coOrderDeclare.isEmail') })))
        }
        callback()
      }
      return {
        shortName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { max: 64, message: this.$t('validate.isOverLength', { max: 64 }), trigger: ['blur', 'change'] }
        ],
        fbaWarehouseCode: [
          { max: Consignee.consigneeFbaWarehouseCodeLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeFbaWarehouseCodeLength }), trigger: ['blur', 'change'] }
        ],
        consigneeName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { max: Consignee.consigneeNameLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeNameLength }), trigger: ['blur', 'change'] }
        ],
        consigneeCompany: [
          { max: Consignee.consigneeCompanyLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeCompanyLength }), trigger: ['blur', 'change'] }
        ],
        consigneePhone: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { max: Consignee.consigneePhoneLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneePhoneLength }), trigger: ['blur', 'change'] }
        ],
        consigneeCountry: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        consigneeProvince: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { max: Consignee.consigneeProvinceLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeProvinceLength }), trigger: ['blur', 'change'] }
        ],
        consigneeCity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { max: Consignee.consigneeCityLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeCityLength }), trigger: ['blur', 'change'] }
        ],
        consigneeDistrict: [
          { max: Consignee.consigneeDistrictLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeDistrictLength }), trigger: ['blur', 'change'] }
        ],
        consigneeDoorplate: [
          { max: Consignee.consigneeDoorplateLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeDoorplateLength }), trigger: ['blur', 'change'] }
        ],
        consigneeStreet: [
          { max: Consignee.consigneeStreetLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeStreetLength }), trigger: ['blur', 'change'] }
        ],
        consigneeAddress: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { max: Consignee.consigneeAddressLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeAddressLength }), trigger: ['blur', 'change'] }
        ],
        consigneePostcode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { max: Consignee.consigneePostcodeLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneePostcodeLength }), trigger: ['blur', 'change'] }
        ],
        consigneeEmail: [
          { validator: validateIsEmail, trigger: 'blur' },
          { max: Consignee.consigneeEmailLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeEmailLength }), trigger: ['blur', 'change'] }
        ]
      }
    },
    consigneeTableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.consigneeTableColumns).map((key) => this.consigneeTableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    shipperDataRule () {
      const validateIsEmail = (rule, value, callback) => {
        if (value !== undefined && value !== '' && !isEmail(value) && value !== null) {
          return callback(new Error(this.$t('validate.format', { 'attr': this.$t('coOrderDeclare.isEmail') })))
        }
        callback()
      }
      return {
        shortName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        shipperName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        shipperContact: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        shipperCountryCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        shipperProvince: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        shipperCity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        shipperDistrict: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        shipperAddress: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        shipperPostcode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        shipperEmail: [
          { validator: validateIsEmail, trigger: 'blur' }
        ]
      }
    },
    shipperTableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.shipperTableColumns).map((key) => this.shipperTableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    }
  },
  created () {
    this.doRefreshDataList()
    this.getBaseData()
  },
  activated () {
    this.doRefreshDataList()
    this.getBaseData()
  },
  methods: {
    resetCoSetCustomerConsigneeFormHandle () {
      this._resetForm('setConsigneeSearchForm')
    },
    orderViewHandle (data) {
      this.$router.push({
        name: 'co-orderEntry',
        query: {
          orderId: data.row.id,
          t: +new Date()
        }
      })
    },
    async getBaseData () {
      this.countryList = await baseData(baseDataApi.countryList).catch(() => {})
      // 共用-是否值
      this.yesOrNoList = await this.getDictTypeList('yesOrNo')
    },
    doRefreshDataList () {
      this.getHasBindData()
      this.getNotBindData()
      this.getSetShipperByCustomerCodeList()
      this.getSetConsigneeByCustomerCodeList(true)
      this.getSetDeclareByCustomerCodeList()
    },
    tabsClick (tab, event) {
      switch (tab.name) {
        case 'setLogisticsProductCode':
          this.getHasBindData()
          this.getNotBindData()
          break
        case 'setShipper':
          this.getSetShipperByCustomerCodeList()
          break
        case 'setConsignee':
          this.getSetConsigneeByCustomerCodeList(true)
          break
        case 'setDeclare':
          this.getSetDeclareByCustomerCodeList()
          break
        default:
          break
      }
    },
    getSetShipperByCustomerCodeList () {
      return this.$http('/co/setcustomershipper/listByCustomerId/' + this.$store.state.user.customerId).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.shipperDataList = res.data
      }).catch(() => {})
    },
    getSetConsigneeByCustomerCodeList (fromSearchBtn) {
      this.consigneeDataListLoading = true
      let page = this.consigneePage
      if (fromSearchBtn) {
        page = 1
        this.consigneePage = 1
      }
      return this.$http.get('/co/setcustomerconsignee/page',
        {
          params: {
            page: page,
            limit: this.consigneeLimit,
            ...this.consigneeDataForm
          }
        }
      ).then(({ data: res }) => {
        this.consigneeDataListLoading = false
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$nextTick(() => {
          this.$refs.consigneeDataList.clearSelection()
          this.consigneeDataList = res.data.list
          this.consigneeTotal = res.data.total
        })
      }).catch(() => {})
    },
    // 分页, 每页条数
    consigneePageSizeChangeHandle (val) {
      this.consigneePage = 1
      this.consigneeLimit = val
      this.getSetConsigneeByCustomerCodeList(false)
    },
    // 分页, 当前页
    consigneePageCurrentChangeHandle (val) {
      this.consigneePage = val
      this.getSetConsigneeByCustomerCodeList(false)
    },
    getSetDeclareByCustomerCodeList () {
      return this.$http('/co/setcustomerdeclare/listByCustomerId/' + this.$store.state.user.customerId).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.declareDataList = res.data
      }).catch(() => {})
    },
    leftDataListSelectionChangeHandle (data) {
      this.leftDataListSelection = data
    },
    leftTableSelectHandler: debounce(function (data) {
      this.leftTableSelectBtnLoading = true
      if (data.length <= 0) {
        this.leftTableSelectBtnLoading = false
        return this.$message.warning('请选在已拥有数据列表')
      }
      let array = []
      for (let i = 0; i < data.length; i++) {
        array.push(data[i].id)
      }
      this.$http.delete('/co/setcustomerlogisticsproduct', { 'data': array }).then(({ data: res }) => {
        this.$nextTick(() => {
          this.leftDataListSelection = []
          this.rightDataListSelection = []
        })
        this.leftTableSelectBtnLoading = false
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 500,
          onClose: () => {
            this.getHasBindData()
            this.getNotBindData()
          }
        })
      }).catch(() => {})
    }),
    leftSearchHandle () {
      this.getHasBindData()
    },
    // 分页, 每页条数
    leftPageSizeChangeHandle (val) {
      this.leftPage = 1
      this.leftLimit = val
      this.getHasBindData()
    },
    // 分页, 当前页
    leftPageCurrentChangeHandle (val) {
      this.leftPage = val
      this.getHasBindData()
    },
    rightDataListSelectionChangeHandle (data) {
      this.rightDataListSelection = data
    },
    rightTableSelectHandler: debounce(function (data) {
      this.rightTableSelectBtnLoading = true
      if (data.length <= 0) {
        this.rightTableSelectBtnLoading = false
        return this.$message.warning('请选在已拥有数据列表')
      }
      let array = []
      for (let i = 0; i < data.length; i++) {
        array.push({
          productCode: data[i].productCode,
          productName: data[i].productName
        })
      }
      this.$http.post('/co/setcustomerlogisticsproduct/saveBatch', array).then(({ data: res }) => {
        this.$nextTick(() => {
          this.rightDataListSelection = []
          this.leftDataListSelection = []
        })
        this.rightTableSelectBtnLoading = false
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 500,
          onClose: () => {
            this.getHasBindData()
            this.getNotBindData()
          }
        })
      }).catch(() => {})
    }),
    rightSearchHandle () {
      this.getNotBindData()
    },
    // 分页, 每页条数
    rightPageSizeChangeHandle (val) {
      this.rightPage = 1
      this.rightLimit = val
      this.getNotBindData()
    },
    // 分页, 当前页
    rightPageCurrentChangeHandle (val) {
      this.rightPage = val
      this.getNotBindData()
    },
    getNotBindData () {
      return this.$http.get('/co/setcustomerlogisticsproduct/pageNotBind', { params: {
        page: this.rightPage,
        limit: this.rightLimit,
        ...this.rightDataForm
      } }).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$nextTick(() => {
          this.$refs.rightTable.clearSelection()
          this.rightLogisticsProductDataList = res.data.list
          this.rightTotal = res.data.total
        })
      }).catch(() => {})
    },
    // 获取信息
    getHasBindData () {
      return this.$http.get('/co/setcustomerlogisticsproduct/page', { params: {
        page: this.leftPage,
        limit: this.leftLimit,
        ...this.leftDataForm
      } }).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$nextTick(() => {
          this.$refs.leftTable.clearSelection()
          this.leftLogisticsProductDataList = res.data.list
          this.leftTotal = res.data.total
        })
      }).catch(() => {})
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'defaulted':
          value = formatterType(scope.row.defaulted, this.yesOrNoList)
          break
        case 'createDate':
          value = timestampFormat(gtmToLtm(scope.row.createDate), 'YYYY-MM-DD')
          break
        case 'shipperCountryCode':
          value = formatterCodeName(scope.row.shipperCountryCode, this.countryList)
          break
        case 'status':
          value = formatterType(scope.row.status, this.statusList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    declareAddClick () {
      this.declareDialogVisible = true
      this.declareViewIndex = 0
      this.declareAddOrUpdate = 0
      this.$nextTick(function () {
        this.declareDataForm = { origin: 'CN' }
      })
    },
    declareViewHandle (data) {
      this.declareViewIndex = data.$index
      this.declareDialogVisible = true
      this.declareAddOrUpdate = 1
      this.declareDataForm = cloneDeep(data.row)
    },
    declareDeleteHandle: debounce(function (data) {
      this.$http.delete('/co/setcustomerdeclare/', { 'data': [data.row.id] }).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 500
        })
        this.doRefreshDataList()
      }).catch(() => {})
    }, 1000, { 'leading': true, 'trailing': false }),
    declareHandleClose () {
      this.$refs.declareDataForm.resetFields()
      this.declareDialogVisible = false
    },
    declareHandleConfirm: debounce(function () {
      this.$refs.declareDataForm.validate((valid) => {
        if (!valid) {
          return false
        }
        this.declareDataForm.customerId = this.$store.state.user.customerId
        this.$http[!this.declareDataForm.id ? 'post' : 'put']('/co/setcustomerdeclare/', this.declareDataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500
          })
          // this.$refs.shipperDataForm.resetFields()
          this.$refs['declareDataForm'].resetFields()
          this.declareDialogVisible = false
          this.doRefreshDataList()
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    shipperDataListCurrentChangeHandle (data) {
      this.shipperDataListSelection = data
    },
    consigneeDataListCurrentChangeHandle (data) {
      this.consigneeDataListSelection = data
    },
    shipperSetDefaultHandle () {
      if (this.shipperDataListSelection.id === undefined) {
        return this.$message.warning('请选择数据')
      }
      if (this.shipperDataListSelection.defaulted === 1) {
        return this.$message.warning('该数据已经为默认')
      }
      this.$http.put('/co/setcustomershipper/defaulted/' + this.shipperDataListSelection.customerId + '/' + this.shipperDataListSelection.id).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 500
        })
        this.doRefreshDataList()
        this.shipperDataListSelection = {}
      }).catch(() => {})
    },
    consigneeAddClick () {
      this.consigneeDataForm = {}
      this.consigneeDialogVisible = true
      this.consigneeViewIndex = 0
      this.consigneeAddOrUpdate = 0
    },
    consigneeViewHandle (data) {
      this.consigneeViewIndex = data.$index
      this.consigneeDialogVisible = true
      this.consigneeAddOrUpdate = 1
      this.consigneeDataForm = cloneDeep(data.row)
    },
    consigneeDeleteHandle: debounce(function (data) {
      if (data.row.defaulted === 1) {
        return this.$message.warning('该数据为默认')
      }
      this.$http.delete('/co/setcustomerconsignee/', { 'data': [data.row.id] }).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 500
        })
        this.doRefreshDataList()
      }).catch(() => {})
    }, 1000, { 'leading': true, 'trailing': false }),
    shipperAddClick () {
      this.shipperDataForm = {}
      this.shipperDialogVisible = true
      this.shipperViewIndex = 0
      this.shipperAddOrUpdate = 0
    },
    shipperViewHandle (data) {
      this.shipperViewIndex = data.$index
      this.shipperDialogVisible = true
      this.shipperAddOrUpdate = 1
      this.shipperDataForm = cloneDeep(data.row)
    },
    shipperDeleteHandle: debounce(function (data) {
      if (data.row.defaulted === 1) {
        return this.$message.warning('该数据为默认')
      }
      this.$http.delete('/co/setcustomershipper/', { 'data': [data.row.id] }).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 500
        })
        this.doRefreshDataList()
      }).catch(() => {})
    }, 1000, { 'leading': true, 'trailing': false }),
    consigneeHandleClose () {
      this.$refs.consigneeDataForm.resetFields()
      this.consigneeDialogVisible = false
    },
    consigneeHandleConfirm: debounce(function () {
      this.$refs.consigneeDataForm.validate((valid) => {
        if (!valid) {
          return false
        }
        this.consigneeDataForm.customerId = this.$store.state.user.customerId
        this.$http[!this.consigneeDataForm.id ? 'post' : 'put']('/co/setcustomerconsignee/', this.consigneeDataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500
          })
          // this.$refs.shipperDataForm.resetFields()
          this.$refs['consigneeDataForm'].resetFields()
          this.consigneeDialogVisible = false
          this.doRefreshDataList()
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    shipperHandleClose () {
      this.$refs.shipperDataForm.resetFields()
      this.shipperDialogVisible = false
    },
    shipperHandleConfirm: debounce(function () {
      this.$refs.shipperDataForm.validate((valid) => {
        if (!valid) {
          return false
        }
        this.shipperDataForm.customerId = this.$store.state.user.customerId
        this.$http[!this.shipperDataForm.id ? 'post' : 'put']('/co/setcustomershipper/', this.shipperDataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500
          })
          // this.$refs.shipperDataForm.resetFields()
          this.$refs['shipperDataForm'].resetFields()
          this.shipperDialogVisible = false
          this.doRefreshDataList()
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>

<style lang="scss"  scoped>
.arrow—btn{
  margin-left: 3px;
  font-size: 20px;
}
.el-pagination{
  text-align: left;
}
</style>
