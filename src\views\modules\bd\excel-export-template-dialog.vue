<template>
  <el-dialog :visible.sync="visible" :title="$t('exportTemplate')" width="50%" :close-on-click-modal="false" :close-on-press-escape="false">
    <div v-if="!pageShow">
      <el-row class="optBtn_panel">
        <el-col :md="24" class="text-right">
          <el-button size="mini" class="primary blue" plain @click="addOrUpdateHandle()">{{ $t('template.add') }}</el-button>
        </el-col>
      </el-row>
      <div ref="tableElm">
        <el-table v-loading="dataListLoading" :data="dataList" border :max-height="600">
          <!-- 动态显示表格 -->
          <el-table-column v-for="(item, index) in tableColumnsArr" :show-overflow-tooltip="item.prop === 'memo'" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
            <template slot-scope="scope">
              <div>
                <div v-if="item.prop === 'createDate'">
                  <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                    <span>{{ formatterFn(scope, item.prop) }}</span>
                  </el-tooltip>
                </div>
                <div v-else-if="item.prop === 'optDate'">
                  <el-tooltip :content="scope.row.optDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                    <span>{{ formatterFn(scope, item.prop) }}</span>
                  </el-tooltip>
                </div>
                <div v-else-if="item.prop === 'status'">
                  <el-tag v-if="scope.row.status === 0" size="small" type="danger">{{ $t('init') }}</el-tag>
                  <el-tag v-else size="small" type="success">{{ $t('audit') }}</el-tag>
                </div>
                <div v-else>
                  <span class="text-overflow">{{ formatterFn(scope, item.prop) }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column :label="$t('handle')" fixed="right" min-width="100">
            <template slot="header" slot-scope="scope">
              <span>{{ $t('handle') }}</span>
            </template>
            <template slot-scope="scope">
              <span class="text-overflow" style='color: #ebb563;' v-if="scope.row.objectId==='0'">公用模板，不可编辑</span>
              <el-button type="text" size="mini"  v-if="scope.row.objectId!=='0' && $hasPermission('bd:excelexporttemplate:update')" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
              <el-button type="text" size="mini"  v-if="scope.row.objectId!=='0' && $hasPermission('bd:excelexporttemplate:update')" @click="editDetailHandle(scope.row.id)">{{ $t('template.editdetail') }}</el-button>
              <el-button type="text" size="mini"  v-if="scope.row.objectId!=='0' && $hasPermission('bd:excelexporttemplate:delete')" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
              <!-- 打印唛头 -->
              <!-- 操作日志 -->
            </template>
          </el-table-column>
        </el-table>
        <el-pagination background :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle" @current-change="pageCurrentChangeHandle"> </el-pagination>
      </div>
      <!-- 新增 / 修改 -->
      <template slot="footer" class="dialog-footer">
        <el-button @click="backFn">{{ $t('back') }}</el-button>
      </template>
    </div>
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"  @cancelAddOrUpdate="cancelAddOrUpdate"></add-or-update>
    <!-- 详情页面 -->
    <detailEdit v-if="detailVisible" ref="detailEdit" @backView="backView"></detailEdit>
  </el-dialog>
</template>

<script>
import { formatterType, gtmToLtm, timestampFormat } from '@/filters/filters'
import dictTypeMixins from '@/mixins/dictTypeMixins'
import { isSignDecimal3 } from '@/utils/validate'
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './excel-export-template-add-or-update-dialog'
import DetailEdit from './excel-export-template-detail'

// table 自定义显示
export default {
  mixins: [dictTypeMixins, mixinViewModule],
  data () {
    return {
      tableColumns: [
        { type: '', width: '150', prop: 'name', label: this.$t('bdExcelExportTemplate.name'), align: 'left', isShow: true, disabled: false },
        { type: '', width: '240', prop: 'description', label: this.$t('bdExcelExportTemplate.description'), align: 'left', isShow: true, disabled: false }
        // { type: '', width: '150', prop: 'url', label: this.$t('bdExcelExportTemplate.url'), align: 'left', isShow: true, disabled: false }
      ],
      visible: false,
      mixinViewModuleOptions: {
        getDataListURL: '/bd/excelexporttemplate/pageClient',
        getDataListIsPage: true,
        deleteURL: '/bd/excelexporttemplate',
        deleteIsBatch: true,
        auditIsBatch: true
      },
      dataForm: {
        id: '',
        url: '',
        masterDTOName: ''
      },
      visibleAddFee: false,
      isAddFee: false,
      isSave: false,
      dataListLoading: false,
      detailVisible: false,
      unusedObjectArray: [],
      unusedAttributeArray: [],
      dataList: [],
      total: 0,
      limit: 10,
      page: 1,
      activeName: 'all',
      tableName: 'ba-receivablefee'
    }
  },
  methods: {
    init (unusedObjectArray, unusedAttributeArray) {
      this.visible = true
      if (unusedObjectArray) {
        this.unusedObjectArray = unusedObjectArray
      }
      if (unusedAttributeArray) {
        this.unusedAttributeArray = unusedAttributeArray
      }
      this.$nextTick(() => {
        if (this.dataForm.url && this.dataForm.masterDTOName) {
          this.limit = 100
          this.page = 1
          this.total = 0
          this.getDataList()
        }
      })
    },
    // 返回
    backFn () {
      this.visible = false
      this.$emit('backView')
    },
    // 分页, 每页条数
    pageSizeChangeHandle (val) {
      this.page = 1
      this.limit = val
      this.getDataList()
    },
    // 分页, 当前页
    pageCurrentChangeHandle (val) {
      this.page = val
      this.getDataList()
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'createDate':
          value = timestampFormat(gtmToLtm(scope.row.createDate), 'YYYY-MM-DD')
          break
        case 'status':
          value = formatterType(scope.row.status, this.statusList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    // 新增 / 修改
    addOrUpdateHandle (id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.dataForm.id = id
        this.$refs.addOrUpdate.dataForm.url = this.dataForm.url
        this.$refs.addOrUpdate.dataForm.exportMainObjectName = this.dataForm.masterDTOName
        this.$refs.addOrUpdate.init()
      })
    },
    // 新增 / 修改
    editDetailHandle (id) {
      this.detailVisible = true
      this.$nextTick(() => {
        this.$refs.detailEdit.dataForm.templateId = id
        this.$refs.detailEdit.dataForm.url = this.dataForm.url
        this.$refs.detailEdit.dataForm.masterDTOName = this.dataForm.masterDTOName
        this.$refs.detailEdit.init(this.unusedObjectArray, this.unusedAttributeArray)
      })
    }
  },
  computed: {
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map(key => this.tableColumns[key])
      arr = tableColumns.filter(item => {
        return item.isShow
      })
      return arr
    },
    dataRule () {
      const sumDSign3Validate = (rule, value, callback) => {
        if (!isSignDecimal3(value)) {
          return callback(new Error(this.$t('validate.decimal3', { number: 3 })))
        }
        callback()
      }
      return {
        sumD: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: sumDSign3Validate, trigger: 'blur' }
        ],
        remark: [{ required: true, message: this.$t('validate.required'), trigger: 'blur' }],
        currency: [{ required: true, message: this.$t('validate.required'), trigger: 'blur' }],
        feeTypeId: [{ required: true, message: this.$t('validate.required'), trigger: 'blur' }]
      }
    },
    pageShow () {
      let ret = false
      if (this.viewVisible) {
        ret = true
      } else if (this.addOrUpdateVisible) {
        ret = true
      }
      return ret
    }
  },
  components: {
    AddOrUpdate,
    DetailEdit
  }
}
</script>
