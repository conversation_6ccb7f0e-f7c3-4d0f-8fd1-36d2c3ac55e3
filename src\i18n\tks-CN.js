const tks = {}

/* 客服管理 - 轨迹查询-tksTracking */
tks.tksTracking = {}
tks.tksTracking.waybillNo = '请输入1个或最多不超过100个运单号，用逗号或换行分隔'
tks.tksTracking.cnSearch = '查看中文轨迹'
tks.tksTracking.enSearch = '查看英文轨迹'
tks.tksTracking.result = '查询结果'
tks.tksTracking.normal = '正常'
tks.tksTracking.abnormal = '异常'
tks.tksTracking.queryNO = '查询单号'
tks.tksTracking.timeZone = '时区'
tks.tksTracking.btyNo = '物流公司单号'
tks.tksTracking.trackingNO = '派送单号'
tks.tksTracking.postalTrackingNo = '邮政单号'
tks.tksTracking.logisticsCompanyTrackNo = '物流公司跟踪单号'
tks.tksTracking.trackingNumber = '派送单号'
tks.tksTracking.destinationCountry = '目的国'
tks.tksTracking.collectPackageTimeliness = '揽收时效'
tks.tksTracking.deliveryPackageTimeliness = '派送时效'
tks.tksTracking.day = '天'
tks.tksTracking.back = '返回'
tks.tksTracking.state = '状态'
tks.tksTracking.content = '内容'
tks.tksTracking.location = '地点'
tks.tksTracking.eventCountry = '轨迹所在国家'
tks.tksTracking.eventProvince = '轨迹所在省/州'
tks.tksTracking.eventCity = '轨迹所在城市'
tks.tksTracking.zipCode = '轨迹所在地邮编'
tks.tksTracking.flightNo = '轨迹航班号'
tks.tksTracking.itemflightNo = '航班号'
tks.tksTracking.timestamp = '时间'
tks.tksTracking.save = '保存'
tks.tksTracking.delete = '删除'
tks.tksTracking.edit = '修改'
tks.tksTracking.add = '增加'
tks.tksTracking.trackDescription = '轨迹说明：所有轨迹发生时间都是发生地所在时区的时间'

export default tks
