import domResize from '@/directive/resize'
export default {
  directives: { domResize },
  data () {
    return {
      searchBoxShow: true,
      tableHeight: 0,
      minSearchCard: true
    }
  },
  created () {
    window.onresize = () => {
      this.redraw()
    }
  },
  beforeDestroy () {
    window.onresize = null
  },
  methods: {
    redraw () {
      this.$nextTick(() => {
        if (this.$refs.tableElm) {
          this.tableHeight = this.$refs.tableElm.offsetHeight - 10
        }
      })
    },
    // 展示与隐藏搜索条件
    searchBoxShowFn () {
      this.searchBoxShow = !this.searchBoxShow
    },
    // minSearch
    minSearch () {
      this.minSearchCard = !this.minSearchCard
    }
  }
}
