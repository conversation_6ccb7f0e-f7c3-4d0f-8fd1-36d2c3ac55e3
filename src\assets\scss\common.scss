*,
*:before,
*:after {
  box-sizing: border-box;
}
// 滚动条出现页面不跳动
html {
  overflow-y: scroll;
}
:root {
  overflow-y: auto;
  overflow-x: hidden;
}

:root body {
  position: absolute;
}
body {
  width: 100vw;
  overflow: hidden;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
  font-size: $--font-size-base;
  line-height: $base--line-height;
  color: $--color-text-primary;
  background-color: #fff;
}
a {
  color: mix(#fff, $--color-primary, 20%);
  text-decoration: none;
  &:focus,
  &:hover {
    color: $--color-primary;
  }
}
img {
  vertical-align: middle;
}
:focus,
:hover {
  outline: none;
}
.success{
  color: #67C23A !important;
}
.warning {
  color: #E6A23C !important;
}
.danger {
  color: #F56C6C !important;
}
.info{
  color: #909399
}
.flex {
  display: flex;
  align-items: center;
}
.flex_1 {
  flex: 1;
}
.el-form-item{
  margin-bottom: 10px
}
.el-form-item__label {
  position:relative;
  line-height: 32px;
}
.el-form-item__label:after{
  content: ":";
  position: relative;
  top: -.5px;
  margin: 0 4px 0 2px;
}
.el-form-item.no_label {
  .el-form-item__label:after{
    content:' ';
    position:absolute;
    right: 0;
  }
}

.form_no_margin{
  .el-form-item{
    margin-bottom: 10px
  }
}

/* Utils
------------------------------ */
[v-cloak] {
  display: none;
}
.clearfix:before,
.clearfix:after {
  content: " ";
  display: table;
}
.clearfix:after {
  clear: both;
}
.fr {
  float: right !important;
}
.fl {
  float: left !important;
}
.fi {
  float: initial !important;
}
.m-auto {
  margin: auto !important;
}
.mt-auto {
  margin-top: auto !important;
}
.mr-auto {
  margin-right: auto !important;
}
.mb-auto {
  margin-bottom: auto !important;
}
.ml-auto {
  margin-left: auto !important;
}
.padding0{
  padding: 0 !important;
}
.text-right {
  text-align: right !important;
}
.text-center {
  text-align: center !important;
}
.text-left {
  text-align: left !important;
}
.w-percent-100, .width100 {
  width: 100% !important;
}
.base-line-height {
  line-height: $base--line-height !important;
}
.inline-block{
  display: inline-block !important;
}
.block{
  display: inline-block !important;
}
.align-top{
  vertical-align: top;
}
.align-middle{
  vertical-align: middle;
}
.scan-input-h {
  height: 60px;
  .el-input__inner {
    height: 100%;
  }
  .el-input-group__prepend{
    min-width: 12em;
    text-align:center
  }
}
.fontSize12{
  font-size: 12px;
}
.fontSize13{
  font-size: 13px;
}
.fontSize14{
  font-size: 14px;
}
.fontSize16{
  font-size: 16px;
}
.fontSize18{
  font-size: 18px;
}
.fontSize32{
  font-size: 32px;
}
.no_margin{
  margin: unset !important;
}
.searh_box{
  margin-left: 3em;
}
.margin-top-up-15{
  margin-top: -15px;
}
.margin_top10{
  margin-top:10px;
}
.margin_top15{
  margin-top:15px;
}
.margin_top20{
  margin-top: 20px;
}
.margin_right15{
  margin-right: 15px;
}
.margin_right25{
  margin-right: 25px;
}
.margin_right10{
  margin-right: 10px;
}
.margin_bottom10{
  margin-bottom:10px;
}
.margin_bottom15{
  margin-bottom:15px;
}
.margin_bottom22{
  margin-bottom:22px;
}
.pointer{
  cursor: pointer
}

// table中添加input select el-form-item的样式重置
.tableForm{
  .el-form-item__error {
    position: relative;
    text-align: left;
  }
  .el-form-item__content{
    margin-left: 0 !important;
    font-size: 12px;
  }
  .el-form-item{
    margin-bottom:0
  }
}
.el-form-item.no-label-margin{
  .el-form-item__content{
    margin: 0 !important;
  }
}
.text-overflow{
  display: block;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.list_panel_600{
  min-height: 640px;
}
.list_panel_500{
  min-height: 500px;
}
.list_panel_400{
  min-height: 400px;
}
.no_tabs{
  .el-tabs__header{
    border:none;
  }
}
.panel_body{
  background: $--color-white;
  padding: 20px;
  min-height: calc(calc(100vh - 38px - 30px - 48px) - 2px);
}
.class_tag_title, .tag_collapse{
  font-size: 14px;
  font-weight: 600;
}



/* Reset element-ui
------------------------------ */
// 按钮
.el-button--text{
  color: #606266;
}

// 表格
.aui-wrapper {
  .el-card + .el-card {
    margin-top: 15px;
  }
  .el-input__prefix .el-input__icon {
    display: inline-block;
    vertical-align: middle;
  }
  .el-date-editor .el-range-separator {
    width: 8%;
    line-height: 24px;
    font-size: 12px;
  }
  .el-table th {
    color: $--color-text-primary;
    background-color: $--background-color-base;
  }
  .el-pagination {
    margin-top: 5px;
    text-align: right;
  }
}
// 表格
.el-table{
  font-size: 12px;
  th{
    padding: 6px 0;
  }
  td{
    padding: 4px 0;
  }
  .cell{
    line-height: 23px;
  }
}
// 表格字体
.el-table--mini {
  font-size: 13px;
}

// 表单字体
.el-form-item__label{
  font-size: 13px;
}

/* tabs
------------------------------ */
.el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active{
  position: relative;
  &:before{
    content: '';
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    border-top:3px solid $--color-primary;
  }
}
// 添加线
.el-tabs--card{
  .el-tabs__item{
    &.is-active{
      &:before{
        content: '';
        width: 100%;
        position: absolute;
        top: 0;
        left: 0;
        border-top:3px solid $--color-primary;
      }
    }
  }
}

/* Common
------------------------------ */
// 图标
.icon-svg {
  width: 1em;
  height: 1em;
  fill: currentColor;
  vertical-align: middle;
  overflow: hidden;
}
// 卡片
.el-card__body{
  padding: 12px;
}
.aui-card--fill .el-card__header {
  height: $content--card-header-height;
  line-height: $content--card-header-height - 36px;
}
.aui-card__title {
  font-size: 16px;
}
// 表单
.aui-form__label-icon {
  display: inline-block;
  margin: 0 3px;
  vertical-align: middle;
  font-size: 18px;
  color: $--color-text-secondary;
}
.el-form-item__content{
  line-height: 32px;
}
//.el-input__inner{
//  height: 32px;
//  line-height: 32px;
//}
.el-range-editor.el-input__inner{
  margin-bottom: 1px;
}
.el-date-editor .el-range__icon{
  line-height: 24px;
}
// 重构样式 会导致 禁用的默认高度是40px
.el-select--default .el-input.is-disabled .el-input__inner{
  height: 32px !important;
}
// 重构选择框
.el-select{
  display: block;
  padding-bottom: 1px;
}
.el-input__suffix{
  top: unset;
  bottom: 0;
  height: auto;
}
// 重构时间关闭小图标
.el-date-editor .el-range__close-icon{
  line-height: 26px;
}
// 按钮
.aui-button--dashed {
  border-style: dashed;
  &:focus,
  &:hover {
    background-color: transparent;
  }
  &-add {
    > span > *[class*="el-icon-"],
    > span > *[class*="icon"] {
      vertical-align: middle;
      font-size: 18px;
      margin-right: 5px;
    }
  }
}
// 主题工具
.aui-theme-tools {
  position: fixed;
  top: $navbar--height + $content--tabs-header-height + 15px;
  right: -210px;
  bottom: 0;
  z-index: 1010;
  width: 210px;
  transition: right .3s;
  &--open {
    right: 0;
  }
  &__toggle {
    position: absolute;
    top: 80px;
    left: -40px;
    width: 40px;
    padding: 10px 8px;
    text-align: center;
    font-size: 20px;
    border-right: 0;
    border-radius: $--border-radius-base 0 0 $--border-radius-base;
    color: #fff;
    background-color: $--color-primary;
    cursor: pointer;
  }
  &__content {
    height: 100%;
    padding: 5px 20px 20px;
    border: 1px solid $--border-color-lighter;
    border-radius: $--border-radius-base 0 0 $--border-radius-base;
    background-color: #fff;
    .el-radio {
      display: block;
      margin-left: 0 !important;
      line-height: 28px;
    }
  }
  &__item + &__item {
    margin-top: 15px;
    border-top: 1px solid $--border-color-lighter;
  }
}


/* Layout
------------------------------ */
.aui-wrapper {
  position: relative;
  padding-top: $navbar--height;
}

/* Sidebar fold
------------------------------ */
.aui-sidebar--fold {
  .aui-navbar{
    left: $sidebar--width-fold;
  }
  .aui-navbar {
    &__header,
    &__brand {
      width: $sidebar--width-fold;
    }
    &__brand {
      &-lg {
        display: none;
      }
      &-mini {
        display: inline-block;
      }
    }
    &__icon-menu--switch {
      transform: rotateZ(180deg);
    }
  }
  .aui-sidebar {
    &__inner {
      width: $sidebar--width-fold + 20px;
    }
    &,
    &__menu {
      width: $sidebar--width-fold;
    }
    &__menu > li {
      text-align: center;
    }
    &__menu-icon {
      margin-right: 0;
      font-size: 18px;
    }
  }
  .aui-content {
    &__wrapper {
      margin-left: $sidebar--width-fold;
    }
    &--tabs > .el-tabs > .el-tabs__header {
      left: $sidebar--width-fold;
    }
    & .content_footer{
      position: relative;
      padding: 0 15px;
      height: 40px;
      line-height: 40px;
      border-top: 1px solid #e0d7d4;
      text-align: center;
      overflow: hidden;
    }
  }
}


/* Sidebar
------------------------------ */
.aui-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  z-index: 1020;
  width: $sidebar--width;
  display: flex;
  flex-direction: column;
  /*background-color: #fff;*/
  box-shadow: 1px 0 2px 0 rgba(0, 0, 0, .05);
  overflow: hidden;
  transition: width .3s;
  &--dark {
    // background-color: $sidebar--background-color-dark;
    background-color: #fff;
    .aui-sidebar__menu,
    > .el-menu--popup {
      // background-color: $sidebar--background-color-dark;
      background-color: $sidebar--background-color-dark;
      .el-menu-item,
      .el-submenu > .el-submenu__title {
        color: $sidebar--text-color-dark;
        font-size: 13px;
        text-overflow: ellipsis;
        overflow: hidden;
        &:focus,
        &:hover {
          color: #fff;
          background-color: mix(#000, $navbar--bgColor, 15%);
        }
        i{
          color: #fff;
        }
      }
      .el-menu,
      .el-submenu.is-opened {
        background-color: mix(#000, $sidebar--background-color-dark, 15%);
      }
      .el-menu-item.is-active{
        background-color: $--color-primary;
      }
      .el-submenu.is-active > .el-submenu__title {
        color: mix(#fff, $sidebar--text-color-dark, 80%);
      }
    }

  }
  &__inner {
    position: relative;
    z-index: 1;
    width: $sidebar--width + 20px;
    height: 100%;
    flex: 1;
    overflow-x: hidden;
    overflow-y: scroll;
    background: $sidebar--background-color-dark;
    background-size: cover;
    transition: width .3s;
  }
  &__menu {
    width: $sidebar--width;
    border-right: 0;
    background-color: transparent;
    transition: width .3s;
    .el-menu-item,
    .el-submenu__title {
      font-size: 13px;
      height: $sidebar--menu-item-height;
      line-height: $sidebar--menu-item-height;
    }
  }
  &__menu-icon {
    display: inline-block;
    vertical-align: middle;
    width: 24px !important;
    margin-right: 5px;
    text-align: center;
    font-size: 13px;
    color: inherit !important;
    transition: font-size .3s;
  }
}


/* Navbar
------------------------------ */
.aui-navbar {
  position: fixed;
  top: 0;
  right: 0;
  left: $sidebar--width;
  z-index: 1030;
  display: flex;
  align-items: stretch;
  height: $navbar--height;
  background-color: $navbar--bgColor;
  box-shadow: 0 1px 0 0 rgba(0, 0, 0, .05);
  &--colorful {
    .aui-navbar__body {
      background-color: transparent;
    }
    .aui-navbar__menu {
      > .el-menu-item,
      > .el-submenu > .el-submenu__title {
        color: #fff;
        &:focus,
        &:hover {
          color: #fff;
          background-color: mix(#000, $navbar--bgColor, 15%);
        }
      }
      > .el-menu-item.is-active,
      .el-submenu.is-active > .el-submenu__title {
        color: $--color-white;
        &:focus,
        &:hover {
          color: $--color-white;
        }
      }
      .el-menu-item i,
      .el-submenu__title i,
      .el-menu-item svg,
      .el-submenu__title svg,
      .el-menu-item .el-dropdown {
        color: #fff !important;
      }
      .el-button {
        color: #fff;
        background-color: transparent;
      }
    }
    .aui-navbar__search {
      &-txt {
        .el-input__inner {
          color: #fff;
          border-color: #fff;
          &::-webkit-input-placeholder {
            color: #fff;
          }
        }
      }
    }
  }
  &__header {
    position: relative;
    width: $sidebar--width;
    height: $logo--height;
    border-bottom: 1px solid #eee;
    transition: width .3s;
    background: $--color-primary;
  }
  &__brand {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 5px;
    margin: 0;
    width: 100%;
    height: 100%;
    font-size: 20px;
    text-transform: uppercase;
    white-space: nowrap;
    //color: #fff;
    overflow: hidden;
    transition: width .3s;
    &-lg,
    &-mini {
      max-width: 100%;
      //color: #fff;
      cursor: pointer;
      &:focus,
      &:hover {
        //color: #fff;
        text-decoration: none;
      }
    }
    &-mini {
      display: none;
    }
  }
  &__body {
    position: relative;
    display: flex;
    flex: 1;
    background-color: #fff;
    overflow: hidden;
  }
  &__menu {
    background-color: transparent;
    border-bottom: 0 !important;
    a:focus,
    a:hover {
      text-decoration: none;
    }
    .el-menu-item,
    .el-submenu > .el-submenu__title {
      height: $navbar--height;
      padding: 0 15px;
      line-height: $navbar--height;
      border-color: transparent !important;
    }
    .el-menu-item.is-active,
    .el-submenu.is-active > .el-submenu__title {
      color: $--color-white;
      &:focus,
      &:hover {
        color: $--color-text-primary;
      }
    }
    .el-menu-item {
      &:focus,
      &:hover {
        .aui-navbar__icon-menu {
          color: $--color-text-primary;
        }
        .el-dropdown {
          color: $--color-text-primary;
          .el-icon-arrow-down {
            transform: rotateZ(180deg);
          }
        }
      }
      * {
        vertical-align: initial;
      }
      .aui-navbar__icon-menu {
        vertical-align: middle;
        font-size: 16px;
      }
      .el-dropdown {
        color: $--color-text-secondary;
        .el-icon-arrow-down {
          width: auto;
          font-size: 13px;
          margin: 0 0 0 5px;
          transition: transform .3s;
        }
      }
    }
    .el-badge {
      display: inline;
      z-index: 2;
      &__content {
        line-height: 16px;
      }
    }
  }
  &__search {
    > *[class*="el-icon-"],
    > *[class*="icon"] {
      display: inline-block;
      vertical-align: middle;
    }
    &-txt {
      width: 0;
      transition: width .3s, margin-left .3s;
      &.is-show {
        width: 210px;
        margin-left: 8px;
      }
      .el-input__inner {
        height: $navbar--height - 20px;
        padding: 0;
        line-height: $navbar--height - 20px;
        border-color: $--color-text-primary;
        border-top: 0;
        border-right: 0;
        border-left: 0;
        border-radius: 0;
        background: transparent;
      }
    }
  }
  &__avatar {
    .el-dropdown-link {
      > img {
        width: 36px;
        height: auto;
        margin-right: 5px;
        border-radius: 100%;
        vertical-align: middle;
      }
    }
  }
}


/* Sidebar
------------------------------ */
.aui-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  z-index: 1020;
  width: $sidebar--width;
  display: flex;
  flex-direction: column;
  /*background-color: #fff;*/
  box-shadow: 1px 0 2px 0 rgba(0, 0, 0, .05);
  overflow: hidden;
  transition: width .3s;
  &--dark {
    // background-color: $sidebar--background-color-dark;
    background-color: #fff;
    .aui-sidebar__menu,
    > .el-menu--popup {
      // background-color: $sidebar--background-color-dark;
      background-color: $sidebar--background-color-dark;
      .el-menu-item,
      .el-submenu > .el-submenu__title {
        color: $sidebar--text-color-dark;
        font-size: 13px;
        &:focus,
        &:hover {
          color: mix(#fff, $sidebar--text-color-dark, 50%);
          // background-color: mix(#fff, $sidebar--background-color-dark, 2.5%);
          background-color: $--color-primary;
        }
        i{
          color: #fff;
        }
      }
      .el-menu,
      .el-submenu.is-opened {
        background-color: mix(#000, $sidebar--background-color-dark, 15%);
      }
      .el-menu-item.is-active{
        background-color: $--color-primary;
      }
      .el-submenu.is-active > .el-submenu__title {
        color: mix(#fff, $sidebar--text-color-dark, 80%);
      }
    }

  }
  &__inner {
    position: relative;
    z-index: 1;
    width: $sidebar--width + 20px;
    height: 100%;
    flex: 1;
    overflow-x: hidden;
    overflow-y: scroll;
    background: $sidebar--background-color-dark;
    background-size: cover;
    transition: width .3s;
  }
  &__menu {
    width: $sidebar--width;
    border-right: 0;
    background-color: transparent;
    transition: width .3s;
    .el-menu-item,
    .el-submenu__title {
      font-size: 13px;
      height: $sidebar--menu-item-height;
      line-height: $sidebar--menu-item-height;
    }
  }
  &__menu-icon {
    display: inline-block;
    vertical-align: middle;
    width: 24px !important;
    margin-right: 5px;
    text-align: center;
    font-size: 13px;
    color: inherit !important;
    transition: font-size .3s;
  }
}


/* Content
------------------------------ */
.aui-content {
  position: relative;
  padding: $content--padding;
  min-height: calc(100vh - #{$content--between});
  &__wrapper {
    position: relative;
    margin-left: $sidebar--width;
    min-height: calc(100vh - #{$content--between});
    background-color: $content--background-color;
    transition: margin-left .3s;
  }
  > .aui-card--fill > .el-card__body {
    min-height: calc(#{$content--fill-height} - 2px);
  }
  > .aui-card--fill > .el-card__header + .el-card__body {
    min-height: calc(#{$content--fill-height} - #{$content--card-header-height} - 2px);
  }
  &--tabs {
    padding: $content--tabs-header-height 0 0;
  }
  &--tabs-tools {
    position: fixed;
    top: $navbar--height;
    right: 0;
    z-index: 931;
    min-width: $content--tabs-header-height;
    height: $content--tabs-header-height;
    padding: 0 12px;
    text-align: center;
    font-size: 16px;
    line-height: $content--tabs-header-height;
    background-color: $--background-color-base;
    cursor: pointer;
  }
  &--tabs-icon-nav {
    display: inline-block;
    vertical-align: middle;
    font-size: 16px;
  }
  > .el-tabs {
    > .el-tabs__header {
      position: fixed;
      top: $navbar--height;
      left: $sidebar--width;
      right: 0;
      z-index: 930;
      padding: 0 55px 0 15px;
      margin: 0;
      box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .05);
      background-color: #fff;
      transition: left .3s;
      > .el-tabs__nav-wrap {
        margin-bottom: 0;
        &:after {
          display: none;
        }
        > .el-tabs__nav-next,
        > .el-tabs__nav-prev {
          line-height: $content--tabs-header-height;
        }
        > .el-tabs__nav-scroll > .el-tabs__nav {
          & > .el-tabs__active-bar {
            display: none;
          }
          & > .el-tabs__item {
            height: $content--tabs-header-height;
            padding: 0 15px;
            line-height: $content--tabs-header-height;
            border: 0;
            color: $--color-text-regular;
            &:focus,
            &:hover,
            &.is-active {
              color: $--color-text-primary;
              background-color: $--background-color-base;
              &:after {
                display: block;
              }
              > .el-icon-close {
                color: $--color-text-primary;
              }
            }
            &:after {
              display: none;
              position: absolute;
              bottom: 0;
              left: 0;
              content: '';
              width: 100%;
              height: 2px;
              background-color: $--color-primary;
            }
            + .el-tabs__item {
              margin-left: 1px;
            }
            > .el-icon-close {
              width: 14px;
              margin-left: 15px;
              color: $--color-text-secondary;
            }
            > i.icon {
              display: inline-block;
              vertical-align: middle;
              font-size: 18px;
            }
          }
        }
      }
    }
    > .el-tabs__content {
      padding: $content--padding;

      > .el-tab-pane {
        min-height: calc(#{$content--fill-height-tabs});

        > .aui-card--fill > .el-card__body {
          min-height: calc(#{$content--fill-height-tabs} - 2px);
        }

        > .aui-card--fill > .el-card__header + .el-card__body {
          min-height: calc(#{$content--fill-height-tabs} - #{$content--card-header-height} - 2px);
        }

        &.is-iframe {
          height: calc(#{$content--fill-height-tabs} + #{$content--padding * 2});
          margin: -$content--padding;
          min-height: auto;

          > .aui-card--fill {
            background-color: transparent;
          }

          > .aui-card--fill > .el-card__header {
            background-color: #fff;
          }

          > .aui-card--fill > .el-card__body {
            height: calc(#{$content--fill-height-tabs} - 2px);
            margin: $content--padding;
            min-height: auto;
            border: $--border-base;
            border-color: $--border-color-lighter;
            border-radius: $--border-radius-base;
            background-color: #fff;
          }

          > .aui-card--fill > .el-card__header + .el-card__body {
            height: calc(#{$content--fill-height-tabs} - #{$content--card-header-height} - 2px);
          }
        }
      }
    }
  }

  // quill富文本编辑器
  .ql-toolbar {
    line-height: 20px;
    &.ql-snow {
      border-color: $--border-color-base;
    }
    .ql-formats {
      margin: 0 5px;
    }
  }
  .ql-container {
    height: 150px;
    &.ql-snow {
      border-color: $--border-color-base;
    }
  }
}


/* Page
------------------------------ */
*[class*="aui-page__"] {
  padding-top: 0;
  .aui-content {
    min-height: auto;
    &__wrapper {
      min-height: 100vh;
      margin-left: 0;
    }
    > .aui-card--fill > .el-card__body {
      min-height: calc(100vh - #{$content--padding * 2} - 2px);
    }
    > .aui-card--fill > .el-card__header + .el-card__body {
      min-height: calc(100vh - #{$content--padding * 2} - #{$content--card-header-height} - 2px);
    }
  }
}

// 重写Dropdown 下拉菜单
.el-button-group .el-button:not(:last-child) {
  padding-bottom: 8px;
}

// 当table 设置fixed的时候 展开expand的内容时候左右拉动 fixed table的内容没有隐藏
.el-table--scrollable-x {
  .el-table__fixed-right {
    .el-table__expanded-cell {
      .index-table-expand {
        visibility: hidden;
      }
    }
  }

  .el-table__body-wrapper:not(.is-scrolling-left) + .el-table__fixed {
    .el-table__fixed-body-wrapper {
      .index-table-expand {
        visibility: hidden;
      }
    }
  }
}
.el-table__body-wrapper.is-scrolling-left + .el-table__fixed{
  .el-table__fixed-body-wrapper{
    .index-table-expand {
      visibility: visible;
    }
  }
}
.el-table__body-wrapper.is-scrolling-right + .el-table__fixed-right{
  .el-table__expanded-cell{
    .index-table-expand {
      visibility: visible;
    }
  }
}
.el-table__body-wrapper.is-scrolling-right ~ .el-table__fixed-right{
  .el-table__expanded-cell{
    .index-table-expand {
      visibility: visible;
    }
  }
}

