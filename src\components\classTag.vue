<template>
  <div>
    <div v-html="html"></div>
    <slot></slot>
  </div>
</template>
<script>
export default {
  props: {
    level: { // tag h
      type: Number,
      default: 1
    },
    newClas: { // 新增样式
      type: String,
      default: ''
    },
    title: { // 标题
      type: String,
      default: ''
    },
    align: { // 对齐
      type: String,
      default: 'center'
    },
    width: { // 线宽度
      type: String,
      default: '100%'
    },
    widthLine: {
      type: String,
      default: '100%'
    },
    direction: { // 线定位方向
      type: String,
      default: 'left'
    },
    X: { // 水平平移  居中 （100 - width） / 2
      type: String,
      default: '0'
    },
    titleX: { // 水平平移  居中 （100 - width） / 2
      type: String,
      default: '0'
    }
  },
  data () {
    return {
      html: ''
    }
  },
  created () {
    this.$nextTick(() => {
      this.html = `<h${this.level} class="class_tag ${this.newClas}" style="width:${this.width}; text-align: ${this.align}">
      <div class="tag_line" style="width:${this.widthLine}; ${this.direction}:${this.X}"></div>
        ${this.hasTitle()}
      </h${this.level}>`
    })
  },
  methods: {
    hasTitle () {
      if (this.title) {
        return `<span class="class_tag_title" style="margin-left:${this.titleX}">${this.title}</span>`
      } else {
        return ``
      }
    }
  }
}
</script>
<style lang="scss">
// 高级查询
.class_tag{
  display: inline-block;
  position: relative;
  margin:20px auto;
  text-align: center;
  color:#666;
  .tag_line{
    position: absolute;
    top: 50%;
    height:1px;
    background: #eee;
    z-index: 1;
  }
  .class_tag_title{
    position: relative;
    background: #fff;
    padding: 10px;
    z-index: 1;
  }
}

</style>
