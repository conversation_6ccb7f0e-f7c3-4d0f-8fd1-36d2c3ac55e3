<template>
  <el-dialog :title="$t('export')" :visible.sync="addMemoDialogFormVisible" width="35%"
             :show-close="false" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-container>
      <el-main>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-badge :value="total" class="item" type="warning">
              <el-button size="small">导出总数</el-button>
            </el-badge>
          </el-col>
          <el-col :span="12">
            <el-button size="small" :loading="true">正在导出</el-button>
          </el-col>
        </el-row>
        <el-row v-if="total>50000" style="padding-top: 20px;">
          <el-col>
            <el-col :span="20">
              数据量大时导出时间可能需数分钟，请耐心等候
            </el-col>
          </el-col>
        </el-row>
      </el-main>
    </el-container>
  </el-dialog>
</template>

<script>
import Cookies from 'js-cookie'
import qs from 'qs'
import axios from 'axios'
import request from '@/utils/request'
export default {
  data () {
    return {
      addMemoDialogFormVisible: true,
      dialogForm: {
      },
      exportURL: '',
      exportMethod: 'get',
      total: 0,
      estimatedTime: 0
    }
  },
  methods: {
    exportDialogHandle () {
      var params = qs.stringify({
        'token': Cookies.get('cs_token'),
        ...this.dialogForm
      })
      let requestUrl = this.exportMethod === 'get' ? `${this.$baseUrl}${this.exportURL}?${params}` : `${this.$baseUrl}${this.exportURL}`
      let httpUtil = axios.create({
        baseURL: request.localtionUrl,
        timeout: 1000 * 180,
        withCredentials: true
      })
      let fileName = ''
      /**
       * 响应拦截
       */
      httpUtil.interceptors.response.use(response => {
        if (!response.headers['content-type'].includes('application/vnd.ms-excel')) {
          let enc = new TextDecoder('utf-8')
          let resJson = JSON.parse(enc.decode(new Uint8Array(response.data))) // 转化成json对象
          return this.$message.error(resJson.msg)
        } else {
          fileName = decodeURIComponent(response.headers['filename'])
          return response
        }
      }, error => {
        return Promise.reject(error)
      })
      httpUtil({
        method: this.exportMethod,
        url: requestUrl,
        data: this.exportMethod === 'post' ? params : '',
        responseType: 'arraybuffer'
      })
        .then(({ data: res }) => {
          if (undefined === res || res === null) {
            this.addMemoDialogFormVisible = false
            return
          }
          const link = document.createElement('a')
          let blob = new Blob([res], { type: 'application/vnd.ms-excel' })
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          link.download = fileName // 下载的文件名
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          this.addMemoDialogFormVisible = false
        })
        .catch(() => {})
    }
  },
  computed: {
    dataRule () {
      return {
        opeareteDescription: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  }
}
</script>

<style scoped>

</style>
