<template>
  <nav class="aui-navbar" :class="`aui-navbar--${$store.state.navbarLayoutType}`">

    <div class="aui-navbar__body">
      <el-menu class="aui-navbar__menu mr-auto" mode="horizontal">
        <el-menu-item index="1" @click="$store.state.sidebarFold = !$store.state.sidebarFold">
          <svg class="icon-svg aui-navbar__icon-menu aui-navbar__icon-menu--switch" aria-hidden="true"><use xlink:href="#icon-outdent"></use></svg>
        </el-menu-item>
        <el-menu-item index="2" @click="refresh()">
          <svg class="icon-svg aui-navbar__icon-menu aui-navbar__icon-menu--refresh" aria-hidden="true"><use xlink:href="#icon-sync"></use></svg>
        </el-menu-item>
        <el-menu-item index="3" @click="fullscreenHandle()">
          <svg class="icon-svg aui-navbar__icon-menu" aria-hidden="true"><use xlink:href="#icon-fullscreen"></use></svg>
        </el-menu-item>
      </el-menu>
      <el-menu class="aui-navbar__menu" mode="horizontal">
        <el-menu-item index="4">
          <el-popover
            placement="top-end"
            trigger="hover"
            :visible-arrow="false"
            >
            <div class="msg-panel">
              <div class="title">
                您有{{noticeObj.unreadCount}}条新消息未阅读~
              </div>
              <ul>
                <li class="flex msg_item" v-for="(item, index) in noticeObj.list" :key="index">
                  <div class="flex_1 text-overflow">
                    <el-link :underline="false" @click="viewDetail(item)">{{index + 1}}、{{item.title}}</el-link>
                  </div>
                </li>
                <li class="flex_1 msg_item text-center" v-if="!noticeObj.unreadCount">
                  <div class="fontSize32 el-icon-chat-dot-square info"></div>
                  <div class="fontSize14 info">{{$t('noData')}}</div>
                </li>
              </ul>
              <span class="more-msg">
                <router-link :to="{name: 'message-notice-receiver'}" :underline="false" >查看全部消息<em class="el-icon-d-arrow-right"></em></router-link>
              </span>
            </div>
            <div slot="reference" >
              <el-badge :value="noticeObj.unreadCount" :is-dot="!noticeObj.unreadCount" :max="99" class="bell_icon">
                <i class="el-icon-bell " :class="noticeObj.unreadCount>0?'icon-animated-bell': ''"></i>
              </el-badge>
            </div>
          </el-popover>
        </el-menu-item>
        <el-menu-item index="5" @click.native="help()">
           <el-tooltip content="帮助文档">
            <svg class="icon-svg aui-navbar__icon-menu aui-navbar__icon-menu--refresh" aria-hidden="true"><use xlink:href="#icon-question"></use></svg>
           </el-tooltip>
        </el-menu-item>
        <!-- <el-menu-item index="6">
          <el-dropdown placement="bottom" :show-timeout="0">
            <svg class="icon-svg aui-navbar__icon-menu" aria-hidden="true"><use xlink:href="#icon-earth"></use></svg>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="(val, key) in i18nMessages" :key="key" v-if="key == 'zh-CN'" @click.native="$i18n.locale = key">{{ val._lang }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-menu-item> -->
        <el-menu-item index="7" class="aui-navbar__avatar">
          <el-dropdown placement="bottom" :show-timeout="0">
            <span class="el-dropdown-link">
              <img src="~@/assets/img/avatar.png">
              <span>{{ $store.state.user.realName }}</span>
              <i class="el-icon-arrow-down"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="updatePasswordHandle()">{{ $t('updatePassword.title') }}</el-dropdown-item>
              <el-dropdown-item @click.native="logoutHandle()">{{ $t('logout') }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-menu-item>
      </el-menu>
    </div>
    <!-- 弹窗, 修改密码 -->
    <update-password v-if="updatePassowrdVisible" ref="updatePassowrd"></update-password>
    <!-- 弹窗，配置快捷菜单 -->
    <set-menu v-if="setMenuVisible" ref="setMenu" @setTopMenu="topMenuFn"></set-menu>
    <!-- 弹窗, 通知 -->
    <notice-view v-if="noticeVisible" ref="noticeView"></notice-view>
  </nav>
</template>

<script>
import { messages } from '@/i18n'
import screenfull from 'screenfull'
import UpdatePassword from './main-navbar-update-password'
import NoticeView from './main-navbar-notice'
import setMenu from './main-menu-set'
import { clearLoginInfo } from '@/utils'
import storage from 'good-storage'
import { mapState } from 'vuex'
export default {
  inject: ['refresh'],
  data () {
    return {
      i18nMessages: messages,
      updatePassowrdVisible: false,
      setMenuVisible: false,
      noticeVisible: false,
      topMenuList: [],
      noticeCallId: ''
    }
  },
  components: {
    UpdatePassword,
    NoticeView,
    setMenu
  },
  mounted () {
    let topMenu = this.$store.state.topMenu || storage.get('topMenu')
    if (topMenu) {
      this.topMenuList = topMenu.resourceList || []
    }
    // Promise.all([this.getNoticeList(), this.getNoticeCount()]).then(({ data: res }) => {}).catch(() => {})
    Promise.all([
      this.getUnreadNotices(),
      this.noticeCallId = setInterval(this.getUnreadNotices, 60 * 60 * 1000)
    ]).then(({ data: res }) => {}).catch(() => {})
  },
  activated () {
    // Promise.all([this.getNoticeList(), this.getNoticeCount()]).then(({ data: res }) => {}).catch(() => {})
  },
  beforeDestroy () {
    if (this.noticeCallId !== '') {
      clearInterval(this.noticeCallId)
    }
  },
  computed: {
    ...mapState({ noticeObj: 'notice' })
  },
  methods: {
    // 获取未读的通知  系统消息
    async getUnreadNotices () {
      await this.$http.get(`/message/notice/getUnreadList/1`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        console.log('unreadCount = ' + res.data.length)
        this.noticeObj.list = res.data
        this.noticeObj.count = res.data.length
        this.noticeObj.unreadCount = res.data.length
        this.noticeObj.unreadSet = new Set([...res.data])
      }).catch(() => {})
    },
    // 全屏
    fullscreenHandle () {
      if (!screenfull.enabled) {
        return this.$message({
          message: this.$t('fullscreen.prompt'),
          type: 'warning',
          duration: 500
        })
      }
      screenfull.toggle()
    },
    topMenuFn (data) {
      this.topMenuList = data.resourceList || []
    },
    // 帮助手册
    help () {
      // console.log('store:' + this.$route.path)
      // console.log(this.$store.state.sidebarMenuList)
      let helpSite = window.SITE_CONFIG['helpSite']
      let currentMenu
      for (let menu of this.$store.state.sidebarMenuList) {
        currentMenu = this.getCurrectMenu(menu)
        if (currentMenu !== null) {
          break
        }
      }
      if (currentMenu === null) {
        window.open(helpSite)
        return
      }
      let helpUrl = currentMenu.helpUrl
      if (helpUrl) {
        window.open(helpSite + helpUrl)
      } else {
        window.open(helpSite)
      }
    },
    // 递归获取当前菜单
    getCurrectMenu (menu) {
      if (this.$route.path.replace('/', '') === menu.url.replace('/', '-').trim()) {
        return menu
      }
      if (menu.children && menu.children.length > 0) {
        // return menu.children.forEach(subMenu => )
        for (let subMenu of menu.children) {
          let currentMenu = this.getCurrectMenu(subMenu)
          if (currentMenu !== null) {
            return currentMenu
          }
        }
      }
      return null
    },
    // 查看通知详情
    viewDetail (item) {
      this.noticeVisible = true
      this.$nextTick(() => {
        this.$refs.noticeView.init()
      })
    },
    // 修改密码
    updatePasswordHandle () {
      this.updatePassowrdVisible = true
      this.$nextTick(() => {
        this.$refs.updatePassowrd.init()
      })
    },
    // 退出
    logoutHandle () {
      this.$confirm(this.$t('prompt.info', { 'handle': this.$t('logout') }), this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        this.$http.post('/cs/auth/logout').then(({ data: res }) => {
          // if (res && res.code !== 0) {
          //   return this.$message.error(res.msg)
          // }
          this.$store.state.contentTabs = []
          clearLoginInfo()
          this.$router.push({ name: 'login' })
        }).catch(() => {})
      }).catch(() => {})
    },
    // 设置导航
    updateMenuHandle () {
      this.setMenuVisible = true
      this.$nextTick(() => {
        this.$refs.setMenu.init()
      })
    },
    // 通过menuId与动态(菜单)路由进行匹配跳转至指定路由
    gotoRouteHandle (menuId, child) {
      let route = ''
      if (child) {
        route = child.children.filter(item => item.id === menuId)[0]
      } else {
        route = this.topMenuList.filter(item => item.id === menuId)[0]
      }
      if (route) {
        let url = route.url.replace(/\//g, '-')
        this.$router.push({ path: url })
      }
    }
  }
}
</script>
<style lang="scss">
  .fastMenu{
    position: relative;
    .el-icon-setting{
      margin-top: -4px;
      vertical-align: middle;
    }
    &:after{
      content: '';
      position: absolute;
      top: 11px;
      bottom: 10px;
      right: 0;
      width: 1px;
      border-right:1px solid #fff;
    }
  }
  .bell_icon{
    line-height: 56px;
  }
  .icon-animated-bell {
    display: inline-block;
    animation: ringing 2s infinite ease 1s;
    transform-origin: 50% 0
  }
  @keyframes ringing {
    0% {
      transform: rotate(-15deg)
    }
    2% {
      transform: rotate(15deg)
    }
    4% {
      transform: rotate(-18deg)
    }
    6% {
      transform: rotate(18deg)
    }
    8% {
      transform: rotate(-22deg)
    }
    10% {
      transform: rotate(22deg)
    }
    12% {
      transform: rotate(-18deg)
    }
    14% {
      transform: rotate(18deg)
    }
    16% {
      transform: rotate(-12deg)
    }
    18% {
      transform: rotate(12deg)
    }
    20% {
      transform: rotate(0deg)
    }
  }
  .msg-panel{
    width: 316px;
    transform-origin: top right;
    background-color: #fff;
    margin: -12px;
    .title{
      display: inline-block;
      width: 100%;
      height: 48px;
      line-height: 48px;
      background-color: #2378be;
      color: #fff;
      padding: 0 24px;
      font-size: 14px;
    }
    & > ul {
      padding: 0;
      margin: 0;
      .msg_item{
        padding: 10px;
        border-bottom: 1px dashed #E4E7ED;
      }
    }
    .more-msg {
      display: inline-block;
      width: 100%;
      height: 40px;
      line-height: 40px;
      font-size: 12px;
      text-align: center;
      color: #666;
      margin-bottom: 6px;
      position: relative;
    }
  }
</style>
