<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1, viewport-fit=cover">
  <meta http-equiv="pragram" content="no-cache">
  <meta http-equiv="cache-control" content="no-cache, no-store, must-revalidate">
  <meta http-equiv="expires" content="0">
  <meta name="format-detection" content="telephone=no">
  <link rel="shortcut icon" href="<%= BASE_URL %><%= process.env.VUE_APP_FAVICON %>">
  <!-- 站点配置 -->
  <script>
    window.SITE_CONFIG = {}
    window.SITE_CONFIG['version'] = 'v1.1.0'
    window.SITE_CONFIG['nodeEnv'] = '<%= process.env.VUE_APP_NODE_ENV %>'
    window.SITE_CONFIG['apiURL'] = '<%= process.env.VUE_APP_API_PROXY_URL %>' // api请求地址
    window.SITE_CONFIG['storeState'] = {} // vuex本地储存初始化状态（用于不刷新页面的情况下，也能重置初始化项目中所有状态）
    window.SITE_CONFIG['contentTabDefault'] = { // 内容标签页默认属性对象
      'name': '', // 名称, 由 this.$route.name 自动赋值（默认，名称 === 路由名称 === 路由路径）
      'params': {}, // 参数, 由 this.$route.params 自动赋值
      'query': {}, // 查询参数, 由 this.$route.query 自动赋值
      'menuId': '', // 菜单id（用于选中侧边栏菜单，与this.$store.state.sidebarMenuActiveName进行匹配）
      'title': '', // 标题
      'isTab': true, // 是否通过tab展示内容?
      'iframeURL': '' // 是否通过iframe嵌套展示内容? (以http[s]://开头, 自动匹配)
    }
    window.SITE_CONFIG['menuList'] = [] // 左侧菜单列表（后台返回，未做处理）
    window.SITE_CONFIG['permissions'] = [] // 页面按钮操作权限（后台返回，未做处理）
    window.SITE_CONFIG['dynamicRoutes'] = [] // 动态路由列表
    window.SITE_CONFIG['dynamicMenuRoutes'] = [] // 动态(菜单)路由列表
    window.SITE_CONFIG['dynamicMenuRoutesHasAdded'] = false // 动态(菜单)路由是否已经添加的状态标示（用于判断是否需要重新拉取数据并进行动态添加操作）
    window.SITE_CONFIG['helpSite'] = 'http://doc.goto56.com/project-2/'
  </script>
</head>
<body>
  <div id="app"></div>
</body>
</html>
