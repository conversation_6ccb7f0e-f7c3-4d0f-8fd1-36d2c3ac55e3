<template>
  <div class="add-body panel_body">
    <!-- <div class="panel-hd">
      <span>{{ opType ? $t(opType): '订单录入'}}</span>
    </div> -->
    <div class="addOrUpdatePanel orderAdd" >
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" :disabled="this.dataForm.status==10 ? false: true" label-width="160px">
        <el-tabs class="no_shadow" type="border-card" v-model="activeFirstTab" :stretch="false"  >
          <el-tab-pane label="主信息" name="firstTab"></el-tab-pane>
          <el-tab-pane label="收件人信息" name="secondTab"></el-tab-pane>
          <el-tab-pane label="发件人信息" name="threeTab"></el-tab-pane>
          <!-- 第一步 -->
          <el-row :gutter="20" v-show="activeFirstTab === 'firstTab'">
            <el-col :md="{span:22}" style="padding-top: 10px">
              <el-row :gutter="10">
                <el-col :span="10" >
                  <el-form-item :label="$t('coOrder.logisticsProductCode')" prop="logisticsProductCode">
                    <el-select v-model="dataForm.logisticsProductCode" :placeholder="$t('coOrder.logisticsProductCode')" @change='logisticsProductChange($event)' auto-complete="new-password" filterable clearable>
                      <el-option v-for="item in logisticsProductByParamsList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                      <el-divider v-if='showAllProductBtn || showCustomerSetProductBtn'></el-divider>
                      <el-button type='type' class='logisticsproductcode-select-btn' v-if='showAllProductBtn' @click="showAllProductHandle()" icon="el-icon-d-caret">展示全部物流产品</el-button>
                      <el-button type='type' class='logisticsproductcode-select-btn' v-if='showCustomerSetProductBtn' @click="showCustomerSetProductHandle()" icon="el-icon-caret-top">展示常用物流产品</el-button>
                    </el-select>
                  </el-form-item>
                  <el-form-item :label="$t('coOrder.customerOrderNo')" prop="customerOrderNo">
                    <el-input v-model="dataForm.customerOrderNo" :placeholder="$t('coOrder.customerOrderNo')"></el-input>
                  </el-form-item>
                  <el-form-item :label="$t('coOrder.subCustomerOrderNo')" prop="subCustomerOrderNo">
                    <el-input v-model="dataForm.subCustomerOrderNo" :placeholder="$t('coOrder.subCustomerOrderNo')"></el-input>
                  </el-form-item>
                  <el-form-item :label="$t('coOrderShipper.iossTaxType')" prop="iossTaxType">
                    <el-select v-model="dataForm.shipper.iossTaxType" :placeholder="$t('coOrderShipper.iossTaxType')">
                      <el-option v-for="item in iossTypeList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item :label="$t('coOrderShipper.iossNo')" prop="iossNo">
                    <el-input v-model="dataForm.shipper.iossNo" :placeholder="$t('coOrderShipper.iossNo')"></el-input>
                  </el-form-item>
                  <el-form-item :label="$t('coOrderShipper.eoriNo')" prop="eoriNo">
                    <el-input v-model="dataForm.shipper.eoriNo" :placeholder="$t('coOrderShipper.eoriNo')"></el-input>
                  </el-form-item>
                  <el-form-item :label="$t('coOrderShipper.vatNo')" prop="vatNo">
                    <el-input v-model="dataForm.shipper.vatNo" :placeholder="$t('coOrderShipper.vatNo')"></el-input>
                  </el-form-item>
                  <el-form-item :label="$t('coOrder.waybillNo')" prop="waybillNo">
                    <el-input v-model="dataForm.waybillNo" :placeholder="$t('coOrder.waybillNoPlaceHold')" :disabled='waybillNoSource !== null && waybillNoSource !== 0'></el-input>
                  </el-form-item>
                  <el-form-item :label="$t('coOrder.deliveryNo')" prop="deliveryNo">
                    <el-input v-model="dataForm.deliveryNo" :placeholder="$t('coOrder.deliveryNoPlaceHold')"></el-input>
                  </el-form-item>
                  <el-form-item :label="$t('coOrder.declareCurrency')" prop="declareCurrency">
                    <el-select v-model="dataForm.declareCurrency" :placeholder="$t('coOrder.declareCurrency')" filterable clearable>
                      <el-option v-for="item in currencyList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item  :label="$t('coOrder.parcelType')" prop="parcelType" >
                    <el-select  filterable v-model="dataForm.parcelType" :placeholder="$t('coOrder.parcelType')" >
                      <el-option v-for="(item, index) in parcelTypeList"  :key="index" :label="item.dictName" :value="item.dictValue"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item :label="$t('coOrder.electric')" prop="electric">
                    <el-radio v-model="dataForm.electric" :label="0">否</el-radio>
                    <el-radio v-model="dataForm.electric" :label="1">是</el-radio>
                  </el-form-item>
                  <!--
                  <el-form-item :label="$t('coOrder.deliveryNo')" prop="deliveryNo">
                    <el-input v-model="dataForm.deliveryNo" :placeholder="$t('coOrder.deliveryNo')" disabled></el-input>
                  </el-form-item>
                  <el-form-item :label="$t('coOrder.status')" prop="status">
                    <el-select v-model="dataForm.status" :placeholder="$t('coOrder.status')" disabled>
                      <el-option v-for="item in orderStatusList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                    </el-select>
                  </el-form-item>-->
                </el-col>
                <el-col :span="10" :offset="2" >
                  <el-form-item :label="$t('coOrder.shopName')" prop="shopName">
                    <el-input v-model="dataForm.shopName" :placeholder="$t('coOrder.shopName')"></el-input>
                  </el-form-item>
                  <el-form-item :label="$t('coOrder.insuredCurrency')" prop="insuredCurrency">
                    <el-select v-model="dataForm.insuredCurrency" :placeholder="$t('coOrder.insuredCurrency')" filterable clearable>
                      <el-option v-for="item in currencyList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item :label="$t('coOrder.insuredAmount')" prop="insuredAmountD">
                    <el-input v-model="dataForm.insuredAmountD" :placeholder="$t('coOrder.insuredAmount')"></el-input>
                  </el-form-item>
                  <el-form-item :label="$t('coOrder.codCurrency')" prop="codCurrency">
                    <el-select v-model="dataForm.codCurrency" :placeholder="$t('coOrder.codCurrency')" filterable clearable>
                      <el-option v-for="item in currencyList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item :label="$t('coOrder.codAmount')" prop="codAmountD">
                    <el-input v-model="dataForm.codAmountD" :placeholder="$t('coOrder.codAmount')"></el-input>
                  </el-form-item>
                  <el-form-item :label="$t('coOrder.taxPayMode')" prop="taxPayMode">
                    <el-select v-model="dataForm.taxPayMode" :placeholder="$t('coOrder.taxPayMode')">
                      <el-option v-for="item in taxPayModeList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <el-col :md="20" v-if="packageDataList.length<2">
              <el-row>
                <el-col :span="8">
                  <el-form-item :label="$t('coOrderPackage.packageWeight')" :required='true'>
                    <el-input v-model="orderPackage.orderWeightD" :placeholder="$t('coOrderPackage.packageWeight')" v-on:change="setOrderPackage"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row >
                <el-col :span="8">
                  <el-form-item :label="$t('coOrderPackage.packageLength')">
                    <el-input v-model="orderPackage.orderLengthD" :placeholder="$t('coOrderPackage.packageLength')" v-on:change="setOrderPackage"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :label="$t('coOrderPackage.packageWidth')">
                    <el-input v-model="orderPackage.orderWidthD" :placeholder="$t('coOrderPackage.packageWidth')" v-on:change="setOrderPackage"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :label="$t('coOrderPackage.packageHeight')">
                    <el-input v-model="orderPackage.orderHeightD" :placeholder="$t('coOrderPackage.packageHeight')" v-on:change="setOrderPackage"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <el-col :md="20">
              <el-form-item :label="$t('coOrder.salesUrl')" prop="salesUrl">
                <el-input v-model="dataForm.salesUrl" :placeholder="$t('coOrder.salesUrl')"></el-input>
              </el-form-item>
              <el-form-item :label="$t('coOrder.customerRemark')" prop="customerRemark" >
                <el-input v-model="dataForm.customerRemark" type="textarea" :placeholder="$t('coOrder.customerRemark')" show-word-limit></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第二步 -->
          <!--收件人-->
          <el-row :gutter="20" v-show="activeFirstTab === 'secondTab'">
            <el-col :md="{span:22}" style="padding-top: 10px">
              <el-row :gutter="20">
                <el-col :md="10">
                  <el-form-item :label="$t('coSetCustomerShipper.consigneeShortName')">
                    <el-select v-model='dataForm.setSConsignee'  filterable clearable @change="selectSetConsignee">
                      <el-option v-for="item in setConsigneeByCustomerCodeList" :key="item.id" :label="item.shortName" :value="item.id"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :md="10">
                  <el-form-item :label="$t('coOrderConsignee.consigneeName')" prop="consignee.consigneeName">
                    <el-input v-model="dataForm.consignee.consigneeName" :placeholder="$t('coOrderConsignee.consigneeName')"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :md="10" :offset="1">
                  <el-form-item :label="$t('coOrderConsignee.consigneeCompany')" prop="consignee.consigneeCompany">
                    <el-input v-model="dataForm.consignee.consigneeCompany" :placeholder="$t('coOrderConsignee.consigneeCompany')"></el-input>
                  </el-form-item>
                </el-col>
                <div class="clearfix">
                  <el-col :md="10">
                    <el-form-item :label="$t('coOrderConsignee.consigneeCountry')" prop="consignee.consigneeCountry">
                      <el-select v-model="dataForm.consignee.consigneeCountry" :placeholder="$t('coOrderConsignee.consigneeCountry')" filterable clearable>
                        <el-option v-for="item in countryList" :key="item.code" :label="`${item.name} ${item.code}`" :value="item.code"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </div>
                <div class="clearfix">
                  <el-col :md="10">
                    <el-form-item :label="$t('coOrderConsignee.consigneeProvince')" prop="consignee.consigneeProvince">
                      <el-input v-model="dataForm.consignee.consigneeProvince" :placeholder="$t('coOrderConsignee.consigneeProvince')"></el-input>
                    </el-form-item>
                  </el-col>
                </div>
                <el-col :md="10">
                  <el-form-item :label="$t('coOrderConsignee.consigneeCity')" prop="consignee.consigneeCity">
                    <el-input v-model="dataForm.consignee.consigneeCity" :placeholder="$t('coOrderConsignee.consigneeCity')"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :md="10" :offset="1">
                  <el-form-item :label="$t('coOrderConsignee.consigneeDistrict')" prop="consignee.consigneeDistrict">
                    <el-input v-model="dataForm.consignee.consigneeDistrict" :placeholder="$t('coOrderConsignee.consigneeDistrict')"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :md="10">
                  <el-form-item :label="$t('coOrderConsignee.consigneePostcode')" prop="consignee.consigneePostcode">
                    <el-input v-model="dataForm.consignee.consigneePostcode" :placeholder="$t('coOrderConsignee.consigneePostcode')"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :md="10" :offset="1">
                  <el-form-item :label="$t('coOrderConsignee.consigneeStreet')" prop="consignee.consigneeStreet">
                    <el-input v-model="dataForm.consignee.consigneeStreet" :placeholder="$t('coOrderConsignee.consigneeStreet')"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :md="10">
                  <el-form-item :label="$t('coOrderConsignee.consigneePhone')" prop="consignee.consigneePhone">
                    <el-input v-model="dataForm.consignee.consigneePhone" :placeholder="$t('coOrderConsignee.consigneePhone')"></el-input>
                  </el-form-item>
                </el-col>

                <el-col :md="10" :offset="1">
                  <el-form-item :label="$t('coOrderConsignee.consigneeDoorplate')" prop="consignee.consigneeDoorplate">
                    <el-input v-model="dataForm.consignee.consigneeDoorplate" :placeholder="$t('coOrderConsignee.consigneeDoorplate')"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :md="21">
                  <el-form-item :label="$t('coOrderConsignee.consigneeAddress')" prop="consignee.consigneeAddress">
                    <el-input v-model="dataForm.consignee.consigneeAddress" :placeholder="$t('coOrderConsignee.consigneeAddress')"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :md="21" >
                  <el-form-item :label="$t('coOrderConsignee.consigneeTaxNo')" prop="consignee.consigneeTaxNo">
                    <el-input v-model="dataForm.consignee.consigneeTaxNo" :placeholder="$t('coOrderConsignee.consigneeTaxNo')"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :md="21" >
                  <el-form-item :label="$t('coOrderConsignee.consigneeIdcard')" prop="consignee.consigneeIdcard">
                    <el-input v-model="dataForm.consignee.consigneeIdcard" :placeholder="$t('coOrderConsignee.consigneeIdcard')"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :md="10" >
                  <el-form-item :label="$t('coOrderConsignee.consigneeEmail')" prop="consignee.consigneeEmail">
                    <el-input v-model="dataForm.consignee.consigneeEmail" :placeholder="$t('coOrderConsignee.consigneeEmail')"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
          <!--发件人-->
          <el-row :gutter="20" v-show="activeFirstTab === 'threeTab'">
            <el-col :md="{span:22}" style="padding-top: 10px">
              <el-row :gutter="20">
                <el-col :md="10">
                  <el-form-item :label="$t('coSetCustomerShipper.shipperShortName')">
                    <el-select v-model="dataForm.setShipper" :placeholder="$t('coSetCustomerShipper.shortName')" filterable clearable @change="selectSetShipper">
                      <el-option v-for="item in setShipperByCustomerCodeList" :key="item.id" :label="item.shortName" :value="item.id"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :md="10">
                  <el-form-item :label="$t('coOrderShipper.shipperName')" prop="shipper.shipperName">
                    <el-input v-model="dataForm.shipper.shipperName" :placeholder="$t('coOrderShipper.shipperName')"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :md="10" :offset="1">
                  <el-form-item :label="$t('coOrderShipper.shipperCompany')" prop="shipper.shipperCompany">
                    <el-input v-model="dataForm.shipper.shipperCompany" :placeholder="$t('coOrderShipper.shipperCompany')"></el-input>
                  </el-form-item>
                </el-col>
                <div class="clearfix">
                  <el-col :md="10">
                    <el-form-item :label="$t('coOrderShipper.shipperCountry')" prop="shipper.shipperCountry">
                      <el-select v-model="dataForm.shipper.shipperCountry" :placeholder="$t('coOrderShipper.shipperCountry')" filterable clearable>
                        <el-option v-for="item in countryList" :key="item.code" :label="`${item.name} ${item.code}`" :value="item.code"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </div>
                <div class="clearfix">
                  <el-col :md="10">
                    <el-form-item :label="$t('coOrderShipper.shipperProvince')" prop="shipper.shipperProvince">
                      <el-input v-model="dataForm.shipper.shipperProvince" :placeholder="$t('coOrderShipper.shipperProvince')"></el-input>
                    </el-form-item>
                  </el-col>
                </div>
                <el-col :md="10" >
                  <el-form-item :label="$t('coOrderShipper.shipperCity')" prop="shipper.shipperCity">
                    <el-input v-model="dataForm.shipper.shipperCity" :placeholder="$t('coOrderShipper.shipperCity')"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :md="10" :offset="1">
                  <el-form-item :label="$t('coOrderShipper.shipperDistrict')" prop="shipper.shipperDistrict">
                    <el-input v-model="dataForm.shipper.shipperDistrict" :placeholder="$t('coOrderShipper.shipperDistrict')"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :md="10">
                  <el-form-item :label="$t('coOrderShipper.shipperPostcode')" prop="shipper.shipperPostcode">
                    <el-input v-model="dataForm.shipper.shipperPostcode" :placeholder="$t('coOrderShipper.shipperPostcode')"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :md="10" :offset="1">
                  <el-form-item :label="$t('coOrderShipper.shipperStreet')" prop="shipper.shipperStreet">
                    <el-input v-model="dataForm.shipper.shipperStreet" :placeholder="$t('coOrderShipper.shipperStreet')"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :md="10">
                  <el-form-item :label="$t('coOrderShipper.shipperPhone')" prop="shipper.shipperPhone">
                    <el-input v-model="dataForm.shipper.shipperPhone" :placeholder="$t('coOrderShipper.shipperPhone')"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :md="10" :offset="1">
                  <el-form-item :label="$t('coOrderShipper.shipperDoorplate')" prop="shipper.shipperDoorplate">
                    <el-input v-model="dataForm.shipper.shipperDoorplate" :placeholder="$t('coOrderShipper.shipperDoorplate')"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :md="21">
                  <el-form-item :label="$t('coOrderShipper.shipperAddress')" prop="shipper.shipperAddress">
                    <el-input v-model="dataForm.shipper.shipperAddress" :placeholder="$t('coOrderShipper.shipperAddress')"></el-input>
                  </el-form-item>
                </el-col>

<!--                <el-col :md="6" >-->
<!--                  <el-form-item :label="$t('coSetCustomerShipper.shortName')">-->
<!--                    <el-select v-model="dataForm.setShipper" :placeholder="$t('coSetCustomerShipper.shortName')" filterable clearable @change="selectSetShipper">-->
<!--                      <el-option v-for="item in setShipperByCustomerCodeList" :key="item.id" :label="item.shortName" :value="item.id"></el-option>-->
<!--                    </el-select>-->
<!--                  </el-form-item>-->
<!--                </el-col>-->
                <el-col :md="10" >
                  <el-form-item :label="$t('coOrderShipper.shipperEmail')" prop="shipper.shipperEmail">
                    <el-input v-model="dataForm.shipper.shipperEmail" :placeholder="$t('coOrderShipper.shipperEmail')"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </el-tabs>

        <!-- 第三部 -->
        <el-tabs class="no_shadow packDetail" type="border-card" v-model="activeSecondName" :stretch="false"  >
          <el-tab-pane label="报关明细" name="declare">
            <div class="text-left">
              <el-form-item class="inline-block"  :label="$t('coOrder.goodsCategory')" prop="goodsCategory" label-width='80px'>
                <el-select v-model="dataForm.goodsCategory" :placeholder="$t('coOrder.goodsCategory')">
                  <el-option v-for="item in goodsCategoryList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                </el-select>
              </el-form-item>
            </div>
            <div id='declareDataForm'>
              <el-form :model="declareDataTableForm"  ref="declareDataForm" :inline-message='true'>
                <el-table :key="Math.random()" ref="declareDataList" v-loading="declareDataListLoading" :data="declareDataTableForm.declareDataList" border max-height="500">
                  <!-- 动态显示表格 -->
                  <!--<el-table-column type="selection" width='50' fixed="left"></el-table-column>-->
                  <el-table-column label="序号" type="index" width="50"></el-table-column>
                  <el-table-column v-for="(item, index) in declareTableColumnsArr" :key="index" :type="item.type" :prop="item.prop"
                                   :render-header="declareCheckColumns.indexOf(item.prop) > -1 ? addRedStar: notAddRedStar"
                                   header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
                    <template slot-scope="scope">
                      <el-form-item :prop="'declareDataList.' + scope.$index + '.'+item.prop"  :rules="declareCheckColumns.indexOf(item.prop) > -1 ? declareDataRules[item.prop]:undefined">
                        <el-input v-model="scope.row[item.prop]" :maxlength="item.maxlength"></el-input>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('handle')"  header-align="center" align="center" min-width="150">
                    <template slot-scope="scope">
                      <el-form-item>
                        <el-link :underline="false"  @click="declareViewHandle(scope.row, scope.$index)" :disabled="dataForm.status!==10">{{ $t('viewMore') }}</el-link>
                        <popconfirm i18nOperateValue="delete" @clickHandle="declareDeleteHandle(scope.$index)" :condition='dataForm.status===10' ></popconfirm>
                      </el-form-item>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form>
              <el-dropdown split-button plain type="primary"  trigger="click" style='width: 100%;margin: 10px 0px;' size="mini"
                           placement='bottom-end' @click='declareHandleConfirm' @command="declareHandleConfirm">
                <span class='el-icon-plus'>{{$t('coOrderPackage.addBtn')}}</span>
                <el-dropdown-menu slot="dropdown" style='min-width: 12%;'>
                  <el-dropdown-item :divided='index!==0' v-for="(item, index) in setDeclareByCustomerCodeList" :key='item.id' :command="item.id" >{{item.chineseName}}</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-form >
    </div>

    <div id="cs_FormFooter" class="el-form-footer" slot="footer">
      <el-button :loading='this.dataForm.status==10 ? false: true' @click="$closeFn">{{ $t('close') }}</el-button>
      <el-button size="medium" @click="draftOrder" :disabled="this.dataForm.status==10 ? false: true" >{{ $t('draft') }}</el-button>
      <el-button type="primary" size="medium" @click="forecastOrder" :loading='this.dataForm.status==10 ? false: true' :disabled="this.dataForm.status==10 ? false: true" >{{ $t('forecast') }}</el-button>
    </div>
    <!-- 报关明细 抽屉   -->
    <el-drawer
      :visible.sync="drawerVisible"
      direction="rtl"
      ref="drawer"
      :destroy-on-close='true'
      :wrapperClosable='false'
      :append-to-body='true'
      size='15%'
    >
      <template slot="title">
        <span>{{$t('viewMore')}}</span>
      </template>
      <div>
        <el-divider></el-divider>
        <el-form :model="rowDrawerData" ref='rowDrawerDataForm' label-width="90px" key='1' style='padding: 0px 10px;'>
          <el-form-item :label="$t('coOrderDeclare.sku')" >
            <el-input v-model="rowDrawerData.sku" maxlength="64" :placeholder="$t('coOrderDeclare.sku')" ></el-input>
          </el-form-item>
          <el-form-item :label="$t('coOrderDeclare.goodsBarcode')" >
            <el-input v-model="rowDrawerData.goodsBarcode" maxlength="36" :placeholder="$t('coOrderDeclare.goodsBarcode')" ></el-input>
          </el-form-item>
          <el-form-item :label="$t('coOrderDeclare.origin')">
            <el-select v-model="rowDrawerData.origin" :placeholder="$t('coOrderDeclare.origin')" filterable clearable>
              <el-option v-for="item in countryList" :key="item.code" :label="item.name" :value="item.code">
                <span style="float: left">{{ item.name }}</span>
                <span style="float: right; color: #DCDFE6; font-size: 13px">{{ item.code }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('coOrderDeclare.material')" >
            <el-input v-model="rowDrawerData.material" maxlength="64" :placeholder="$t('coOrderDeclare.material')" ></el-input>
          </el-form-item>
          <el-form-item :label="$t('coOrderDeclare.productModel')" >
            <el-input v-model="rowDrawerData.productModel" maxlength="32" :placeholder="$t('coOrderDeclare.productModel')" ></el-input>
          </el-form-item>
          <el-form-item :label="$t('coOrderDeclare.purpose')" >
            <el-input v-model="rowDrawerData.purpose" maxlength="64" :placeholder="$t('coOrderDeclare.purpose')" ></el-input>
          </el-form-item>
          <el-form-item :label="$t('coOrderDeclare.brand')"  >
            <el-input v-model="rowDrawerData.brand" maxlength="32" :placeholder="$t('coOrderDeclare.brand')" ></el-input>
          </el-form-item>
          <el-form-item :label="$t('coOrderDeclare.productUrl')"  >
            <el-input v-model="rowDrawerData.productUrl" maxlength="255" :placeholder="$t('coOrderDeclare.productUrl')" ></el-input>
          </el-form-item>
          <el-form-item :label="$t('coOrderDeclare.pickingRemark')" >
            <el-input v-model="rowDrawerData.pickingRemark" maxlength="512"  show-word-limit :autosize="{ minRows: 4}" type="textarea"  :placeholder="$t('coOrderDeclare.pickingRemark')" ></el-input>
          </el-form-item>
        </el-form>
        <div style='justify-content: center;display: flex;'>
          <el-button class='rowDrawerDataButton' @click="$refs.drawer.closeDrawer()">{{$t('close')}}</el-button>
          <el-button class='rowDrawerDataButton' type="primary" @click="saveRowDrawerData" >{{ $t('save') }}</el-button>
        </div>
      </div>
    </el-drawer>
    <!-- 包裹明细 -->
    <el-dialog title="包裹明细" :visible.sync="packageDialogVisible" width="60%" style="min-width: 900px; " :close-on-press-escape="false" :close-on-click-modal="false" :before-close="packageHandleClose">
      <el-form :model="packageDataForm" :rules="packageDataRule" ref="packageDataForm" >
        <el-row :gutter="20" type="flex" justify="center">
          <el-col>
            <el-row :gutter="10">
<!--              <el-col :span="12">-->
<!--                <el-form-item :label="$t('coOrderPackage.channelLabelUrl')" prop="channelLabelUrl">-->
<!--                  <el-input v-model="packageDataForm.channelLabelUrl" :placeholder="$t('coOrderPackage.channelLabelUrl')" disabled></el-input>-->
<!--                </el-form-item>-->
<!--              </el-col>-->
              <el-col :span="18">
                <el-form-item :label="$t('coOrderPackage.packageWeight')" prop="packageWeightD">
                  <el-input v-model="packageDataForm.packageWeightD" :placeholder="$t('coOrderPackage.packageWeight')"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col class="borderLeft">
            <el-row :gutter="10">
              <el-col :span="18">
                <el-form-item :label="$t('coOrderPackage.packageLength')" prop="packageLengthD">
                  <el-input v-model="packageDataForm.packageLengthD" :placeholder="$t('coOrderPackage.packageLength')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="18">
                <el-form-item :label="$t('coOrderPackage.packageWidth')" prop="packageWidthD">
                  <el-input v-model="packageDataForm.packageWidthD" :placeholder="$t('coOrderPackage.packageWidth')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="18">
                <el-form-item :label="$t('coOrderPackage.packageHeight')" prop="packageHeight">
                  <el-input v-model="packageDataForm.packageHeightD" :placeholder="$t('coOrderPackage.packageHeight')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="18">
                <el-form-item :label="$t('coOrderPackage.packageCustomerNo')" prop="packageCustomerNo">
                  <el-input v-model="packageDataForm.packageCustomerNo" :placeholder="$t('coOrderPackage.packageCustomerNo')"></el-input>
                </el-form-item>
              </el-col>
              <!--              <el-col :span="12">-->
              <!--                <el-form-item :label="$t('coOrderPackage.packageSerialNo')" prop="packageSerialNo">-->
              <!--                  <el-input v-model="packageDataForm.packageSerialNo" :placeholder="$t('coOrderPackage.packageSerialNo')"></el-input>-->
              <!--                </el-form-item>-->
              <!--              </el-col>-->
              <!--              <el-col :span="12">-->
              <!--                <el-form-item :label="$t('coOrderPackage.packageDeliveryNo')" prop="packageDeliveryNo">-->
              <!--                  <el-input v-model="packageDataForm.packageDeliveryNo" :placeholder="$t('coOrderPackage.packageDeliveryNo')"></el-input>-->
              <!--                </el-form-item>-->
              <!--              </el-col>-->
            </el-row>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
          <el-button @click="packageHandleClose">取 消</el-button>
          <el-button type="primary" @click="packageHandleConfirm">确 定</el-button>
        </span>
    </el-dialog>
  </div>
</template>

<script>
import debounce from 'lodash/debounce'
import cloneDeep from 'lodash/cloneDeep'
import dictTypeMixins from '@/mixins/dictTypeMixins'
import areaBox from '@/components/areaBox'
import { formatterType, gtmToLtm, timestampFormat } from '@/filters/filters'
import { isDecimal3, isDecimal2, isPlusInteger2, isChinese, isEmail } from '@/utils/validate'
import { Consignee } from '@/utils/fieldLength'
import baseDataApi from '@/api'
import baseData from '@/api/baseData'
import _oPairs from 'lodash/toPairs'
import closeMixins from '@/mixins/closeMixins'
export default {
  mixins: [dictTypeMixins, closeMixins],
  data () {
    return {
      active: 0,
      orderStatusList: [],
      logisticsProductByParamsList: [],
      countryList: [],
      currencyList: [],
      taxPayModeList: [],
      iossTypeList: [],
      parcelTypeList: [],
      goodsCategoryList: [],
      setShipper: null,
      setShipperByCustomerCodeList: [],
      setConsigneeByCustomerCodeList: [],
      setCustomerDeclare: null,
      setDeclareByCustomerCodeList: [],
      activeFirstName: 'consignee',
      activeSecondName: 'declare',
      activeFirstTab: 'firstTab',
      node: 11,
      waybillNoSource: '',
      waybillNoSourceMap: new Map(),
      dataForm: {
        id: null,
        customerOrderNo: null,
        subCustomerOrderNo: null,
        waybillNo: null,
        deliveryNo: null,
        status: 10,
        orderLogisticsType: 10,
        logisticsProductCode: null,
        taxPayMode: 11,
        parcelType: 10,
        goodsCategory: 15,
        electric: 0,
        insuredAmountD: null,
        insuredCurrency: 'USD',
        codAmountD: null,
        codCurrency: 'USD',
        declareCurrency: 'USD',
        customerRemark: null,
        salesUrl: null,
        shopName: null,
        consignee: {
          consigneeName: null,
          consigneeCompany: null,
          consigneePhone: null,
          consigneeEmail: null,
          consigneeCountry: null,
          consigneeProvince: null,
          consigneeCity: null,
          consigneeDistrict: null,
          consigneeAddress: null,
          consigneePostcode: null,
          consigneeDoorplate: null,
          consigneeStreet: null,
          consigneeTaxNo: null,
          consigneeIdcard: null
        },
        shipper: {
          iossTaxType: null,
          vatNo: null,
          vatCompanyEnName: null,
          vatRegisterCountry: null,
          vatRegisterAddress: null,
          eoriNo: null,
          shipperName: null,
          shipperCompany: null,
          shipperPhone: null,
          shipperEmail: null,
          shipperCountry: null,
          shipperProvince: null,
          shipperCity: null,
          shipperDistrict: null,
          shipperAddress: null,
          shipperPostcode: null,
          shipperDoorplate: null,
          shipperStreet: null
        },
        orderDeclareList: [],
        packageList: [],
        setShipper: null,
        setSConsignee: null
      },
      declareTableColumns: [
        { type: '', width: '150', maxlength: 18, prop: 'orderId', label: this.$t('coOrderDeclare.orderId'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '150', maxlength: 64, prop: 'chineseName', label: this.$t('coSetCustomerDeclare.chineseName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', maxlength: 64, prop: 'englishName', label: this.$t('coSetCustomerDeclare.englishName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', maxlength: 36, prop: 'hsCode', label: this.$t('coOrderDeclare.hsCode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', maxlength: 18, prop: 'unitDeclarePriceD', label: this.$t('coSetCustomerDeclare.unitDeclarePrice'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', maxlength: 18, prop: 'unitNetWeightD', label: this.$t('coSetCustomerDeclare.unitNetWeight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', maxlength: 18, prop: 'quantity', label: this.$t('coSetCustomerDeclare.quantity'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', maxlength: 18, prop: 'id', label: this.$t('system.id'), align: 'center', isShow: false, disabled: false }
      ],
      declareDataList: [],
      drawerVisible: false,
      opType: '',
      declareDataListLoading: false,
      declareDialogVisible: false,
      showAllProductBtn: false,
      showCustomerSetProductBtn: false,
      declareDataTableForm: {
        declareDataList: []
      },
      declareDataForm: {
        id: '',
        creator: '',
        createDate: '',
        orderId: '',
        chineseName: '',
        englishName: '',
        quantity: '',
        unitNetWeightD: '',
        unitDeclarePriceD: '',
        brand: '',
        goodsBarcode: '',
        sku: '',
        hsCode: '',
        productModel: '',
        material: '',
        purpose: '',
        origin: 'CN',
        pickingRemark: '',
        productUrl: '',
        setCustomerDeclare: ''
      },
      rowDrawerData: {
        index: '',
        id: '',
        creator: '',
        createDate: '',
        orderId: '',
        chineseName: '',
        englishName: '',
        quantity: '',
        unitNetWeightD: '',
        unitDeclarePriceD: '',
        brand: '',
        goodsBarcode: '',
        sku: '',
        hsCode: '',
        productModel: '',
        material: '',
        purpose: '',
        origin: 'CN',
        pickingRemark: '',
        productUrl: '',
        setCustomerDeclare: ''
      },
      declareCheckColumns: ['chineseName', 'englishName', 'quantity', 'unitNetWeightD', 'unitDeclarePriceD'],
      packageTableColumns: [
        { type: '', width: '150', prop: 'orderId', label: this.$t('coOrderPackage.orderId'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '150', prop: 'packageSerialNo', label: this.$t('coOrderPackage.packageSerialNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'packageDeliveryNo', label: this.$t('coOrderPackage.packageDeliveryNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'packageWeightD', label: this.$t('coOrderPackage.packageWeight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'packageSize', label: this.$t('coOrderPackage.packageSize'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'id', label: this.$t('system.id'), align: 'center', isShow: false, disabled: false }
      ],
      packageDataList: [],
      packageDataListLoading: false,
      packageDialogVisible: false,
      packageDataForm: {
        id: '',
        creator: '',
        createDate: '',
        orderId: '',
        packageSerialNo: '',
        packageDeliveryNo: '',
        packageCustomerNo: '',
        packageWeightD: '',
        packageLengthD: '',
        packageWidthD: '',
        packageHeightD: '',
        channelLabelUrl: ''
      },
      orderPackage: {
        orderWeightD: null,
        orderLengthD: null,
        orderWidthD: null,
        orderHeightD: null
      },
      // 0 新增 1 修改
      declareAddOrUpdate: 0,
      packageViewIndex: 0,
      // 0 新增 1 修改
      packageAddOrUpdate: 0
    }
  },
  props: {
    // excl导入的产品编号
    action: {
      type: String,
      required: false
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat
  },
  computed: {
    dataRule () {
      const validateIsEmail = (rule, value, callback) => {
        if (value !== null && value !== '' && value !== undefined && !isEmail(value)) {
          return callback(new Error(this.$t('validate.format', { 'attr': this.$t('coOrderDeclare.isEmail') })))
        }
        callback()
      }
      const validateDecimal2 = (rule, value, callback) => {
        if (value !== null && value !== '' && value !== undefined && !isDecimal2(value)) {
          return callback(new Error(this.$t('validate.format', { 'attr': this.$t('coOrderDeclare.isDecimal2') })))
        }
        callback()
      }
      const validateHasChinese = (rule, value, callback) => {
        if (isChinese(value)) {
          return callback(new Error(this.$t('validate.format', { 'attr': this.$t('coOrderDeclare.notInputChinese') })))
        }
        callback()
      }
      return {
        customerOrderNo: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { max: 36, message: this.$t('validate.isOverLength', { max: 36 }), trigger: ['blur', 'change'] }
        ],
        subCustomerOrderNo: [
          { max: 36, message: this.$t('validate.isOverLength', { max: 36 }), trigger: ['blur', 'change'] }
        ],
        shopName: [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' },
          { max: 32, message: this.$t('validate.isOverLength', { max: 32 }), trigger: ['blur', 'change'] }
        ],
        waybillNo: [
          { required: this.waybillNoSource === 0, message: this.$t('validate.required'), trigger: 'blur' },
          { max: 36, message: this.$t('validate.isOverLength', { max: 36 }), trigger: ['blur', 'change'] }
        ],
        deliveryNo: [
          { max: 36, message: this.$t('validate.isOverLength', { max: 36 }), trigger: ['blur', 'change'] }
        ],
        logisticsProductCode: [
          { required: true, message: this.$t('validate.required'), trigger: ['blur', 'change'] }
        ],
        'consignee.consigneeName': [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { max: Consignee.consigneeNameLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeNameLength }), trigger: ['blur', 'change'] }
        ],
        'consignee.consigneePhone': [
          { validator: validateHasChinese, trigger: 'blur' },
          { max: Consignee.consigneePhoneLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneePhoneLength }), trigger: ['blur', 'change'] }
        ],
        'consignee.consigneeCompany': [
          // { max: Consignee.consigneeCompanyLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeCompanyLength }), trigger: ['blur', 'change'] }
        ],
        'consignee.consigneeDistrict': [
          { max: Consignee.consigneeDistrictLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeDistrictLength }), trigger: ['blur', 'change'] }
        ],
        'consignee.consigneeStreet': [
          { max: Consignee.consigneeStreetLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeStreetLength }), trigger: ['blur', 'change'] }
        ],
        'consignee.consigneeDoorplate': [
          { max: Consignee.consigneeDoorplateLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeDoorplateLength }), trigger: ['blur', 'change'] }
        ],
        'consignee.consigneeTaxNo': [
          { max: Consignee.consigneeTaxNoLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeTaxNoLength }), trigger: ['blur', 'change'] }
        ],
        'consignee.consigneeIdcard': [
          { max: Consignee.consigneeIdcardLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeIdcardLength }), trigger: ['blur', 'change'] }
        ],
        'consignee.consigneeCountry': [
          { required: true, message: this.$t('validate.required'), trigger: ['blur', 'change'] }
        ],
        'consignee.consigneeProvince': [
          // { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { max: Consignee.consigneeProvinceLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeProvinceLength }), trigger: ['blur', 'change'] }
        ],
        'consignee.consigneeCity': [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { max: Consignee.consigneeCityLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeCityLength }), trigger: ['blur', 'change'] }
        ],
        'consignee.consigneeAddress': [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { max: Consignee.consigneeAddressLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeAddressLength }), trigger: ['blur', 'change'] }
        ],
        'consignee.consigneePostcode': [
          { max: Consignee.consigneePostcodeLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneePostcodeLength }), trigger: ['blur', 'change'] }
        ],
        'consignee.consigneeEmail': [
          { validator: validateIsEmail, trigger: 'blur' },
          { max: Consignee.consigneeEmailLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeEmailLength }), trigger: ['blur', 'change'] }
        ],
        shipperEmail: [
          { validator: validateIsEmail, trigger: 'blur' },
          { max: 64, message: this.$t('validate.isOverLength', { max: 64 }), trigger: ['blur', 'change'] }
        ],
        codAmount: [
          { validator: validateDecimal2, trigger: 'blur' }
        ],
        insuredAmount: [
          { validator: validateDecimal2, trigger: 'blur' }
        ]
      }
    },
    declareDataRules () {
      const validateDecimal3 = (rule, value, callback) => {
        if (!isDecimal3(value)) {
          return callback(new Error(this.$t('validate.format', { 'attr': this.$t('coOrderDeclare.isDecimal3') })))
        }
        callback()
      }
      const validateDecimal2 = (rule, value, callback) => {
        if (!isDecimal2(value)) {
          return callback(new Error(this.$t('validate.format', { 'attr': this.$t('coOrderDeclare.isDecimal2') })))
        }
        callback()
      }
      const validateIsPlusInteger2 = (rule, value, callback) => {
        if (!isPlusInteger2(value)) {
          return callback(new Error(this.$t('validate.format', { 'attr': this.$t('coOrderDeclare.isPlusInteger2') })))
        }
        callback()
      }
      const validateIsChinese = (rule, value, callback) => {
        if (isChinese(value)) {
          return callback(new Error(this.$t('validate.format', { 'attr': this.$t('coOrderDeclare.notChinese') })))
        }
        callback()
      }
      return {
        chineseName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        englishName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: validateIsChinese, trigger: 'blur' }
        ],
        quantity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: validateIsPlusInteger2, trigger: 'blur' }
        ],
        unitNetWeightD: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: validateDecimal3, trigger: 'blur' }
        ],
        unitDeclarePriceD: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: validateDecimal2, trigger: 'blur' }
        ]
      }
    },
    // orderPackageRle () {
    //   const validateDecimal3 = (rule, value, callback) => {
    //     if (!isDecimal3(value)) {
    //       return callback(new Error(this.$t('validate.format', { 'attr': this.$t('coOrderDeclare.isDecimal3') })))
    //     }
    //     callback()
    //   }
    //   const validateIsPlusInteger2 = (rule, value, callback) => {
    //     if (value !== null && value !== '' && value !== undefined && !isPlusInteger2(value)) {
    //       return callback(new Error(this.$t('validate.format', { 'attr': this.$t('coOrderDeclare.isPlusInteger2') })))
    //     }
    //     callback()
    //   }
    //   return {
    //     'orderPackage.orderWeightD': [
    //       { required: true, message: this.$t('validate.required'), trigger: 'blur' },
    //       { validator: validateDecimal3, trigger: 'blur' }
    //     ],
    //     'orderPackage.orderLengthD': [
    //       { validator: validateIsPlusInteger2, trigger: 'blur' }
    //     ],
    //     'orderPackage.orderWidthD': [
    //       { validator: validateIsPlusInteger2, trigger: 'blur' }
    //     ],
    //     'orderPackage.orderHeightD': [
    //       { validator: validateIsPlusInteger2, trigger: 'blur' }
    //     ]
    //   }
    // },
    declareTableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.declareTableColumns).map((key) => this.declareTableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    packageTableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.packageTableColumns).map((key) => this.packageTableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    packageDataRule () {
      const validateDecimal3 = (rule, value, callback) => {
        if (!isDecimal3(value)) {
          return callback(new Error(this.$t('validate.format', { 'attr': this.$t('coOrderDeclare.isDecimal3') })))
        }
        callback()
      }
      const validateIsPlusInteger2 = (rule, value, callback) => {
        if (value !== null && value !== '' && value !== undefined && !isPlusInteger2(value)) {
          return callback(new Error(this.$t('validate.format', { 'attr': this.$t('coOrderDeclare.isPlusInteger2') })))
        }
        callback()
      }
      return {
        packageWeightD: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: validateDecimal3, trigger: 'blur' }
        ],
        packageLengthD: [
          { validator: validateIsPlusInteger2, trigger: 'blur' }
        ],
        packageWidthD: [
          { validator: validateIsPlusInteger2, trigger: 'blur' }
        ],
        packageHeightD: [
          { validator: validateIsPlusInteger2, trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    this.showAllProductBtn = false
    this.showCustomerSetProductBtn = false
    this.getDetail()
  },
  activated () {
    if (this.$route.query.opType) {
      this.opType = this.$route.query.opType
    }
    this.$nextTick(() => {
      // 如果菜单浮动位置 需要初始化
      this.$footerScroll()
      // 查询订单详情
      if (this.$route.query.orderId) {
        this.getOrderInfo(this.$route.query.orderId)
      } else if (this.$route.query.res) {
        this.fillData(this.$route.query.res)
      }
    })
    // this.getDetail()
    // 验证
    // this.$refs['dataForm'].validate((valid, object) => {
    //   if (!valid) {
    //     return false
    //   }
    // })
  },
  methods: {
    prev () {
      if (this.active > 0) this.active--
    },
    next () {
      if (this.active++ > 2) this.active = 0
    },
    logisticsProductChange (code) {
      this.waybillNoSource = this.waybillNoSourceMap.get(code)
    },
    getDetail () {
      this.getBaseData()
      this.getDict()
    },
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        this.$refs['dataForm'].resetFields()
      })
    },
    async getDict () {
      // 获取相关字典
      this.orderStatusList = await this.getDictTypeList('OrderStatus') // 订单状态
      this.taxPayModeList = await this.getDictTypeList('OrderTaxPayMode') // 税制模式
      this.iossTypeList = await this.getDictTypeList('IossType') // IOSS类型
      this.parcelTypeList = await this.getDictTypeList('OrderParcelType') // 包裹类型
      this.goodsCategoryList = await this.getDictTypeList('OrderGoodsCategory') // 物品种类
    },
    async getBaseData () {
      this.countryList = await baseData(baseDataApi.countryList)
      this.currencyList = await baseData(baseDataApi.currencyList)
      this.setShipperByCustomerCodeList = await baseData(baseDataApi.setShipperByCustomerCodeList + this.$store.state.user.customerId)
      this.setDeclareByCustomerCodeList = await baseData(baseDataApi.setDeclareByCustomerCodeList + this.$store.state.user.customerId)
      this.setConsigneeByCustomerCodeList = await baseData(baseDataApi.setConsigneeByCustomerCodeList(this.$store.state.user.customerId))
      this.logisticsProductByParamsList = await baseData('/co/setcustomerlogisticsproduct/list?logisticsType=10&status=1').then((data) => {
        return new Promise(resolve => {
          if (!data) {
            return resolve([])
          }
          let array = []
          for (let i = 0; i < data.length; i++) {
            array.push({
              name: data[i].productName,
              code: data[i].productCode
            })
            this.waybillNoSourceMap.set(data[i].productCode, data[i].waybillNoSource)
          }
          return resolve(array)
        }).then(async (arr) => {
          if (!arr || arr.length === 0) {
            this.showAllProductBtn = false
            this.showCustomerBindProductBtn = false
            arr = await baseData(baseDataApi.enableLogisticsProductByCurrent + '?logisticsType=10')
            for (let item of arr) {
              this.waybillNoSourceMap.set(item.code, item.waybillNoSource)
            }
          } else {
            this.showAllProductBtn = true
          }
          return arr
        })
      }).catch(() => {})

      // 将停用的渠道清除
      let existProduct = false
      for (let i = 0; i < this.logisticsProductByParamsList.length; i++) {
        if (this.logisticsProductByParamsList[i].code === this.dataForm.logisticsProductCode) {
          existProduct = true
          break
        }
      }
      if (!existProduct && this.opType !== 'coOrder.update' && this.opType !== 'coOrder.copy' && this.opType !== 'coOrder.rcopy') {
        this.dataForm.logisticsProductCode = null
      }
    },
    saveRowDrawerData () {
      this.declareDataTableForm.declareDataList.splice(this.rowDrawerData.index, 1, this.rowDrawerData)
      this.$nextTick(() => {
        this.$refs.drawer.closeDrawer()
      })
    },
    getOrderInfo (id) {
      this.$http.get('/co/order/' + id).then(async ({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        if (res.data.shipper) {
          this.dataForm.shipper = res.data.shipper
        } else {
          for (let key in this.dataForm.shipper) {
            this.dataForm.shipper[key] = ''
          }
        }
        delete res.data['shipper']
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
        this.declareDataTableForm.declareDataList = res.data.orderDeclareList
        this.packageDataList = res.data.packageList
        this.dataForm.subCustomerOrderNo = this.packageDataList[0].subCustomerOrderNo
        // 黑名单的物流产品不给复制
        await baseData(baseDataApi.enableLogisticsProductByCurrent + '?logisticsType=10').then((arr) => {
          if (!arr.find(item => item.code === this.dataForm.logisticsProductCode)) {
            this.dataForm.logisticsProductCode = null
            this.$set(this.dataForm, 'logisticsProductCode', null)
            if (!this.logisticsProductList || this.logisticsProductList.length <= 0) {
              this.logisticsProductList = arr
            }
          }
        })
        if (this.dataForm.logisticsProductCode && !this.logisticsProductByParamsList.find(item => item.code === res.data.logisticsProductCode)) {
          let obj = { 'code': res.data.logisticsProductCode, 'name': res.data.logisticsProductName }
          this.logisticsProductByParamsList.push(obj)
        }
        this.waybillNoSourceMap.set(res.data.logisticsProductCode, res.data.waybillNoSource)
        this.waybillNoSource = res.data.waybillNoSource
        if (this.$route.query.opType === 'coOrder.copy') {
          this.dataForm.id = null
          this.dataForm.customerOrderNo = null
          this.dataForm.subCustomerOrderNo = null
          this.dataForm.waybillNo = null
          this.dataForm.postalTrackingNo = null
          this.dataForm.deliveryNo = null
          this.dataForm.status = 10
        } else if (this.$route.query.opType === 'coOrder.rcopy') {
          this.dataForm.id = null
          this.dataForm.subCustomerOrderNo = null
          this.dataForm.waybillNo = null
          this.dataForm.postalTrackingNo = null
          this.dataForm.deliveryNo = null
          this.dataForm.status = 10
        }
        this.getOrderPackage()
      }).catch(() => {})
    },
    fillData (res) {
      if (res.data.shipper) {
        this.dataForm.shipper = res.data.shipper
      } else {
        for (let key in this.dataForm.shipper) {
          this.dataForm.shipper[key] = ''
        }
      }
      delete res.data['shipper']
      this.dataForm = {
        ...this.dataForm,
        ...res.data
      }
      this.declareDataTableForm.declareDataList = res.data.orderDeclareList
      this.packageDataList = res.data.packageList
      this.dataForm.subCustomerOrderNo = this.packageDataList[0].subCustomerOrderNo
      if (this.dataForm.logisticsProductCode && !this.logisticsProductByParamsList.find(item => item.code === res.data.logisticsProductCode)) {
        let obj = { 'code': res.data.logisticsProductCode, 'name': res.data.logisticsProductName }
        this.logisticsProductByParamsList.push(obj)
      }
      this.waybillNoSourceMap.set(res.data.logisticsProductCode, res.data.waybillNoSource)
      this.waybillNoSource = res.data.waybillNoSource
      if (this.$route.query.opType === 'coOrder.copy') {
        this.dataForm.id = null
        this.dataForm.customerOrderNo = null
        this.dataForm.subCustomerOrderNo = null
        this.dataForm.waybillNo = null
        this.dataForm.deliveryNo = null
        this.dataForm.status = 10
        this.declareDataTableForm.declareDataList = []
      } else if (this.$route.query.opType === 'coOrder.rcopy') {
        this.dataForm.id = null
        this.dataForm.subCustomerOrderNo = null
        this.dataForm.waybillNo = null
        this.dataForm.deliveryNo = null
        this.dataForm.status = 10
        this.declareDataTableForm.declareDataList = []
      }
      this.getOrderPackage()
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'createDate':
          value = timestampFormat(gtmToLtm(scope.row.createDate), 'YYYY-MM-DD')
          break
        case 'status':
          value = formatterType(scope.row.status, this.statusList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    declareAddClick () {
      this.declareDialogVisible = true
      this.declareAddOrUpdate = 0
    },
    declareViewHandle (row, index) {
      this.drawerVisible = true
      this.rowDrawerData = cloneDeep(row)
      this.rowDrawerData.index = index
    },
    declareDeleteHandle (index) {
      this.declareDataTableForm.declareDataList.splice(index, 1)
    },
    addRedStar (h, { column }) {
      return [h('span', { style: 'color: red' }, '*'), h('span', ' ' + column.label)]
    },
    notAddRedStar (h, { column }) {
      return [h('span', ' ' + column.label)]
    },
    declareHandleClose () {
      this.$refs.declareDataForm.resetFields()
      this.declareDialogVisible = false
    },
    declareHandleConfirm (id) {
      if (id) {
        this.selectSetDeclare(id)
      }
      this.declareDataTableForm.declareDataList.push(cloneDeep(this.declareDataForm))
      this.declareDataForm = {
        id: '',
        creator: '',
        createDate: '',
        orderId: '',
        chineseName: '',
        englishName: '',
        quantity: '',
        unitNetWeightD: '',
        unitDeclarePriceD: '',
        brand: '',
        goodsBarcode: '',
        sku: '',
        hsCode: '',
        productModel: '',
        material: '',
        purpose: '',
        origin: 'CN',
        pickingRemark: '',
        productUrl: '',
        setCustomerDeclare: ''
      }
    },
    packageAddClick () {
      this.packageDialogVisible = true
      this.packageViewIndex = 0
      this.packageAddOrUpdate = 0
    },
    packageViewHandle (data) {
      this.packageViewIndex = data.$index
      this.packageDialogVisible = true
      this.packageAddOrUpdate = 1
      this.packageDataForm = cloneDeep(data.row)
    },
    packageDeleteHandle (data) {
      this.packageViewIndex = data.$index
      this.packageDataList.splice(this.packageViewIndex, 1)
      this.getOrderPackage()
    },
    packageHandleClose () {
      this.$refs.packageDataForm.resetFields()
      this.packageDialogVisible = false
    },
    packageHandleConfirm () {
      this.$refs.packageDataForm.validate((valid) => {
        if (!valid) {
          return false
        }
        if (this.packageAddOrUpdate === 1) {
          this.packageDataList.splice(this.packageViewIndex, 1, cloneDeep(this.packageDataForm))
        } else {
          this.packageDataList.push(cloneDeep(this.packageDataForm))
        }
        this.getOrderPackage()
        this.$refs.packageDataForm.resetFields()
        this.packageDialogVisible = false
      })
    },
    dataFormValidate () {
      let self = this
      return new Promise(function(resolve, reject) {
        self.$refs['dataForm'].validate((valid, object) => {
          if (!valid) {
            let rulesArr = _oPairs(object)
            if (rulesArr[0][0].indexOf('consignee') > -1) {
              // 是否还有收件人没通过验证
              self.activeFirstTab = 'secondTab'
              reject(self.activeFirstTab)
              self.$message({
                message: '还有收件人信息未正确填写完整',
                type: 'warning',
                duration: 2500
              })
            } else if (rulesArr[0][0].indexOf('shipper') > -1) {
              // 是否还有发件人没通过验证
              self.activeFirstTab = 'threeTab'
              reject(self.activeFirstTab)
              self.$message({
                message: '还有发件人信息未正确填写完整',
                type: 'warning',
                duration: 2500
              })
            } else {
              self.activeFirstTab = 'firstTab'
              reject(self.activeFirstTab)
              self.$message({
                message: '还有主信息未正确填写完整',
                type: 'warning',
                duration: 2500
              })
            }
            return false
          }
          resolve()
        })
      })
    },
    declareDataFormValidate () {
      let self = this
      return new Promise(function(resolve, reject) {
        self.$refs['declareDataForm'].validate((valid) => {
          if (!valid) {
            self.$message({
              message: '请把报关信息正确填写完整',
              type: 'warning',
              duration: 2500,
              offset: 70
            })
            return reject(new Error('请把报关信息正确填写完整'))
          }
          resolve()
        })
      })
    },
    // 表单提交
    forecastOrder: debounce(function () {
      Promise.all([ this.dataFormValidate(), this.declareDataFormValidate() ]).then(() => {
        // 预报
        if (!this.orderPackage.orderWeightD) {
          return this.$message.error('请填写重量')
        }
        this.dataForm.status = 11
        this.node = 13
        this.placeOrder('forecast')
      }).catch(() => {
        setTimeout(() => {
          let isError = document.getElementsByClassName('is-error')
          isError[0].querySelector('input').focus()
        }, 100)
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    draftOrder: debounce(function () {
      Promise.all([ this.dataFormValidate(), this.declareDataFormValidate() ]).then(() => {
        // 草稿
        if (!this.orderPackage.orderWeightD) {
          return this.$message.error('请填写重量')
        }
        this.dataForm.status = 10
        this.node = 11
        this.placeOrder('draft')
      }).catch(() => {
        setTimeout(() => {
          let isError = document.getElementsByClassName('is-error')
          isError[0].querySelector('input').focus()
        }, 100)
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    placeOrder (data) {
      this.dataForm.orderDeclareList = this.declareDataTableForm.declareDataList
      this.dataForm.packageList = this.packageDataList
      this.dataForm.packageList[0].subCustomerOrderNo = this.dataForm.subCustomerOrderNo
      if (this.declareDataTableForm.declareDataList.length <= 0) {
        this.$message({
          message: this.$t('coOrderDeclare.atLeastOne'),
          type: 'warning'
        })
        this.dataForm.status = 10
        return false
      }
      // if (this.packageDataList.length <= 0) {
      //   this.$message({
      //     message: this.$t('coOrderPackage.atLeastOne'),
      //     type: 'warning'
      //   })
      //   this.dataForm.status = 10
      //   return false
      // }
      this.$http[!this.dataForm.id ? 'post' : 'put']('/co/order', { 'orders': [this.dataForm], 'node': this.node }).then(({ data: res }) => {
        if (res.code !== 0) {
          this.dataForm.status = 10
          return this.$message({
            dangerouslyUseHTMLString: true,
            message: res.msg,
            type: 'error'
          })
        } else if (res.data.failureNum > 0) {
          this.dataForm.status = 10
          return this.$message({
            dangerouslyUseHTMLString: true,
            message: res.data.failureList[0].message,
            type: 'error'
          })
        }
        this.$router.push({ name: 'co-orderList' })
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          dangerouslyUseHTMLString: true,
          duration: 500,
          onClose: () => {
            this.dataForm.status = 10
            this.$router.push({ name: 'co-orderList', params: { activeName: data } })
          }
        })
      }).catch(() => {})
    },
    selectSetConsignee(id) {
      let consigneeArray = this.setConsigneeByCustomerCodeList
      for (let i = 0; i < consigneeArray.length; i++) {
        if (id === consigneeArray[i].id) {
          this.dataForm.consignee.consigneeName = consigneeArray[i].consigneeName
          this.dataForm.consignee.consigneeCompany = consigneeArray[i].consigneeCompany
          this.dataForm.consignee.consigneePhone = consigneeArray[i].consigneePhone
          this.dataForm.consignee.consigneeEmail = consigneeArray[i].consigneeEmail
          this.dataForm.consignee.consigneeCountry = consigneeArray[i].consigneeCountry
          this.dataForm.consignee.consigneeProvince = consigneeArray[i].consigneeProvince
          this.dataForm.consignee.consigneeCity = consigneeArray[i].consigneeCity
          this.dataForm.consignee.consigneeDistrict = consigneeArray[i].consigneeDistrict
          this.dataForm.consignee.consigneeAddress = consigneeArray[i].consigneeAddress
          this.dataForm.consignee.consigneePostcode = consigneeArray[i].consigneePostcode
          this.dataForm.consignee.consigneeDoorplate = consigneeArray[i].consigneeDoorplate
          this.dataForm.consignee.consigneeStreet = consigneeArray[i].consigneeStreet
          this.dataForm.consignee.consigneeTaxNo = consigneeArray[i].consigneeTaxNo
          this.dataForm.consignee.consigneeIdcard = consigneeArray[i].consigneeIdcard
          return
        }
      }
    },
    selectSetShipper (data) {
      let shipperArray = this.setShipperByCustomerCodeList
      for (let i = 0; i < shipperArray.length; i++) {
        if (data === shipperArray[i].id) {
          this.dataForm.shipper.shipperName = shipperArray[i].shipperName
          this.dataForm.shipper.shipperCompany = shipperArray[i].shipperCompany
          this.dataForm.shipper.shipperPhone = shipperArray[i].shipperContact
          this.dataForm.shipper.shipperEmail = shipperArray[i].shipperEmail
          this.dataForm.shipper.shipperCountry = shipperArray[i].shipperCountryCode
          this.dataForm.shipper.shipperProvince = shipperArray[i].shipperProvince
          this.dataForm.shipper.shipperCity = shipperArray[i].shipperCity
          this.dataForm.shipper.shipperDistrict = shipperArray[i].shipperDistrict
          this.dataForm.shipper.shipperAddress = shipperArray[i].shipperAddress
          this.dataForm.shipper.shipperPostcode = shipperArray[i].shipperPostcode
          this.dataForm.shipper.shipperDoorplate = shipperArray[i].shipperDoorplate
          this.dataForm.shipper.shipperStreet = shipperArray[i].shipperStreet
          return
        }
      }
    },
    selectSetDeclare (data) {
      let declareArray = this.setDeclareByCustomerCodeList
      for (let i = 0; i < declareArray.length; i++) {
        if (data === declareArray[i].id) {
          this.declareDataForm.chineseName = declareArray[i].chineseName
          this.declareDataForm.englishName = declareArray[i].englishName
          this.declareDataForm.quantity = declareArray[i].quantity
          this.declareDataForm.unitNetWeightD = declareArray[i].unitNetWeightD
          this.declareDataForm.unitDeclarePriceD = declareArray[i].unitDeclarePriceD
          this.declareDataForm.brand = declareArray[i].brand
          this.declareDataForm.goodsBarcode = declareArray[i].goodsBarcode
          this.declareDataForm.sku = declareArray[i].sku
          this.declareDataForm.hsCode = declareArray[i].hsCode
          this.declareDataForm.productModel = declareArray[i].productModel
          this.declareDataForm.material = declareArray[i].material
          this.declareDataForm.purpose = declareArray[i].purpose
          this.declareDataForm.origin = declareArray[i].origin
          this.declareDataForm.pickingRemark = declareArray[i].pickingRemark
          this.declareDataForm.productUrl = declareArray[i].productUrl
          return
        }
      }
    },
    async showAllProductHandle () {
      this.logisticsProductByParamsList = await baseData(baseDataApi.enableLogisticsProductByCurrent + '?logisticsType=10').then((data) => {
        let array = []
        for (let i = 0; i < data.length; i++) {
          array.push({
            name: data[i].name,
            code: data[i].code
          })
          this.waybillNoSourceMap.set(data[i].code, data[i].waybillNoSource)
        }
        return array
      })
      this.showCustomerSetProductBtn = true
      this.showAllProductBtn = false
    },
    async showCustomerSetProductHandle () {
      this.logisticsProductByParamsList = await baseData('/co/setcustomerlogisticsproduct/list?logisticsType=10&status=1').then((data) => {
        let array = []
        for (let i = 0; i < data.length; i++) {
          array.push({
            name: data[i].productName,
            code: data[i].productCode
          })
        }
        return array
      }).catch(() => {}).finally(() => {
        this.showAllProductBtn = true
        this.showCustomerSetProductBtn = false
      })
    },
    getOrderPackage () {
      if (this.orderPackage === undefined) {
        this.orderPackage = {
          orderWeightD: null,
          orderLengthD: null,
          orderWidthD: null,
          orderHeightD: null
        }
      }
      if (this.packageDataList.length === 1) {
        this.orderPackage.orderWeightD = this.packageDataList[0].packageWeightD
        this.orderPackage.orderLengthD = this.packageDataList[0].packageLengthD
        this.orderPackage.orderWidthD = this.packageDataList[0].packageWidthD
        this.orderPackage.orderHeightD = this.packageDataList[0].packageHeightD
      } else {
        this.orderPackage.orderWeightD = null
        this.orderPackage.orderLengthD = null
        this.orderPackage.orderWidthD = null
        this.orderPackage.orderHeightD = null
      }
    },
    setOrderPackage: function () {
      if (this.orderPackage.orderWeightD > 0) {
        if (this.packageDataList.length === 1) {
          this.packageDataList[0].packageWeightD = this.orderPackage.orderWeightD
          this.packageDataList[0].packageLengthD = this.orderPackage.orderLengthD
          this.packageDataList[0].packageWidthD = this.orderPackage.orderWidthD
          this.packageDataList[0].packageHeightD = this.orderPackage.orderHeightD
        } else if (this.packageDataList.length === 0) {
          this.packageDataList.push({
            packageWeightD: this.orderPackage.orderWeightD,
            packageLengthD: this.orderPackage.orderLengthD,
            packageWidthD: this.orderPackage.orderWidthD,
            packageHeightD: this.orderPackage.orderHeightD
          })
        }
      }
    },
    // 返回
    backFn () {
      this.visible = false
      this.$emit('close')
    }
  },
  components: {
    areaBox
  }
}
</script>
<style lang="scss" scoped>
  .addRowBtn{
    width: 100%;
    margin: 10px 0px;
  }
  .orderAdd{
    .el-form-item{
      margin-bottom: 22px;
    }
    .el-divider--vertical{
      height: auto;
    }
    .consigneeArea{
      .title{
        color: #2378be;
      }
    }
    .shipperArea{
      .title{
        color: #9cc82b;
      }
    }
    .packDetail {
      margin-top: 50px;
    }
  }
  .el-divider--horizontal {
    display: block;
    height: 2px !important;
    width: 100%;
    margin: 5px 0 !important;
  }
  .el-select-dropdown__item_btn {
    font-size: 12px;
    padding: 0 20px;
    position: relative;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #606266;
    height: 34px;
    line-height: 34px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    cursor: pointer;
  }
  .logisticsproductcode-select-btn {
    width: 100% !important;
    text-align: center !important;
    border: 0px !important;
    color: cornflowerblue !important;
  }
  /deep/ .el-button-group .el-button:not(:last-child) {
     padding-bottom: 7px !important;
  }
  /deep/ .el-button-group .el-button--primary {
    color: #409EFF;
    background: #ecf5ff;
    border-color: #b3d8ff;
  }
  /deep/ .el-button-group .el-button--primary:hover {
    background: #409EFF;
    border-color: #409EFF;
    color: #FFF;
  }
  /deep/.el-dropdown .el-dropdown__caret-button::before {
    background: #409eff !important;
  }
  /deep/ .el-button-group .el-button:first-child {
    width: 86%;
  }
  /deep/ .el-button-group .el-button:last-child {
    width: 14%;
  }
  /deep/ .el-icon-arrow-down:before {
    content: "常用报关项\3\E6DF" !important;
  }
  /deep/ div.popper__arrow {
    left: 50% !important;
  }
  /deep/ #declareDataForm {
    .el-form-item{
      margin-bottom: 0px !important;
    }
  }
  .rowDrawerDataButton{
    min-width: 100px;
  }
</style>
