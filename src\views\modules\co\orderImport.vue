<template>
  <div class="add-body panel_body" >
    <div class="mod-co__orderImport padding_top20" ref="listPanel" >
      <el-row :gutter="20">
        <el-col :md="{span:10}" v-loading='loading' element-loading-text="导入中…" >
          <el-upload
            :action="url"
            :data="param"
            drag
            :before-upload="beforeUploadHandle"
            :on-success="successHandle"
            :on-error="errorHandle"
            :headers="getToken"
            accept=".xlsx,.xls"
            class="text-center margin_bottom15">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text" v-html="$t('upload.text')"></div>
            <div class="el-upload__tip" slot="tip">只能上传xlsx/xls文件，且不超过2M</div>
          </el-upload>
          <el-card class="box-card" shadow="never">
            <div slot="header" class="clearfix">
              <strong>导入结果</strong>
            </div>
            <el-row :gutter="20" class="margin_bottom15">
              <el-col :span="8">
                <el-badge :value="totalCount" class="item" type="warning">
                  <el-button size="small">下单总数</el-button>
                </el-badge>
              </el-col>
              <el-col :span="8">
                <el-badge :value="successCount" class="item" type="primary">
                  <el-button size="small">成功总数</el-button>
                </el-badge>
              </el-col>
              <el-col :span="8">
                <el-badge :value="failCount" class="item">
                  <el-button size="small">失败总数</el-button>
                </el-badge>
              </el-col>
              <!--<el-col :span="6">-->
                <!--<el-button size="small" type="primary">下单结果导出</el-button>-->
              <!--</el-col>-->
            </el-row>
            <el-table :data="failInfoList" border max-height="350" >
              <el-table-column v-for="(item, index) in resultTableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
        <el-col :md="{span:14}">
          <el-tabs class="no_shadow" type="border-card" v-model="activeSecondName" :stretch="false">
            <el-tab-pane label="模板列表" name="template" >
              <el-col v-for="(item, index) in excelModuleList"  :key="index" :span="6" class="fileTpl-box">
                <el-tooltip :content="'描述:' + item.description" placement="top">
                  <a class="fileTpl" href="javascript:;" @click="loadOrderTemplateByDiy(item.id)">
                    <img src="@/assets/img/orderModel.png" title=""/>
                    <span class="desc">{{item.name}}</span>
                  </a>
                </el-tooltip>
              </el-col>
<!--              <div v-for="(item, index) in excelModuleList"  :key="index" class="fileTpl-box">-->
<!--                <a class="fileTpl" href="javascript:;" @click="loadOrderTemplateByDiy(item.id)">-->
<!--                  <img src="@/assets/img/orderModel.png" title=""/>-->
<!--                  <span class="desc">{{item.name}}</span>-->
<!--                </a>-->
<!--              </div>-->
<!--              <div class="fileTpl-box">-->
<!--                <a class="fileTpl" href="javascript:;" @click="loadOrderTemplate">-->
<!--                  <img src="@/assets/img/orderModel.png" title=""/>-->
<!--                  <span class="desc">标准格式</span>-->
<!--                </a>-->
<!--              </div>-->
            </el-tab-pane>
            <el-tab-pane label="物流产品列表" name="logisticsProduct" >
              <el-form ref="searchForm" :model="dataForm"  inline label-width="80px">
                <el-form-item :label="$t('coOrder.logisticsProductCode')" >
                  <el-select v-model="dataForm.logisticsProductCode" filterable clearable>
                    <el-option v-for="item in logisticsProductDataList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                  </el-select>
                </el-form-item>
              </el-form>
              <el-table :data="logisticsProductDataList" border height="315" max-height="600" >
                <el-table-column v-for="(item, index) in logisticsProductTableColumnsArr" :key="index" :type="item.type" :prop="item.prop" :header-align="item.align" :align="item.align" :min-width="item.width" :label="item.label">
                  <template slot-scope="scope">
                    <div>
                      {{formatterFn(scope,item.prop)}}
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
import qs from 'qs'
import baseData from '@/api/baseData'
import baseDataApi from '@/api'
import { formatterType } from '@/filters/filters'

export default {
  data () {
    return {
      isSaas: this.$store.state.user.isSaas,
      token: Cookies.get('cs_token'),
      lang: 'zh-CN',
      url: `${this.$baseUrl}/co/order/import`,
      param: null,
      loading: false,
      activeSecondName: 'template',
      resultTableColumns: [
        { type: '', width: '120', prop: 'customerOrderNo', label: this.$t('coOrder.customerOrderNo'), align: 'left', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'code', label: '下单结果', align: 'left', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'message', label: '消息提示', align: 'center', isShow: true, disabled: false }
      ],
      resultDataList: [],
      dataForm: {
        logisticsProductCode: null
      },
      logisticsProductTableColumns: [
        { type: '', width: '120', prop: 'code', label: '物流产品编码', align: 'left', isShow: true, disabled: false },
        { type: '', width: '200', prop: 'name', label: '物流产品名称', align: 'left', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'qwlCode', label: '趣物流代码', align: 'left', isShow: this.$store.state.user.isSaas, disabled: false },
        { type: '', width: '200', prop: 'qwlName', label: '趣物流名称', align: 'left', isShow: this.$store.state.user.isSaas, disabled: false }
      ],
      logisticsProductDataList: [],
      qwlCodeList: [],
      totalCount: 0,
      successCount: 0,
      failCount: 0,
      sizeLimit: 100,
      exportURL: '/co/setdiyimportmoduledetail/export',
      excelModuleList: [],
      failInfoList: []
    }
  },
  computed: {
    getToken () {
      return {
        token: this.token,
        'Accept-Language': this.lang
      }
    },
    resultTableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.resultTableColumns).map((key) => this.resultTableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    logisticsProductTableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.logisticsProductTableColumns).map((key) => this.logisticsProductTableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    }
  },
  created () {
    this.getBaseData()
  },
  methods: {
    beforeUploadHandle (file) {
      this.loading = true
      let regStr = '(.xlsx)$|(.xls)$'
      let regx = new RegExp(regStr)
      if (!regx.test(file.name)) {
        this.$message.error(this.$t('upload.tip', { 'format': `xlsx、xls` }))
        this.loading = false
        return false
      }
      const sizeLimit = file.size / 1024 / 1024 < this.sizeLimit
      if (this.sizeLimit > 0 && !sizeLimit) {
        this.loading = false
        this.$message.error(this.$t('upload.sizeMsg', { 'size': `${this.sizeLimit}` }))
        return false // 必须返回false
      }
      this.failInfoList = []
      this.successCount = 0
      this.failCount = 0
      this.totalCount = 0
    },
    // 上传失败
    errorHandle (error) {
      this.loading = false
      console.log('error', error)
      let myError = error.toString()
      myError = myError.replace('Error: ', '')
      console.log('myError')
      return this.$message({
        dangerouslyUseHTMLString: true,
        message: myError || '发生错误',
        type: 'error',
        duration: 2500
      })
    },
    successHandle (res, file) {
      this.loading = false
      if (res.code !== 0) {
        return this.$message.error(res.msg)
      }
      this.successCount = res.data.successNum
      this.failCount = res.data.failureNum
      this.totalCount = res.data.successNum + res.data.failureNum
      if (this.failCount > 0) {
        for (let i = 0; i < res.data.failureList.length; i++) {
          let fauluerOb = res.data.failureList[i]
          fauluerOb.code = '失败'
          this.failInfoList.push(fauluerOb)
        }
      }
    },
    queryPageByParam () {
    },
    loadOrderTemplateByDiy (moduleId) {
      var params = qs.stringify({
        'token': this.token,
        'moduleId': moduleId
      })
      window.location.href = `${this.$baseUrl}${this.exportURL}?${params}`
    },
    // 获取趣物流代码列表
    getQwlCodeList () {
      return this.$http.get('/bd/logisticsproduct/qwlCodeList').then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        res.data.forEach(item => {
          Object.keys(item).forEach(key => this.qwlCodeList.push({ 'dictValue': key, 'dictName': item[key] }))
        })
      }).catch(() => { })
    },
    async getBaseData () {
      this.logisticsProductDataList = await baseData(baseDataApi.enableLogisticsProductByCurrent + '?logisticsType=10')
      this.excelModuleList = await baseData(baseDataApi.excelModuleList)
      this.getQwlCodeList()
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'qwlName':
          value = formatterType(scope.row.qwlCode, this.qwlCodeList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    }
  }
}
</script>

<style lang="scss" scoped>
.fileTpl-box{
  min-height: 65px;
  max-height: 800px;
  display: inline-block;
  overflow: hidden;
  overflow-y: auto;
  .fileTpl {
    display: block;
    position: relative;
    width: 80px;
    height: 80px;
    margin: 0 10px 10px 0;
    overflow: hidden;
    padding: 8px;
    background-color: #e3e3e3;
    img{
      width: 64px;
      height: 64px
    }
    .desc {
      position: absolute;
      left: 0;
      width: 80px;
      bottom: 0;
      height: 25px;
      line-height: 25px;
      text-align: center;
      font-size: 12px;
      color: #fff;
      background-color: rgba(0, 0, 0, .4);
      transition: .3s;
    }
    &:hover{
      .desc {
        font-size: 15px;
        transition: .3s;
      }
    }
  }
}
</style>
