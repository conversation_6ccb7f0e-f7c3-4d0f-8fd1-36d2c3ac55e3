<template>
  <el-drawer
    size="50%"
    :modal="false"
    :title="$t('psCalculateExpense.quotationRemark')"
    :visible.sync="viewVisible"
    :direction="direction"
    :before-close="cancelFn">

    <div class="content">
      <div v-if="dataForm.quotationRemark" v-html="dataForm.quotationRemark"></div>
      <div v-else style="text-align: center; color: red;">暂无</div>
    </div>
  </el-drawer>

</template>

<script>
export default {
  data () {
    return {
      dataForm: {
        quotationId: '',
        quotationRemark: ''
      },
      direction: 'rtl',
      viewVisible: false
    }
  },
  created () {
    // 初始化时为window绑定一个方法
    window['vueDefinedMyProp'] = (receiveParams) => {
      this.receiveParamsFromHtml(receiveParams)
    }
  },
  methods: {
    init () {
      this.viewVisible = true
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        if (this.dataForm.quotationId) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/ps/expressquotationdesc/${this.dataForm.quotationId}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm.quotationRemark = res.data
      }).catch(() => {})
    },
    // 取消
    cancelFn () {
      this.$emit('backView')
    },

    receiveParamsFromHtml (res) {
      console.log(res)
    },
    invokeHtmlMethod () {
      window.frames['iframeMap'].lodaTable()
    }
  }
}
</script>

<style>
  .el-drawer__body {
    overflow: auto;
  }
  .content {
    margin: 5px 20px;
    height: 100%;
  }
</style>
