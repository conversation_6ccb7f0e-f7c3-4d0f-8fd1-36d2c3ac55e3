/*
* 获取基础数据
* url 接口地址
* param 参数 默认是空对象
*
*/

import http from '@/utils/request'

export default (url, param = {}) => {
  return new Promise((resolve, reject) => {
    http.get(url, { params: param }).then(({ data: res }) => {
      if (res.code !== 0) {
        reject(res)
        return []
      }
      // console.log('请求参数：' + param)
      // console.log('结果：' + res.data)
      resolve(res.data)
    }).catch((res) => {
      reject(res)
    })
  })
}
