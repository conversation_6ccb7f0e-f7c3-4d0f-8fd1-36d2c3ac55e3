<template>
  <el-popover
    v-model="popoverShow"
    :class="customClass"
    placement="top"
    :width="$i18n.locale === 'en-US' ? '190px' : '160px'"
    trigger="click">
    <p><i class="el-icon-question warning font_size16"></i><span>{{ $t('prompt.info', { 'handle': $t(i18nOperateValue) }) }}</span></p>
    <div class="text-right">
      <el-button size="mini" type="text"  @click="closePopover()">{{ $t('cancel') }}</el-button>
      <el-button type="primary" size="mini" v-loading="this.loading" @click="clickHandle()" >{{ $t('confirm') }}</el-button>
    </div>
    <el-link style="margin-left: 0px;" size='mini' :underline="false" slot="reference" v-show="condition" >{{ $t(i18nOperateValue) }}</el-link>
  </el-popover>
</template>

<script>
export default {
  props: {
    i18nOperateValue: {
      required: true,
      type: String
    },
    customClass: {
      // 默认是el-link 如果是按钮则传-> el-button--text el-button--mini el-button
      required: false,
      type: String
    },
    condition: {
      required: false,
      default: true,
      type: Boolean
    }
  },
  data () {
    return {
      popoverShow: false,
      loading: false
    }
  },
  methods: {
    closePopover () {
      this.popoverShow = false
    },
    clickHandle () {
      this.loading = true
      this.popoverShow = false
      this.$emit('clickHandle')
      this.loading = false
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
