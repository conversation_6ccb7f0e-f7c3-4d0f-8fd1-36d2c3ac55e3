<template>
  <div class="add-body panel_body">
    <div class="panel-hd">
      <span v-text="$t('detail')"></span>
    </div>
    <div class="detail-desc">
      <el-form ref="form" label-width="80px">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item :label="$t('coSetDiyimportModuleDetail.customerId')">
              <span v-text="dataForm.customerId"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coSetDiyimportModuleDetail.matchId')">
              <span v-text="dataForm.matchId"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coSetDiyimportModuleDetail.matchDetailId')">
              <span v-text="dataForm.matchDetailId"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coSetDiyimportModuleDetail.objectType')">
              <span v-text="dataForm.objectType"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coSetDiyimportModuleDetail.moduleId')">
              <span v-text="dataForm.moduleId"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coSetDiyimportModuleDetail.repeatableColumns')">
              <span v-text="dataForm.repeatableColumns"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coSetDiyimportModuleDetail.fieldValue')">
              <span v-text="dataForm.fieldValue"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coSetDiyimportModuleDetail.fieldName')">
              <span v-text="dataForm.fieldName"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coSetDiyimportModuleDetail.digital')">
              <span v-text="dataForm.digital"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coSetDiyimportModuleDetail.allowEmpty')">
              <span v-text="dataForm.allowEmpty"></span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div id="cs_FormFooter" class="el-form-footer" slot="footer">
      <el-button @click="backFn">{{ $t('back') }}</el-button>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      dataForm: {
        id: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: '',
        status: '',
        version: '',
        customerId: '',
        matchId: '',
        matchDetailId: '',
        objectType: '',
        moduleId: '',
        repeatableColumns: '',
        fieldValue: '',
        fieldName: '',
        digital: '',
        allowEmpty: ''
      }
    }
  },
  methods: {
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/co/setdiyimportmoduledetail/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 返回
    backFn () {
      this.visible = false
      this.$emit('backView')
    }
  }
}
</script>
