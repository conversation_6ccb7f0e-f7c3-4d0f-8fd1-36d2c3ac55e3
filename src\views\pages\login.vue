<template>
  <div class="aui-wrapper aui-page__login">
    <!-- <div class="bgImg" v-if="companyInfo.bgImg" :style="{ backgroundImage: `url(${companyInfo.bgImg})` }"></div> -->
    <div class="bgImg" v-if="companyInfo.bgImg" :style="{ backgroundImage: `url(${companyInfo.bgImg})` }"></div>
    <div class="aui-content__wrapper" v-if="!isValidateUrl">
      <main class="aui-content">
        <div class="login-header">
          <div>
            <h2  style="font-family: Slabo;font-weight: bolder;letter-spacing: 4px; font-size:36px; color:orange">{{ validateMsg }}</h2>
          </div>
        </div>
      </main>
    </div>
    <div class="aui-content__wrapper" v-if="isValidateUrl">
      <main class="aui-content">
        <div class="login-header">
          <h2 class="login-brand">
            <img class="logoImg" v-if="companyInfo.logoImg" :src="companyInfo.logoImg" alt="logo" />
            <span v-else style="letter-spacing: 3px; font-size: 24px;">{{ dataForm.companyName }}</span>
          </h2>
        </div>
        <div class="login-body">
          <h3 class="login-title">{{ $t('login.title') }}</h3>
          <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" status-icon>
            <el-form-item>
              <el-select v-model="$i18n.locale" class="w-percent-100">
                <el-option v-for="(val, key) in i18nMessages" :key="key" :label="val._lang" :value="key"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="username">
              <el-input ref="username" v-model="dataForm.username" :placeholder="`${$t('login.username')} / ${$t('login.mail')}`" autofocus>
                <span slot="prefix" class="el-input__icon">
                  <svg class="icon-svg" aria-hidden="true"><use xlink:href="#icon-user"></use></svg>
                </span>
              </el-input>
            </el-form-item>
            <el-form-item prop="password">
              <el-input ref="password" v-model="dataForm.password" type="password" :placeholder="$t('login.password')">
                <span slot="prefix" class="el-input__icon">
                  <svg class="icon-svg" aria-hidden="true"><use xlink:href="#icon-lock"></use></svg>
                </span>
              </el-input>
            </el-form-item>
            <el-form-item prop="companyCode" v-if="!dataForm.companyId">
              <el-input ref="companyCode" v-model="dataForm.companyCode" placeholder="公司代码">
              </el-input>
            </el-form-item>
            <el-form-item prop="captcha">
              <el-row :gutter="20">
                <el-col :span="14">
                  <el-input ref="captcha" v-model="dataForm.captcha" :placeholder="$t('login.captcha')">
                    <span slot="prefix" class="el-input__icon">
                      <svg class="icon-svg" aria-hidden="true"><use xlink:href="#icon-safetycertificate"></use></svg>
                    </span>
                  </el-input>
                </el-col>
                <el-col :span="10" class="login-captcha">
                  <img :src="captchaPath" @click="getCaptcha()" />
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="dataFormSubmitHandle()" :loading="isLogin" class="w-percent-100 bg">{{ $t('login.title') }}</el-button>
            </el-form-item>
            <div class="text-right">
              <el-link type="info" @click="gotoPage('register')"> 免费注册 </el-link>
              <!-- <el-link type="info" @click="gotoPage('forgetPassword')" style="padding-left: 20px;">{{ $t('login.forgetPassword') }}</el-link> -->
            </div>
          </el-form>
        </div>
        <div style="padding: 10px; color: #fff; font-size:14px;">建议使用 Google Chrome <img src="../../assets/img/chrome.png" width="18" height="18" style="margin-top:-4px;" /> , Firefox <img src="../../assets/img/firefox.png" width="18" height="18" style="margin-top:-4px;" /> 浏览器，更好使用体验！</div>
        <div class="login-footer">
          <div>
            <div v-if="companyInfo.bottomContent && companyInfo.bottomContent !== 'undefined'" v-html="companyInfo.bottomContent"></div>
          </div>
          <p v-if="companyInfo.icpName">
            <a :href="companyInfo.icpLink" target="_blank">{{ companyInfo.icpName }}</a>
          </p>
          <p v-else>
            {{ $t('login.copyright') }}
          </p>
        </div>
      </main>
    </div>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
import debounce from 'lodash/debounce'
import { messages } from '@/i18n'
import { getUUID } from '@/utils'
export default {
  data() {
    return {
      i18nMessages: messages,
      captchaPath: '',
      forgetUrl: '',
      isLogin: false,
      // pageShow: false,
      isValidateUrl: false,
      validateMsg: '',
      dataForm: {
        companyId: '',
        companyName: '',
        companyCode: '',
        username: '',
        password: '',
        uuid: '',
        captcha: ''
      },
      companyInfo: {
        bgImg: '',
        logoImg: '',
        bottomContent: ''
      }
    }
  },
  computed: {
    dataRule() {
      return {
        username: [{ required: true, message: this.$t('validate.required'), trigger: 'blur' }],
        password: [{ required: true, message: this.$t('validate.required'), trigger: 'blur' }],
        companyCode: [{ required: true, message: this.$t('validate.required'), trigger: 'blur' }],
        captcha: [{ required: true, message: this.$t('validate.required'), trigger: 'blur' }]
      }
    }
  },
  created() {
    this.forgetUrl = window.location.origin
    Promise.all([this.getByCompany(), this.getCaptcha()]).then(() => {})
  },
  methods: {
    gotoPage (routeName) {
      this.$router.push({ name: routeName })
    },
    getByCompany() {
      // 获取公司登录信息
      const host = window.location.host
      // this.pageShow = true
      this.$http
        .get('/sys/website/getByDomain/' + host)
        .then(({ data: res }) => {
          // this.pageShow = false
          if (res.code !== 0) {
            this.validateMsg = res.msg
            return
          }
          this.companyInfo = res.data || {}
          this.dataForm.companyId = res.data.companyId
          this.dataForm.companyName = res.data.companyName
          this.isValidateUrl = true
        })
        .catch(() => {
          // this.pageShow = false
          this.validateMsg = '系统访问出错'
        })
    },
    // 获取验证码
    getCaptcha() {
      this.dataForm.uuid = getUUID()
      this.captchaPath = `${this.$baseUrl}/cs/auth/captcha?uuid=${this.dataForm.uuid}`
    },
    // 表单提交
    dataFormSubmitHandle: debounce(
      function() {
        // 聚焦点
        if (!this.dataForm.username) {
          this.$refs.username.focus()
          return
        } else if (!this.dataForm.password) {
          this.$refs.password.focus()
          return
        } else if (!this.dataForm.captcha) {
          this.$refs.captcha.focus()
          return
        }
        this.$refs['dataForm'].validate(valid => {
          if (!valid) {
            return false
          }
          this.isLogin = true
          this.$http
            .post('/cs/auth/login', this.dataForm)
            .then(({ data: res }) => {
              this.isLogin = false
              if (res.code !== 0) {
                this.getCaptcha()
                return this.$message.error(res.msg)
              }
              Cookies.set('cs_token', res.data.token)
              this.$router.replace({ name: 'home' })
            })
            .catch(() => {
              this.isLogin = false
            })
        })
      },
      1000,
      { leading: true, trailing: false }
    )
  }
}
</script>
<style lang="scss" scoped>
.bg{
  background:#409EFF;
  border-color: #409EFF;
}
.logoImg {
  max-width: 300px;
  max-height: 70px;
  overflow: hidden;
}
.bgImg {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  /*z-index: -1;*/
  background-size: 100% auto;
  /*filter: blur(3px);*/
}

</style>
