<template>
  <el-dialog :visible.sync="visible" :title="$t('fba.splitOrder')"  min-widt="800"  top="30px"
             :close-on-click-modal="false" :close-on-press-escape="false" :lock-scroll="true" class="location_model">
    <div class="detail-desc">
      <el-form ref="dataForm"  class="form_no_margin" :model="orderForm"  label-width="120px">
        <el-row :gutter="2" type="flex">
          <el-col :span="24">
            <description :label="$t('coOrder.customerOrderNo')">
              {{orderForm.customerOrderNo}}
            </description>
            <description :label="$t('coOrder.forecastWeight')">
              {{orderForm.forecastWeightD}} {{orderForm.standardUnit === 0 ? ' KG' : ' LB'}}
            </description>
            <description :label="$t('coOrder.packageQty')">
              {{orderForm.packageQty}}
            </description>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <el-form :model="dataForm"  ref="dataForm" label-width="140px" :rules="dataRule" @keyup.enter.native="dataFormSubmitHandle()">
      <div  class="orderAdd">
        <div class="panel-hd" style="font-size: 14px;margin: 10px">
          <span v-text="$t('fba.splitRule')"></span>
        </div>
        <el-row :gutter="22">
          <el-col :span="13">
            <el-form-item :label="$t('fba.maxWeightPerOrder')" prop="maxWeightPerOrder">
              <el-input v-model="dataForm.maxWeightPerOrder"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="22" >
          <el-col :span="13">
            <el-form-item :label="$t('fba.maxPackageQtyPerOrder')" prop="maxPackageQtyPerOrder">
              <el-input v-model="dataForm.maxPackageQtyPerOrder" ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <template slot="footer">
      <el-button @click="cancelFn">{{ $t('cancel') }}</el-button>
      <el-button type="primary" :loading="buttonLoading" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import comMixins from '@/mixins/comMixins'
import { isPlusInteger2, isPlusFloat, isDecimal3 } from '@/utils/validate'
export default {
  mixins: [comMixins],
  data () {
    return {
      buttonLoading: false,
      visible: false,
      comMixins: {
      },
      dataForm: {
        id: '',
        maxWeightPerOrder: '',
        maxPackageQtyPerOrder: ''
      },
      orderForm: {
        customerOrderNo: '',
        forecastWeightD: '',
        standardUnit: '',
        packageQty: ''
      },
      orderId: ''
    }
  },
  computed: {
    dataRule () {
      const isInteger = (rule, value, callback) => {
        if (value && !isPlusInteger2(value)) {
          return callback(new Error('请输入大于0的正整数'))
        }
        if (value > Number.MAX_SAFE_INTEGER) {
          return callback(new Error('数值超过最大值[' + Number.MAX_SAFE_INTEGER + ']限制'))
        }
        callback()
      }
      const isFloat = (rule, value, callback) => {
        if (value && !(isPlusFloat(value) || isPlusInteger2(value))) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value && value <= 0) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value && !isDecimal3(value)) {
          return callback(new Error('限制1~3位小数'))
        }
        if (value && value > Number.MAX_SAFE_INTEGER) {
          return callback(new Error('数值超过最大值[' + Number.MAX_SAFE_INTEGER + ']限制'))
        }
        callback()
      }
      return {
        maxWeightPerOrder: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isFloat, trigger: 'blur' }
        ],
        maxPackageQtyPerOrder: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isInteger, trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    this.getBaseData()
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.$refs['dataForm'].clearValidate()
      })
    },
    // 获取信息
    getInfo () {
    },
    async getBaseData () {
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.buttonLoading = true
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          this.buttonLoading = false
          return false
        }
        this.$http['post']('co/order/splitOrder', this.dataForm).then(({ data: res }) => {
          this.buttonLoading = false
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('splitOrderAfterHandle')
            }
          })
        }).catch(() => {
          this.buttonLoading = false
        })
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    // 取消
    cancelFn () {
      this.visible = false
    }
  }
}
</script>

<style lang="scss">
  .customer-list {
    .el-select {
      width: 100%;
    }
  }
</style>
