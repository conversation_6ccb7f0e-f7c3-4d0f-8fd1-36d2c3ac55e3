<template>
  <div class="orderArea">
    <div class="tab-area">
      <div v-show='showBtn' class="left-btn">&lt;</div>
      <div class='scroll-area'>
        <ul class="orderAreaTab">
          <draggable v-model="localAttributeList" animation="1000" draggable="li" dragClass="dragClass" ghostClass="ghostClass" chosenClass="chosenClass">
            <li class="tabItem" :class="selectedAttribute === item.attribute? 'actived':''"
                v-for="(item, index) in localAttributeList" :key="index" @click="areaTabClick(item)">{{item.label}}</li>
          </draggable>
        </ul>
      </div>
      <div v-show='showBtn' class="right-btn">&gt;</div>
    </div>
    <div class="areaBox" >
      <el-input ref="textarea" type="textarea" :placeholder="`请输入内容,最多支持查询${noSize}个单号`" @change="noChange()" v-model="dataForm.no" :autosize="autosize" resize="none"></el-input>
    </div>
  </div>
</template>

<script>
import elementResizeDetectorMaker from 'element-resize-detector'
import draggable from 'vuedraggable'
export default {
  props: {
    attributeList: {
      type: Array,
      required: true,
      default: () => {
        return []
      }
    },
    autosize: {},
    noSize: {
      default: 500
    }
  },
  data () {
    return {
      showBtn: false,
      dataForm: {
        no: ''
      },
      selectedAttribute: ''
    }
  },
  components: {
    draggable
  },
  activated () {
    if (this.attributeList && this.attributeList.length > 0) {
      this.selectedAttribute = this.attributeList[0].attribute
    }
  },
  mounted () {
    const _this = this
    const erd = elementResizeDetectorMaker()
    erd.listenTo(document.querySelector('.orderAreaTab'), element => {
      _this.$nextTick(() => {
        let orderAreaTabWidth = document.getElementsByClassName('orderAreaTab')[0].scrollWidth
        let areaBoxWidth = document.getElementsByClassName('areaBox')[0].clientWidth
        this.showBtn = orderAreaTabWidth > areaBoxWidth
      })
    })
    const right = document.querySelector('.right-btn')
    const left = document.querySelector('.left-btn')
    let scroller = document.querySelector('.orderAreaTab')
    right.addEventListener('click', () => {
      scroller.scrollLeft += 62
    })
    left.addEventListener('click', () => {
      scroller.scrollLeft -= 62
    })
  },
  created () {
    if (this.attributeList && this.attributeList.length > 0) {
      this.selectedAttribute = this.attributeList[0].attribute
    }
    document.documentElement.scrollLeft = 300
  },
  computed: {
    localAttributeList: {
      get() {
        return this.attributeList
      },
      set(value) {
        this.$emit('update:attributeList', value)
      }
    }
  },
  methods: {
    resizeTextarea () {
      this.$nextTick(() => {
        this.$refs.textarea.resizeTextarea()
      })
    },
    setValue () {
      var orderCodeArray = []
      if (this.dataForm.no !== null) {
        this.dataForm.no = this.dataForm.no.replace(/\n|\s+/g, ',').trim()
        this.dataForm.no = this.dataForm.no.replace(/,$/, '')
        orderCodeArray = this.dataForm.no.split(',')
      }
      if (orderCodeArray.length > this.noSize) {
        this.$message.warning(this.$t('threeNoInput.outMessage', { 'size': `${this.noSize}` }))
        return false
      }
      let otherAttributes = this.attributeList.map(item => item.attribute).filter(item => item !== this.selectedAttribute)
      this.$emit('setValue', this.dataForm.no, this.selectedAttribute, otherAttributes)
      return true
    },
    noChange () {
      this.setValue()
    },
    clearValue () {
      this.selectedAttribute = ''
      this.dataForm.no = ''
      let allAttributes = this.attributeList.map(item => item.attribute)
      this.$emit('clearValue', allAttributes)
      if (this.attributeList && this.attributeList.length > 0) {
        this.selectedAttribute = this.attributeList[0].attribute
      }
    },
    areaTabClick (item) {
      this.selectedAttribute = item.attribute
      this.noChange()
    }
  }
}
</script>

<style lang="scss">
.orderArea {
  .areaBox {
    .el-form-item__label {
      max-width: 80px;
      margin-top: -25px;
    }
    .el-textarea__inner {
      border-radius: 0;
    }
    .el-form-item__content {
      margin-left: 80px !important;
    }
  }
}
</style>
<style lang="scss" scoped>
.orderArea{
  position: relative;
  min-width: 280px;
  overflow-x: hidden;
  overflow-y: hidden;
  white-space: nowrap;
  .areaBox{
    padding-top: 26px;
    .el-form-item__label{
      max-width: 80px;
      margin-top: -25px;
    }
    .el-textarea__inner{
      border-radius: 0;
    }
    .el-form-item__content{
      margin-left:80px;
    }
  }
  .tab-area {
    position: absolute;
    right: 0;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .left-btn {
      top:0;
      left: 0;
      border: 1px solid #DCDFE6;
      line-height: 25px;
      color: $--color-primary;
      font-weight: bold;
      &.actived, &:hover{
        background: #b3d8ff;
        border-left-color: #DCDFE6;
        color: #fff;
        cursor:pointer;
      }
    }
    .right-btn {
      top:0;
      right: 0;
      border: 1px solid #DCDFE6;
      line-height: 25px;
      color: $--color-primary;
      font-weight: bold;
      &.actived, &:hover{
        background: #b3d8ff;
        border-left-color: #DCDFE6;
        color: #fff;
        cursor:pointer;
      }
    }
  }
  .scroll-area {
    display: contents;
    min-width: auto;
    overflow-x: scroll;
    overflow-y: hidden;
    white-space: nowrap;
    align-items: center;
    justify-content: center;
    transition: all .3s ease-in-out;
    .orderAreaTab {
      div{
        display: flex;
        width: 100%;
      }
      display: flex;
      overflow-x: scroll;
      overflow-y: hidden;
      list-style: none;
      width: 100%;
      padding: 0;
      margin: 0 0px;

      .tabItem {
        flex: 1;
        min-width: fit-content;
        display: inline-block;
        text-align: center;
        border: 1px solid #DCDFE6;
        padding: 0px 5px 0px 5px;
        height: 25px;
        line-height: 25px;
        margin-left: 0px;
        cursor: pointer;

        &:first-child {
          margin-left: 0px;
        }

        &.actived, &:hover {
          background: $--color-primary;
          border-left-color: #DCDFE6;
          color: #fff;
        }
        &:hover {
          background:  #1890ff5c;
          border-left-color: #DCDFE6;
          color: #fff;
        }
        &:active {
          cursor: grabbing;
        }
      }
    }
    /* 设置滚动条的样式 */
    ::-webkit-scrollbar {
      width: 2px;
      height: 2px;
    }
    /* 滚动槽 */
    ::-webkit-scrollbar-track {
      background-color: #b3d8ff;
      border-radius: 10px;
    }
    /* 滚动条滑块 */
    ::-webkit-scrollbar-thumb {
      background-color: $--color-primary;
      border-radius: 10px;
    }
  }
}
.dragClass{
  background: #1890ff5c !important;
  cursor: grabbing !important;
}
.chosenClass{
  background: #1890ff5c !important;
  cursor: grabbing !important;
}
.ghostClass{
  background: #1890ff5c !important;
  cursor: grabbing !important;
}
</style>
