import t from './sys/zh-CN'

/* 此处写自定义的国际化 */
export default {
  ...t,
  /* 公用 */
  back: '返回',
  close: '关闭',
  fastMenu: '快捷菜单',
  startTime: '开始时间',
  endTime: '结束时间',
  backList: '返回列表',
  description: '说明',
  threeNoInput: {
    outMessage: '单号不能超过{size}个',
    no: '单号'
  },
  baCurrencyRate: { /* 汇率 */
    ...t.baCurrencyRate,
    status: '状态',
    noEnable: '未启用',
    allStatus: '所有'
  },
  bdTrackingNo: { /* 运单号库 BdTrackingNo */
    ...t.bdTrackingNo,
    prefix: '前缀',
    suffix: '后缀',
    startSerialNo: '起始序号',
    generateRecordCount: '生成记录数',
    add: '生成运单号',
    delete: '作废',
    export: '导出'
  },
  validate: {
    ...t.validate,
    format: '{attr}-格式错误',
    decimal3: '只能输入{number}位以内小数',
    bdTrackingNoRequiredPrefix: '前缀必须在3到4位',
    generateRecordCount: '最大值为100000',
    existSpaces: '存在空格',
    errorEmail: '邮箱格式错误',
    isOverLength: '只能输入1-{max}位长度',
    whoIsOverLength: '{who}只能输入1-{max}位长度',
    /* 物流产品外部编码 BdLogisticsProductOuterCode */
    bdLogisticsProductOuterCodeLengthOuterCode: '外部编码长度在 1 到 32 个字符',
    bdLogisticsProductOuterCodeLengthOuterName: '外部编码名称长度在 1 到 64 个字符',
    /* 港口 BdSeaport */
    bdSeaportCode: '港口代号长度在 1 到 20 个字符',
    bdSeaportCodeExist: '港口代号已存在',
    bdSeaportName: '中文名长度在 1 到 64 个字符',
    bdSeaportEnglishName: '英文名长度在 1 到 64 个字符',
    required: '必填项不能为空',
    postCodeValidate: '{attr}格式错误,最多32位',
    lengthValidate: '{attr}格式错误,只能输入正数，最多保留1位小数位',
    weight: '{attr}格式错误,只能输入正数，最多保留3位小数位'
  },
  wsFlight: {
    /* 空运航班 WsFlight */
    id: 'ID',
    flightNo: '航班号',
    airlinesCompany: '航空公司',
    airRoutes: '空运航线',
    type: '类型',
    takeoffAirport: '起运机场',
    transferAirport: '中转机场',
    landingAirport: '降落机场',
    takeoffDate: '起飞时间',
    landingDate: '降落时间',
    memo: '备注'
  },
  draft: '暂存草稿',
  forecast: '预报',
  batchPrint: '批量打印',
  batchPrintInvoice: '批量打印发票',
  batchDownloadLabel: '批量下载面单',
  guidPrefix: 'GUID前缀',
  noType: '单号类型',
  printLabel: '打印面单',
  cancelForecast: '取消预报',
  intercept: '拦截',
  deletedCancel: '撤销',
  completeDeleted: '彻底删除',
  setDefault: '设置默认',
  prompt: {
    ...t.prompt,
    forecast: '请选择预报数据',
    deleted: '请选择删除数据',
    uploadEmpty: '请上传付款水单',
    cancelForecast: '请选择取消预报数据',
    intercept: '请选择拦截数据',
    batchPrint: '请选择批量打印数据',
    deletedCancel: '请选择撤销数据',
    completeDeleted: '请选择彻底删除数据'
  },
  coOrder: {
    /* 订单信息 CoOrder */
    id: '订单编号',
    printProofOfPostage: '打印发货证明',
    printProofOfSignFor: '打印签收证明',
    customerOrderNo: '客户单号',
    subCustomerOrderNo: '子客户单号',
    customerBoxNo: '客户箱号',
    update: '订单--修改',
    copy: '订单--复制',
    customerOrderNoOrInWarehouseNo: '客户单号/入库单号',
    waybillNo: '运单号',
    waybillNoPlaceHold: '物流产品设置为手工输入才需要填写该单号',
    forecastMoney: '预估费用',
    customerOrderNoLike: '模糊客户单号',
    customerOrderNoLikePlaceHold: '根据客户单号模糊查询',
    postalTrackingNo: '邮政单号',
    deliveryNo: '派送单号',
    deliveryNoPlaceHold: '只有线上物流才需要填写该单号',
    splitBatchNo: '拆单批次号',
    logisticsProductCode: '物流产品',
    taxPayMode: '税费模式',
    taxPayAccount: '税费支付人账号',
    customsMethod: '报关方式',
    forecastWeight: '预报重量',
    parcelType: '包裹类型',
    goodsCategory: '货物类别',
    electric: '是否带电',
    magnetized: '是否带磁',
    liquid: '是否液体',
    powder: '是否粉末',
    remote: '是否偏远',
    insuredAmount: '保险金额',
    insuredCurrency: '保险币种',
    codAmount: 'COD金额',
    codCurrency: 'COD币种',
    declareCurrency: '申报币种',
    consigneeCountry: '收货国家',
    consigneeName: '收件人姓名',
    consigneeCompany: '收件人公司',
    customerRemark: '客户备注',
    serviceId: '客服编号',
    salesmanId: '业务员编号',
    customerId: '客户编号',
    customerName: '客户名称',
    customerSimpleCode: '客户简码',
    franchiseeId: '加盟商编号',
    warehouseId: '仓库编号',
    platformType: '来源平台',
    shopName: '店铺',
    salesUrl: '销售URL',
    packageQty: '包裹件数',
    print: '是否打印',
    status: '订单状态',
    createDate: '创建时间',
    orderByMode: '排序模式',
    fbaWarehouseCheckBoxTips: '以亚马逊仓库作为收件地址',
    fillInSenderInfo: '填写发件人信息',
    enterDeclareInfo: '录入申报信息',
    amazonWarehouse: '亚马逊仓库',
    printInvoice: '打印发票',
    asynNoStatus: '异步单号状态',
    noType: '单号类型',
    electricCode: '电池代码',
    getAsynNoStatus: '异步单号已获取'
  },
  coOrderShipper: {
    /* 订单发货人 CoOrderShipper */
    id: '订单编号',
    shipperName: '姓名',
    shipperCompany: '公司',
    shipperPhone: '电话',
    shipperEmail: 'Email',
    shipperCountry: '国家',
    shipperProvince: '省/州',
    shipperCity: '城市',
    shipperDistrict: '区',
    shipperAddress: '地址',
    shipperPostcode: '邮编',
    shipperDoorplate: '门牌',
    shipperStreet: '街道',
    iossTaxType: 'IOSS增值税类型',
    iossNo: 'IOSS登记号',
    eoriNo: 'EORI登记号',
    vatNo: 'VAT/发件人税号',
    vatCompanyEnName: 'VAT公司英文名',
    vatRegisterCountry: 'VAT注册国家',
    vatRegisterAddress: 'VAT注册地址'
  },
  bdAttachments: {
    name: '文件名',
    file: '文件',
    attachmentUrl: '附件地址'
  },
  coOrderPackage: {
    addBtn: '增加报关明细',
    /* 订单包裹 CoOrderPackage */
    id: 'ID',
    orderId: '订单ID',
    packageSerialNo: '包裹序号',
    packageDeliveryNo: '子派送单号',
    subCustomerOrderNo: '子客户单号',
    packageCustomerNo: '客户包裹号',
    packageWeight: '重量(KG)',
    packageSize: '尺寸(CM)',
    packageLength: '长(CM)',
    packageWidth: '宽(CM)',
    packageHeight: '高(CM)',
    channelLabelUrl: '渠道面单URL',
    atLeastOne: '至少填写一个包裹明细'
  },
  coOrderOperateLog: {
    /* 订单操作日志 CoOrderOperateLog */
    id: 'ID',
    orderId: '订单编号',
    operateNode: '操作节点',
    opeareteDescription: '操作描述'
  },
  coOrderOther: {
    /* 订单其他信息 CoOrderOther */
    id: '订单编号',
    customerRealWeight: '客户实际重量'
  },
  coOrderDeclare: {
    /* 申报明细 CoOrderDeclare */
    id: 'ID',
    orderId: '订单编号',
    chineseName: '中文品名',
    englishName: '英文品名',
    quantity: '数量',
    unitNetWeight: '单位净重(KG)',
    unitDeclarePrice: '申报单价',
    brand: '品牌',
    goodsBarcode: '货物条码',
    sku: 'SKU',
    hsCode: '海关编码',
    productModel: '产品型号',
    material: '材质',
    purpose: '用途',
    origin: '原产地',
    pickingRemark: '配货备注',
    productUrl: '商品URL',
    /* 报关信息 验证 */
    isDecimal3: '1-3位小数',
    isDecimal2: '1-2位小数',
    isPlusInteger2: '非0正整数',
    letterAndNumber: '字母和数字组合',
    notChinese: '非中文',
    notInputChinese: '不能含中文',
    isEmail: '邮箱',
    atLeastOne: '至少填写一个报关明细'
  },
  coOrderConsignee: {
    /* 订单收货人 CoOrderConsignee */
    id: '订单编号',
    consigneeName: '姓名',
    recipient: '收件人',
    consigneeCompany: '公司',
    consigneePhone: '电话',
    consigneeEmail: 'Email',
    consigneeCountry: '国家',
    consigneeProvince: '省/州',
    consigneeCity: '城市',
    consigneeDistrict: '区',
    consigneeAddress: '地址',
    detailAddress: '详细地址',
    consigneePostcode: '邮编',
    consigneeDoorplate: '门牌号',
    consigneeStreet: '街道/街区/社区',
    consigneeIdcard: '证件号',
    consigneeTaxNo: '税号'
  },
  coOrderChannelLabel: {
    /* 订单渠道标签 CoOrderChannelLabel */
    id: 'ID',
    orderId: '订单编号',
    customerOrderNo: '客户单号',
    waybillNo: '运单号',
    deliveryNo: '派送单号',
    channelLabelUrl: '渠道面单地址'
  },
  coSetCustomerShipper: {
    /* 客户发货人信息-常用发货人设置 CoSetCustomerShipper */
    id: 'ID',
    customerId: '客户编码',
    defaulted: '是否默认',
    shortName: '别名',
    shipperShortName: '常用发件人',
    consigneeShortName: '常用收件人',
    shipperName: '名称',
    shipperCompany: '公司',
    shipperContact: '电话',
    shipperEmail: 'Email',
    shipperCountryName: '发货国家名称',
    shipperCountryCode: '国家',
    shipperProvince: '省/州',
    shipperCity: '城市',
    shipperDistrict: '区',
    shipperAddress: '地址',
    shipperPostcode: '邮编',
    shipperDoorplate: '门牌号',
    shipperStreet: '街道'
  },
  coSetCustomerConsignee: {
    id: 'ID',
    customerId: '客户编码',
    defaulted: '是否默认',
    shortName: '编码',
    fbaWarehouseCode: 'Fba仓库代码',
    consigneeName: '姓名',
    consigneeCompany: '公司',
    consigneePhone: '电话',
    consigneeCountry: '国家',
    consigneeProvince: '省/州',
    consigneeCity: '城市',
    consigneeDistrict: '区',
    consigneeStreet: '街道/街区',
    consigneeDoorplate: '门牌',
    consigneeAddress: '地址',
    consigneePostcode: '邮编',
    consigneeIdcard: '证件号',
    consigneeEmail: 'Email',
    consigneeTaxNo: '税号'
  },
  coSetCustomerLogisticsProduct: {
    /* 客户产品信息-常用运输方式设置 CoSetCustomerLogisticsProduct */
    id: 'ID',
    productCode: '物流产品编码',
    productName: '物流产品名称',
    customerCode: '所属客户编码',
    customerName: '所属客户名称'
  },
  coSetCustomerDeclare: {
    setCustomerDeclare: '常用报关',
    /* 客户报关信息-常用报关设置 CoSetCustomerDeclare */
    id: 'ID',
    customerCode: '客户编码',
    chineseName: '中文品名',
    englishName: '英文品名',
    quantity: '数量',
    unitNetWeight: '单位净重（KG）',
    unitDeclarePrice: '申报单价',
    brand: '品牌',
    goodsBarcode: '货物条码',
    sku: 'SKU',
    hsCode: '海关编码',
    productModel: '产品型号',
    material: '材质',
    purpose: '用途',
    origin: '原产地',
    pickingRemark: '配货备注',
    productUrl: '商品URL'
  },
  interceptOrderDataForm: {
    remark: '备注'
  },
  tabPane: {
    batch: '批次信息',
    waybill: '运单信息',
    shipperConsignee: '收发人信息',
    consignee: '收货人信息',
    declare: '报关信息',
    operateLog: '操作日志',
    exception: '异常信息',
    track: '轨迹信息',
    package: '包裹信息'
  },
  header: {
    operateLog: '操作日志',
    remark: '备注信息',
    baseInfo: '基础信息',
    recipientInfo: '收件人信息',
    senderInfo: '发件人信息',
    boxCargoInfo: '箱货信息',
    waybill: '运单信息',
    customer: '客户信息',
    exception: '异常信息',
    operationLog: '操作日志',
    shipper: '发件人',
    consignee: '收件人',
    track: '轨迹信息',
    workOrder: '工单信息',
    feedbackRecord: '反馈记录',
    setEmergencyLevel: '设置紧急程度',
    customerEvaluation: '客户评价',
    answerService: '回答客服'
  },
  btn: {
    addWorkOrder: '点击此新增工单',
    uploadAttachment: '上传附件',
    divideWorkOrder: '分配工单',
    replayAndFinish: '完结工单',
    onlySubmitReplay: '提交'
  },
  label: {
    warmPrompt: '温馨提示',
    productCode: '物流产品编码',
    productName: '物流产品名称',
    attachment: '查看附件',
    time: '时间',
    ip: 'IP'
  },
  coSetDiyimportModuleDetail: {
    id: 'ID',
    customerId: '客户编号',
    matchId: '字段映射编码',
    matchDetailId: '字段映射明细编码',
    objectType: '对象类型',
    moduleId: '自定义模版编码',
    repeatableColumns: '可重复列',
    fieldValue: '字段值',
    serialNo: '列序号',
    fieldName: '字段名',
    titleName: '表头名',
    digital: '是否数字',
    allowEmpty: '是否允许空'
  },
  coSetDiyimportModule: {
    id: 'ID',
    customerId: '客户编码',
    mainObjectName: '导入模块',
    name: '模板名称',
    description: '描述'
  },
  /* 订单平台客户参数 CoApiPlatformCustomerParam */
  coApiPlatformCustomerParam: {
    id: 'ID',
    platformId: '平台编号',
    customerId: '客户编号',
    key: 'KEY',
    value: 'VALUE',
    description: '描述'
  },
  placeholder: {
    logisticsProductCode: '请输入物流产品编码',
    evaluationContent: '请输入您宝贵的意见或者建议'
  },
  csmWorkOrderEvaluation: {
    id: 'ID',
    workOrderId: '工单编号',
    workOrderNo: '工单号',
    resolved: '问题已解决',
    score: '工单评分',
    evaluationContent: '评价内容',
    createDate: '评价时间',
    addTypeTips: '联系客服添加“客服工单类型”'
  },
  bdCustomerApiuser: {
    title: '系统对接',
    dom: '对接文档',
    parceldom: '小包对接文档',
    fbadom: 'FBA对接文档',
    expressdom: '快递对接文档',
    id: 'ID',
    customerId: '客户编号',
    usertoken: 'usertoken'
  },
  wsComWaybill: {
    id: 'ID',
    exception: '异常单详情',
    customerOrderNo: '客户单号',
    waybillNo: '运单号',
    deliveryNo: '派送单号',
    logisticsProductCode: '物流产品',
    logisticsChannelCode: '物流渠道',
    providerCode: '供应商',
    providerNo: '供应商代码',
    forecastWeight: '预报重量(KG)',
    weight: '入库实重(KG)',
    volumeWeight: '入库体积重(KG)',
    volume: '体积',
    inAdjustWeight: '入库重(KG)',
    outAdjustWeight: '出库重(KG)',
    exceptionFlag: '是否异常',
    createDate: '导入时间',
    status: '运单状态',
    arrearage: '是否欠费'
  },
  expressCustomerCalculate: {
    deliveryWarehouse: '交货仓'
  },
  fba: {
    standardUnit: '标准单位',
    returnList: '是否返回FBA订单列表？',
    weightUnit: '重量单位',
    lengthUnit: '长度单位',
    totalBox: '总箱数',
    picUrl: '图片',
    uploadPic: '点击此处上传',
    totalVol: '总体积',
    totalWeight: '总重量',
    currency: '申报币种',
    uploadAttachments: '上传附件',
    importBoxCargoInfo: '导入箱货信息',
    enterBoxInfo: '录入箱信息',
    boxNo: '箱号',
    oneByOneForLabel: '箱号一一对应',
    boxSerialNo: '箱序号',
    boxNoPrefix: '箱号前缀',
    sku: 'SKU',
    enName: '英文名',
    cnName: '中文名',
    splitOrder: '拆单',
    cancelSplitOrder: '取消拆单',
    minWeightPerOrder: '单票最小重量(KG)',
    maxWeightPerOrder: '单票最大重量(KG)',
    splitRule: '拆单规则',
    minPackageQtyPerOrder: '单票最小箱数',
    maxPackageQtyPerOrder: '单票最大箱数',
    qty: '数量',
    unitDeclarePrice: '申报单价',
    unitNetWeight: '单位净重',
    totalPrice: '总价值',
    hsCode: 'HSCODE',
    countryOfOrigin: '原产地',
    material: '材质',
    specificationAndModel: '规格型号',
    purpose: '用途',
    brand: '品牌',
    hsCode9610: '9610HSCODE',
    productUrl: '销售网址',
    pickingRemark: '配货备注',
    declareUnit: '申报单位',
    moreProductInfo: '更多商品信息',
    convenientEntry: '便捷录入',
    boxNoOrMarkNo: '箱号/FBA唛头',
    subCustomerOrderNo: '子客户单号',
    boxCount: '箱数',
    changeBoxInfo: '变更箱信息',
    scanBoxNoOrMarkNoPlaceholder: '扫描一个或多个箱号/FBA唛头',
    serialNo: '序号',
    lineNo: '行号',
    inputSerialNoPlaceholder: '填写1-3,表示1-3行;填写1,3,5,表示第1,3,5行',
    quickFill: '根据行号快捷填充',
    clear: '清空',
    boxInfo: '箱信息',
    autoAppend: '自动追加',
    addBoxNo: '追加箱号',
    resetBoxNo: '重置箱号',
    waybillNo: '运单号',
    printSystemShippingMark: '打印系统箱唛',
    length: '长',
    width: '宽',
    height: '高',
    weight: '重量',
    warehouseCode: '仓库代码',
    fbaWarehouse: 'FBA仓库',
    sortRate: '比价预报',
    batchSortRate: '批量比价预报',
    usedAndForecasting: '选用并预报',
    reSortRate: '清空并重新比价',
    reRate: '重新询价',
    batchOrderImport: '批量导入订单',
    baseInfoInputTips: '请把基础信息和收件人信息填写完整!并录入箱信息.',
    boxInfoInputTips: '请把箱货信息填写完整',
    printSysBoxMark: '打印系统箱唛',
    header: {
      copy: 'FBA订单 -- 复制',
      update: 'FBA订单 -- 修改'
    }
  },
  express: {
    returnList: '返回快递订单列表',
    header: {
      copy: '快递订单 -- 复制',
      update: '快递订单 -- 修改'
    },
    switchConciseMode: '切换简洁录入模式',
    switchFullMode: '切换完整录入模式',
    forecastAndInWarehouse: '预报并入库',
    customerBoxNo: '客户箱号',
    boxNoLenPrefix: '箱号共',
    boxNoLenSuffix: '位数',
    generateBoxNo: '生成箱号',
    addAnRow: '新增一行',
    editForm: '编辑表单',
    addConsigneeInfo: '追加收发件人信息',
    addDeclareInfo: '补充申报信息'
  },
  exportTemplate: '编辑模板',
  template: {
    detail: '模板明细',
    add: '新增模板',
    editdetail: '编辑模板明细',
    download: '下载模板',
    type: '模板名称',
    edit: '编辑模板'
  },
  bdExcelExportTemplateDetail: {
    columnNo: '列序号',
    fieldName: '字段名',
    titleName: '表头名',
    fixValue: '固定内容'
  },
  bdExcelExportTemplate: {
    name: '模板名称',
    exportMainObjectName: '导出模块',
    description: '描述',
    detailOneLine: '申报明细单行显示'
  },
  /* 询价结果 BaSortRateLog */
  baSortRateLog: {
    id: 'ID',
    orderId: '订单ID',
    channelRouterId: '渠道链路',
    logisticsChannelCode: '物流渠道',
    providerChannelCode: 'API渠道代码',
    type: '报价来源',
    currency: '币种',
    payableSum: '金额',
    balanceWeight: '结算重',
    balanceWeightUnit: '结算重单位',
    message: '错误信息',
    createDate: '询价时间'
  },

  /* 轨迹业务主数据队列表 TksTrackingQueue */
  tksTrackingWaybill: {
    waybillNo: '运单号',
    diffHour: '距最新轨迹时间小时差',
    departureCountry: '起运地国家',
    destinationCountry: '目的地国家',
    customerCode: '客户',
    customerName: '客户',
    customerId: '客户',
    providerId: '供应商',
    mawbNo: '提单号',
    customerOrderNo: '客户单号',
    deliveryNo: '派送单号',
    masterDeliveryNo: '主派送单号',
    postalTrackingNo: '邮政单号',
    subDeliveryNo: '子派送单号',
    logisticsProductCode: '物流产品',
    logisticsChannelCode: '物流渠道',
    businessType: '业务类型',
    status: '状态',
    createDate: '创建时间',
    planTrackingTime: '计划抓取时间',
    lastTrackingTime: '最新轨迹时间',
    inTime: '入库时间',
    outTime: '出库时间',
    lastTrackingLocation: '最新轨迹地址',
    lastTrackingCode: '轨迹节点',
    lastTrackingContent: '最新轨迹内容',
    collectPackageTimeliness: '揽收时效',
    deliveryPackageTimeliness: '派送时效',
    batchDel: '批量删除',
    batchToAutonomousPush: '批量移至自主推送',
    updatePostCodeForDelivery: '批量变更签收邮编',
    batchDelTks: '批量删除轨迹',
    trackingFlag: '跟踪状态',
    registerNo: '注册单号'
  },
  bdCustomerRegister: {
    code: '用户名',
    password: '密码',
    confirmPassword: '确认密码',
    captcha: '验证码',
    name: '公司名称（或个人姓名）',
    phone: '手机号码',
    email: '电子邮件',
    address: '联系地址'
  },
  /* 换标打印 WsChangeLabelPrintOrder */
  wsChangeLabelPrintOrder: {
    id: 'ID',
    customerId: '客户',
    customerName: '客户',
    orderId: '订单ID',
    waybillId: '运单ID',
    customerOrderNo: '客户单号',
    deliveryNo: '派送单号',
    waybillNo: '运单号',
    logisticsProductCode: '运输方式',
    logisticsChannelCode: '渠道代码',
    pushOrderToProviderStatus: '同步订单到供应商状态',
    pushOrderToProviderErrorMessage: '同步订单到供应商错误信息',
    deliveryNoSource: '派送单号来源',
    hasLabel: '是否有面单',
    channelLabelUrl: '渠道面单地址',
    printTimes: '打印次数',
    warehouseId: '仓库ID'
  }
}
