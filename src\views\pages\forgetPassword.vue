<template>
  <div class="aui-wrapper aui-page__login">
    <div class="aui-content__wrapper">
      <main class="aui-content">
        <div class="login-header">
          <h2 class="login-brand">{{ $t('brand.lg') }}</h2>
        </div>
        <div class="login-body">
          <h3 class="login-title">{{ $t('forget.title') }}</h3>
          <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" status-icon>
            <el-form-item>
              <el-select v-model="$i18n.locale" class="w-percent-100">
                <el-option v-for="(val, key) in i18nMessages" :key="key" :label="val._lang" :value="key"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="email">
              <el-input ref="email" v-model="dataForm.email" :placeholder="$t('forget.email')" autofocus>
                <span slot="prefix" class="el-input__icon">
                  <svg class="icon-svg" aria-hidden="true"><use xlink:href="#icon-user"></use></svg>
                </span>
              </el-input>
            </el-form-item>
            <el-form-item prop="captcha">
              <el-row :gutter="20">
                <el-col :span="14">
                  <el-input ref="captcha" v-model="dataForm.captcha" :placeholder="$t('login.captcha')">
                    <span slot="prefix" class="el-input__icon">
                      <svg class="icon-svg" aria-hidden="true"><use xlink:href="#icon-safetycertificate"></use></svg>
                    </span>
                  </el-input>
                </el-col>
                <el-col :span="10" class="login-captcha">
                  <img :src="captchaPath" @click="getCaptcha()">
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="dataFormSubmitHandle()" :loading="disable" class="w-percent-100 bg">{{ $t('forget.send') }}</el-button>
            </el-form-item>
          </el-form>
        </div>
        <!-- <div class="login-footer">
          <p>
            <a :href="$baseUrl + '/its'" target="_blank">{{ $t('login.demo') }}</a>
          </p>
          <p><a href="http://www.xx.cn/" target="_blank">{{ $t('login.copyright') }}</a>2019 © xx.cn</p>
        </div> -->
      </main>
    </div>
  </div>
</template>

<script>
import debounce from 'lodash/debounce'
import { messages } from '@/i18n'
import { getUUID } from '@/utils'
import { isEmail } from '@/utils/validate'
export default {
  data () {
    return {
      i18nMessages: messages,
      captchaPath: '',
      disable: false,
      dataForm: {
        email: '',
        uuid: '',
        captcha: ''
      }
    }
  },
  computed: {
    dataRule () {
      const isMail = (rule, value, callback) => {
        if (!isEmail(value)) {
          return callback(new Error(this.$t('forget.illegalEmail')))
        }
        callback()
      }
      return {
        email: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isMail, trigger: 'blur' }
        ],
        captcha: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    this.getCaptcha()
  },
  methods: {
    // 获取验证码
    getCaptcha () {
      this.dataForm.uuid = getUUID()
      this.captchaPath = `${this.$baseUrl}/auth/captcha?uuid=${this.dataForm.uuid}`
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      // 聚焦点
      if (!this.dataForm.email) {
        this.$refs.email.focus()
        return
      } else if (!this.dataForm.captcha) {
        this.$refs.captcha.focus()
        return
      }

      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.disable = true
        this.$http.post('/auth/sendRestUrl', this.dataForm).then(({ data: res }) => {
          this.disable = false
          if (res.code !== 0) {
            this.getCaptcha()
            return this.$message.error(res.data)
          } else {
            this.dataForm.email = ''
            this.dataForm.captcha = ''
            this.getCaptcha()
            return this.$message.success(this.$t('forget.sendSuccess'))
          }
        }).catch(() => {
          this.disable = false
        })
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
<style lang="scss" scoped>
.bg{
  background:$--color-primary;
  border-color: $--color-primary;
}
</style>
