
export default {
  system: {
    id: 'ID',
    creator: '创建人',
    createDate: '创建时间',
    updater: '更新人',
    updateDate: '更新时间',
    status: '状态',
    warehouseId: '仓库编号',
    warehouseName: '仓库名称',
    startTime: '开始时间',
    endTime: '结束时间',
    yes: '是',
    no: '否',
    to: '至',
    startDate: '起始日期',
    endDate: '结束日期',
    start: '起始',
    end: '结束',
    index: '序号'
  },
  loading: '加载中...',
  brand: {
    lg: '订单管理系统',
    mini: '订单管理系统'
  },
  baseInfo: '基础信息',
  pushData: '数据推送',
  status: '状态',
  add: '新增',
  addDeclareCargo: '新增申报信息',
  recharge: '充值',
  save: '保存',
  saveAndConfirm: '保存并确认',
  printProductBarcode: '打印商品条码',
  delete: '删除',
  deleteBatch: '删除',
  update: '修改',
  query: '查询',
  import: '导入',
  import_log_info: '导入日志',
  import_module1: 'FBA-多票批量导入-按箱号',
  import_module2: 'FBA-多票批量导入-按总箱数',
  import_module3: 'FBA-单票导入',
  import_amazon_module: '亚马逊模板',
  templateDownload: '  模板下载:',
  export: '导出',
  exportStandard: '标准导出',
  exportDetails: '详细导出',
  handle: '操作',
  confirm: '确定',
  continue: '继续',
  submit: '提交',
  cancel: '取消',
  logout: '退出',
  enable: '启用',
  disable: '停用',
  selectImportTemplate: '模板',
  select: '请选择',
  seniorSearch: '更多条件',
  seniorSearch2: '收起',
  view: '查看',
  viewMore: '查看更多',
  copy: '复制',
  log: '日志',
  resets: '重置',
  detail: '详情',
  goBack: '返回上一级',
  noData: '暂无数据',
  printLocationBarcode: '打印库位条码',
  createLocation: '库位初始化',
  audit: '审核',
  preAudit: '预收',
  abandon: '弃审',
  finish: '完成',
  tableConfigTooltip: '自定义列显示',
  errHandle: '异常处理',
  details: '明细',
  chargingBatch: '批量计费',
  charging: '计费',
  createOrder: '下单',
  prompt: {
    title: '提示',
    info: '确定进行[{handle}]操作?',
    success: '操作成功',
    failed: '操作失败',
    uploadEmpty: '请上传付款水单',
    auditBatch: '请选择审核项',
    deleteBatch: '请选择删除项',
    abandonBatch: '请选择弃审项',
    finishBatch: '请选择完成项',
    warehouse: '请先选择一个仓库',
    actionStatusBatch: '请选择操作项',
    export: '请选择导出模板'
  },
  msNotice: {
    title: '标题',
    type: '类型',
    publishDate: '发布时间',
    creatorName: '发布者'
  },
  validate: {
    format: '{attr}-格式错误',
    decimal3: '只能输入{number}位以内小数',
    required: '必填项不能为空',
    limitOneDecimal: '{attr}最多只能保留一位小数',
    number: '只能输入3位以内小数',
    endWeight: '结束重量必须大于起始重量',
    weight: '{attr}格式错误,只能输入正数，最多保留3位小数位',
    zoneValidate: '{attr}格式错误,只能输入正数',
    discountValidate: '{attr}格式错误,只能输入正数，最多保留2位小数位',
    lengthValidate: '{attr}格式错误,只能输入正数，最多保留1位小数位',
    postCodeValidate: '{attr}格式错误,最多32位',
    existSpaces: '存在空格',
    errorEmail: '邮箱格式错误',
    isOverLength: '只能输入1-{max}位长度'
  },
  upload: {
    text: '将文件拖到此处，或<em>点击上传</em>',
    recharge: '将付款水单拖到此处，或<em>点击上传</em>',
    tip: '只支持{format}格式文件！',
    button: '点击上传',
    sizeMsg: '上传文件大小不能超过{size}MB！',
    attachment: '附件',
    fileNameFormat: '文件名只能中文，字母，数字，中划线和下划线'
  },
  datePicker: {
    range: '至',
    start: '开始日期',
    end: '结束日期',
    selectd: '请选择时间'
  },
  fullscreen: {
    prompt: '您的浏览器不支持此操作'
  },
  updatePassword: {
    title: '修改密码',
    username: '账号',
    password: '原密码',
    newPassword: '新密码',
    comfirmPassword: '确认密码',
    validate: {
      comfirmPassword: '确认密码与新密码输入不一致',
      rule: '新密码必须由 6-30位字母、数字、特殊符号组成.'
    }
  },
  contentTabs: {
    closeCurrent: '关闭当前标签页',
    closeOther: '关闭其它标签页',
    closeAll: '关闭全部标签页'
  },
  notFound: {
    desc: '抱歉！您访问的页面<em>失联</em>啦...',
    back: '上一页',
    home: '首页'
  },
  login: {
    title: '登录',
    username: '用户名',
    password: '密码',
    captcha: '验证码',
    demo: 'ITS',
    copyright: 'Copyright © 2022 深圳市互联通科技有限公司',
    forgetPassword: '忘记密码?',
    mail: '邮箱'
  },
  forget: {
    title: '忘记密码',
    email: '邮箱地址',
    send: '发送邮件',
    illegalEmail: '请输入正确的邮箱格式',
    sendSuccess: '重置密码的邮件已经发送到您的邮箱，请注意查收...'
  },
  reset: {
    title: '重置密码',
    passwordFirst: '请输入你的新密码',
    passwordSecond: '请再次输入',
    successMsgPerfix: '恭喜你，密码重置成功！你现在可以使用新密码登陆系统了',
    successMsgSuffix: '秒后将自动跳转至登陆页面...',
    passwordDiff: '请输入相同的密码'
  },
  home: {
    desc: {
      title: '项目介绍',
      list: [
        'WOMS'
      ]
    }
  },
  process: {
    name: '名称',
    key: '标识',
    deployFile: '部署流程文件',
    id: '流程ID',
    deploymentId: '部署ID',
    version: '版本',
    resourceName: 'XML',
    diagramResourceName: '图片',
    deploymentTime: '部署时间',
    active: '激活',
    suspend: '挂起',
    convertToModel: '转换为模型'
  },
  running: {
    id: '实例ID',
    definitionKey: '定义Key',
    processDefinitionId: '定义ID',
    processDefinitionName: '定义名称',
    activityId: '当前环节',
    suspended: '是否挂起',
    suspended0: '否',
    suspended1: '是'
  },
  schedule: {
    beanName: 'bean名称',
    beanNameTips: ',spring bean名称, 如: testTask',
    pauseBatch: '暂停',
    resumeBatch: '恢复',
    runBatch: '执行',
    log: '日志列表',
    params: '参数',
    cronExpression: 'cron表达式',
    cronExpressionTips: '如: 0 0 12 * * ?',
    remark: '备注',
    status: '状态',
    status0: '暂停',
    status1: '正常',
    statusLog0: '失败',
    statusLog1: '成功',
    pause: '暂停',
    resume: '恢复',
    run: '执行',
    jobId: '任务ID',
    times: '耗时(单位: 毫秒)',
    createDate: '执行时间'
  },
  mail: {
    name: '名称',
    config: '邮件配置',
    subject: '主题',
    createDate: '创建时间',
    send: '发送邮件',
    content: '内容',
    smtp: 'SMTP',
    port: '端口号',
    username: '邮箱账号',
    password: '邮箱密码',
    mailTo: '收件人',
    mailCc: '抄送',
    params: '模板参数',
    paramsTips: '如：{"code": "123456"}',
    templateId: '模版ID',
    status: '状态',
    status0: '发送失败',
    status1: '发送成功',
    mailFrom: '发送者',
    sendDate: '发送时间'
  },
  sms: {
    mobile: '手机号',
    status: '状态',
    status0: '发送失败',
    status1: '发送成功',
    config: '短信配置',
    send: '发送短信',
    platform: '平台类型',
    platform1: '阿里云',
    platform2: '腾讯云',
    params: '参数',
    paramsTips: '如：{"code": "123456"}',
    params1: '参数1',
    params2: '参数2',
    params3: '参数3',
    params4: '参数4',
    createDate: '发送时间',
    aliyunAccessKeyId: 'Key',
    aliyunAccessKeyIdTips: '阿里云AccessKeyId',
    aliyunAccessKeySecret: 'Secret',
    aliyunAccessKeySecretTips: '阿里云AccessKeySecret',
    aliyunSignName: '短信签名',
    aliyunTemplateCode: '短信模板',
    aliyunTemplateCodeTips: '短信模板CODE',
    qcloudAppId: 'AppId',
    qcloudAppIdTips: '腾讯云AppId',
    qcloudAppKey: 'AppKey',
    qcloudAppKeyTips: '腾讯云AppKey',
    qcloudSignName: '短信签名',
    qcloudTemplateId: '短信模板',
    qcloudTemplateIdTips: '短信模板ID'
  },
  oss: {
    config: '云存储配置',
    upload: '上传文件',
    url: 'URL地址',
    createDate: '创建时间',
    type: '类型',
    type1: '七牛',
    type2: '阿里云',
    type3: '腾讯云',
    type4: 'FastDFS',
    type5: '本地上传',
    qiniuDomain: '域名',
    qiniuDomainTips: '七牛绑定的域名',
    qiniuPrefix: '路径前缀',
    qiniuPrefixTips: '不设置默认为空',
    qiniuAccessKey: 'AccessKey',
    qiniuAccessKeyTips: '七牛AccessKey',
    qiniuSecretKey: 'SecretKey',
    qiniuSecretKeyTips: '七牛SecretKey',
    qiniuBucketName: '空间名',
    qiniuBucketNameTips: '七牛存储空间名',
    aliyunDomain: '域名',
    aliyunDomainTips: '阿里云绑定的域名，如：http://cdn.renren.io',
    aliyunPrefix: '路径前缀',
    aliyunPrefixTips: '不设置默认为空',
    aliyunEndPoint: 'EndPoint',
    aliyunEndPointTips: '阿里云EndPoint',
    aliyunAccessKeyId: 'AccessKeyId',
    aliyunAccessKeyIdTips: '阿里云AccessKeyId',
    aliyunAccessKeySecret: 'AccessKeySecret',
    aliyunAccessKeySecretTips: '阿里云AccessKeySecret',
    aliyunBucketName: 'BucketName',
    aliyunBucketNameTips: '阿里云BucketName',
    qcloudDomain: '域名',
    qcloudDomainTips: '腾讯云绑定的域名',
    qcloudPrefix: '路径前缀',
    qcloudPrefixTips: '不设置默认为空',
    qcloudAppId: 'AppId',
    qcloudAppIdTips: '腾讯云AppId',
    qcloudSecretId: 'SecretId',
    qcloudSecretIdTips: '腾讯云SecretId',
    qcloudSecretKey: 'SecretKey',
    qcloudSecretKeyTips: '腾讯云SecretKey',
    qcloudBucketName: 'BucketName',
    qcloudBucketNameTips: '腾讯云BucketName',
    qcloudRegion: '所属地区',
    qcloudRegionTips: '请选择',
    qcloudRegionBeijing1: '北京一区（华北）',
    qcloudRegionBeijing: '北京',
    qcloudRegionShanghai: '上海（华东）',
    qcloudRegionGuangzhou: '广州（华南）',
    qcloudRegionChengdu: '成都（西南）',
    qcloudRegionChongqing: '重庆',
    qcloudRegionSingapore: '新加坡',
    qcloudRegionHongkong: '香港',
    qcloudRegionToronto: '多伦多',
    qcloudRegionFrankfurt: '法兰克福',
    localDomain: '域名',
    localDomainTips: '绑定的域名，如：http://cdn.renren.io',
    fastdfsDomain: '域名',
    fastdfsDomainTips: '绑定的域名，如：http://cdn.renren.io',
    localPrefix: '路径前缀',
    localPrefixTips: '不设置默认为空',
    localPath: '存储目录',
    localPathTips: '如：D:/upload'
  },
  dept: {
    name: '名称',
    parentName: '上级部门',
    sort: '排序',
    parentNameDefault: '一级部门'
  },
  dict: {
    dictName: '名称',
    dictType: '类型',
    dictValue: '值',
    sort: '排序',
    remark: '备注',
    createDate: '创建时间'
  },
  news: {
    title: '标题',
    pubDate: '发布时间',
    createDate: '创建时间',
    content: '内容'
  },
  logError: {
    module: '模块名称',
    requestUri: '请求URI',
    requestMethod: '请求方式',
    requestParams: '请求参数',
    ip: '操作IP',
    userAgent: '用户代理',
    createDate: '创建时间',
    errorInfo: '异常信息'
  },
  logLogin: {
    creatorName: '用户名',
    status: '状态',
    status0: '失败',
    status1: '成功',
    status2: '账号已锁定',
    operation: '操作类型',
    operation0: '登录',
    operation1: '退出',
    ip: '操作IP',
    userAgent: 'User-Agent',
    createDate: '创建时间'
  },
  logOperation: {
    module: '模块名称',
    status: '状态',
    status0: '失败',
    status1: '成功',
    creatorName: '用户名',
    operation: '用户操作',
    requestUri: '请求URI',
    requestMethod: '请求方式',
    requestParams: '请求参数',
    requestTime: '请求时长',
    ip: '操作IP',
    userAgent: 'User-Agent',
    createDate: '创建时间'
  },
  menu: {
    name: '名称',
    icon: '图标',
    type: '类型',
    type0: '菜单',
    type1: '按钮',
    sort: '排序',
    url: '路由',
    helpUrl: '帮助手册地址',
    helpUrlPlaceholder: '不用包含文件集的地址，例如：http://doc.goto56.com/project-3/doc-225，只填 doc-225',
    permissions: '授权标识',
    permissionsTips: '如: sys:menu:save',
    parentName: '上级菜单',
    parentNameDefault: '一级菜单',
    resource: '授权资源',
    resourceUrl: '资源URL',
    resourceMethod: '请求方式',
    resourceAddItem: '添加一项'
  },
  params: {
    paramCode: '编码',
    paramValue: '值',
    remark: '备注'
  },
  role: {
    name: '名称',
    remark: '备注',
    createDate: '创建时间',
    menuList: '菜单授权',
    deptList: '数据授权'
  },
  user: {
    username: '用户名',
    deptName: '所属部门',
    companyName: '所属公司',
    email: '邮箱',
    mobile: '手机号',
    status: '状态',
    status0: '停用',
    status1: '正常',
    createDate: '创建时间',
    password: '密码',
    comfirmPassword: '确认密码',
    realName: '真实姓名',
    gender: '性别',
    gender0: '男',
    gender1: '女',
    gender2: '保密',
    roleIdList: '角色配置',
    validate: {
      comfirmPassword: '确认密码与密码输入不一致'
    }
  },
  sysCompany: {
    type: '类型',
    code: '编码',
    name: '名称',
    country: '国家',
    province: '省/州',
    city: '市',
    district: '区',
    street: '地址',
    postcode: '邮编',
    contact: '联系人',
    phone: '电话',
    memo: '备注',
    parentId: '所属公司',
    selectType: '请选择公司类型'
  },
  bdRegion: {
    level: '级别',
    code: '代码',
    name: '名称',
    enName: '英文名称',
    postcode: '邮编',
    path: '级联路径',
    parentCode: '父级代码'
  },
  bdTrackingNo: { /* 运单号库 BdTrackingNo */
    id: 'ID',
    productOrChannel: '物流产品/渠道',
    productOrChannelId: '物流产品/渠道',
    waybillNo: '运单号',
    usedMode: '领用方式',
    user: '领用人',
    usedDate: '领用时间',
    customerId: '客户编号',
    customerName: '客户简称'
  },
  bdFeeType: { /* 费用项 BdFeeType */
    id: 'ID',
    feeCategory: '费用项类别',
    feeName: '费用项名称',
    internationalTransportMode: '干线运输方式',
    divideMethod: '分摊方式'
  },
  bdLogisticsProductOuterCode: { /* 物流产品外部编码 BdLogisticsProductOuterCode */
    id: 'ID',
    outerCode: '外部编码',
    outerName: '外部编码名称',
    logisticsProductId: '物流产品',
    source: '来源',
    memo: '备注'
  },
  baCurrencyRate: { /* 汇率 BaCurrencyRate */
    id: 'ID',
    localCurrency: '本位币种',
    exchangeCurrency: '兑换币种',
    exchangeRate: '汇率',
    effectiveDate: '生效时间'
  },
  bdAirport: { /* 机场 BdAirport */
    id: 'ID',
    iataCode: 'IATA代码',
    icaoCode: 'ICAO代码',
    country: '国家/地区',
    city: '城市',
    name: '中文名',
    englishName: '英文名'
  },
  sysSerialNumber: {
    type: '类型',
    productOrChannel: '物流产品/渠道',
    productOrChannelId: '物流产品/渠道',
    serialNumber: '起始序号',
    version: '版本号',
    status: '状态'
  },
  bdAirlineCompany: { /* 航空公司 BdAirlineCompany */
    id: 'ID',
    iataCode: 'IATA代号',
    icaoCode: 'ICAO代号',
    numericCode: '数字代号',
    name: '中文名',
    englishName: '英文名'
  },
  bdSeaport: { /* 港口 BdSeaport */
    id: 'ID',
    code: '港口代号',
    country: '国家或地区',
    city: '城市',
    name: '中文名',
    englishName: '英文名'
  },
  bdShippingCompany: { /* 船公司 BdShippingCompany */
    id: 'ID',
    englishCode: '英文简称',
    mainCode: '第一代码',
    secondCode: '第二代码',
    countryCode: '国家',
    name: '中文名',
    englishName: '英文名'
  },
  bdCurrency: { /* 币种 BdCurrency */
    id: 'ID',
    code: '币种代号',
    name: '币种名称',
    customsCode: '海关币制代号'
  },
  wsVoyageNumber: { /* 海运航次 WsVoyageNumber */
    id: 'ID',
    voyageNo: '航次',
    shippingCompany: '船公司',
    shippingRoutes: '海运航线',
    departurePort: '起运港',
    arrivalPort: '目的港',
    customsClearanceDate: '截关时间',
    sailingDate: '开船时间',
    memo: '备注'
  },
  baRecharge: { /* 充值单 BaRecharge */
    id: '充值单号',
    settlementObjectType: '结算对象类型',
    settlementObjectId: '结算对象编码',
    settlementObjectName: '结算对象名称',
    payerContacts: '付款联系人',
    payerPhone: '付款人电话',
    currency: '币种',
    sum: '金额',
    serialNumber: '支付流水号',
    bankAccountId: '收款账户',
    bankName: '开户行',
    accountNumber: '卡号',
    attachmentUrl: '附件',
    invalidRemark: '作废原因',
    customerRemark: '客户备注',
    receivableRemark: '收款备注',
    bookkeepingTime: '入账时间',
    bookkeeper: '入账人',
    rechargeDate: '充值时间'
  },
  baReceivableFee: { /* 应收费用明细 BaReceivableFee */
    id: '费用明细单号',
    receivableBizOrderId: '费用主单号',
    createType: '费用产生方式',
    settlementObjectType: '结算对象类型',
    settlementObjectId: '结算对象编码',
    settlementObjectName: '结算对象名称',
    businessType: '业务类型',
    orderType: '单据类型',
    businessId: '业务单号',
    waybillNo: '运单号',
    waybillNoOrBusinessId: '运单号/业务单号',
    deliveryNo: '派送单号',
    customerVoucherNo: '客户单号',
    customerVoucherNoOrSerialNumber: '客户单号/流水号',
    receivableBillId: '对账单号',
    consigneeCountry: '收件人国家',
    consigneeProvince: '收件人省/州',
    consigneeCity: '收件人城市',
    consigneePostcode: '收件人邮编',
    zone: '分区',
    logisticsProductCode: '物流产品',
    additionFeeType: '偏远超限',
    billsType: '计费单类型',
    balanceWeightUnit: '结算重单位',
    balanceWeight: '结算重',
    totalWeight: '总重量(KG)',
    feeTypeId: '费用项',
    currency: '币种',
    sum: '金额',
    balanceWeightK: '结算重(KG)',
    qty: '数量',
    formula: '计算公式',
    optDate: '业务发生时间',
    billingDate: '计费时间',
    billingStatus: '计费状态',
    auditor: '审核人',
    no_approved: '不同意',
    approved: '确认',
    auditDate: '审核时间',
    memo: '备注',
    feeType: '费用类型',
    income: '收入',
    expend: '支出',
    businessDeductions: '业务扣费',
    accountTopIp: '账户充值'
  },
  /* 应收对账单 BaReceivableBill */
  baReceivableBill: {
    id: '对账单号',
    settlementObjectType: '结算对象类型',
    settlementObjectId: '结算对象编码',
    settlementObjectName: '结算对象名称',
    totalSum: '总金额',
    billDeadline: '帐期结束日期',
    accountingPeriod: '会计期间',
    remark: '备注',
    auditor: '审核人',
    auditDate: '审核时间',
    closed: '结账人',
    closeDate: '结账时间',
    downloadFile: '下载账单'
  },
  /* 应收实收费用单 BaReceivableBizOrder */
  baReceivableBizOrder: {
    currency: '币种',
    sum: '金额',
    referencePrice: '参考公斤价',
    balanceWeight: '结算重(KG)',
    qty: '数量'
  },
  /* 作业成本费用明细表 BaOperatingCostFee */
  baOperatingCostFee: {
    feeTypeId: '费用项',
    currency: '币种',
    sum: '金额',
    remark: '备注'
  },
  /* 应收计费子单 BaSubBillsReceivable */
  baSubBillsReceivable: {
    packageNo: '箱号/FBA唛头',
    deliveryNo: '派送单号',
    balanceWeight: '结算重',
    weight: '重量',
    length: '长',
    width: '宽',
    height: '高',
    volD: '体积(CM³)',
    girthD: '周长',
    volumeWeightD: '材积重',
    volumeCarryWeightD: '材积进位重',
    weightCarryWeightD: '实重进位重',
    balanceWeightUnit: '结算重单位',
    receivableRemark: '备注'
  },
  /* 工单沟通记录明细-baseEntity CsmWorkOrderCommunication */
  csmWorkOrderCommunication: {
    id: 'ID',
    workOrderId: '工单编号',
    workOrderNo: '工单号',
    communicationType: '沟通类别',
    communicationContent: '沟通内容',
    ipAddress: 'IP地址',
    creatorName: '创建人名称',
    createDate: '时间'
  },
  /* 工单关联附件-baseEntity CsmWorkOrderAttachment */
  csmWorkOrderAttachment: {
    id: 'ID',
    workOrderId: '工单编号',
    workOrderNo: '工单号',
    relationObject: '关联对象',
    relationId: '关联编号',
    attachmentUrl: '附件地址'
  },
  /* 客服工单 CsmWorkOrder */
  csmWorkOrder: {
    id: 'ID',
    workOrderNo: '工单号',
    workOrderTypeId: '工单类型',
    workOrderTypeParentId: '工单类别',
    waybillNo: '运单编号',
    customerVoucherNo: '客户单号',
    problemStatement: '问题描述',
    contactPerson: '联系人',
    phone: '联系电话',
    email: '联系邮箱',
    urgentLevel: '紧急程度',
    assignee: '分配人',
    assignTime: '分配时间',
    acceptServiceId: '受理客服',
    startProcessingTime: '开始处理时间',
    firstReplyTime: '第一次答复时间',
    lastReplyTime: '最新答复时间',
    lastConsultationTime: '最新咨询时间',
    completionTime: '处理完成时间',
    completionType: '完成类型',
    closeTime: '关闭时间',
    closeType: '关闭类型',
    newNote: '新的答复',
    newConsultation: '新的咨询',
    creatorType: '创建人类型',
    creatorName: '创建人名称',
    status: '工单状态',
    creator: '提交人',
    createDate: '提交时间',
    createDuration: '创建时长',
    companyCustomerServiceStaffId: '专属客服',
    assignDuration: '分配时长',
    lastReplyDuration: '最新答复时长',
    processDuration: '处理时长',
    export: '导出报表'
  },
  /* 工单日志 CsmWorkOrderLog */
  csmWorkOrderLog: {
    id: 'ID',
    workOrderId: '工单编号',
    logCode: '日志动作',
    logDescription: '日志描述',
    creator: '操作人',
    createDate: '操作时间'
  },
  /* 干线主单 wsComMawb */
  wsComMawb: {
    departureTime: '开航时间',
    mawbNo: '提单号'
  },
  /* 干线分单 wsComHawb */
  wsComHawb: {
    mawbNo: '提单号'
  },
  label: {
    attachment: '查看附件'
  },
  psCalculateExpense: {
    customer: '客户',
    alliance: '加盟商',
    provide: '供应商',
    quotationRemark: '走货说明',
    specialItem: '可运输物品类型',
    logisticsChannel: '物流渠道',
    logisticsProduct: '物流产品',
    billingItemType: '计费物流品类',
    aging: '时效',
    country: '国家',
    postcode: '邮编',
    productType: '货品类型',
    length: '长(cm)',
    width: '宽(cm)',
    height: '高(cm)',
    weight: '实重(kg)',
    calculate: '计算',
    feeType: '费用项',
    sum: '报价金额',
    currency: '报价币种',
    address: '详细地址',
    city: '城市',
    province: '省/州',
    district: '区',
    street: '街道',
    name: '收件人名/公司',
    phone: '电话',
    weightUnit: '报价重量单位',
    balanceWeight: '报价结算重',
    sysSum: '本位币金额',
    sysCurrency: '本位币种',
    sysBalanceWeight: '系统结算重',
    sysWeightUnit: '系统重量单位',
    declareCurrency: '申报币种',
    declareSum: '申报金额',
    formula: '计算公式'
  },
  diyimport: {
    fullRequired: '生成必填列'
  }
}
