<template>
  <el-dialog :visible.sync="visible" title="提示" :close-on-click-modal="false" width='260px' top='30vh' :close-on-press-escape="false" :show-close='false'  :lock-scroll="true" class="location_model" >
      <el-row style='text-align: center'>
        <span class='el-icon-question'>{{$t('express.returnList')}}</span>
      </el-row>
    <template slot="footer">
      <div style='justify-content: right;display: flex;'>
        <el-button @click='closeHandle'>{{ $t('system.no') }}</el-button>
        <el-button type='primary' @click='handleSubmit()'>{{ $t('system.yes') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      visible: false
    }
  },
  computed: {
  },
  methods: {
    init () {
      this.visible = true
    },
    handleSubmit: debounce(function () {
      this.$router.push({ name: 'express-orderList' })
      this.closeHandle()
    }, 1000, { 'leading': true, 'trailing': false }),
    closeHandle () {
      this.visible = false
      this.$emit('closeReturnListDialog')
    }
  }
}
</script>

<style lang='scss' scoped>
  /deep/ .el-dialog{
    border-radius: 5px;
  }
</style>
