<template>
  <div class="aui-card--fill">
    <div class="mod-co__inorder flex_wrap" v-show="!panelShow" ref="listPanel">
      <el-card class="flex_tab no_shadow" type="border-card" >
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" :max-height="tableHeight">
            <!-- 动态显示表格 -->
            <el-table-column v-for="(item, index) in tableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
              <template slot-scope="scope">
                <div>
                  <div v-if="item.prop === 'createDate'">
                    <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                      <span>{{formatterFn(scope,item.prop)}}</span>
                    </el-tooltip>
                  </div>
                  <div v-else-if="item.prop === 'title'">
                    <el-link v-if="scope.row.status === 1" type="info" @click="viewLocalHandle(scope.row)">{{ scope.row.title }}</el-link>
                    <el-link v-if="scope.row.status === 0" type="primary" @click="viewLocalHandle(scope.row)">{{ scope.row.title }}</el-link>
                  </div>
                  <div v-else-if="item.prop === 'status'">
                    <el-tag v-if="scope.row.status === 1" type="info"> 已读 </el-tag>
                    <el-tag v-else type="danger"> 未读</el-tag>
                  </div>
                  <div v-else-if="item.prop === 'publishDate'">
                    <el-tooltip :content="scope.row.publishDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                      <span>{{formatterFn(scope,item.prop)}}</span>
                    </el-tooltip>
                  </div>
                  <div v-else>
                    {{formatterFn(scope,item.prop)}}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="150">
              <template slot="header" slot-scope="scope">
                <span>{{$t('handle')}}</span>
                <el-tooltip :content="$t('tableConfigTooltip')" placement="top">
                  <el-link style="margin-left:10px; font-size:16px" class="el-icon-setting" @click="settingCol(tableColumns)"></el-link>
                </el-tooltip>
              </template>
              <template slot-scope="scope">
                <el-link :underline="false"  @click="viewLocalHandle(scope.row)">{{ $t('view') }}</el-link>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle" @current-change="pageCurrentChangeHandle">
        </el-pagination>
      </el-card>
    </div>
    <!-- 详情页面 -->
    <viewDetail v-if="viewVisible" ref="viewDetail" @backView="backView"></viewDetail>
  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import listPage from '@/mixins/listPage'
import { formatterType, gtmToLtm, timestampFormat } from '@/filters/filters'
import dictTypeMixins from '@/mixins/dictTypeMixins'
// table 自定义显示
import ViewDetail from './notice-receiver-view'
export default {
  mixins: [listPage, mixinViewModule, dictTypeMixins],
  data () {
    return {
      tableColumns: [
        { type: '', width: '400', prop: 'title', label: this.$t('msNotice.title'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'type', label: this.$t('msNotice.type'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'status', label: this.$t('status'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '110', prop: 'publishDate', label: this.$t('msNotice.publishDate'), align: 'center', isShow: true, disabled: false }
        // { type: '', width: '80', prop: 'creatorName', label: this.$t('msNotice.creatorName'), align: 'center', isShow: true, disabled: false }
      ],
      mixinViewModuleOptions: {
        getDataListURL: '/message/noticereceiver/page',
        getDataListIsPage: true
      },
      dataForm: {
        id: ''
      },
      tableName: 'notice',
      noticeTypeList: []
    }
  },
  created () {
    // 获取相关字典
    this.getDict()
  },
  methods: {
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'createDate':
          value = timestampFormat(gtmToLtm(scope.row.createDate), 'YYYY-MM-DD')
          break
        case 'type':
          value = formatterType(scope.row.type, this.noticeTypeList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    // 获取相关字典
    async getDict () {
      this.publishStatusList = await this.getDictTypeList('publish-status') // 发布状态
      this.noticeTypeList = await this.getDictTypeList('notice-type') // 通知类型
      this.receiverTypeList = await this.getDictTypeList('notice-receiver-type') // 通知接收者类型
    },
    // 查看
    viewLocalHandle (row) {
      row.status = 1
      this.viewVisible = true
      this.$nextTick(() => {
        this.$refs.viewDetail.dataForm.id = row.id
        this.$refs.viewDetail.noticeTypeList = this.noticeTypeList
        this.$refs.viewDetail.publishStatusList = this.publishStatusList
        this.$refs.viewDetail.receiverTypeList = this.receiverTypeList
        this.$refs.viewDetail.init()
      })
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat
  },
  computed: {
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    panelShow () {
      let ret = false
      if (this.viewVisible) {
        ret = true
      }
      return ret
    }
  },
  components: {
    ViewDetail
  }
}
</script>
