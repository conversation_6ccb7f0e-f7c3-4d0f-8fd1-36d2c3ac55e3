import Cookies from 'js-cookie'
import qs from 'qs'
import request from '@/utils/request'
import axios from 'axios'
export default {
  data () {
    /* eslint-disable */
    return {
      // 设置属性
      mixinViewModuleOptions: {
        activatedIsNeed: true,    // 此页面是否在激活（进入）时，调用查询数据列表接口？
        getDataListURL: '',       // 数据列表接口，API地址
        getDataListURLOfRequestType: 'get',       // 数据列表接口，API地址 请求方式 默认get
        getDataListIsPage: false, // 数据列表接口，是否需要分页？
        deleteURL: '',            // 删除接口，API地址
        deleteIsBatch: false,     // 删除接口，是否需要批量？
        deleteIsBatchKey: 'id',   // 删除接口，批量状态下由那个key进行标记操作？比如：pid，uid...
        exportURL: '',             // 导出接口，API地址
        exportRequestMethod: 'get',      // 导出接口，API地址
        auditURL: '',            // 审核接口，API地址
        auditIsBatch: false,     // 审核接口，是否需要批量？
        auditIsBatchKey: 'id',   // 审核接口，批量状态下由那个key进行标记操作？比如：pid，uid...
        importExcelVisible: false
      },
      // 默认属性
      dataForm: {},               // 查询条件
      dataList: [],               // 数据列表
      order: '',                  // 排序，asc／desc
      orderField: '',             // 排序，字段
      page: 1,                    // 当前页码
      limit: 10,                  // 每页数
      total: 0,                   // 总条数
      dataListLoading: false,     // 数据列表，loading状态
      expButtonLoading: false,
      dataListSelections: [],     // 数据列表，多选项
      addOrUpdateVisible: false,   // 新增／更新，弹窗visible状态
      updateReturnstrategyVisible: false,   // 修改退货策略，弹窗visible状态
      viewVisible:false,           // 查看 visible状态
      logVisible:false,            // 查看日志 logVisible状态
      showAdvancedSearch: false,   // 点击高级搜索显示
      showMoreIcon: 'el-icon-caret-bottom', // 高级搜索图标初始下拉样式
      isListPage: true,
      subDataForm: {},               // 子表查询条件
      subDataList: [],               // 子表数据列表
      downloadData: {},              // 下载参数
      subOrder: '',                  // 子表排序，asc／desc
      subOrderField: '',             // 子表排序，字段
      subPage: 1,                    // 子表当前页码
      subLimit: 10,                  // 子表每页数
      subTotal: 0,                   // 子表总条数
      auditButtonLoading: false,     // 审核弃审，loading状态
      subDataListLoading: false,     // 子表数据列表，loading状态
      downloadFileButtonLoading: false,     // 下载文件，loading状态
      subDataListSelections: [],     // 子表数据列表，多选项
      subAddOrUpdateVisible: false,   // 子表新增／更新，弹窗visible状态
      subUpdateReturnstrategyVisible: false,   // 子表修改退货策略，弹窗visible状态
      subViewVisible:false,           // 子表查看 visible状态
      subLogVisible:false            // 子表查看日志 logVisible状态
    }
    /* eslint-enable */
  },
  computed: {
    multipleNoInputAutoSize () {
      this.$nextTick(() => {
        this.$refs.multipleNoInput.resizeTextarea()
      })
      return { minRows: 4, maxRows: 6 }
    },
    showMoreIconFn () { // 高级搜索图标样式
      return this.showAdvancedSearch ? 'el-icon-caret-top' : 'el-icon-caret-bottom'
    }
  },
  // activated () {
  //   if (this.mixinViewModuleOptions.activatedIsNeed) {
  //     this.getDataList()
  //   }
  // },
  mounted () {
    if (this.mixinViewModuleOptions.activatedIsNeed) {
      this.getDataList()
    }
  },
  methods: {
    // 通过参数分页查询
    queryPageByParam () {
      this.page = 1
      this.getDataList()
    },
    queryPageByParamCache () {
      this.page = 1
      // 查询条件预缓存到后台
      let params = {
        order: this.order,
        orderField: this.orderField,
        page: this.mixinViewModuleOptions.getDataListIsPage ? this.page : null,
        limit: this.mixinViewModuleOptions.getDataListIsPage ? this.limit : null,
        ...this.dataForm
      }
      this.$http.post('/common/cacheQueryParams', params).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        let tempDataForm = { ...this.dataForm }
        this.dataForm = { 'queryId': res.data }
        // 查询
        this.getDataList()
        this.dataForm = { ...tempDataForm }
      }).catch(() => {})
    },
    cacheQueryParams (params) {
      return new Promise((resolve, reject) => {
        if (!params) {
          params = {
            order: this.order,
            orderField: this.orderField,
            page: this.mixinViewModuleOptions.getDataListIsPage ? this.page : null,
            limit: this.mixinViewModuleOptions.getDataListIsPage ? this.limit : null,
            ...this.dataForm
          }
        }
        this.$http.post('/common/cacheQueryParams', params)
          .then(({ data: res }) => {
            if (res.code !== 0) {
              this.$message.error(res.msg)
              reject(new Error(res.msg))
            } else {
              resolve(res.data)
            }
          })
          .catch((error) => {
            reject(error)
          })
      })
    },
    // 获取数据列表
    getDataList () {
      if (!this.mixinViewModuleOptions.getDataListURL) {
        return
      }
      this.viewVisible = false
      this.addOrUpdateVisible = false
      this.dataListLoading = true
      let dataListURLOfRequestType = this.mixinViewModuleOptions.getDataListURLOfRequestType.toLowerCase()
      let params = {}
      if (dataListURLOfRequestType === 'get') {
        params = {
          params: {
            order: this.order,
            orderField: this.orderField,
            page: this.mixinViewModuleOptions.getDataListIsPage ? this.page : null,
            limit: this.mixinViewModuleOptions.getDataListIsPage ? this.limit : null,
            ...this.dataForm
          }
        }
      } else {
        params = {
          order: this.order,
          orderField: this.orderField,
          page: this.mixinViewModuleOptions.getDataListIsPage ? this.page : null,
          limit: this.mixinViewModuleOptions.getDataListIsPage ? this.limit : null,
          ...this.dataForm
        }
      }
      this.$http[dataListURLOfRequestType](
        this.mixinViewModuleOptions.getDataListURL,
        params
      ).then(({ data: res }) => {
        this.dataListSelections = []
        this.dataListLoading = false
        if (res.code !== 0) {
          this.dataList = []
          this.total = 0
          return this.$message.error(res.msg)
        }
        this.dataList = this.mixinViewModuleOptions.getDataListIsPage ? res.data.list : res.data
        this.total = this.mixinViewModuleOptions.getDataListIsPage ? res.data.total : 0
        if (!this.mixinViewModuleOptions.activatedIsNeed) {
          this.mixinViewModuleOptions.activatedIsNeed = false
        }
        this.$getDataListCallback && this.$getDataListCallback()
      }).catch(() => {
        this.dataListLoading = false
      })
    },
    $getDataListCallback () {
      // 查询列表的回调方法
      return false
    },
    // 多选
    dataListSelectionChangeHandle (val) {
      this.dataListSelections = val
    },
    // 排序
    dataListSortChangeHandle (data) {
      if (!data.order || !data.prop) {
        this.order = ''
        this.orderField = ''
        return false
      }
      this.order = data.order.replace(/ending$/, '')
      this.orderField = data.prop.replace(/([A-Z])/g, '_$1').toLowerCase()
      this.getDataList()
    },
    // 分页, 每页条数
    pageSizeChangeHandle (val) {
      // if (this.$refs.multipleNoInput || this.$refs.threeNoInput) {
      //   this.page = 1
      //   this.limit = val
      //   this.cacheQueryParams().then((queryId) => {
      //     let tempDataForm = { ...this.dataForm }
      //     this.dataForm = { 'queryId': queryId }
      //     // 查询
      //     this.getDataList()
      //     this.dataForm = { ...tempDataForm }
      //   })
      // } else {
      //   this.page = 1
      //   this.limit = val
      //   this.getDataList()
      // }
      this.page = 1
      this.limit = val
      this.getDataList()
    },
    // 分页, 当前页
    pageCurrentChangeHandle (val) {
      // if (this.$refs.multipleNoInput || this.$refs.threeNoInput) {
      //   this.page = val
      //   this.cacheQueryParams().then((queryId) => {
      //     let tempDataForm = { ...this.dataForm }
      //     this.dataForm = { 'queryId': queryId }
      //     // 查询
      //     this.getDataList()
      //     this.dataForm = { ...tempDataForm }
      //   })
      // } else {
      //   this.page = val
      //   this.getDataList()
      // }
      this.page = val
      this.getDataList()
    },
    // 重置
    _resetForm (formName) {
      this.$nextTick(() => {
        this.$refs[formName].resetFields()
      })
    },
    commonMultipleNoInputSetValueEmit (noList, selectedAttribute, otherAttributes) {
      if (noList && noList.length > 0) {
        this.$set(this.dataForm, selectedAttribute, noList)
        otherAttributes.forEach(item => this.$set(this.dataForm, item, ''))
      } else {
        this.$set(this.dataForm, selectedAttribute, '')
        otherAttributes.forEach(item => this.$set(this.dataForm, item, ''))
      }
      return true
    },
    // 通用批量单号查询重置值
    commonMultipleNoInputClearValueEmit (allAttributes) {
      allAttributes.forEach(item => this.$set(this.dataForm, item, ''))
    },
    // 新增 / 修改
    addOrUpdateHandle (id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.dataForm.id = id
        this.$refs.addOrUpdate.init()
      })
    },
    // 根据类型获取数据字典集合
    getDictTypeList (type) {
      return new Promise((resolve, reject) => {
        this.$http.get('/sys/dict/dictList', { params: { dictType: type } }).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          if (res.data.length) {
            res.data.forEach(item => {
              item.dictValue = isNaN(Number(item.dictValue)) ? item.dictValue : Number(item.dictValue)
            })
          }
          // this.dictTypeList = res.data
          // callback && callback.call(this)
          resolve(res.data)
        }).catch(() => {})
      })
    },
    // 查看
    viewHandle (id) {
      this.viewVisible = true
      this.$nextTick(() => {
        this.$refs.viewDetail.dataForm.id = id
        this.$refs.viewDetail.init()
      })
    },
    // 查看操作日志
    showLog (id) {
      this.logVisible = true
      this.$nextTick(() => {
        this.$refs.logDetail.init(id)
      })
    },
    // 删除
    deleteHandle (id, isRecoilSend) {
      if (this.mixinViewModuleOptions.deleteIsBatch && !id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: this.$t('prompt.deleteBatch'),
          type: 'warning',
          duration: 1000
        })
      }
      this.$confirm(this.$t('prompt.info', { 'handle': this.$t(isRecoilSend ? 'recoilSend' : 'delete') }), this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        this.$http.delete(
          `${this.mixinViewModuleOptions.deleteURL}${this.mixinViewModuleOptions.deleteIsBatch ? '' : '/' + id}`,
          this.mixinViewModuleOptions.deleteIsBatch ? {
            'data': id ? [id] : this.dataListSelections.map(item => item[this.mixinViewModuleOptions.deleteIsBatchKey])
          } : {}
        ).then(({ data: res }) => {
          if (res.code !== 0) {
            this.queryPageByParam()
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 1000,
            onClose: () => {
              this.queryPageByParam()
            }
          })
        }).catch(() => {})
      }).catch(() => {})
    },
    // 导出
    exportHandle () {
      var params = qs.stringify({
        'token': Cookies.get('cs_token'),
        ...this.dataForm
      })
      window.location.href = `${this.$baseUrl}${this.mixinViewModuleOptions.exportURL}?${params}`
    },
    exportByMsgHandle (url) {
      this.expButtonLoading = true
      let method = this.mixinViewModuleOptions.exportRequestMethod
      let params = null
      if (method.toLowerCase() === 'post') {
        params = qs.stringify({
          'token': Cookies.get('cs_token')
        })
      } else {
        params = qs.stringify({
          'token': Cookies.get('cs_token'),
          ...this.dataForm
        })
      }
      let requestUrl = url || `${this.$baseUrl}${this.mixinViewModuleOptions.exportURL}?${params}`
      let httpUtil = axios.create({
        baseURL: request.localtionUrl,
        timeout: 1000 * 180,
        withCredentials: true
      })
      let fileName = ''
      /**
       * 响应拦截
       */
      httpUtil.interceptors.response.use(response => {
        if (!response.headers['content-type'].includes('application/vnd.ms-excel')) {
          let enc = new TextDecoder('utf-8')
          let resJson = JSON.parse(enc.decode(new Uint8Array(response.data))) // 转化成json对象
          return this.$message.error(resJson.msg)
        } else {
          fileName = decodeURIComponent(response.headers['filename'])
          return response
        }
      }, error => {
        const { response } = error
        const { error: printErrorLog } = console
        printErrorLog('error Object => ', error)
        printErrorLog('error response => ', response)
        if (response) {
          if (response.status === 414) {
            // 处理 "414 Request-URI Too Large" 错误
            this.$message({
              dangerouslyUseHTMLString: true,
              message: '请求的URL过长，服务器无法处理!' + '(' + response.statusText + ')',
              type: 'error',
              duration: 4000
            })
          } else {
            this.$message({
              dangerouslyUseHTMLString: true,
              message: response.statusText || '发生错误',
              type: 'error',
              duration: 4000
            })
          }
        } else if (!window.navigator.onLine) {
          this.$message({
            dangerouslyUseHTMLString: true,
            message: '无法访问网络，请检查网络设置！',
            type: 'error',
            duration: 4000
          })
        }
        return Promise.reject(error)
      })
      // 忽略大小写判断method是否post
      let data = ''
      if (method.toLowerCase() === 'post') {
        data = this.dataForm
      }
      httpUtil({
        method: method,
        url: requestUrl,
        data: data,
        responseType: 'arraybuffer'
      })
        .then(({ data: res }) => {
          if (undefined === res || res === null) {
            this.expButtonLoading = false
            return
          }
          const link = document.createElement('a')
          let blob = new Blob([res], { type: 'application/vnd.ms-excel' })
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          link.download = fileName // 下载的文件名
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          this.expButtonLoading = false
        })
        .catch(() => {
        })
    },
    // 新增按钮事件
    addOrUpdateAction (id) {
      this.addOrUpdateVisible = false
      this.$nextTick(() => {
        this.$refs.addOrUpdate.dataForm.id = id
        this.$refs.addOrUpdate.init()
      })
    },
    // 关闭新增窗口
    cancelAddOrUpdate () {
      this.addOrUpdateVisible = false
    },
    // 返回预览
    backView () {
      this.viewVisible = false
    },
    // 日志返回预览
    logbackView () {
      this.logVisible = false
    },
    // 关闭修改退货策略窗口
    cancelUpdateReturnstrategy () {
      this.updateReturnstrategyVisible = false
    },
    // 审核
    auditHandle (id) {
      if (this.mixinViewModuleOptions.auditIsBatch && !id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: this.$t('prompt.auditBatch'),
          type: 'warning',
          duration: 1000
        })
      }
      this.$confirm(this.$t('prompt.info', { 'handle': this.$t('audit') }), this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        this.$http.post(
          `${this.mixinViewModuleOptions.auditURL}${this.mixinViewModuleOptions.auditIsBatch ? '' : '/' + id}`,
          id ? [id] : this.dataListSelections.map(item => item.id)
        ).then(({ data: res }) => {
          if (res.code !== 0) {
            this.queryPageByParam()
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 1000,
            onClose: () => {
              this.queryPageByParam()
            }
          })
        }).catch(() => {})
      }).catch(() => {})
    },
    // 导入
    importExcelHandle () {
      this.mixinViewModuleOptions.importExcelVisible = true
      this.$nextTick(() => {
        this.$refs.importExcel.init()
      })
    },
    // 导入返回
    backImportExcel () {
      this.mixinViewModuleOptions.importExcelVisible = false
    },
    // 通过参数分页查询子表
    querySubPageByParam () {
      this.subPage = 1
      this.getSubDataList()
    },
    // 获取子表数据列表
    getSubDataList () {
      this.subViewVisible = false
      this.subaddOrUpdateVisible = false
      this.subdataListLoading = true
      this.$http.get(
        this.mixinViewModuleOptions.getSubDataListURL,
        {
          params: {
            order: this.subOrder,
            orderField: this.subOrderField,
            page: this.mixinViewModuleOptions.getSubDataListIsPage ? this.subPage : null,
            limit: this.mixinViewModuleOptions.getSubDataListIsPage ? this.subLimit : null,
            ...this.subDataForm
          }
        }
      ).then(({ data: res }) => {
        this.subDataListLoading = false
        if (res.code !== 0) {
          this.subDataList = []
          this.subTotal = 0
          return this.$message.error(res.msg)
        }
        // @expand-change的数据监听需在赋值前就绑定，否则vue无法及时监听到feeItemList变化，导致第二次展开才会渲染数据
        if (this.mixinViewModuleOptions.initFeeItemListIsNeed) {
          this.initFeeItemList(res.data.list)
        }
        this.subDataList = this.mixinViewModuleOptions.getSubDataListIsPage ? res.data.list : res.data
        this.subTotal = this.mixinViewModuleOptions.getSubDataListIsPage ? res.data.total : 0
        if (!this.mixinViewModuleOptions.activatedIsNeedBySub) {
          this.mixinViewModuleOptions.activatedIsNeedBySub = false
        }
        this.$getSubDataListCallback && this.$getSubDataListCallback()
      }).catch(() => {
        this.subDataListLoading = false
      })
    },
    initFeeItemList (dataList) {
      dataList.forEach(item => {
        item.feeItemList = []
      })
    },
    $getSubDataListCallback () {
      // 查询列表的回调方法
      return false
    },
    // 多选
    subDataListSelectionChangeHandle (val) {
      this.subDataListSelections = val
    },
    // 排序
    subDataListSortChangeHandle (data) {
      if (!data.order || !data.prop) {
        this.order = ''
        this.orderField = ''
        return false
      }
      this.subOrder = data.order.replace(/ending$/, '')
      this.subOrderField = data.prop.replace(/([A-Z])/g, '_$1').toLowerCase()
      this.getSubDataList()
    },
    // 分页, 每页条数
    subPageSizeChangeHandle (val) {
      this.subPage = 1
      this.subLimit = val
      this.getSubDataList()
    },
    // 分页, 当前页
    subPageCurrentChangeHandle (val) {
      this.subPage = val
      this.getSubDataList()
    },
    downloadFileByMsgHandle (url) {
      this.downloadFileButtonLoading = true
      var params = qs.stringify({
        'token': Cookies.get('cs_token'),
        ...this.downloadData
      })
      let requestUrl = `${this.$baseUrl}${url}?${params}`
      // let requestUrl = url + '?' + params
      let httpUtil = axios.create({
        baseURL: request.localtionUrl,
        timeout: 1000 * 180,
        withCredentials: true
      })
      let fileName = ''
      /**
       * 响应拦截
       */
      httpUtil.interceptors.response.use(response => {
        if (!response.headers['content-type'].includes('application/pdf') && !response.headers['content-type'].includes('application/x-zip-compressed')) {
          let enc = new TextDecoder('utf-8')
          let resJson = JSON.parse(enc.decode(new Uint8Array(response.data))) // 转化成json对象
          return this.$message.error(resJson.msg)
        } else {
          fileName = decodeURIComponent(response.headers['filename'])
          return response
        }
      }, error => {
        const { response } = error
        const { error: printErrorLog } = console
        printErrorLog('error Object => ', error)
        printErrorLog('error response => ', response)
        if (response) {
          if (response.status === 414) {
            // 处理 "414 Request-URI Too Large" 错误
            this.$message({
              dangerouslyUseHTMLString: true,
              message: '请求的URL过长，服务器无法处理!' + '(' + response.statusText + ')',
              type: 'error',
              duration: 4000
            })
          } else {
            this.$message({
              dangerouslyUseHTMLString: true,
              message: response.statusText || '发生错误',
              type: 'error',
              duration: 4000
            })
          }
        } else if (!window.navigator.onLine) {
          this.$message({
            dangerouslyUseHTMLString: true,
            message: '无法访问网络，请检查网络设置！',
            type: 'error',
            duration: 4000
          })
        }
        return Promise.reject(error)
      })
      httpUtil({
        method: 'get',
        url: requestUrl,
        data: '',
        responseType: 'arraybuffer'
      })
        .then(({ data: res }) => {
          if (undefined === res || res === null) {
            this.downloadFileButtonLoading = false
            return
          }
          const link = document.createElement('a')
          let blob = new Blob([res], { type: 'application/pdf' })
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          link.download = fileName // 下载的文件名
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          this.downloadFileButtonLoading = false
        })
        .catch(() => {})
    }
  }
}
