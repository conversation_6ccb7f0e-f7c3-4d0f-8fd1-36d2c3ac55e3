<template>
  <div class="aui-card--fill">
    <div class="mod-co__inorder flex_wrap pane_wrap" v-show="!panelShow" ref="listPanel">
      <el-card class="search_box" shadow="never">
        <el-form ref="searchForm" class="form_no_margin" :model="dataForm" @keyup.enter.native="searchHandle()" label-width="100px">
          <el-row :gutter="10" type="flex">
            <el-col :span="5">
              <three-no-input ref="threeNoInput" :autosize="threeNoInputAutoSize()" :orderAreaList='orderAreaList' :deliveryNo.sync="dataForm.deliveryNos"
                              :waybillNo.sync="dataForm.waybillNos" :customerOrderNo.sync="dataForm.customerVoucherNos"
                              :noSize="5000" />
            </el-col>
            <el-col :span="18">
              <el-row :gutter="10">
                <el-col :span="9">
                  <el-form-item :label="$t('system.status')" prop="status">
                    <el-select filterable v-model="dataForm.status"
                               :placeholder="$t('system.status')" clearable>
                      <el-option v-for="(item, index) in billsStatusList" :key="index" :label="item.dictName"
                                 :value="item.dictValue"/>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="15">
                  <el-form-item :label="$t('baReceivableFee.consigneeCountry')" prop="consigneeCountry">
                    <el-select v-model="dataForm.consigneeCountry" filterable placeholder="" clearable>
                      <el-option v-for="item in countryList" :key="item.code"
                                 :label="`${item.name} ${item.code}`" :value="item.code"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col :span="9">
                  <el-form-item :label="$t('baReceivableFee.logisticsProductCode')" prop="logisticsProductCode">
                    <el-select filterable v-model="dataForm.logisticsProductCode"
                               :placeholder="$t('baReceivableFee.logisticsProductCode')" clearable>
                      <el-option v-for="(item, index) in listAllByCurrent" :key="index" :label="item.name"
                                 :value="item.code"/>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="15">
                  <el-form-item :label="$t('baReceivableFee.optDate')" prop="optDate">
                    <el-date-picker :clearable="false"
                                    :picker-options="pickerOptions"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    popper-class="hideDateClean"
                                    v-model="optDateArray"
                                    class="w-percent-100"
                                    type="datetimerange"
                                    :start-placeholder="$t('system.startTime')"
                                    :end-placeholder="$t('system.endTime')"
                                    :default-time="['00:00:00', '23:59:59']">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row v-show="!searchBoxShow" :gutter="10">
                <el-col :span="9">
                  <el-form-item :label="$t('wsComHawb.mawbNo')" prop="mawbNo">
                    <el-input v-model="dataForm.mawbNo" :placeholder="$t('wsComHawb.mawbNo')" clearable />
                  </el-form-item>
                </el-col>
                <el-col :span="15">
                <el-form-item :label="$t('baReceivableFee.additionFeeType')" prop="additionFeeType">
                  <el-select filterable v-model="dataForm.additionFeeType"
                             :placeholder="$t('baReceivableFee.additionFeeType')" clearable>
                    <el-option v-for="(item, index) in additionFeeTypeList" :key="index" :label="item.dictName"
                               :value="item.dictValue"/>
                  </el-select>
                </el-form-item>
              </el-col>
              </el-row>
              <el-row v-show="!searchBoxShow" :gutter="10">
                <el-col :span="9">
                  <el-form-item :label="$t('baReceivableFee.billsType')" prop="billsType">
                    <el-select filterable v-model="dataForm.billsType"
                               :placeholder="$t('baReceivableFee.billsType')" clearable>
                      <el-option v-for="(item, index) in billsTypeList" :key="index" :label="item.dictName"
                                 :value="item.dictValue"/>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="15">
                  <el-form-item :label="$t('wsComMawb.departureTime')" prop="billingDate">
                    <el-date-picker class="w-percent-100"
                                    v-model="departureTimeArr"
                                    :clearable="true"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    popper-class="hideDateClean"
                                    :picker-options="pickerOptions"
                                    type="datetimerange"
                                    :start-placeholder="$t('datePicker.start')"
                                    :end-placeholder="$t('datePicker.end')"
                                    :default-time="['00:00:00', '23:59:59']">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <div class="search_box_btn no_more">
              <el-button type="primary" @click="searchHandle()" icon="el-icon-search">查询</el-button>
              <el-button @click.native="resetForm('searchForm')" icon="el-icon-refresh-right">重置</el-button>
              <el-button type="text" @click="searchBoxShowFn">{{ searchBoxShow ? '展开' : '收起' }}<i
                :class="searchBoxShow ? 'el-icon-arrow-down': 'el-icon-arrow-up'"></i></el-button>
            </div>
          </el-row>
        </el-form>
      </el-card>
      <el-card type="flex" shadow="never" class="flex_tab card_pane">
        <el-row class="optBtn_panel">
          <el-col :md="10" :offset='1' class="optBtn_leftFixed">
            <!--保留空格符-->
            <div v-if="countSumLoading" >
              {{ $t('baReceivableBizOrder.currency') }}:&nbsp;&nbsp;<b class='el-icon-loading'></b>
              <el-divider direction="vertical"></el-divider>
              {{ $t('baReceivableBizOrder.sum') }}:&nbsp;&nbsp;<b class='el-icon-loading'></b>
            </div>
            <div v-else-if="totalSumList.length===0" >
              {{ $t('baReceivableBizOrder.currency') }}:&nbsp;&nbsp;<b>--</b>
              <el-divider direction="vertical"></el-divider>
              {{ $t('baReceivableBizOrder.sum') }}:&nbsp;&nbsp;<b>--</b>
            </div>
            <div v-else v-for="(currencySum, index) in totalSumList" :key="index">
              {{ $t('baReceivableBizOrder.currency') }}:&nbsp;<b>{{ currencySum.currency }}</b>
              <el-divider direction="vertical"></el-divider>
              {{ $t('baReceivableBizOrder.sum') }}:&nbsp;<b>{{ currencySum.sumD | numberFormat(3) }}</b>
            </div>
          </el-col>
          <el-col :md='6'>
            <!--保留空格符-->
            <div v-if="countQtyLoading" >
              {{ $t('baReceivableBizOrder.balanceWeight') }}:&nbsp;&nbsp;<b class='el-icon-loading'></b>
              <el-divider direction="vertical"></el-divider>
              {{ $t('baReceivableBizOrder.qty') }}:&nbsp;&nbsp;<b class='el-icon-loading'></b>
            </div>
            <div v-else-if="totalInfo.balanceWeightD===null" >
              {{ $t('baReceivableBizOrder.balanceWeight') }}:&nbsp;&nbsp;<b>--</b>
              <el-divider direction="vertical"></el-divider>
              {{ $t('baReceivableBizOrder.qty') }}:&nbsp;&nbsp;<b>--</b>
            </div>
            <div v-else>
              {{ $t('baReceivableBizOrder.balanceWeight') }}:&nbsp;<b>{{ totalInfo.balanceWeightD }}</b>
              <el-divider direction="vertical"></el-divider>
              {{ $t('baReceivableBizOrder.qty') }}:&nbsp;<b>{{ totalInfo.packageQty }}</b>
            </div>
          </el-col>
          <el-col :md="6" class="text-right">
            <el-button size="mini" type="primary" plain @click="exportDialogHandle()">{{ $t('export') }}</el-button>
          </el-col>
        </el-row>
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <ux-grid ref="tableData" v-loading="dataListLoading"  size='mini' :widthResize="true" :border="false" :show-overflow="true"
                   @selection-change="controlButton"  :max-height="tableHeight">
            <!-- 动态显示表格 -->
            <ux-table-column v-for="(item, index) in tableColumnsArr"
                             :key="index" :type="item.type" :field="item.prop" show-overflow-tooltips
                             header-align="center" :align="item.align" :resizable="true" :border="false"
                             :min-width="item.width" :title="item.label">
              <template slot-scope="scope">
                  <span v-if="item.prop === 'createDate'">
                    <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                      <span>{{formatterFn(scope,item.prop)}}</span>
                    </el-tooltip>
                  </span>
                <span v-else-if="item.prop === 'status'">
                    <span style="color: red" v-if="scope.row.status === 20">{{formatterFn(scope,item.prop)}}</span>
                    <span v-else>{{formatterFn(scope,item.prop)}}</span>
                  </span>
                <span v-else-if="item.prop === 'sumList'">
                    <span v-for="(item, index) in scope.row.sumList" :key="index" >
                      <span>{{ item.sum | numberFormat(3)}}</span>
                      <span style="color: green;font-size: 8px;padding-left: 3px;" class="order-sum">{{item.currency }}</span>
                    </span>
                  </span>
                <span v-else-if="item.prop === 'additionFeeTypeArray'">
                    <span v-for="(additionFeeType, index) in scope.row.additionFeeTypeArray" :key="index" >
                      <span>{{ formatterAdditionFeeTypeFn(additionFeeType)}}</span>
                    </span>
                  </span>
                <span v-else>
                    <span>{{formatterFn(scope,item.prop)}}</span>
                  </span>
              </template>
            </ux-table-column>
            <ux-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="135">
              <template slot="header" slot-scope="scope">
                <span>{{$t('handle')}}</span>
                <el-tooltip :content="$t('tableConfigTooltip')" placement="top">
                  <el-link style="margin-left:10px; font-size:16px" class="el-icon-setting" @click="settingCol(tableColumns)"></el-link>
                </el-tooltip>
              </template>
              <template slot-scope="scope">
                <el-link :underline="false"  @click="allDetailHandle(scope.row)">{{ $t('view') }}</el-link>
              </template>
            </ux-table-column>
          </ux-grid>
        </div>
        <el-pagination background :current-page="page" :page-sizes="[10, 20, 50, 100, 500]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle" @current-change="pageCurrentChangeHandle">
        </el-pagination>
      </el-card>
    </div>
    <!-- 自定义列显示弹窗 -->
    <table-set ref="tableSet"></table-set>
    <!-- 详情页面 -->
    <viewDetail v-if="viewVisible" ref="viewDetail" @backView="backView"/>
    <!-- 查看所有费用明细-->
    <bills-receivable-all-detail-dialog v-if="allDetailVisible" :waybill-type='10' ref="allDetailDialog" @refreshDataList="searchHandle"/>
    <!--导出-->
    <exportExcel v-if="exportVisible" ref="exportExcel"/>
  </div>
</template>

<script>
import classTag from '@/components/classTag'
import mixinViewModule from '@/mixins/view-module'
import threeNoInput from '@/components/three-no-input'
import listPage from '@/mixins/listPage'
import { formatterType, gtmToLtm, timestampFormat, formatterName, formatterShowName, formatterCodeName, numberFormat, formatterCodeNativeName } from '@/filters/filters'
import dictTypeMixins from '@/mixins/dictTypeMixins'
import api from '@/api'
import baseData from '@/api/baseData'
import comMixins from '@/mixins/comMixins'
// table 自定义显示
import tableSet from '@/components/tableSet'
import BillsReceivableAllDetailDialog from './billsAndBiz-all-detail-dialog'
import ExportExcel from '@/components/exportExcel'
const getBeforeDate = (interval) => {
  let day = 30
  if (interval === null || interval === undefined) {
    interval = 1
  }
  let date = interval * day
  return new Date(getNowDate() - 3600 * 1000 * 24 * date + 1)
}
const getNowDate = () => {
  return new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1)
}
export default {
  mixins: [listPage, mixinViewModule, dictTypeMixins, comMixins],
  data () {
    return {
      tableColumns: [
        { type: '', width: '165', prop: 'waybillNo', label: this.$t('baReceivableFee.waybillNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '165', prop: 'deliveryNo', label: this.$t('baReceivableFee.deliveryNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '165', prop: 'customerVoucherNo', label: this.$t('baReceivableFee.customerVoucherNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '110', prop: 'sumList', label: this.$t('baReceivableBill.totalSum'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'logisticsProductCode', label: this.$t('baReceivableFee.logisticsProductCode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'status', label: this.$t('system.status'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '90', prop: 'balanceWeightUnit', label: this.$t('baReceivableFee.balanceWeightUnit'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '90', prop: 'balanceWeightD', label: this.$t('baReceivableFee.balanceWeight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'consigneeCountry', label: this.$t('baReceivableFee.consigneeCountry'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'consigneePostcode', label: this.$t('baReceivableFee.consigneePostcode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'zone', label: this.$t('baReceivableFee.zone'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'mawbNo', label: this.$t('wsComHawb.mawbNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'departureTime', label: this.$t('wsComMawb.departureTime'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '90', prop: 'billsType', label: this.$t('baReceivableFee.billsType'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '165', prop: 'businessId', label: this.$t('baReceivableFee.businessId'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'optDate', label: this.$t('baReceivableFee.optDate'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'additionFeeTypeArray', label: this.$t('baReceivableFee.additionFeeType'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'receivableRemark', label: this.$t('baReceivableFee.memo'), align: 'center', isShow: true, disabled: false }
      ],
      mixinViewModuleOptions: {
        getDataListURL: '/ba/billsreceivable/page',
        getDataListURLOfRequestType: 'post',
        getDataListIsPage: true,
        deleteURL: '/ba/billsreceivable',
        deleteIsBatch: true,
        exportURL: '/ba/billsreceivable/export',
        initFeeItemListIsNeed: true
      },
      monthInterval: null,
      dataForm: {
        id: '',
        optBeginDate: timestampFormat(getBeforeDate(this.monthInterval)),
        optStartDate: timestampFormat(getBeforeDate(this.monthInterval)),
        optEndDate: timestampFormat(getNowDate()),
        departureStartTime: null,
        departureEndTime: null,
        mawbNo: '',
        logisticsProductCode: '',
        customerVoucherNos: '',
        settlementObjectId: this.$store.state.user.customerId,
        waybillNos: '',
        deliveryNos: '',
        packageNos: '',
        additionFeeType: '',
        consigneeCountry: '',
        businessId: ''
      },
      totalSumList: [],
      totalInfo: {},
      optDateArray: [timestampFormat(getBeforeDate(this.monthInterval)), timestampFormat(getNowDate())],
      departureTimeArr: [],
      isCharging: false,
      rollback: false,
      currencyList: [],
      countryList: [],
      allDetailVisible: false,
      exportVisible: false,
      chargingDialogFormVisible: false,
      countSumLoading: false,
      countQtyLoading: false,
      logisticsProductByParamsList: [],
      listAllByCurrent: [],
      billsStatusList: [],
      billsTypeList: [],
      weightUnitList: [],
      usingFeeTypeList: [],
      createTypeList: [],
      additionFeeTypeList: [],
      businessTypeList: [],
      userList: [],
      importType: '',
      importParams: {
        type: '',
        importType: ''
      },
      loading: false,
      customerList: [],
      // 订单查询
      orderAreaList: [
        {
          label: '运单号',
          value: 2
        },
        {
          label: '客户单号',
          value: 1
        },
        {
          label: '派送单号',
          value: 3
        }
      ],
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          if (!minDate) {
            this.selectDate = ''
          } else {
            this.selectDate = minDate.getTime()
            if (maxDate) {
              this.selectDate = ''
            }
          }
        },
        disabledDate: (time) => {
          if (this.selectDate !== '') {
            let day = 30
            const one = this.monthInterval * day * 24 * 3600 * 1000
            const minTime = this.selectDate - one
            const maxTime = this.selectDate + one
            return time.getTime() < minTime || time.getTime() > maxTime
          }
        }
      }
    }
  },
  created () {
    this.getDict()
    this.getBaseData()
    this.getSumInfo()
  },
  methods: {
    $getDataListCallback () {
      this.$refs.tableData.reloadData(this.dataList)
    },
    // 列表项checked事件
    controlButton (selection) {
      this.dataListSelections = selection
      this.statusArray = selection.map(item => item.billsType)
      if (this.statusArray.length <= 0) {
        this.rollback = false
        return
      }
      if (this.statusArray.indexOf(30) > -1) {
        // if (this.statusArray.indexOf(20) > -1 || this.statusArray.indexOf(30) > -1) {
        this.rollback = true
      } else {
        this.rollback = false
      }
    },
    // 多单号高度调节事件
    threeNoInputAutoSize () {
      this.$nextTick(() => {
        this.$refs.threeNoInput.resizeTextarea()
      })
      if (this.searchBoxShow) {
        return { minRows: 2, maxRows: 2 }
      } else {
        return { minRows: 5, maxRows: 5 }
      }
    },
    // 列表查询方法
    searchHandle () {
      this.expands = []
      this.subExpands = []
      let initFlag = this.$refs.threeNoInput.setValue()
      if (initFlag) {
        this.queryPageByParam()
      }
      // 根据查询条件汇总费用
      this.getSumInfo()
    },
    allDetailHandle (row) {
      this.allDetailVisible = true
      this.$nextTick(() => {
        this.$refs.allDetailDialog.dataForm.businessId = row.businessId
        this.$refs.allDetailDialog.init()
      })
    },
    // 获取基础数据
    getBaseData () {
      // 所有物流产品 包含未启用
      baseData(api.logisticsProductByParamsList).then(res => {
        this.logisticsProductByParamsList = res
      })
      // 获得当前公司能使用的所有启用的物流产品列表
      baseData(api.listAllByCurrent).then(res => {
        this.listAllByCurrent = res
      })
      // 国家
      baseData(api.countryList).then(res => {
        this.countryList = res
      })
      // 币种
      baseData(api.currencyList).then(res => {
        this.currencyList = res
      })
      // 费用项
      baseData(api.usingFeeTypeList).then(res => {
        this.usingFeeTypeList = res
      })
      // 用户信息
      baseData(api.userList).then(res => {
        this.userList = res
      })
      // 时间搜索条件的间隔月数
      baseData(api.baBizMonthInterval).then(res => {
        this.monthInterval = res || 1
      })
    },
    // 获取字典数据
    getDict () {
      this.getDictTypeList('billsStatus').then(res => {
        this.billsStatusList = res
      })
      this.getDictTypeList('receivableBillsType').then(res => {
        this.billsTypeList = res
      })
      this.getDictTypeList('weightUnit').then(res => {
        this.weightUnitList = res
      })
      this.getDictTypeList('feeBusinessType').then(res => {
        this.businessTypeList = res
      })
      this.getDictTypeList('createType').then(res => {
        this.createTypeList = res
      })
      this.getDictTypeList('baAdditionFeeType').then(res => {
        this.additionFeeTypeList = res
      })
    },
    // 根据查询条件汇总金额
    async getSumInfo () {
      this.totalSumList = await this.getSumByParamWithoutPage()
      this.totalInfo = await this.getWeightByParamWithoutPage()
    },
    // 根据查询条件，以币种维度汇总金额
    getSumByParamWithoutPage () {
      this.countSumLoading = true
      let url = '/ba/billsreceivable/getSumByParamWithoutPage'
      return new Promise((resolve, reject) => {
        this.$http
          .post(url, this.dataForm)
          .then(({ data: res }) => {
            this.countSumLoading = false
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            res.data = res.data || 0
            resolve(res.data)
          })
          .catch((err) => {
            this.countSumLoading = false
            reject(err)
          })
      })
    },
    // 根据查询条件，以币种维度汇总金额
    getWeightByParamWithoutPage () {
      this.countQtyLoading = true
      let url = '/ba/billsreceivable/getWeightByParamWithoutPage'
      return new Promise((resolve, reject) => {
        this.$http
          .post(url, this.dataForm)
          .then(({ data: res }) => {
            this.countQtyLoading = false
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            res.data = res.data
            resolve(res.data)
          })
          .catch((err) => {
            this.countQtyLoading = false
            reject(err)
          })
      })
    },
    // 导出
    exportDialogHandle () {
      this.exportVisible = true
      this.$nextTick(() => {
        this.$refs.exportExcel.addMemoDialogFormVisible = true
        this.dataForm.optBeginDate = this.dataForm.optStartDate
        this.$refs.exportExcel.dialogForm = this.dataForm
        this.$refs.exportExcel.total = this.total
        this.$refs.exportExcel.exportURL = this.mixinViewModuleOptions.exportURL
        this.$refs.exportExcel.exportMethod = 'post'
        this.$refs.exportExcel.exportDialogHandle()
      })
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'createDate':
          value = timestampFormat(gtmToLtm(scope.row.createDate), 'YYYY-MM-DD')
          break
        case 'logisticsProductCode':
          value = formatterCodeName(scope.row.logisticsProductCode, this.logisticsProductByParamsList)
          break
        case 'balanceWeightD':
          value = scope.row.balanceWeightD === 0 ? 'NA' : scope.row.balanceWeightD
          break
        case 'status':
          value = formatterType(scope.row.status, this.billsStatusList)
          break
        case 'billsType':
          value = formatterType(scope.row.billsType, this.billsTypeList)
          break
        case 'balanceWeightUnit':
          value = formatterType(scope.row.balanceWeightUnit, this.weightUnitList)
          break
        case 'feeTypeId':
          value = formatterName(scope.row.feeTypeId, this.usingFeeTypeList)
          break
        case 'createType':
          value = formatterType(scope.row.createType, this.createTypeList)
          break
        case 'businessType':
          value = formatterType(scope.row.businessType, this.businessTypeList)
          break
        case 'auditor':
          value = formatterShowName(scope.row.auditor, this.userList, 'realName')
          break
        case 'creator':
          value = formatterShowName(scope.row.creator, this.userList, 'realName')
          break
        case 'volD':
          value = numberFormat(scope.row.volD, 0)
          break
        case 'sumD':
          value = numberFormat(scope.row.sumD, 3)
          break
        case 'consigneeCountry':
          value = formatterCodeNativeName(scope.row.consigneeCountry, this.countryList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    // 格式化列显示
    formatterAdditionFeeTypeFn (additionFeeType) {
      let value = formatterType(additionFeeType, this.additionFeeTypeList)
      return value
    },
    // 重置表单
    resetForm (searchForm) {
      this.optDateArray = [timestampFormat(getBeforeDate(this.monthInterval)), timestampFormat(getNowDate())]
      this.departureTimeArr = []
      this.$refs.threeNoInput.clearValue()
      this._resetForm(searchForm)
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat,
    formatterName,
    formatterCodeName,
    formatterCodeNativeName,
    numberFormat
  },
  computed: {
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        let userType = this.$store.state.user.userType
        if (userType === 0) {
          return item.isShow
        }
        return item.isShow
      })
      return arr
    },
    panelShow () {
      let ret = false
      if (this.addOrUpdateVisible) {
        ret = true
      }
      return ret
    }
  },
  watch: {
    departureTimeArr: {
      handler (newVal, oldVal) {
        if (newVal !== null) {
          this.dataForm.departureStartTime = newVal[0]
          this.dataForm.departureEndTime = newVal[1]
          return
        }
        this.dataForm.departureStartTime = this.dataForm.departureEndTime = ''
      },
      deep: true
    },
    optDateArray: {
      handler (newVal, oldVal) {
        if (newVal !== null) {
          this.dataForm.optStartDate = newVal[0]
          this.dataForm.optEndDate = newVal[1]
          return
        }
        this.dataForm.optStartDate = this.dataForm.optEndDate = ''
      },
      deep: true
    }
  },
  components: {
    BillsReceivableAllDetailDialog,
    tableSet,
    classTag,
    ExportExcel,
    threeNoInput
  }
}
</script>
<style lang="scss" scoped>

</style>
