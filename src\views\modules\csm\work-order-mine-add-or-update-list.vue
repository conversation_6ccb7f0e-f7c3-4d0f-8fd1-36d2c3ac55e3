<template>
  <div class="aui-card--fill" v-loading='loading'>
    <div class="mod-co__inorder flex_wrap" v-show="!panelShow" ref="listPanel">
        <el-row :gutter="20">
          <div v-if='baseData.workOrderTypeParentList && baseData.workOrderTypeParentList.length > 0'>
            <el-col  :sm="{span:8}" class="margin_bottom22" v-for="(item, index) in baseData.workOrderTypeParentList" :key="index">
              <el-card class="box-card" shadow="never" >
                <div slot="header" class="text-center clearfix">
                  <strong class="fontSize18">{{item.name}}</strong>
                </div>
                <div class="text-center" style="min-height:80px">
                  <div class="fontSize14" >{{item.description}}</div>
                  <div class="margin_top15">
                    <el-button size="mini" type="primary" @click="goLink(item.parentId, item.id)"><i class="el-icon-plus"></i> {{$t('btn.addWorkOrder')}}</el-button>
                  </div>
                </div>
              </el-card>
            </el-col>
          </div>
          <div v-else-if='gotBaseData'>
            <el-col>
              <el-card class="box-card flex_wrap add_type_tips" shadow="never" >
                <span class='darkgrey_color'><b>{{ $t('csmWorkOrderEvaluation.addTypeTips') }}</b></span>
              </el-card>
            </el-col>
          </div>
          <div id="cs_FormFooter" class="el-form-footer">
            <el-button @click="cancelFn">{{ $t('cancel') }}</el-button>
          </div>
        </el-row>
    </div>
    <!-- 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="refreshDataList" @cancelAddOrUpdate="cancelAddOrUpdate"></add-or-update>
  </div>
</template>

<script>
import baseDataApi from '@/api'
import baseData from '@/api/baseData'
import AddOrUpdate from './work-order-mine-add-or-update-detail'
export default {
  name: 'customer',
  data () {
    return {
      dataForm: {
        id: ''
      },
      addOrUpdateVisible: false,
      loading: false,
      gotBaseData: false,
      baseData: {
        workOrderTypeParentList: []
      }
    }
  },
  created() {
    this.loading = true
  },
  methods: {
    async getDict () {
    },
    async getBaseData () {
      this.baseData.workOrderTypeParentList = await baseData(baseDataApi.workOrderTypeList, { level: 1, status: 1 }).catch(() => {})
        .finally(() => {
          this.loading = false
          this.gotBaseData = true
        })
    },
    init () {
      this.$nextTick(() => {
        this.getBaseData()
      })
    },
    goLink (parentId, id) {
      // this.$router.push({ name: src })
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.dataForm.workOrderTypeParentId = parentId
        this.$refs.addOrUpdate.init(id)
      })
    },
    refreshDataList () {
      this.getBaseData()
    },
    // 关闭新增窗口
    cancelAddOrUpdate () {
      this.addOrUpdateVisible = false
      this.$emit('cancelAddOrUpdate')
      this.$emit('refreshDataList')
    },
    // 取消
    cancelFn () {
      this.visible = false
      this.$emit('cancelAddOrUpdate')
      this.$emit('refreshDataList')
    }
  },
  computed: {
    panelShow () {
      let ret = false
      if (this.addOrUpdateVisible) {
        ret = true
      }
      return ret
    }
  },
  components: {
    AddOrUpdate
  }
}
</script>

<style scoped>
.add_type_tips{
  font-size: xxx-large;
  margin: 0 auto;
  text-align: center;
  display: flex;
  justify-content: center;
}
.darkgrey_color{
  color: darkgrey;
}
</style>
