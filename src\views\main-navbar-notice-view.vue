<template>
  <el-dialog
    width="70%"
    :visible.sync="visible"
    :title="dataForm.title"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    :append-to-body="true">
    <template slot="title">
      <el-row>
        <el-col :md="12" >
          <h2>{{ dataForm.title }}</h2>
        </el-col>
<!--        <el-col :md="12" class="text-right" style="padding-top: 25px; padding-right:100px;">-->
<!--          {{ `${dataForm.creatorName} 于 ${dataForm.publishDate} 发布` }}-->
<!--        </el-col>-->
      </el-row>
    </template>
    <div class="el-dialog-div">
      <div v-html="dataForm.content"></div>
    </div>
    <template slot="footer">
      <el-link style="margin-right: 20px;" @click="showMoreNotice">更多通知...</el-link>
      <el-button type="success" icon="el-icon-close" @click="closeFn">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { formatterType, gtmToLtm, timestampFormat } from '@/filters/filters'
export default {
  data () {
    return {
      visible: false,
      noticeId: '',
      dataForm: {
        id: '',
        title: '',
        type: '',
        publishDate: '',
        content: '',
        creatorName: ''
      }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.getInfo()
      })
    },
    // 获取信息
    getInfo () {
      if (this.noticeId === 0) {
        return
      }
      this.$http.get(`/message/notice/${this.noticeId}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
        // 标记已读
        this.readNotice()
      }).catch(() => {})
    },
    // 查看更多通知
    showMoreNotice () {
      this.closeFn()
      this.$router.replace({ name: 'message-notice-receiver' })
    },
    // 标记已读
    readNotice () {
      this.$http.get(`/message/notice/read/${this.noticeId}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
      }).catch(() => {})
    },
    // 关闭
    closeFn () {
      this.visible = false
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat
  }
}
</script>

<style lang="scss" scoped>
 .el-dialog-div {
    height: 50vh;
    overflow: auto;
  }
</style>
