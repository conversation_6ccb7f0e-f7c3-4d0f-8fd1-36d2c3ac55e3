<template>
  <div class="aui-card--fill">
    <div class="mod-co__inorder flex_wrap" v-show="!panelShow" ref="listPanel">
      <el-card class="search_box" shadow="never">
        <el-form ref="searchForm" class="form_no_margin" :model="dataForm" @keyup.enter.native="queryPageByParam()" label-width="80px">
          <el-row :gutter="10" type="flex">
            <el-col >
              <el-row :gutter="10">
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('bdExcelExportTemplate.name')" prop="name">
                    <el-input v-model="dataForm.name" :placeholder="$t('bdExcelExportTemplate.name')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('bdExcelExportTemplate.exportMainObjectName')" prop="exportMainObjectName">
                    <el-select filterable v-model="dataForm.exportMainObjectName"
                               :placeholder="$t('bdExcelExportTemplate.exportMainObjectName')" clearable>
                      <el-option v-for="item in excelMasterList" v-if="item.className === 'CoOrderDTO'" :key="item.className" :label="item.name" :value="item.className" :disabled="item.disabled"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <div class="search_box_btn no_more">
              <el-button type="primary" @click="queryPageByParam()" icon="el-icon-search">查询</el-button>
              <el-button @click.native="_resetForm('searchForm')" icon="el-icon-refresh-right">重置</el-button>
            </div>
          </el-row>
        </el-form>
      </el-card>

      <el-card class="flex_tab no_shadow" type="border-card" >
        <el-row class="optBtn_panel">
          <el-col :md="12">
            <el-button size="mini" type="primary" plain @click="deleteHandle()">{{ $t('delete') }}</el-button>
            <!--保留空格符-->
          </el-col>
          <el-col :md="12" class="text-right">
            <el-button size="mini" type="primary" plain v-if="$hasPermission('bd:excelexporttemplate:update')" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
          </el-col>
        </el-row>
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" :max-height="tableHeight">
            <el-table-column type="expand" fixed="left">
              <template slot-scope="scope">
                <el-form label-position="right" inline class="index-table-expand">
                  <el-form-item :label="$t('system.createDate')"><span>{{ scope.row.createDate | gtmToLtm }}</span></el-form-item>
                  <el-form-item :label="$t('system.updateDate')"><span>{{ scope.row.updateDate | gtmToLtm }}</span></el-form-item>
                </el-form>
              </template>
            </el-table-column>
            <!-- 动态显示表格 -->
            <el-table-column type="selection" width='50' fixed="left"></el-table-column>
            <el-table-column v-for="(item, index) in tableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
              <template slot-scope="scope">
                <div>
                  <div v-if="item.prop === 'createDate'">
                    <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                      <span>{{formatterFn(scope,item.prop)}}</span>
                    </el-tooltip>
                  </div>
                  <div v-else>
                    {{formatterFn(scope,item.prop)}}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="150">
              <template slot="header" slot-scope="scope">
                <span>{{$t('handle')}}</span>
                <el-tooltip :content="$t('tableConfigTooltip')" placement="top">
                  <el-link style="margin-left:10px; font-size:16px" class="el-icon-setting" @click="settingCol(tableColumns)"></el-link>
                </el-tooltip>
              </template>
              <template slot-scope="scope">
                <el-button type="text" size="mini"  v-if="$hasPermission('bd:excelexporttemplate:update')" @click="addOrUpdateHandle(scope.row.id,scope.row.exportMainObjectName,scope.row.url)">{{ $t('update') }}</el-button>
                <el-button type="text" size="mini"  v-if="$hasPermission('bd:excelexporttemplate:update')" @click="editDetailHandle(scope.row.id,scope.row.exportMainObjectName,scope.row.url)">{{ $t('template.editdetail') }}</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle" @current-change="pageCurrentChangeHandle">
        </el-pagination>
      </el-card>
    </div>
    <!-- 自定义列显示弹窗 -->
    <table-set ref="tableSet"></table-set>
    <!-- 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"  @cancelAddOrUpdate="cancelAddOrUpdate"></add-or-update>
    <!-- 详情页面 -->
    <detailEdit v-if="detailVisible" ref="detailEdit" @backView="backView"></detailEdit>
  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import listPage from '@/mixins/listPage'
import dictTypeMixins from '@/mixins/dictTypeMixins'
// table 自定义显示
import tableSet from '@/components/tableSet'
import AddOrUpdate from './excel-export-template-add-or-update'
import DetailEdit from './excel-export-template-detail'
import api from '@/api'
import baseData from '@/api/baseData'
import { formatterType, gtmToLtm, timestampFormat, formatterClassName } from '@/filters/filters'
export default {
  mixins: [listPage, mixinViewModule, dictTypeMixins],
  data () {
    return {
      tableColumns: [
        { type: '', width: '150', prop: 'systemTemplate', label: this.$t('bdExcelExportTemplate.systemTemplate'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '150', prop: 'type', label: this.$t('bdExcelExportTemplate.type'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '150', prop: 'name', label: this.$t('bdExcelExportTemplate.name'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'exportMainObjectName', label: this.$t('bdExcelExportTemplate.exportMainObjectName'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '150', prop: 'description', label: this.$t('bdExcelExportTemplate.description'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'url', label: this.$t('bdExcelExportTemplate.url'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '150', prop: 'objectId', label: this.$t('bdExcelExportTemplate.objectId'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '150', prop: 'id', label: this.$t('system.id'), align: 'center', isShow: false, disabled: false }
      ],
      mixinViewModuleOptions: {
        getDataListURL: '/bd/excelexporttemplate/pageClient',
        getDataListIsPage: true,
        deleteURL: '/bd/excelexporttemplate',
        deleteIsBatch: true
      },
      dataForm: {
        id: ''
      },
      excelMasterList: [],
      activeName: 'all',
      detailVisible: false,
      tableName: 'bd-excelexporttemplate'
    }
  },
  created () {
    this.getBaseData()
  },
  methods: {
    async getBaseData () {
      // urlDTO对应的字段
      baseData(api.getExcelExportMaster).then(res => {
        this.excelMasterList = res
      })
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'exportMainObjectName':
          value = formatterClassName(scope.row.exportMainObjectName, this.excelMasterList)
          break
        case 'createDate':
          value = timestampFormat(gtmToLtm(scope.row.createDate), 'YYYY-MM-DD')
          break
        case 'status':
          value = formatterType(scope.row.status, this.statusList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    // 返回
    backFn () {
      this.detailVisible = false
      this.$emit('cancelAddOrUpdate')
    },
    // 新增 / 修改
    addOrUpdateHandle (id, exportMainObjectName, url) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.dataForm.id = id
        this.$refs.addOrUpdate.dataForm.url = url
        this.$refs.addOrUpdate.dataForm.exportMainObjectName = exportMainObjectName
        this.$refs.addOrUpdate.init()
      })
    },
    // 新增 / 修改
    editDetailHandle (id, exportMainObjectName, url) {
      this.detailVisible = true
      this.$nextTick(() => {
        this.$refs.detailEdit.dataForm.templateId = id
        this.$refs.detailEdit.dataForm.url = url
        this.$refs.detailEdit.dataForm.masterDTOName = exportMainObjectName
        this.$refs.detailEdit.init()
      })
    },
    // 自定义列显示
    settingCol (tableColumns) {
      this.$nextTick(() => {
        this.$refs['tableSet'].init(this.tableName, tableColumns)
      })
    }
  },
  computed: {
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    panelShow () {
      let ret = false
      if (this.addOrUpdateVisible) {
        ret = true
      }
      return ret
    }
  },
  components: {
    AddOrUpdate,
    DetailEdit,
    tableSet
  }
}
</script>
