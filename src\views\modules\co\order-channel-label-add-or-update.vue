<template>
  <div class="add-body panel_body">
    <div class="panel-hd">{{!dataForm.id ? $t('add') : $t('update')}}</div>
    <div class="addOrUpdatePanel" >
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" label-width="80px">
        <el-row type="flex" justify="start">
          <el-col :md="{span:13, offset: 2}">
            <el-form-item :label="$t('coOrderChannelLabel.orderId')" prop="orderId">
              <el-input v-model="dataForm.orderId" :placeholder="$t('coOrderChannelLabel.orderId')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderChannelLabel.customerOrderNo')" prop="customerOrderNo">
              <el-input v-model="dataForm.customerOrderNo" :placeholder="$t('coOrderChannelLabel.customerOrderNo')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderChannelLabel.waybillNo')" prop="waybillNo">
              <el-input v-model="dataForm.waybillNo" :placeholder="$t('coOrderChannelLabel.waybillNo')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderChannelLabel.deliveryNo')" prop="deliveryNo">
              <el-input v-model="dataForm.deliveryNo" :placeholder="$t('coOrderChannelLabel.deliveryNo')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderChannelLabel.channelLabelUrl')" prop="channelLabelUrl">
              <el-input v-model="dataForm.channelLabelUrl" :placeholder="$t('coOrderChannelLabel.channelLabelUrl')"></el-input>
            </el-form-item>
          </el-col>

        </el-row>
      </el-form>
      <div id="cs_FormFooter" class="el-form-footer">
        <el-button @click="cancelFn">{{ $t('cancel') }}</el-button>
        <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      dataForm: {
        id: '',
        creator: '',
        createDate: '',
        orderId: '',
        customerOrderNo: '',
        waybillNo: '',
        deliveryNo: '',
        channelLabelUrl: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        creator: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        createDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        orderId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        customerOrderNo: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        waybillNo: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        deliveryNo: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        channelLabelUrl: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/co/orderchannellabel/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/co/orderchannellabel/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    // 取消
    cancelFn () {
      this.visible = false
      this.$emit('cancelAddOrUpdate')
    }
  }
}
</script>
