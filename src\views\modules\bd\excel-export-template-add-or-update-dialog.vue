<template>
  <detailPage @goBack="cancelFn" :title="!dataForm.id ? $t('add') : $t('update')" isFooter>
    <div class="addOrUpdatePanel">
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" label-width="120px">
      <el-row>
        <el-col :md="{span: 12, offset: 2}">
            <el-form-item :label="$t('bdExcelExportTemplate.name')" prop="name">
              <el-input v-model="dataForm.name" :placeholder="$t('bdExcelExportTemplate.name')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('bdExcelExportTemplate.description')" prop="description">
              <el-input v-model="dataForm.description" :placeholder="$t('bdExcelExportTemplate.description')"></el-input>
            </el-form-item>
            <el-form-item v-if="false" :label="$t('bdExcelExportTemplate.detailOneLine')" prop="detailOneLine">
              <el-radio-group v-model="dataForm.detailOneLine" style="width: 100%">
                <el-radio
                  v-for="item in yesOrNoList"
                  :key="item.dictValue"
                  :label="item.dictValue">
                  {{item.dictName}}
                </el-radio>
              </el-radio-group>
            </el-form-item>
        </el-col>
      </el-row>
      </el-form>
    </div>
    <div style="text-align: center;padding-top: 5%;">
      <el-button size="mini" plain :loading="buttonLoading" @click="cancelFn()">{{ $t('cancel') }}</el-button>
      <el-button size="mini" type="primary" :loading="buttonLoading" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </div>
  </detailPage>
</template>

<script>
import debounce from 'lodash/debounce'
import dictTypeMixins from '@/mixins/dictTypeMixins'
export default {
  mixins: [dictTypeMixins],
  data () {
    return {
      buttonLoading: false,
      dataForm: {
        id: '',
        systemTemplate: '',
        type: '',
        name: '',
        description: '',
        url: '',
        detailOneLine: '0',
        objectId: '',
        companyId: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: '',
        status: '',
        exportMainObjectName: '',
        version: ''
      },
      yesOrNoList: []
    }
  },
  created () {
    this.getDict()
  },
  computed: {
    dataRule () {
      return {
        version: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 数据字典
    async getDict () {
      this.yesOrNoList = await this.getDictTypeList('yesOrNo')
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/bd/excelexporttemplate/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    resetForm () {
      this.$refs['dataForm'].resetFields()
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.buttonLoading = true
        this.$http[!this.dataForm.id ? 'post' : 'put']('/bd/excelexporttemplate/', this.dataForm).then(({ data: res }) => {
          this.buttonLoading = false
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {
          this.buttonLoading = false
        })
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    // 取消
    cancelFn () {
      this.$emit('cancelAddOrUpdate')
    }
  }
}
</script>
