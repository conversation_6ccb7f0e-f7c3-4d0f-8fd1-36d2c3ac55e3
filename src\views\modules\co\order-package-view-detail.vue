<template>
  <div class="add-body panel_body">
    <div class="panel-hd">
      <span v-text="$t('detail')"></span>
    </div>
    <div class="detail-desc">
      <el-form ref="form" label-width="80px">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item :label="$t('coOrderPackage.orderId')">
              <span v-text="dataForm.orderId"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderPackage.packageSerialNo')">
              <span v-text="dataForm.packageSerialNo"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderPackage.packageDeliveryNo')">
              <span v-text="dataForm.packageDeliveryNo"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderPackage.packageCustomerNo')">
              <span v-text="dataForm.packageCustomerNo"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderPackage.packageWeight')">
              <span v-text="dataForm.packageWeightD"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderPackage.packageLength')">
              <span v-text="dataForm.packageLengthD"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderPackage.packageWidth')">
              <span v-text="dataForm.packageWidthD"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderPackage.packageHeight')">
              <span v-text="dataForm.packageHeightD"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderPackage.channelLabelUrl')">
              <span v-text="dataForm.channelLabelUrl"></span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div id="cs_FormFooter" class="el-form-footer" slot="footer">
      <el-button @click="backFn">{{ $t('back') }}</el-button>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      dataForm: {
        id: '',
        creator: '',
        createDate: '',
        orderId: '',
        packageSerialNo: '',
        packageDeliveryNo: '',
        packageCustomerNo: '',
        packageWeightD: '',
        packageLengthD: '',
        packageWidthD: '',
        packageHeightD: '',
        channelLabelUrl: ''
      }
    }
  },
  methods: {
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/co/orderpackage/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 返回
    backFn () {
      this.visible = false
      this.$emit('backView')
    }
  }
}
</script>
