<template>
  <div class="home aui-card--fill">
    <div class="mod-home">
      <el-row type="flex" :gutter="20" class="margin_bottom15">
        <el-col :sm="14" ref="leftWrap">
          <el-card  class="box-card" shadow="never">
            <div slot="header" class="clearfix">
              <h2 class="card_title">账户可用余额</h2>
              <router-link v-if="$hasPermission('ba:recharge:save')" :to="{name: 'ba-recharge'}">
                <el-button size="medium"  type="primary" icon="el-icon-money" style="font-size:14px;margin-left: 40px">充值</el-button>
              </router-link>
            </div>
            <el-row :gutter="10" class="margin_bottom15">
              <el-col :lg="{span:6}" :md="{span:8}" :sm="{span:6}" class="item_status" v-for="(item, index) in moneyList" :key="index">
                <div class="total_m text-right">
                  <div class="text-right">
                    <span :class="item.usableSum < 0? 'danger': ''"> {{item.usableSum | numberFormat(3)}}</span>
                    <span class="order-sum" style="font-size:14px;">{{item.currency}}</span>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-card>
          <el-card class="box-card" shadow="never">
            <div slot="header" class="clearfix">
              <h2 class="inline-block card_title">今日预报订单</h2>
            </div>
            <el-row :gutter="20">
              <el-col :md="{span:12}" class="item_status">
                <div class="flex">
                  <div class="flex_1 status">待预报：
                    <a href="javascript:void(0);" class="number warning" @click="goToPage({name: 'co-in-order',  query: {activeName: 'draft'}}, 'homeWaitPutway')">{{statusArr['draft'].num || 0}}</a>
                  </div>
                </div>
              </el-col>
              <el-col :md="{span:12}" class="item_status">
                <div class="flex">
                  <div class="flex_1 status">已预报：
                    <a href="javascript:void(0);" class="number success" @click="goToPage({name: 'co-in-order',  query: {activeName: 'forecast'}}, 'homeWaitPutway')">{{statusArr['forecast'].num || 0}}</a>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
        <el-col :sm="10">
          <el-card class="box-card align_to_left" shadow="never">
            <div slot="header" class="clearfix">
              <h2 class="card_title no_margin">通知公告</h2>
              <router-link class="more" :to="{name: 'message-notice-receiver'}" :underline="false" >更多 >></router-link>
<!--              <el-link :underline="false" class="more">更多 >></el-link>-->
            </div>
<!--            <ul class="notice_list">-->
<!--              <li class="clearfix" v-for="i in 5" :key="i">-->
<!--                <el-link>新系统发布公告</el-link>-->
<!--                <span class="update">2019-07-08</span>-->
<!--              </li>-->
<!--            </ul>-->
            <template v-if='noticeList && noticeList.length > 0'>
            <ul class="notice_list" v-for="(item, index) in noticeList" :key="index">
              <li class="clearfix">
                <el-link :underline="false" @click="viewDetail(item)">{{item.title}}</el-link>
                <span class="update">{{item.publishDate}}</span>
              </li>
            </ul>
            </template>
            <template v-else>
              <span class='no_notice'>暂无通知公告</span>
            </template>
          </el-card>
        </el-col>
      </el-row>

      <el-card class="box-card" shadow="never" v-loading="!isChart">
        <div class="clearfix" ><h2 class="no-margin no-padding fl">近7天预报订单</h2></div>
        <ve-chart class="w-percent-100" v-if="isChart" :data="chartData" :settings="chartSettings"></ve-chart>
      </el-card>
    </div>
    <el-dialog
      :visible.sync="dialogVisible"
      width="50%"
    >
      <div class="news_dialog_body">
        <div v-for="i in 100" :key="i">新系统发布公告</div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 弹窗, 通知 -->
    <notice-view v-if="noticeVisible" ref="noticeView"></notice-view>
  </div>
</template>

<script>
import Vue from 'vue'
import VCharts from 'v-charts'
import { numberFormat, timestampFormat } from '@/filters/filters'
import NoticeView from '../main-navbar-notice-view'

const getBeforeDate = (date, endTime) => {
  return new Date(getEndDate(endTime) - 3600 * 1000 * 24 * date + 1)
}
const getEndDate = (endTime) => {
  endTime = endTime || new Date()
  return new Date(new Date(endTime.toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1)
}

export function getNowDate () {
  return new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1)
}

export function getBeforeDay (day) {
  return new Date(getNowDate() - 3600 * 1000 * 24 * day + 1)
}
Vue.use(VCharts)
export default {
  data () {
    this.typeArr = ['line', 'histogram', 'pie']
    this.index = 0
    return {
      isChart: false,
      chartData: {
        columns: ['日期', '预报订单'],
        rows: []
      },
      chartSettings: { type: this.typeArr[this.index] },
      dialogVisible: false,
      noticeVisible: false,
      moneyType: 'CNY',
      dataForm: {
        page: 1,
        limit: 10,
        id: '',
        settlementObjectType: 10,
        omsFlag: 'oms'
      },
      createDataArray: [getBeforeDate(7), getEndDate()],
      moneyList: [], // 余额列表
      rechangeList: [], // 今日充值列表
      receivableList: [], // 今日支出列表
      noticeList: [], // 公告列表
      inOrderParam: {},
      outOrderParam: {
        id: '',
        status: '',
        logisticsChannelId: null,
        createDate1: '',
        createDate2: ''
      },
      returnOrderParam: {
        id: '',
        isErr: 0,
        status: 40 // 已上架
      },
      statusArr: {
        // 草稿
        'draft': {
          value: 10,
          num: 0
        },
        // 预报
        'forecast': {
          value: 11,
          num: 0
        },
        // 入库
        'inWarehouse': {
          value: 12,
          num: 0
        },
        // 到仓
        'outWarehouse': {
          value: 13,
          num: 0
        },
        // 所有订单
        'all': {
          value: 14,
          num: 0
        },
        // 删除
        'deleted': {
          value: 15
          // num: 0
        }
      },
      outStatusArr: {
        send: {
          num: 0
        },
        all: {
          num: 0
        },
        new: {
          num: 0
        },
        audit: {
          num: 0
        },
        draft: {
          num: 0
        },
        arrived: {
          num: 0
        }
      },
      countErrOutOrderNum: 0,
      noInventoryErrOutOrderNum: 0,
      returnOreder: {
        return: {
          num: 0
        },
        putway: {
          num: 0
        }
      },
      fbaStatus: {
        waitLoading: 0,
        waitConfirm: 0
      },
      fabAwaitStocked: 0,
      fbaDestroyOrder: 0,
      dataViewShow: false,
      data1: [],
      data2: [],
      dataArray: [getBeforeDate(7), getEndDate()],
      countDataForm: {
        createDate1: null,
        createDate2: null,
        customerId: this.$store.state.user.customerId
      },
      countDataArray: [getBeforeDay(1), getNowDate()]
    }
  },
  components: {
    NoticeView
  },
  watch: {
    countDataArray: {
      handler (value, oldName) {
        if (value !== '' && value !== null && value !== undefined) {
          this.countDataForm.createDate1 = timestampFormat(value[0])
          this.countDataForm.createDate2 = timestampFormat(value[1])
        } else {
          this.countDataArray = [getBeforeDay(1), getNowDate()]
        }
      },
      // immediate: true 第一次初始化的时候会被执行 immediate: false 第一次初始化的时候不会被执行
      immediate: true
    }
  },
  created () {
    this.getAll()
    this.getBefore30DayCount()
  },
  methods: {
    getBefore30DayCount () {
      this.isChart = false
      this.$http.get('/co/order/getBefore30DayCount',
        {
          params: { before30Day: getBeforeDate(7) }
        }).then(({ data: res }) => {
        if (res.code !== 0) {
          this.total = 0
          return this.$message.error(res.msg)
        }
        let dayCount = res.data
        let dayKeys = Object.keys(dayCount)
        let rows = []
        dayKeys.forEach(function (dayKey) {
          rows.push({ '日期': dayKey.substring(4, 6) + '月' + dayKey.substring(6, 8) + '日', '预报订单': dayCount[dayKey] })
          // rows.push({ '日期': dayCount.key + '日', '预报订单': dayCount.value })
          // rows.push({ '日期': '1月' + (i + 1) + '日', '预报订单': dayCount.value })
        })
        this.chartData.rows = rows
        this.isChart = true
      }).catch(() => {})
    },
    // 查看通知详情
    viewDetail (item) {
      this.noticeVisible = true
      this.$nextTick(() => {
        this.$refs.noticeView.noticeId = item.id
        this.$refs.noticeView.init()
      })
    },
    getAll () {
      Promise.race([
        this.accountFn(),
        this.getStatusCount(),
        this.getUnreadNotices()
      ]).then(() => {
      })
    },
    goToPage (routeParam, emitName) {
      this.$router.push(routeParam)
      if (emitName) {
        this.$bus.$emit(emitName)
      }
    },
    // 获取未读的通知  系统消息
    async getUnreadNotices () {
      await this.$http.get(`/message/notice/getUnreadList/20`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.noticeList = res.data
      }).catch(() => {})
    },
    // 余额
    accountFn () {
      this.$http.get('/ba/receivableaccount/querySelfUsableSum', {
        params: this.dataForm
      }).then(({ data: res }) => {
        if (res.code !== 0) {
          this.moneyList = []
          this.total = 0
          return this.$message.error(res.msg)
        }
        this.moneyList = res.data || []
      })
    },
    getStatusCount () {
      this.$http.post('/co/order/getStatusCount', this.countDataForm).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.statusArr['draft'].num = res.data[this.statusArr['draft'].value]
        this.statusArr['forecast'].num = res.data[this.statusArr['forecast'].value]
        this.statusArr['inWarehouse'].num = res.data[this.statusArr['inWarehouse'].value]
        this.statusArr['outWarehouse'].num = res.data[this.statusArr['outWarehouse'].value]
        this.statusArr['all'].num = res.data[0]
        this.statusArr['deleted'].num = res.data[this.statusArr['deleted'].value]
      }).catch(() => {})
    }
  },
  filters: {
    numberFormat
  }
}
</script>
<style lang="scss" scoped>
  .news_dialog_body{
    position: relative;
    max-height: 350px;
    overflow: hidden;
    overflow-y: auto;
  }
  .home {
    .el-card__header{
      padding: 10px;
      height: 48px;
    }
    .rightWrap{
      position: relative;
      height: 100%;
      overflow: hidden;
      .el-card__body{
        max-height: 260px;
        overflow: hidden;
      }
    }
    .card_title{
      margin: 0;
      display: inline-block;
      line-height: 24px;
    }
    .item_status{
      position: relative;
      padding-left:30px !important;
      &:first-child{
        border-left: none;
      }
      .title{
        margin: 0;
        padding: 0;
        color: #606266;
      }
      .status{
        color:#606266;
        padding: 5px 0;
        .number{
          color: #303133;
          font-size: 32px;
          text-decoration: unset;
          &.danger{
            color:#F56C6C
          }
        }
      }
    }
    .total_m{
      font-size: 30px;
      text-align: left;
      .unit{
        font-size: 28px;
        margin-left: 8px;
      }
    }
    .payPanel{
      border-top: 1px solid #EBEEF5;
      padding:15px 5px;
      .table{
        margin-top: 10px;
      }
    }
    .more{
      float: right;
    }
    .notice_list{
      margin: 0;
      padding: 0;
      list-style: none;
      &>li {
        position: relative;
        padding: 5px ;
        border-bottom: 1px dashed #EBEEF5;
        .update{
          float: right;
        }
      }
    }
  }
  .no_notice {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 20%;
    font-size: 18px;
    color: darkgrey;
  }
  .align_to_left {
    flex: 1;
    height: 100%;
  }
</style>
