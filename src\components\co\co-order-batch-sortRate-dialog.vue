<template>
<el-dialog :visible.sync="visible"  :title="$t('fba.sortRate')"  width="90%"  top="10px" style='max-height: 5000px' :modal-append-to-body="false" :append-to-body="false" :close-on-click-modal="false" :close-on-press-escape="false"  :lock-scroll="true" :before-close="cancelFn" >
    <!--    <div class=" flex_wrap" >-->
    <el-card v-show="false" class="search_box" shadow="never">
      <el-form ref="searchForm"  class="form_no_margin" :model="dataForm" @keyup.enter.native="getSortList()" label-width="120px">
        <el-row :gutter="2" type="flex">
          <el-col >
            <el-row :gutter="2">
              <el-col :sm="24" :md="10">
                <el-form-item :label="$t('wsComWaybill.logisticsChannelCode')" prop="packageNo">
                  <el-select v-model="dataForm.logisticsChannelCode" filterable clearable>
                    <el-option v-for="item in logisticsChannelList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <div class="search_box_btn">
            <el-row>
              <el-button type="primary" @click="getSortList()" icon="el-icon-search">查询</el-button>
              <el-button @click.native="_resetForm('searchForm')" icon="el-icon-refresh-right">重置</el-button>
            </el-row>
          </div>
        </el-row>
      </el-form>
    </el-card>
  <el-card class="no_shadow" v-loading='dialogLoading' type="border-card">
    <el-row class="optBtn_panel">
      <el-col :md="12" class="optBtn_leftFixed">
        <!--保留空格符-->
      </el-col>
      <el-col :md="12" class="text-right">
        <el-button size="mini" type="primary" plain  @click="cleanAndReSortRate()">{{ $t('fba.reSortRate') }}</el-button>
      </el-col>
    </el-row>
    <el-form :model='sortLogForm' style="margin-top: 10px"  ref="sortLogForm" key='9' :inline-message='true'>
      <el-row v-for="(order, index) in sortLogForm.dataList" :key="index"  class='margin_top10 margin_bottom10'>
        <el-card class="no_shadow"  type="border-card">
          <el-col :sm="24" :md="2">
            <el-row>
              <el-col :sm="24" :md="24">
                  <span v-text="$t('wsComWaybill.customerOrderNo')" style="display: block;font-size: smaller"></span>
                  <span v-text="order.customerOrderNo" style="font-size: 8px" class="info"></span>
              </el-col>
            </el-row>
            <el-row style="padding-top: 5px">
              <el-col :sm="24" :md="24">
                  <span v-text="$t('wsComWaybill.waybillNo')"  style="display: block;font-size: smaller"></span>
                  <span v-text="order.waybillNo" style="font-size: 8px" class="info"></span>
              </el-col>
            </el-row>
          </el-col>
          <el-col :sm="24" :md="22">
            <el-form-item v-if="order.sortRateMsg !== null" :label="$t('baSortRateLog.message')">
              <span v-text="order.sortRateMsg"></span>
            </el-form-item>
            <ux-grid v-else v-loading="order.auditing" :ref="`table${order.id}`" size='mini' @sort-change="sortChange"
                     element-loading-text="预报中，请稍等" :key="index" :data="order.sortRateList"
                      :sort-config='{defaultSort: "asc"}'
                     max-height="160px" :widthResize="true" :border="false">
              <!--          <el-table v-else id="declareTable" v-loading="item.dataListLoading" :data="item.sortRateList" max-height="410px">-->
              <!-- 动态显示表格 -->
              <ux-table-column v-for="(item, index) in tableColumnsArr" :show-overflow-tooltip="item.prop === 'receivableRemark'"
                               :sortable="item.prop === 'sortByReceivableSum'" :key="index" :type="item.type" :resizable="true" :border="false"
                               :field="item.prop" header-align="center" :align="item.align" :min-width="item.width" :title="item.label">
                <template slot-scope="scope">
                  <div>
                    <div v-if="item.prop === 'sortByReceivableSum'">
                      <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                        <span>{{formatterFn(scope,'createDate')}}</span>
                      </el-tooltip>
                    </div>
                    <div v-else-if="item.prop === 'businessId'">
                      <el-link  type="primary" :underline="false" @click="clickExpand(scope.row, 'tableExpand', $event)">{{scope.row.businessId}}</el-link>
                    </div>
                    <div v-else-if="item.prop === 'status'">
                      <el-badge is-dot class="badge_status" v-if="scope.row.status === 2" type="warning" style="margin-top: 10px"></el-badge>
                      <el-badge is-dot class="badge_status" v-else-if="scope.row.status === 0" type="success" style="margin-top: 10px"></el-badge>
                      <el-badge is-dot class="badge_status" v-else-if="scope.row.status === 3" type="primary" style="margin-top: 10px"></el-badge>
                      <el-badge is-dot class="badge_status" v-else-if="scope.row.status === 1" type="danger" style="margin-top: 10px"></el-badge>
                      <el-badge is-dot class="badge_status" v-else-if="scope.row.status === 4" type="info" style="margin-top: 10px"></el-badge>
                      <span>{{ formatterFn(scope,item.prop) }}</span>
                    </div>
                    <div v-else-if="item.prop === 'message'">
                      <el-tooltip placement="top">
                        <div slot="content" style="max-width: 400px;">{{scope.row.message}}</div>
                        <span class="text-overflow">{{formatterFn(scope,item.prop)}}</span>
                      </el-tooltip>
                    </div>
                    <div v-else>
                      <span class="text-overflow">{{formatterFn(scope,item.prop)}}</span>
                    </div>
                  </div>
                </template>
              </ux-table-column>
              <ux-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="150">
                <template slot="header" slot-scope="scope">
                  <span>{{$t('handle')}}</span>
                  <el-tooltip :content="$t('tableConfigTooltip')" placement="top">
                  </el-tooltip>
                </template>
                <template slot-scope="scope">
                  <div >
                    <el-link v-loading="scope.row.status===4" element-loading-spinner="el-icon-loading"  v-show="order.status===10 && scope.row.status!==1 && scope.row.status!==4" :underline='false' @click="usedAndForecasting(order, scope.row)" :class="'el-link--default'">
                      {{ $t('fba.usedAndForecasting') }}
                    </el-link>
                    <el-link v-loading="scope.row.status===4" element-loading-spinner="el-icon-loading" v-show="order.status===10 && scope.row.status!==3" :underline='false' @click="reRate(scope.row)" :class="'el-link--default'">
                      {{ $t('fba.reRate') }}
                    </el-link>
                  </div>
                </template>
              </ux-table-column>
            </ux-grid>
          </el-col>
        </el-card>
      </el-row>
    </el-form>
  </el-card>
  </el-dialog>
</template>

<script>
import listPage from '@/mixins/listPage'
import mixinViewModule from '@/mixins/view-module'
import api from '@/api'
import baseData from '@/api/baseData'
import { formatterType, gtmToLtm, timestampFormat, formatterCodeName, formatterName } from '@/filters/filters'
export default {
  mixins: [mixinViewModule, listPage],
  data () {
    return {
      visible: false,
      dialogLoading: false,
      dataForm: {
        orderIds: [],
        logisticsChannelCode: ''
      },
      sortLogForm: {
        dataList: []
      },
      getSortChannelUrl: `/co/order/getSortRateChannel`,
      requestCount: 0,
      logisticsChannelList: [],
      weightUnitList: [],
      quotationSourceList: [],
      mixinViewModuleOptions: {
        getDataListURL: '/ba/sortratelog/page',
        getDataListIsPage: true,
        activatedIsNeed: false
      },
      dataListLoading: false,
      tableColumns: [
        { type: '', width: '10', prop: 'id', label: this.$t('comInSubWaybill.id'), align: 'center', isShow: false, disabled: false },
        { type: '', width: '80', prop: 'type', label: this.$t('baSortRateLog.type'), align: 'center', isShow: true, disabled: false },
        // { type: '', width: '120', prop: 'channelRouterId', label: this.$t('baSortRateLog.channelRouterId'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'logisticsChannelCode', label: this.$t('baSortRateLog.logisticsChannelCode'), align: 'center', isShow: true, disabled: false },
        // { type: '', width: '120', prop: 'providerChannelCode', label: this.$t('baSortRateLog.providerChannelCode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'status', label: this.$t('system.status'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'currency', label: this.$t('baSortRateLog.currency'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'receivableSumD', label: this.$t('baSortRateLog.payableSum'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'balanceWeightD', label: this.$t('baSortRateLog.balanceWeight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'balanceWeightUnit', label: this.$t('baSortRateLog.balanceWeightUnit'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '200', prop: 'message', label: this.$t('baSortRateLog.message'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'sortByReceivableSum', label: this.$t('baSortRateLog.createDate'), align: 'center', isShow: true, disabled: false }
      ]
    }
  },
  computed: {
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    }
  },
  created () {
    this.getDict()
    this.getBaseData()
  },
  methods: {
    sortChange (column, prop, order) {
      // console.log('column:' + column + ',prop:' + prop + ',order:' + order)
    },
    init (orderIds) {
      this.visible = true
      this.dialogLoading = true
      this.sortLogForm.dataList = []
      this.dataForm.orderIds = orderIds
      this.$nextTick(() => {
        this.getSortChannelUrl = `/co/order/getSortRateChannel`
        this.sortRate()
      })
    },
    sortRate () {
      new Promise(this.getSortChannelList).then(() => {
        if (this.sortLogForm.dataList.length > 0) {
          let sortRateList = []
          this.sortLogForm.dataList.forEach(order => {
            order.sortRateList.filter(item => item.status === 4).forEach(item => sortRateList.push(item))
          })
          // 批量询价
          this.batchReRate(sortRateList)
          this.dialogLoading = false
        }
      }).finally(() => {

      })
    },
    // 获取比价渠道
    getSortChannelList (resolve, reject) {
      this.dataListLoading = true
      this.$http.post(
        this.getSortChannelUrl,
        this.dataForm.orderIds
      ).then(({ data: res }) => {
        this.dataListLoading = false
        if (res.code !== 0) {
          return reject(this.$message.error(res.msg))
        } else {
          if (res.data) {
            res.data.forEach(order => {
              // 设置预报loading属性
              order.auditing = false
            })
          }
          this.sortLogForm.dataList = res.data || []
        }
      }).catch(() => {}).finally(() => resolve())
    },
    // 清空并重新获取比价渠道
    cleanAndReSortRate () {
      this.dialogLoading = true
      this.getSortChannelUrl = `/co/order/cleanAndReSortRate`
      this.sortRate()
    },
    // 选用比价结果并预报
    usedAndForecasting (orderObject, logObject) {
      this.$set(orderObject, 'auditing', true)
      this.$http.post(`/co/order/forecastingBySortRateLog/${logObject.id}`).then(({ data: res }) => {
        this.$set(orderObject, 'auditing', false)
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        } else {
          // 复制属性
          Object.keys(logObject).forEach(key => {
            if (res.data[key] !== undefined) {
              this.$set(logObject, key, res.data[key])
            }
          })
          // 预报成功,设置为已获取尾程单号
          if (res.data.status === 3) {
            orderObject.status = 11
          }
        }
      }).catch(() => {})
    },
    // 重新询价
    reRate (logObject) {
      logObject.status = 4
      this.reRateByObject(logObject)
    },
    reRateByObject (logObject) {
      this.requestCount++
      this.$http.post(
        `/co/order/reRate`,
        logObject
      ).then(({ data: res }) => {
        this.requestCount--
        if (res.code !== 0) {
          this.$set(logObject, 'status', 1)
          this.$set(logObject, 'message', res.msg)
        } else {
          // 复制属性
          Object.keys(logObject).forEach(key => {
            if (res.data[key] !== undefined) {
              this.$set(logObject, key, res.data[key])
            }
          })
          this.$nextTick(() => {
            this.$refs[`table${logObject.orderId}`][0].clearSort()
            this.$refs[`table${logObject.orderId}`][0].sort('sortByReceivableSum', 'asc')
          })
        }
      }).catch(() => {})
    },
    sleep (ms) {
      return new Promise((resolve) => setTimeout(resolve, ms))
    },
    /**
     * 批量比价
     */
    async batchReRate (sortRateList) {
      // 最大请求数
      let requestMax = 10
      for (let i = 0; i < sortRateList.length && this.visible;) {
        if (this.requestCount < requestMax) {
          let thisSortItem = sortRateList[i]
          if (thisSortItem.status === 4) {
            this.reRateByObject(thisSortItem)
          }
          i++
        } else {
          // 当前请求数达到最大请求数时的休眠时间
          await this.sleep(500)
        }
      }
    },
    async getBaseData () {
      this.logisticsChannelList = await baseData(api.listByCurrent).catch((res) => {
        const msg = res.msg ? res.msg : res
        this.$message.error(msg)
      })
      this.channelRouterList = await baseData(api.logisticschannelrouteList).catch((res) => {
        const msg = res.msg ? res.msg : res
        this.$message.error(msg)
      })
    },
    async getDict () {
      // 获得使用状态数据字典.
      this.statusList = await this.getDictTypeList('baSortRateStatus')
      this.quotationSourceList = await this.getDictTypeList('channelQuotationSource') // 渠道报价来源
      this.getDictTypeList('weightUnit').then(res => {
        this.weightUnitList = res
      })
    },
    // 取消
    cancelFn () {
      this.visible = false
      this.$emit('sortRateAfterHandle')
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'logisticsChannelCode':
          value = formatterCodeName(scope.row.logisticsChannelCode, this.logisticsChannelList)
          break
        case 'status':
          value = formatterType(scope.row.status, this.statusList)
          break
        case 'balanceWeightUnit':
          value = formatterType(scope.row.balanceWeightUnit, this.weightUnitList)
          break
        case 'type':
          value = formatterType(scope.row.type, this.quotationSourceList)
          break
        case 'channelRouterId':
          value = formatterName(scope.row.channelRouterId, this.channelRouterList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat,
    formatterCodeName,
    formatterName
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep #declareTable .el-form-item  {
    margin-bottom: 0px !important;
  }
  ::v-deep  .elx-table--body-wrapper .el-loading-mask .el-loading-spinner {
    margin-top: -21% !important;
  }
  ::v-deep  .elx-table--body-wrapper .row--current .el-loading-mask {
    background-color: #e6f7ff !important;
  }
  ::v-deep  .elx-table--body-wrapper .row--current .row--hover .el-loading-mask {
    background-color: #d8effb !important;
  }
</style>
