<template>
  <div class="aui-card--fill">
    <div class="mod-co__inorder flex_wrap" v-show="!panelShow" ref="listPanel">
      <el-card class="search_box" shadow="never">
        <el-form ref="searchForm" class="form_no_margin" :model="dataForm" @keyup.enter.native="queryPageByParam()" label-width="80px">
          <el-row :gutter="10" type="flex">
            <el-col >
              <el-row :gutter="10">
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderDeclare.orderId')" prop="orderId">
                    <el-input v-model="dataForm.orderId" :placeholder="$t('coOrderDeclare.orderId')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderDeclare.chineseName')" prop="chineseName">
                    <el-input v-model="dataForm.chineseName" :placeholder="$t('coOrderDeclare.chineseName')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderDeclare.englishName')" prop="englishName">
                    <el-input v-model="dataForm.englishName" :placeholder="$t('coOrderDeclare.englishName')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderDeclare.quantity')" prop="quantity">
                    <el-input v-model="dataForm.quantity" :placeholder="$t('coOrderDeclare.quantity')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderDeclare.unitNetWeight')" prop="unitNetWeight">
                    <el-input v-model="dataForm.unitNetWeight" :placeholder="$t('coOrderDeclare.unitNetWeight')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderDeclare.unitDeclarePrice')" prop="unitDeclarePrice">
                    <el-input v-model="dataForm.unitDeclarePrice" :placeholder="$t('coOrderDeclare.unitDeclarePrice')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderDeclare.brand')" prop="brand">
                    <el-input v-model="dataForm.brand" :placeholder="$t('coOrderDeclare.brand')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderDeclare.goodsBarcode')" prop="goodsBarcode">
                    <el-input v-model="dataForm.goodsBarcode" :placeholder="$t('coOrderDeclare.goodsBarcode')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderDeclare.sku')" prop="sku">
                    <el-input v-model="dataForm.sku" :placeholder="$t('coOrderDeclare.sku')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderDeclare.hsCode')" prop="hsCode">
                    <el-input v-model="dataForm.hsCode" :placeholder="$t('coOrderDeclare.hsCode')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderDeclare.productModel')" prop="productModel">
                    <el-input v-model="dataForm.productModel" :placeholder="$t('coOrderDeclare.productModel')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderDeclare.material')" prop="material">
                    <el-input v-model="dataForm.material" :placeholder="$t('coOrderDeclare.material')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderDeclare.purpose')" prop="purpose">
                    <el-input v-model="dataForm.purpose" :placeholder="$t('coOrderDeclare.purpose')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderDeclare.origin')" prop="origin">
                    <el-input v-model="dataForm.origin" :placeholder="$t('coOrderDeclare.origin')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderDeclare.pickingRemark')" prop="pickingRemark">
                    <el-input v-model="dataForm.pickingRemark" :placeholder="$t('coOrderDeclare.pickingRemark')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('coOrderDeclare.productUrl')" prop="productUrl">
                    <el-input v-model="dataForm.productUrl" :placeholder="$t('coOrderDeclare.productUrl')" clearable ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <div class="search_box_btn no_more">
              <el-button type="primary" @click="queryPageByParam()" icon="el-icon-search">查询</el-button>
              <el-button @click.native="_resetForm('searchForm')" icon="el-icon-refresh-right">重置</el-button>
            </div>
          </el-row>
        </el-form>
      </el-card>

      <el-card class="flex_tab no_shadow" type="border-card" >
        <el-row class="optBtn_panel">
          <el-col :md="12">
            <el-button size="mini" @click="deleteHandle()">{{ $t('delete') }}</el-button>
            <el-button size="mini" >{{ $t('audit') }}</el-button>
            <!--保留空格符-->
          </el-col>
          <el-col :md="12" class="text-right">
            <el-button size="mini"  v-if="$hasPermission('co:orderdeclare:save')" plain @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
          </el-col>
        </el-row>
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" :max-height="tableHeight">
            <!-- 动态显示表格 -->
            <el-table-column type="selection" width='50' fixed="left"></el-table-column>
            <el-table-column v-for="(item, index) in tableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
              <template slot-scope="scope">
                <div>
                  <div v-if="item.prop === 'createDate'">
                    <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                      <span>{{formatterFn(scope,item.prop)}}</span>
                    </el-tooltip>
                  </div>
                  <div v-else>
                    {{formatterFn(scope,item.prop)}}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="150">
              <template slot="header" slot-scope="scope">
                <span>{{$t('handle')}}</span>
                <el-tooltip :content="$t('tableConfigTooltip')" placement="top">
                  <el-link style="margin-left:10px; font-size:16px" class="el-icon-setting" @click="settingCol(tableColumns)"></el-link>
                </el-tooltip>
              </template>
              <template slot-scope="scope">
                <el-link :underline="false"  @click="viewHandle(scope.row.id)">{{ $t('view') }}</el-link>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination background :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle" @current-change="pageCurrentChangeHandle">
        </el-pagination>
      </el-card>
    </div>
    <!-- 自定义列显示弹窗 -->
    <table-set ref="tableSet"></table-set>
    <!-- 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList" @cancelAddOrUpdate="cancelAddOrUpdate"></add-or-update>
    <!-- 详情页面 -->
    <viewDetail v-if="viewVisible" ref="viewDetail" @backView="backView"></viewDetail>
  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import listPage from '@/mixins/listPage'
import { formatterType, gtmToLtm, timestampFormat } from '@/filters/filters'
import dictTypeMixins from '@/mixins/dictTypeMixins'
// table 自定义显示
import tableSet from '@/components/tableSet'
import AddOrUpdate from './order-declare-add-or-update'
import ViewDetail from './order-declare-view-detail'
export default {
  mixins: [listPage, mixinViewModule, dictTypeMixins],
  data () {
    return {
      tableColumns: [
        { type: '', width: '150', prop: 'orderId', label: this.$t('coOrderDeclare.orderId'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'chineseName', label: this.$t('coOrderDeclare.chineseName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'englishName', label: this.$t('coOrderDeclare.englishName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'quantity', label: this.$t('coOrderDeclare.quantity'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'unitNetWeight', label: this.$t('coOrderDeclare.unitNetWeight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'unitDeclarePrice', label: this.$t('coOrderDeclare.unitDeclarePrice'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'brand', label: this.$t('coOrderDeclare.brand'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'goodsBarcode', label: this.$t('coOrderDeclare.goodsBarcode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'sku', label: this.$t('coOrderDeclare.sku'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'hsCode', label: this.$t('coOrderDeclare.hsCode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'productModel', label: this.$t('coOrderDeclare.productModel'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'material', label: this.$t('coOrderDeclare.material'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'purpose', label: this.$t('coOrderDeclare.purpose'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'origin', label: this.$t('coOrderDeclare.origin'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'pickingRemark', label: this.$t('coOrderDeclare.pickingRemark'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'productUrl', label: this.$t('coOrderDeclare.productUrl'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'id', label: this.$t('system.id'), align: 'center', isShow: true, disabled: false }
      ],
      mixinViewModuleOptions: {
        getDataListURL: '/co/orderdeclare/page',
        getDataListIsPage: true,
        deleteURL: '/co/orderdeclare',
        deleteIsBatch: true
      },
      dataForm: {
        id: ''
      },
      activeName: 'all',
      tableName: 'co-orderdeclare'
    }
  },
  created () {
  },
  methods: {
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'createDate':
          value = timestampFormat(gtmToLtm(scope.row.createDate), 'YYYY-MM-DD')
          break
        case 'status':
          value = formatterType(scope.row.status, this.statusList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    // 自定义列显示
    settingCol (tableColumns) {
      this.$nextTick(() => {
        this.$refs['tableSet'].init(this.tableName, tableColumns)
      })
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat
  },
  computed: {
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    panelShow () {
      let ret = false
      if (this.addOrUpdateVisible) {
        ret = true
      } else if (this.viewVisible) {
        ret = true
      }
      return ret
    }
  },
  components: {
    AddOrUpdate,
    ViewDetail,
    tableSet
  }
}
</script>
