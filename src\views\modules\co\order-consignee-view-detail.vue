<template>
  <div class="add-body panel_body">
    <div class="panel-hd">
      <span v-text="$t('detail')"></span>
    </div>
    <div class="detail-desc">
      <el-form ref="form" label-width="80px">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item :label="$t('coOrderConsignee.consigneeName')">
              <span v-text="dataForm.consigneeName"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderConsignee.consigneeCompany')">
              <span v-text="dataForm.consigneeCompany"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderConsignee.consigneePhone')">
              <span v-text="dataForm.consigneePhone"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderConsignee.consigneeEmail')">
              <span v-text="dataForm.consigneeEmail"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderConsignee.consigneeCountry')">
              <span v-text="dataForm.consigneeCountry"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderConsignee.consigneeProvince')">
              <span v-text="dataForm.consigneeProvince"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderConsignee.consigneeCity')">
              <span v-text="dataForm.consigneeCity"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderConsignee.consigneeDistrict')">
              <span v-text="dataForm.consigneeDistrict"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderConsignee.consigneeAddress')">
              <span v-text="dataForm.consigneeAddress"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderConsignee.consigneePostcode')">
              <span v-text="dataForm.consigneePostcode"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderConsignee.consigneeDoorplate')">
              <span v-text="dataForm.consigneeDoorplate"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderConsignee.consigneeStreet')">
              <span v-text="dataForm.consigneeStreet"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderConsignee.consigneeIdcard')">
              <span v-text="dataForm.consigneeIdcard"></span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div id="cs_FormFooter" class="el-form-footer" slot="footer">
      <el-button @click="backFn">{{ $t('back') }}</el-button>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      dataForm: {
        id: '',
        creator: '',
        createDate: '',
        consigneeName: '',
        consigneeCompany: '',
        consigneePhone: '',
        consigneeEmail: '',
        consigneeCountry: '',
        consigneeProvince: '',
        consigneeCity: '',
        consigneeDistrict: '',
        consigneeAddress: '',
        consigneePostcode: '',
        consigneeDoorplate: '',
        consigneeStreet: '',
        consigneeIdcard: ''
      }
    }
  },
  methods: {
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/co/orderconsignee/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 返回
    backFn () {
      this.visible = false
      this.$emit('backView')
    }
  }
}
</script>
