<template>
  <el-dialog :visible.sync="visible" title="录入材积" :close-on-click-modal="false" width='1100px' :close-on-press-escape="false"
             :show-close='false' :lock-scroll="true" class="location_model" top="10px">
    <areaBox :title="$t('fba.convenientEntry')" :displayPullBack='true' id='areabox'>
      <el-row :gutter="10">
        <el-form :model="dataForm" key='5' ref="quickFillForm" :rules="dataRule">
          <el-col :span='24'>
            <el-row :gutter="10">
              <el-col :span='24'>
                <div class='flex'>
                  <el-form-item :label="$t('fba.boxCount')" label-width="90px" prop='boxCount'>
                    <el-col :span="8">
                      <el-input v-model='dataForm.boxCount' :placeholder="$t('fba.boxCount')" clearable></el-input>
                    </el-col>
                    <el-col :span="6">
                      <el-button size="mini" type='primary' plain @click='changeBoxInfo'> 变更箱数 </el-button>
                    </el-col>
                  </el-form-item>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span='24'>
                <div class='flex'>
                  <el-form-item  label-width="90px" label="行号范围" >
                    <el-col :span="10">
                      <el-input v-model='start' clearable></el-input>
                    </el-col>
                    <el-col class="line" :span="2">-</el-col>
                    <el-col :span="10">
                      <el-input v-model='end' clearable></el-input>
                    </el-col>
                  </el-form-item>
                  <el-form-item prop='packageLength'>
                    <el-input v-model='dataForm.packageLength' placeholder="长(CM)" clearable></el-input>
                  </el-form-item>
                  <el-form-item prop='packageWidth'>
                    <el-input v-model='dataForm.packageWidth' placeholder="宽(CM)" clearable></el-input>
                  </el-form-item>
                  <el-form-item prop='packageHeight'>
                    <el-input v-model='dataForm.packageHeight' placeholder="高(CM)" clearable></el-input>
                  </el-form-item>
                  <el-form-item prop='packageWeight'>
                    <el-input v-model='dataForm.packageWeight' placeholder="重量(KG)" clearable></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-button type='primary' size='mini' plain @click='quickFill'>{{ $t('fba.quickFill') }}</el-button>
                  </el-form-item>
                </div>
              </el-col>
            </el-row>
          </el-col>
        </el-form>
      </el-row>
    </areaBox>
    <div style='border-top: 2px dashed #DCDFE6;margin-top: 10px'>
      <div class="flex_table" ref="tableElm" v-domResize="redraw" id='boxDataFormId'>
        <el-form :model='boxDataForm' ref='boxDataForm'  key='6' :inline-message='true'>
          <el-table  :data='boxDataForm.boxDataList'  :max-height="tableHeight" height="300px"  class='width100 margin_top10' >
            <el-table-column :label="$t('fba.serialNo')" prop='packageSerialNo' type='index' width='50'>
              <template slot-scope="scope">
                <el-form-item :prop="'boxDataList.' + scope.$index + '.packageSerialNo'" >
                  <span v-text="scope.row.packageSerialNo = scope.$index + 1"></span>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop='packageLengthD' :label="$t('fba.length')" min-width='100' header-align='center' align='center' :render-header="addRedStar">
              <template slot-scope="scope">
                <el-form-item :prop="'boxDataList.' + scope.$index + '.packageLengthD'" :rules='boxRule.packageLengthD'>
                  <el-input  v-model="scope.row.packageLengthD"></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop='packageWidthD' :label="$t('fba.width')" min-width='100' header-align='center' align='center'  :render-header="addRedStar">
              <template slot-scope="scope">
                <el-form-item :prop="'boxDataList.' + scope.$index + '.packageWidthD'" :rules='boxRule.packageWidthD'>
                  <el-input  v-model="scope.row.packageWidthD"></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop='packageHeightD' :label="$t('fba.height')" min-width='100' header-align='center' align='center'  :render-header="addRedStar">
              <template slot-scope="scope">
                <el-form-item :prop="'boxDataList.' + scope.$index + '.packageHeightD'" :rules='boxRule.packageHeightD'>
                  <el-input  v-model="scope.row.packageHeightD"></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop='packageWeightD' :label="$t('fba.weight')" min-width='100' header-align='center' align='center' :render-header="addRedStar">
              <template slot-scope="scope">
                <el-form-item :prop="'boxDataList.' + scope.$index + '.packageWeightD'" :rules='boxRule.packageWeightD'>
                  <el-input  v-model="scope.row.packageWeightD"></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column :label="$t('handle')"  min-width='100' header-align='center' align='center' >
              <template slot='header' slot-scope='scope'>
                <span>{{ $t('handle') }}</span>
              </template>
              <template slot-scope='scope'>
                <el-link :underline='false' @click='copyRow(scope.row)'>{{ $t('copy') }}</el-link>
                <popconfirm i18nOperateValue="delete" @clickHandle='deleteRow(scope.$index)'>{{ $t('delete') }}</popconfirm>
              </template>
            </el-table-column>
          </el-table>
          <el-button class='el-icon-plus width100 margin_top10' size='mini' type='primary' plain @click='addRow'>{{ $t('add') }}</el-button>
        </el-form>
      </div>
    </div>
    <template slot="footer">
      <div style='justify-content: center;display: flex;'>
        <el-button @click='closeHandle'>{{ $t('close') }}</el-button>
        <el-button type='primary' :loading='submitLoading' @click='handleSubmit()'>{{ $t('save') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import areaBox from '@/components/areaBox'
import listPage from '@/mixins/listPage'
import { isDecimal3, isDecimal1, isPlusFloat, isPlusInteger2, serialNumberCheck } from '@/utils/validate'
import NP from 'number-precision'
export default {
  mixins: [listPage],
  data () {
    return {
      visible: false,
      autoAppend: false,
      autoGenerateBox: false,
      submitLoading: false,
      customerOrderNo: null,
      start: 1,
      end: 2,
      dataForm: {
        boxCount: '',
        packageSerialNo: '',
        packageLength: '',
        packageHeight: '',
        packageWeight: '',
        packageWidth: ''
      },
      boxDataForm: {
        packageSerialNo: '',
        packageLengthD: '',
        packageWidthD: '',
        packageHeightD: '',
        packageWeightD: '',
        boxDataList: []
      }
    }
  },
  computed: {
    boxRule () {
      const isFloat1 = (rule, value, callback) => {
        if (value && !(isPlusFloat(value) || isPlusInteger2(value))) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value <= 0) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value && !isDecimal1(value)) {
          return callback(new Error('仅保留1位小数'))
        }
        callback()
      }
      const isFloat3 = (rule, value, callback) => {
        if (value && !(isPlusFloat(value) || isPlusInteger2(value))) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value <= 0) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value && !isDecimal3(value)) {
          return callback(new Error('仅保留3位小数'))
        }
        callback()
      }
      return {
        packageLengthD: [
          { required: true, message: this.$t('validate.required'), trigger: 'change' },
          { validator: isFloat1, trigger: 'blur' }
        ],
        packageWidthD: [
          { required: true, message: this.$t('validate.required'), trigger: 'change' },
          { validator: isFloat1, trigger: 'blur' }
        ],
        packageHeightD: [
          { required: true, message: this.$t('validate.required'), trigger: 'change' },
          { validator: isFloat1, trigger: 'blur' }
        ],
        packageWeightD: [
          { required: true, message: this.$t('validate.required'), trigger: 'change' },
          { validator: isFloat3, trigger: 'blur' }
        ]
      }
    },
    dataRule () {
      const isFloat1 = (rule, value, callback) => {
        if (value && !(isPlusFloat(value) || isPlusInteger2(value))) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value && value <= 0) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value && !isDecimal1(value)) {
          return callback(new Error('仅保留1位小数'))
        }
        callback()
      }
      const isFloat3 = (rule, value, callback) => {
        if (value && !(isPlusFloat(value) || isPlusInteger2(value))) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value && value <= 0) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value && !isDecimal3(value)) {
          return callback(new Error('仅保留3位小数'))
        }
        callback()
      }
      const isInteger = (rule, value, callback) => {
        if (value && !isPlusInteger2(value)) {
          return callback(new Error('请输入大于0的正整数'))
        }
        callback()
      }
      const checkSerialNumber = (rule, value, callback) => {
        if (value && !serialNumberCheck(value)) {
          return callback(new Error('只能用数字，逗号，中划线组合'))
        }
        callback()
      }
      return {
        boxCount: [
          { validator: isInteger, trigger: 'blur' }
        ],
        packageSerialNo: [
          { validator: checkSerialNumber, trigger: 'blur' }
        ],
        packageWeight: [
          { validator: isFloat3, trigger: 'blur' }
        ],
        packageHeight: [
          { validator: isFloat1, trigger: 'blur' }
        ],
        packageWidth: [
          { validator: isFloat1, trigger: 'blur' }
        ],
        packageLength: [
          { validator: isFloat1, trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init (packageQty, boxDataList) {
      this.visible = true
      this.submitLoading = false
      this.dataForm.boxCount = packageQty
      this.start = 1
      this.end = packageQty
      if (boxDataList && boxDataList.length > 0) {
        this.boxDataForm.boxDataList = boxDataList
      }
      this.$nextTick(() => {
        if (this.boxDataForm.boxDataList.length === 0) {
          for (let i = 1; i <= this.dataForm.boxCount; i++) {
            let obj = {
              packageLengthD: '',
              packageWidthD: '',
              packageHeightD: '',
              packageWeightD: ''
            }
            this.boxDataForm.boxDataList.push(obj)
          }
        }
      })
    },
    changeBoxInfo () {
      this.$refs.quickFillForm.validateField('boxCount', (error) => {
        if (error) {
          return false
        }
        if (this.boxDataForm.boxDataList && this.boxDataForm.boxDataList.length <= 0) {
          this.$message({
            message: '没有找到可用箱号前缀！请新增一行箱数据或填写客户单号/入库单号作为可用箱号',
            type: 'warning',
            duration: 2500
          })
          return false
        }
        let newBoxDataList = []
        for (let i = 0; i < this.dataForm.boxCount; i++) {
          let obj = {
            packageLengthD: '',
            packageWidthD: '',
            packageHeightD: '',
            packageWeightD: ''
          }
          newBoxDataList.push(obj)
        }
        this.boxDataForm.boxDataList = newBoxDataList
      })
    },
    addRow () {
      let obj = {
        packageLengthD: '',
        packageWidthD: '',
        packageHeightD: '',
        packageWeightD: ''
      }
      this.boxDataForm.boxDataList.push(obj)
      this.dataForm.boxCount = this.boxDataForm.boxDataList.length || 0
    },
    copyRow (row) {
      let obj = {
        ...row
      }
      this.boxDataForm.boxDataList.push(obj)
      this.dataForm.boxCount = this.boxDataForm.boxDataList.length || 0
    },
    deleteRow (index) {
      this.boxDataForm.boxDataList.splice(index, 1)
      this.dataForm.boxCount = this.boxDataForm.boxDataList.length || null
    },
    quickFill () {
      this.$refs.quickFillForm.validate((valid) => {
        if (!valid) {
          this.$message({
            message: '请正确填写需要填充的信息',
            type: 'warning',
            duration: 2000
          })
          return false
        }
        if (!this.start || !this.end || !isPlusInteger2(this.start) || !isPlusInteger2(this.end) ||
            this.start > this.dataForm.boxCount || this.end > this.dataForm.boxCount || this.start > this.end) {
          this.$message({ message: '行数范围无效', type: 'warning', duration: 2000 })
          return false
        }
        this.boxDataForm.boxDataList.forEach((item, curIndex) => {
          if (curIndex >= (this.start - 1) && curIndex <= (this.end - 1)) {
            if (this.dataForm.packageLength) {
              this.$set(item, 'packageLengthD', this.dataForm.packageLength)
            }
            if (this.dataForm.packageWidth) {
              this.$set(item, 'packageWidthD', this.dataForm.packageWidth)
            }
            if (this.dataForm.packageHeight) {
              this.$set(item, 'packageHeightD', this.dataForm.packageHeight)
            }
            if (this.dataForm.packageWeight) {
              this.$set(item, 'packageWeightD', this.dataForm.packageWeight)
            }
          }
        })
      })
    },
    addRedStar(h, { column }) {
      return [h('span', { style: 'color: red' }, '*'), h('span', ' ' + column.label)]
    },
    handleSubmit: debounce(function () {
      this.submitLoading = true
      this.$refs['boxDataForm'].validate(async (valid) => {
        if (!valid) {
          this.$message({
            message: '请把箱信息填写完整',
            type: 'warning',
            duration: 2000,
            onClose: () => {
              this.submitLoading = false
            }
          })
          return false
        }
        let totalWeightD = 0
        for (let item of this.boxDataForm.boxDataList) {
          totalWeightD = NP.plus(totalWeightD, Number.parseFloat(item.packageWeightD))
        }
        this.$emit('inputBoxInfoEmit', this.boxDataForm.boxDataList, this.$naturalNumberFormat(totalWeightD))
        this.closeHandle()
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    closeHandle () {
      this.visible = false
      this.$emit('closeInputBoxInfo')
    }
  },
  components: {
    areaBox
  }
}
</script>

<style lang='scss' scoped>
#areabox ::v-deep .el-form-item {
  padding: 0px 15px !important;
}
#boxDataFormId{
  ::v-deep .el-form-item {
    margin-bottom: 0!important;
  }
}
</style>
