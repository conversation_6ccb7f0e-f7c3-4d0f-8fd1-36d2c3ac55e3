<script>
import { Col } from 'element-ui'
const responsive = {
  1: { xs: 24 },
  2: { xs: 24, sm: 12 },
  3: { xs: 24, sm: 12, md: 8 },
  4: { xs: 24, sm: 12, md: 6 }
}
export default {
  name: 'description',
  components: {
    Col
  },
  props: {
    label: {
      type: String,
      default: 'test',
      required: false
    },
    col: {
      required: false,
      default: 3
    },
    size: {
      type: String,
      required: false,
      default: 'large'
    },
    labelStyle: {
      required: false
    },
    layout: {
      type: String,
      required: false,
      default: 'horizontal'
    }
  },
  render () {
    return (
      <Col class="content_box" {...{ props: typeof this.col === 'number' ? responsive[this.col] : this.col }}>
        <div class="label" {...{ style: this.labelStyle }}>{this.label}</div>
        <div class="content">{this.$slots.default}</div>
      </Col>
    )
  }
}
</script>

<style lang="scss" scoped>
.content_box {
  min-height: 40px;
}
/deep/ .label {
  color: rgba(0, 0, 0, 0.85);
  display: table-cell;
  line-height: 20px;
  margin-right: 8px;
  padding-bottom: 16px;
  white-space: nowrap;

  &:not(:empty):after {
    content: ':';
    margin: 0 8px 0 2px;
    position: relative;
    top: -0.5px;
  }
}

/deep/ .content {
  color: rgba(0, 0, 0, 0.65);
  display: table-cell;
  min-height: 40px;
  // line-height: 24px;
  padding-bottom: 16px;
  word-break: break-all;
  width: 100%;
  &:empty {
    content: ' ';
    height: 40px;
    padding-bottom: 16px;
  }
}
</style>
