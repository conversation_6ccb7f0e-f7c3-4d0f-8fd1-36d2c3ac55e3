<template>
  <div class="aui-card--fill">
    <div class="mod-co__inorder flex_wrap" v-show="!panelShow" ref="listPanel">
      <el-card class="search_box" shadow="never">
        <el-form ref="searchForm" class="form_no_margin" :model="dataForm" @keyup.enter.native="searchHandle()" label-width="90px">
          <el-row  type="flex">
            <el-col :span="8">
              <three-no-input ref="threeNoInput" :orderAreaList='orderAreaList'
                              :customerOrderNo.sync="dataForm.customerOrderNos"
                              :waybillNo.sync="dataForm.waybillNos"
                              :deliveryNo.sync="dataForm.deliveryNos" :autosize="threeNoInputAutoSize" :noSize="200" />
            </el-col>
            <el-col :span="16">
              <el-row :gutter="10">
                <div class="fl width100">
                  <el-col :span="12">
                    <el-form-item :label="$t('coOrder.status')" prop="status">
                      <el-select v-model="dataForm.status" clearable filterable :placeholder="$t('coOrder.status')" >
                        <el-option v-for="item in dict.statusList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="$t('wsComWaybill.createDate')" prop="createDateArray">
                      <el-date-picker v-model="createDateArray" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange" :start-placeholder="$t('startTime')" :end-placeholder="$t('endTime')" style="width: 100%"></el-date-picker>
                    </el-form-item>
                  </el-col>
                </div>
              </el-row>
            </el-col>
            <div class="search_box_btn">
              <el-button type="primary" @click="searchHandle()" icon="el-icon-search">查询</el-button>
              <el-button @click.native="resetHandle()" icon="el-icon-refresh-right">重置</el-button>
            </div>
          </el-row>
        </el-form>
      </el-card>

      <el-card class="flex_tab no_shadow" type="border-card" >
        <el-row class="optBtn_panel">
          <el-col :md="24" class="text-right">
            <!--保留空格符-->
            <el-button v-if="$hasPermission('csm:customerWeight:import')" size="mini" type="primary" plain @click="importHandle()">{{ $t('import') }}</el-button>
            <el-button v-if="$hasPermission('csm:customerWeight:export')" size="mini" type="primary" plain @click="exportHandle()">{{ $t('export') }}</el-button>
          </el-col>
        </el-row>
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <el-table ref="tableData" v-loading="dataListLoading" :data="dataList"  :max-height="tableHeight" @selection-change="dataListSelectionChangeHandle">
            <!-- 动态显示表格 -->
            <el-table-column type="index" width='50' fixed="left"></el-table-column>
            <el-table-column v-for="(item, index) in tableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label" >
              <template slot-scope="scope">
                <div>
                  <div v-if="item.prop === 'createDate'">
                    <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                      <span>{{formatterFn(scope,item.prop)}}</span>
                    </el-tooltip>
                  </div>
                  <div  v-else>
                    {{formatterFn(scope,item.prop)}}
                  </div>
                </div>
              </template>
            </el-table-column>
            <!--<el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="150">
              <template slot="header" slot-scope="scope">
                <span>{{$t('handle')}}</span>
                <el-tooltip :content="$t('tableConfigTooltip')" placement="top">
                  <el-link style="margin-left:10px; font-size:16px" class="el-icon-setting" @click="settingCol(tableColumns)"></el-link>
                </el-tooltip>
              </template>
              <template slot-scope="scope">
                <el-link :underline="false"  @click="viewHandle(scope.row.id)">{{ $t('view') }}</el-link>
              </template>
            </el-table-column>-->
          </el-table>
        </div>
        <el-pagination background :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle" @current-change="pageCurrentChangeHandle">
        </el-pagination>
      </el-card>
    </div>
    <!-- 自定义列显示弹窗 -->
    <table-set ref="tableSet"></table-set>
    <!-- 详情页面 -->
    <viewDetail v-if="viewVisible" ref="viewDetail" @backView="backView"></viewDetail>
  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import listPage from '@/mixins/listPage'
import { formatterType, gtmToLtm, timestampFormat, formatterCodeNativeName, formatterShowName, formatterUser } from '@/filters/filters'
import dictTypeMixins from '@/mixins/dictTypeMixins'
// table 自定义显示
import threeNoInput from '@/components/three-no-input'
import baseDataApi from '@/api'
import baseData from '@/api/baseData'
import { getBeforeDay, getNowDate } from '@/utils/tools'
// table 自定义显示
import tableSet from '@/components/tableSet'

export default {
  mixins: [listPage, mixinViewModule, dictTypeMixins],
  data () {
    return {
      tableColumns: [
        { type: '', width: '150', prop: 'orderId', label: this.$t('wsComWaybill.orderId'), align: 'center', isShow: false, disabled: true },
        { type: '', width: '150', prop: 'customerOrderNo', label: this.$t('wsComWaybill.customerOrderNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'waybillNo', label: this.$t('wsComWaybill.waybillNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'deliveryNo', label: this.$t('wsComWaybill.deliveryNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'logisticsProductCode', label: this.$t('wsComWaybill.logisticsProductCode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'status', label: this.$t('coOrder.status'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'forecastWeightD', label: this.$t('coOrder.forecastWeight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'customerRealWeightD', label: this.$t('coOrderOther.customerRealWeight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'inAdjustWeightD', label: this.$t('wsComWaybill.inAdjustWeight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'volume', label: this.$t('wsComWaybill.volume'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'volumeWeightD', label: this.$t('wsComWaybill.volumeWeight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'exceptionFlag', label: this.$t('wsComWaybill.exceptionFlag'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '130', prop: 'createDate', label: this.$t('wsComWaybill.createDate'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'id', label: this.$t('system.id'), align: 'center', isShow: false, disabled: true }
      ],
      mixinViewModuleOptions: {
        getDataListURL: '/co/order/customerRealWeight/page',
        getDataListIsPage: true,
        getDataListURLOfRequestType: 'post',
        exportURL: '/co/order/customerRealWeight/export'
      },
      dataForm: {
        customerOrderNos: '',
        waybillNos: '',
        deliveryNos: '',
        logisticsProductCode: '',
        status: '',
        createDateFrom: '',
        createDateTo: '',
        customerId: this.$store.state.user.customerId
      },
      orderAreaList: [
        {
          label: '运单号',
          value: 2
        },
        {
          label: '客户单号',
          value: 1
        },
        {
          label: '派送单号',
          value: 3
        }
      ],
      // 数据字典
      dict: {
        statusList: [],
        objectTypeList: [],
        exceptionFlagList: []
      },
      createDateArray: [getBeforeDay(7), getNowDate()],
      baseData: {
        logisticsProductByParamsList: []
      }
    }
  },
  watch: {
    createDateArray: {
      handler (value, oldName) {
        if (value !== undefined && value !== '' && value !== null) {
          this.dataForm.createDateFrom = timestampFormat(value[0])
          this.dataForm.createDateTo = timestampFormat(value[1])
        } else {
          this.createDateArray = [getBeforeDay(7), getNowDate()]
        }
      },
      // immediate: true 第一次初始化的时候会被执行 immediate: false 第一次初始化的时候不会被执行
      immediate: true
    }
  },
  created () {
    // 获取基础数据
    this.getBaseData()
    this.getDict()
  },
  methods: {
    async getDict () {
      this.dict.statusList = await this.getDictTypeList('OrderStatus')
      this.dict.exceptionFlagList = await this.getDictTypeList('yesOrNo')
    },
    async getBaseData () {
      this.baseData.logisticsProductByParamsList = await baseData(baseDataApi.listAllByCurrent).catch(() => {})
    },
    searchHandle () {
      let initFlag = this.$refs.threeNoInput.setValue()
      if (initFlag) {
        this.queryPageByParam()
      }
    },
    resetHandle () {
      this.createDateArray = [getBeforeDay(7), getNowDate()]
      this.$refs.threeNoInput.clearValue()
      this._resetForm('searchForm')
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'logisticsProductCode':
          value = formatterCodeNativeName(scope.row.logisticsProductCode, this.baseData.logisticsProductByParamsList)
          break
        case 'status':
          value = formatterType(scope.row.status, this.dict.statusList)
          break
        case 'exceptionFlag':
          value = formatterType(scope.row.exceptionFlag, this.dict.exceptionFlagList)
          break
        case 'volume':
          value = scope.row.lengthD + '*' + scope.row.widthD + '*' + scope.row.heightD
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    // 自定义列显示
    settingCol (tableColumns) {
      this.$nextTick(() => {
        this.$refs['tableSet'].init(this.tableName, tableColumns)
      })
    },
    importHandle () {
      this.$router.push({ name: 'csm-customer-weight-update' })
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat,
    formatterCodeNativeName,
    formatterShowName,
    formatterUser
  },
  computed: {
    batchInterceptDataRule () {
      return {
        opeareteDescription: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    },
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    panelShow () {
      let ret = false
      if (this.addOrUpdateVisible) {
        ret = true
      } else if (this.viewVisible) {
        ret = true
      }
      return ret
    },
    threeNoInputAutoSize () {
      this.$nextTick(() => {
        this.$refs.threeNoInput.resizeTextarea()
      })
      if (this.searchBoxShow) {
        return { minRows: 5, maxRows: 7 }
      } else {
        return { minRows: 7, maxRows: 7 }
      }
    }
  },
  components: {
    // AddOrUpdate,
    threeNoInput,
    tableSet
  }
}
</script>
