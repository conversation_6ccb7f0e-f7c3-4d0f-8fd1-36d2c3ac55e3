<template>
  <el-dialog
    v-if="this.noticeObj.count > 0"
    width="70%"
    :visible.sync="visible"
    :title="dataForm.title"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    :append-to-body="true">
    <template slot="title">
      <el-row>
        <el-col :md="12" >
          <h2>{{ dataForm.title }}</h2>
        </el-col>
<!--        <el-col :md="12" class="text-right" style="padding-top: 25px; padding-right:100px;">-->
<!--          {{ `${dataForm.creatorName} 于 ${dataForm.publishDate} 发布` }}-->
<!--        </el-col>-->
      </el-row>
    </template>
    <div class="el-dialog-div">
      <div v-html="dataForm.content"></div>
    </div>
    <template slot="footer">
      <el-link v-if="noticeObj.index >= noticeObj.count - 1" style="margin-right: 20px;" @click="showMoreNotice">更多通知...</el-link>
      <el-button v-if="noticeObj.index > 0" type="info" icon="el-icon-arrow-left" @click="prevNotice">上一条</el-button>
      <el-button v-if="noticeObj.index < noticeObj.count - 1" type="primary" @click="nextNotice">下一条<i class="el-icon-arrow-right el-icon--right"></i></el-button>
      <el-button v-if="noticeObj.index >= noticeObj.count - 1" type="success" icon="el-icon-close" @click="closeFn">关闭</el-button>
    </template>
  </el-dialog>
  <el-dialog
    v-else
    width="60%"
    :visible.sync="visible"
    :title="dataForm.title"
    :close-on-click-modal="true"
    :close-on-press-escape="true"
    :show-close="true"
    :append-to-body="true">
    <template slot="title">
      <el-row>
        <el-col :md="24" style="text-align:center">
          <h2>暂无未读消息</h2>
          <el-link @click="showMoreNotice">点击查看更多...</el-link>
        </el-col>
      </el-row>
    </template>
  </el-dialog>
</template>

<script>
import { formatterType, gtmToLtm, timestampFormat } from '@/filters/filters'
import { mapState } from 'vuex'
export default {
  data () {
    return {
      visible: false,
      type: 1,
      dataForm: {
        id: '',
        title: '',
        type: '',
        publishDate: '',
        content: '',
        creatorName: ''
      }
    }
  },
  computed: {
    ...mapState({ noticeObj: 'notice' })
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        Promise.all([
          this.getInfo()
        ]).then(() => {
          this.getUnreadNotices()
        })
      })
    },
    // 获取未读的通知
    async getUnreadNotices () {
      await this.$http.get(`/message/notice/getUnreadList/${this.type}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.noticeObj.list = res.data
        this.noticeObj.count = res.data.length
        this.noticeObj.unreadCount = res.data.length
        this.noticeObj.unreadSet = new Set([...res.data])
      }).catch(() => {})
    },
    // 获取信息
    getInfo () {
      if (this.noticeObj.count === 0) {
        return
      }
      let id = this.noticeObj.list[this.noticeObj.index].id
      this.$http.get(`/message/notice/` + id).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
        // 标记已读
        this.readNotice()
      }).catch(() => {})
    },
    // 查看更多通知
    showMoreNotice () {
      this.closeFn()
      this.$router.replace({ name: 'message-notice-receiver' })
    },
    // 标记已读
    readNotice () {
      let noticeId = this.noticeObj.list[this.noticeObj.index].id
      this.$http.get(`/message/notice/read/${noticeId}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        // 未阅读集合
        this.noticeObj.unreadSet.delete(noticeId)
        this.noticeObj.unreadCount = this.noticeObj.unreadSet.size
        // console.log('noticeObj.unreadSet:' + [...this.noticeObj.unreadSet])
      }).catch(() => {})
    },
    // 上一条
    prevNotice () {
      if (this.noticeObj.index > 0) {
        this.noticeObj.index--
        this.getInfo()
      }
    },
    // 下一条
    nextNotice () {
      if (this.noticeObj.index < this.noticeObj.count - 1) {
        this.noticeObj.index++
        this.getInfo()
      }
    },
    // 关闭
    closeFn () {
      this.visible = false
      // 重置未读消息对象
      this.noticeObj.index = 0
      this.noticeObj.unreadCount = 0
      this.noticeObj.unreadSet = new Set()
      this.noticeObj.count = 0
      this.noticeObj.list = []
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat
  }
}
</script>

<style lang="scss" scoped>
 .el-dialog-div {
    height: 50vh;
    overflow: auto;
  }
</style>
