<template>
  <div class="aui-card--fill">
    <div class="mod-co__inorder flex_wrap" v-show="!panelShow" ref="listPanel">
      <el-card class="search_box" shadow="never">
        <el-form ref="searchForm" class="form_no_margin" :model="dataForm" @keyup.enter.native="searchHandle()" label-width="100px">
          <el-row :gutter="10" type="flex">
            <el-col :span="8">
              <three-no-input ref="threeNoInput" :customerOrderNo.sync="dataForm.customerOrderNos" :waybillNo.sync="dataForm.waybillNos" :deliveryNo.sync="dataForm.deliveryNos" :autosize="{ minRows: 3, maxRows: 3}" :noSize="200" />
            </el-col>
            <el-col :span="16">
              <el-row :gutter="10">
                <div class="fl width100">
                  <el-col :span="12">
                    <el-form-item :label="$t('csmWorkOrder.workOrderNo')" prop="workOrderNo">
                      <el-input v-model="dataForm.workOrderNo" :placeholder="$t('csmWorkOrder.workOrderNo')" clearable ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="$t('csmWorkOrder.status')" prop="status">
                      <el-select filterable v-model="dataForm.status" :placeholder="$t('csmWorkOrder.status')" clearable>
                        <el-option v-for="(item, index) in dict.statusList" :key="index" :label="item.dictName" :value="item.dictValue"/>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </div>
                <el-col :span="12">
                  <el-form-item :label="$t('csmWorkOrder.problemStatement')" prop="problemStatement">
                    <el-input v-model="dataForm.problemStatement" :placeholder="$t('csmWorkOrder.problemStatement')" clearable ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t('csmWorkOrder.createDate')" prop="createDateArray">
                    <el-date-picker v-model="createDateArray" value-format="yyyy-MM-dd hh:mm:ss" type="datetimerange" :start-placeholder="$t('startTime')" :end-placeholder="$t('endTime')" style="width: 100%"></el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <div class="search_box_btn no_more">
              <el-button type="primary" @click="searchHandle()" icon="el-icon-search">查询</el-button>
              <el-button @click.native="resetHandle()" icon="el-icon-refresh-right">重置</el-button>
            </div>
          </el-row>
        </el-form>
      </el-card>

      <el-card class="flex_tab no_shadow" type="border-card" >
        <el-row class="optBtn_panel">
<!--          <el-col :md="12">-->
<!--            <el-button size="mini" @click="deleteHandle()">{{ $t('delete') }}</el-button>-->
<!--            <el-button size="mini" >{{ $t('audit') }}</el-button>-->
<!--            &lt;!&ndash;保留空格符&ndash;&gt;-->
<!--          </el-col>-->
          <el-col :md="24" class="text-right">
            <!--v-if="$hasPermission('csm:workorder:save')"-->
            <el-button size="mini" v-if="$hasPermission('csm:workOrderMine:add')" type="primary" plain @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
          </el-col>
        </el-row>
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <el-table v-loading="dataListLoading" :data="dataList"  @selection-change="dataListSelectionChangeHandle" :max-height="tableHeight">
            <!-- 动态显示表格 -->
            <el-table-column type="selection" width='50' fixed="left"></el-table-column>
            <el-table-column v-for="(item, index) in tableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
              <template slot-scope="scope">
                <div>
                  <div v-if="item.prop === 'createDate'">
                    <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                      <span>{{formatterFn(scope,item.prop)}}</span>
                    </el-tooltip>
                  </div>
                  <div class="text-overflow" v-else>
                    {{formatterFn(scope,item.prop)}}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="150">
              <template slot="header" slot-scope="scope">
                <span>{{$t('handle')}}</span>
                <el-tooltip :content="$t('tableConfigTooltip')" placement="top">
                  <el-link style="margin-left:10px; font-size:16px" class="el-icon-setting" @click="settingCol(tableColumns)"></el-link>
                </el-tooltip>
              </template>
              <template slot-scope="scope">
                <el-link :underline="false" v-if="$hasPermission('csm:workOrderMine:view')"  @click="viewHandle(scope.row.id)">{{ $t('view') }}</el-link>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination background :current-page="page" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="pageSizeChangeHandle" @current-change="pageCurrentChangeHandle">
        </el-pagination>
      </el-card>
    </div>
    <!-- 自定义列显示弹窗 -->
    <table-set ref="tableSet"></table-set>
    <!-- 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="searchHandle" @cancelAddOrUpdate="cancelAddOrUpdate"></add-or-update>
    <!-- 详情页面 -->
    <viewDetail v-if="viewVisible" ref="viewDetail" @backView="backView"></viewDetail>
  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import listPage from '@/mixins/listPage'
import { formatterType, gtmToLtm, timestampFormat, formatterName, formatterUser } from '@/filters/filters'
import dictTypeMixins from '@/mixins/dictTypeMixins'
import threeNoInput from '@/components/three-no-input'
// table 自定义显示
import tableSet from '@/components/tableSet'
import AddOrUpdate from './work-order-mine-add-or-update-list'
import ViewDetail from './work-order-mine-view-detail'
import baseDataApi from '@/api'
import baseData from '@/api/baseData'
import { getBeforeMonth, getNowDate } from '@/utils/tools'
import DateUtil from '@/utils/date'

export default {
  mixins: [listPage, mixinViewModule, dictTypeMixins],
  data () {
    return {
      tableColumns: [
        { type: '', width: '180', prop: 'workOrderNo', label: this.$t('csmWorkOrder.workOrderNo'), align: 'center', isShow: true, disabled: false },
        /* { type: '', width: '150', prop: 'creatorName', label: this.$t('csmWorkOrder.creatorName'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'creatorType', label: this.$t('csmWorkOrder.creatorType'), align: 'center', isShow: true, disabled: false }, */
        { type: '', width: '150', prop: 'problemStatement', label: this.$t('csmWorkOrder.problemStatement'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'workOrderTypeId', label: this.$t('csmWorkOrder.workOrderTypeId'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'status', label: this.$t('csmWorkOrder.status'), align: 'center', isShow: true, disabled: false },
        // { type: '', width: '150', prop: 'companyCustomerServiceStaffId', label: this.$t('csmWorkOrder.companyCustomerServiceStaffId'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'acceptServiceId', label: this.$t('csmWorkOrder.acceptServiceId'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'createDuration', label: this.$t('csmWorkOrder.createDuration'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'createDate', label: this.$t('csmWorkOrder.createDate'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'id', label: this.$t('system.id'), align: 'center', isShow: false, disabled: true }
      ],
      mixinViewModuleOptions: {
        getDataListURL: '/csm/workorder/page',
        getDataListIsPage: true,
        deleteURL: '/csm/workorder',
        deleteIsBatch: true
      },
      dataForm: {
        customerOrderNos: '',
        waybillNos: '',
        deliveryNos: '',
        workOrderNo: '',
        status: '',
        problemStatement: '',
        createDateFrom: '',
        createDateTo: '',
        workOrderTypeId: '',
        creator: this.$store.state.user.customerId
      },
      activeName: 'all',
      tableName: 'csm-workorder',
      createDateArray: [getBeforeMonth(1), getNowDate()],
      dict: {
        statusList: [],
        creatorTypeList: []
      },
      baseData: {
        workOrderTypeParentList: [],
        workOrderTypeList: [],
        creatorList: [],
        assigneeList: []
      }
    }
  },
  watch: {
    createDateArray: {
      handler (value, oldName) {
        this.dataForm.createDateFrom = timestampFormat(value[0])
        this.dataForm.createDateTo = timestampFormat(value[1])
      },
      // immediate: true 第一次初始化的时候会被执行 immediate: false 第一次初始化的时候不会被执行
      immediate: true
    }
  },
  created () {
    // 基础资料
    this.getBaseData()
    // 数据字典
    this.getDict()
  },
  methods: {
    async getDict () {
      this.dict.statusList = await this.getDictTypeList('CsmWorkOrderStatus')
      this.dict.creatorTypeList = await this.getDictTypeList('CsmWorkOrderCreatorType')
    },
    async getBaseData () {
      this.baseData.workOrderTypeParentList = await baseData(baseDataApi.workOrderTypeList, { level: 1 }).catch(() => {})
      this.baseData.workOrderTypeList = await baseData(baseDataApi.workOrderTypeList, { level: 2 }).catch(() => {})
      // 用户信息
      this.baseData.creatorList = await baseData(baseDataApi.userList).catch(() => {})
      // 用户信息
      this.baseData.assigneeList = await baseData(baseDataApi.userList).catch(() => {})
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'status':
          value = formatterType(scope.row.status, this.dict.statusList)
          break
        case 'creatorType':
          value = formatterType(scope.row.creatorType, this.dict.creatorTypeList)
          break
        case 'workOrderTypeId':
          value = formatterName(scope.row.workOrderTypeId, this.baseData.workOrderTypeList) || formatterName(scope.row.workOrderTypeId, this.baseData.workOrderTypeParentList)
          break
        case 'acceptServiceId':
          value = formatterUser(scope.row.acceptServiceId, this.baseData.assigneeList)
          break
        case 'createDuration':
          value = DateUtil.getDifferHour(Date.now(), scope.row.createDate)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    // 自定义列显示
    settingCol (tableColumns) {
      this.$nextTick(() => {
        this.$refs['tableSet'].init(this.tableName, tableColumns)
      })
    },
    searchHandle () {
      let initFlag = this.$refs.threeNoInput.setValue()
      if (initFlag) {
        this.queryPageByParam()
      }
    },
    resetHandle () {
      this.createDateArray = [getBeforeMonth(2), getNowDate()]
      this.$refs.threeNoInput.clearValue()
      this._resetForm('searchForm')
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat,
    formatterName,
    formatterUser
  },
  computed: {
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    panelShow () {
      let ret = false
      if (this.addOrUpdateVisible) {
        ret = true
      } else if (this.viewVisible) {
        ret = true
      }
      return ret
    }
  },
  components: {
    AddOrUpdate,
    ViewDetail,
    tableSet,
    threeNoInput
  }
}
</script>
