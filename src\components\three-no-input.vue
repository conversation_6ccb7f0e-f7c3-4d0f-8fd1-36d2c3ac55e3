<template>
  <div class="orderArea">
    <el-form-item class="areaBox" prop="no" label-width='0'>
      <el-input ref="textarea" type="textarea" :placeholder="`最多支持查询${noSize}个单号.根据单号查询会忽略时间范围`" v-model="dataForm.no" :autosize="autosize" resize="none"></el-input>
    </el-form-item>
    <ul class="orderAreaTab">
      <li class="tabItem" :class="orderAreaVal === item.value? 'actived':''" v-for="(item, index) in orderAreaArray" :key="index" @click="areaTabClick(item)">{{item.label}}</li>
    </ul>
  </div>
</template>

<script>
import { includes, isArray } from 'lodash'

export default {
  props: {
    customerOrderNo: {
      type: String
    },
    waybillNo: {
      type: String
    },
    deliveryNo: {
      type: String
    },
    postalTrackingNo: {
      type: String
    },
    autosize: {},
    noSize: {
      default: 500
    },
    hideItem: { // 隐藏某项
      type: Array,
      default: () => {
        return [4]
      }
    },
    orderAreaList: {
      type: Array
    },
    label: {
      type: String,
      default: 'threeNoInput.no'
    }
  },
  data () {
    return {
      dataForm: {
        no: ''
      },
      orderAreaArray: [],
      orderAreaVal: 2
    }
  },
  created () {
    // console.log('this.orderAreaList', this.orderAreaList)
    this.orderAreaArray = this.orderAreaList
    if (isArray(this.hideItem)) {
      let _self = this
      this.orderAreaArray = this.orderAreaList.filter((item) => {
        return !includes(_self.hideItem, item.value, 0)
      })
      this.orderAreaVal = this.orderAreaList[0].value
    } else if (this.hideItem) {
      this.orderAreaArray = this.orderAreaList.filter((item) => {
        return item.value !== this.hideItem
      })
      this.orderAreaVal = this.orderAreaList[0].value
    }
  },
  methods: {
    resizeTextarea () {
      this.$nextTick(() => {
        this.$refs.textarea.resizeTextarea()
      })
    },
    setValue () {
      let orderCodeArray = []
      if (this.dataForm.no !== null) {
        this.dataForm.no = this.dataForm.no.replace(/\n|\s+/g, ',').trim()
        this.dataForm.no = this.dataForm.no.replace(/,$/, '')
        orderCodeArray = this.dataForm.no.split(',')
      }
      if (orderCodeArray.length > this.noSize) {
        this.$message.warning(this.$t('threeNoInput.outMessage', { 'size': `${this.noSize}` }))
        return false
      }
      switch (this.orderAreaVal) {
        case 1:
          this.$emit('update:customerOrderNo', this.dataForm.no)
          this.$emit('update:waybillNo', '')
          this.$emit('update:deliveryNo', '')
          break
        case 2:
          this.$emit('update:customerOrderNo', '')
          this.$emit('update:waybillNo', this.dataForm.no)
          this.$emit('update:deliveryNo', '')
          break
        case 3:
          this.$emit('update:customerOrderNo', '')
          this.$emit('update:waybillNo', '')
          this.$emit('update:deliveryNo', this.dataForm.no)
          break
        case 4:
          this.$emit('update:customerOrderNo', '')
          this.$emit('update:waybillNo', '')
          this.$emit('update:deliveryNo', '')
          this.$emit('update:postalTrackingNo', this.dataForm.no)
          break
      }
      return true
    },
    clearValue () {
      this.orderAreaVal = 2
      this.dataForm.no = ''
      this.$emit('update:customerOrderNo', '')
      this.$emit('update:waybillNo', '')
      this.$emit('update:deliveryNo', '')
      this.$emit('update:postalTrackingNo', '')
    },
    areaTabClick (item) {
      this.orderAreaVal = item.value
    }
  }
}
</script>

<style lang="scss">
  .orderArea {
    .areaBox {
      .el-form-item__label {
        //max-width: 80px;
        margin-top: -25px;
      }
      .el-textarea__inner {
        border-radius: 0;
      }
      .el-form-item__content {
        //margin-left: 80px !important;
      }
    }
  }
</style>
<style lang="scss" scoped>
  .orderArea{
    position: relative;
    min-width: 280px;
    .areaBox{
      padding-top: 25px;
      .el-form-item__label{
        //max-width: 80px;
        margin-top: -25px;
      }
      .el-textarea__inner{
        border-radius: 0;
      }
      .el-form-item__content{
        //margin-left:80px;
      }
    }
    .orderAreaTab {
      position: absolute;
      top: 1px;
      right: 0;
      display: flex;
      width: 100%;
      list-style: none;
      padding: 0;
      margin: 0 0px;
      .tabItem {
        flex: 1;
        min-width: fit-content;
        text-align: center;
        border:1px solid #DCDFE6;
        height: 25px;
        line-height: 25px;
        margin-left: 0px;
        cursor: pointer;
        &:first-child{
          margin-left: 0px;
        }
        &.actived, &:hover{
          background: $--color-primary;
          border-left-color: #DCDFE6;
          color: #fff;
        }
      }
    }
  }
</style>
