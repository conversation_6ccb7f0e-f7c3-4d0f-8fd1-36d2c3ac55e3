<template>
  <el-dialog :visible.sync="visible" :title="$t('template.detail')" :close-on-click-modal="false" :modal-append-to-body="false" width='90%' top="5px"
             :close-on-press-escape="false" :show-close='false' :lock-scroll="true" class="location_model" >
    <el-card type="border-card" >
      <el-row class="optBtn_panel">
        <el-divider content-position="left" v-show="requiredFieldList.length>0">
          <span style="margin-left: 20px;">模板必须包含以下所有必填项</span>
          <el-popover
            placement="right-start"
            :title="$t('description')"
            width="275"
            v-model="showTips"
            trigger="hover">
            <div class="popover-content" :style="highlight===0 ? 'color: #ebb563;font-size:15px;' : ''">1.以下必填项都需包含在模板中才可保存成功。</div>
            <div class="popover-content" :style="highlight===1 ? 'color: #ebb563;font-size:15px;' : ''">2.可点击《生成必填项》，快捷地把未选中的必填项追加到末端。</div>
            <div class="popover-content" :style="highlight===2 ? 'color: #ebb563;font-size:15px;' : ''">3.单击选中列为"灰色并加宽"时，可把 从excel复制的表头直接粘贴(Ctrl+V)到列上(无需粘贴到输入框),本页面列数不足的时候会自动添加列。</div>
            <el-button type="text" style="margin-left: 5px;font-size: 15px;color: #ebb563" icon="el-icon-question" slot="reference">提示(鼠标悬停查看)</el-button>
          </el-popover>
        </el-divider>
        <el-col>
          <el-card v-show="requiredFieldList.length>0">
            <el-row v-for="(objItem, objIndex) in requiredFieldList" :key="objIndex">
              <el-col :span="2">
                <span v-text="objItem.objectName + ':'"></span>
              </el-col>
              <el-col :span="22">
                <el-col :span="6" v-for="(item, index) in objItem.fieldList" :key="index">
                  <span style="color: grey" v-text="item.fullName"></span>
                </el-col>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <div class="flex_table" ref="tableElm">
            <el-form :model='dataForm' ref="dataForm" key='2' :inline-message='true'>
              <el-table v-loading="dataListLoading" :data="dataArr" :border="true" row-key :header-cell-style="headerCellStyle" :cell-style="cellStyle" @header-click="headerClick" @cell-click="cellClick">
                <!-- 动态显示表格 -->
                <el-table-column width='50' fixed="left">
                  <template slot-scope="scope">
                    <span v-text="title[scope.$index]"></span>
                  </template>
                </el-table-column>
                <el-table-column v-for="item in tableColumns" :column-key="item.key" :index="item.index" :key="item.key" :type="item.type" header-align="center" :align="item.align" :width="item.minWidth" :label="item.showLabel">
                  <template #header>
                    <div>
<!--                                              <span style="text-align: center" v-text="getLabelByIndex(item.index)"></span>-->
                      <span>
                          <el-link v-show="tableColumns.length>1" style="margin-left:5px;font-size:12px;line-height: 12px;" @click="deleteCol(item.index)">删除列</el-link>
                          <el-link style="margin-left:2px;font-size:12px;line-height: 12px;" @click="addCol(item.index, true)">添加列</el-link>
                        </span>
                    </div>
                  </template>
                  <template slot-scope="scope">
                    <div>
                      <el-form-item :prop="item.key + item.index">
                          <span v-if="scope.$index===1">
                            <!--初始不渲染select-->
                            <el-input v-if="!showSelectList.includes(item.value)" :placeholder="formatterFn(scope.row[item.value])" @mouseover.native="showSelect(item.value)" size="mini"></el-input>
                            <el-select v-if="showSelectList.includes(item.value)" v-model="scope.row[item.value]" clearable filterable @change="setTableValue(scope.row[item.value], item.value)" size="mini">
                              <el-option-group v-for="group in excelFieldListOptions" :key="group.objectName" :label="group.objectName">
                                <el-option v-for="item in group.fieldList" :key="item.fullFieldName" :label="item.fullName" :value="item.fullFieldName" :disabled="item.disabled">
                                  <template #default>
                                    <span :style="item.allowEmpty || item.disabled ? '' : 'color: #ebb563;'" v-text="item.fullName"></span>
                                    <span v-if="!item.allowEmpty" style="margin-left: 10px;color: #ebb563;">(必填)</span>
                                  </template>
                                </el-option>
                              </el-option-group>
                            </el-select>
                          </span>
                        <span v-else-if="scope.$index===0">
                            <el-input v-model="scope.row[item.value]" clearable size="mini" resize="both" @focus="inputFocusHandle" @blur="inputBlurHandle"></el-input>
                          </span>
                        <span v-else>
                            <span style="font-size: 12px;line-height: 12px;" v-text="scope.row[item.value]" ></span>
                          </span>
                      </el-form-item>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </el-form>
          </div>
        </el-col>
      </el-row>
    </el-card>
    <template slot="footer">
      <el-button size="mini" type="primary" v-if="requiredFieldList.length>0" :loading="buttonLoading" @click="fullRequired" @mouseover.native="showTipsFu(true, 1)" @mouseout.native="showTipsFu(false, 1)">{{ $t('diyimport.fullRequired')}}</el-button>
      <el-button size="mini" type="primary" :loading="buttonLoading" @click="dataFormSubmitHandle()" @mouseover.native="showTipsFu(true, 0)" @mouseout.native="showTipsFu(false, 0)">{{ $t('confirm') }}</el-button>
      <el-button @click="backFn">{{ $t('back') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import api from '@/api'
import baseData from '@/api/baseData'
export default {
  data() {
    return {
      visible: false,
      buttonLoading: false,
      columnNumber: 0,
      selectCol: -2,
      dataListLoading: false,
      copyLoading: false,
      showTips: false,
      inputFocus: false,
      highlight: 2,
      importFieldMap: new Map(),
      dataForm: {
        moduleId: '',
        mainObjectName: ''
      },
      excelFieldList: [],
      showSelectList: [],
      dataList: [
        [], // 表头
        [], // 字段
        [], // 备注
        [] // 类型
      ],
      tableColumns: [
      ],
      title: ['表头', '字段', '备注'],
      labelNameArr: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
    }
  },
  created() {
    this.getBaseData()
    this.listenerPaste()
  },
  beforeDestory () {
    document.removeEventListener('keydown', this.handleEvent)
  },
  activated () {
    document.addEventListener('keydown', this.handleEvent)
  },
  methods: {
    listenerPaste () {
      this.$nextTick(() => {
        // 监听 ctrl+v键盘事件
        document.addEventListener('paste', (event) => {
          // 获取解析 粘贴的文本
          let text = (event.clipboardData || window.clipboardData).getData('text')
          if (this.selectCol !== -2) {
            this.fullByExcelCopy(text, this.selectCol)
          }
        })
      })
    },
    async getBaseData () {
      // urlDTO对应的字段
      baseData(api.getExcelImportField + this.dataForm.mainObjectName).then(res => {
        this.excelFieldList = res
        this.excelFieldList.forEach((v, i) => {
          this.importFieldMap.set(v.fullFieldName, v)
        })
      })
    },
    init () {
      this.visible = true
      this.getBaseData()
      this.dataList = [[], [], [], []]
      this.tableColumns = []
      this.showSelectList = []
      this.columnNumber = 0
      this.selectCol = -2
      this.getInfo()
    },
    // 获取信息
    getInfo () {
      this.dataListLoading = true
      this.$http.get(`/co/setdiyimportmoduledetail/${this.dataForm.moduleId}`).then(({ data: res }) => {
        this.dataListLoading = false
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        if (res.data.length > 0) {
          res.data.forEach((v, i) => {
            this.addCol(i, false)
            this.addRow(v.fieldName, v.fieldValue, '', v.objectType)
            this.setTableValue(v.objectType + '.' + v.fieldValue, i)
          })
        } else {
          this.addCol(-1, true)
        }
        if (res.data.length <= 0) {
          this.showTipsFu(true, 2)
          setTimeout(() => {
            this.showTipsFu(false, 2)
          }, 6000)
        }
      }).catch(() => {})
    },
    // 按列添加行数据
    addRow(fieldName, fieldValue, memo, objectType, index) {
      if (fieldValue !== '' && objectType !== '') {
        fieldValue = objectType + '.' + fieldValue
      }
      if (index === undefined) {
        this.dataList[0].push(fieldName)
        this.dataList[1].push(fieldValue)
        this.dataList[2].push(memo)
        this.dataList[3].push(objectType)
      } else {
        this.dataList[0].splice(index, 0, fieldName)
        this.dataList[1].splice(index, 0, fieldValue)
        this.dataList[2].splice(index, 0, memo)
        this.dataList[3].splice(index, 0, objectType)
      }
    },
    // 添加列
    addCol(index, addRows) {
      if (addRows) {
        this.addRow('', '', '', '')
      }
      let labelName = this.getLabelByIndex(this.tableColumns.length)
      let col = { prop: labelName, label: labelName, showLabel: labelName, key: this.columnNumber + '', value: this.columnNumber, index: (index + 1), minWidth: '120', maxWidth: '200', type: '', align: 'center', isShow: true, disabled: false }
      this.tableColumns.splice(index + 1, 0, col)
      this.changeTableColIndex()

      this.columnNumber = this.columnNumber + 1
    },
    deleteCol(index) {
      if (this.tableColumns.length > 1) {
        this.tableColumns.splice(index, 1)
        this.changeTableColIndex()
      } else {
        this.$message.error('最少要保留一列')
      }
      // this.$forceUpdate()
    },
    changeTableColIndex() {
      for (let i = 0; i < this.tableColumns.length; i++) {
        let obj = this.tableColumns[i]
        this.$set(obj, 'index', i) // 更新元素属性值
        this.$set(this.tableColumns, i, obj) // 更新元素值
      }
    },
    getLabelByIndex (index) {
      let i = parseInt(index / this.labelNameArr.length)
      let j = index % this.labelNameArr.length
      let labelName = ''
      if (i > 0) {
        labelName += this.labelNameArr[i - 1]
      }
      labelName += this.labelNameArr[j]
      return labelName
    },
    // 返回
    backFn () {
      this.visible = false
      this.$emit('backView')
    },
    // 二维数组转化为对象，提交到后台用
    arrayToObject() {
      let objList = []
      for (let tableIndex = 0; tableIndex < this.tableColumns.length; tableIndex++) {
        let tableColumn = this.tableColumns[tableIndex]
        let index = tableColumn.value
        let obj = {
          fieldName: this.dataList[0][index],
          fieldValue: this.dataList[1][index],
          memo: this.dataList[2][index],
          objectType: this.dataList[3][index],
          moduleId: this.dataForm.moduleId,
          mainObjectName: this.dataForm.mainObjectName,
          serialNo: tableIndex
        }
        objList.push(obj)
      }
      return objList
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      if (this.requiredFieldList.length>0) {
        return this.$message.error('模板需包含所有必填字段才可保存')
      }
      this.buttonLoading = true
      let tableList = this.arrayToObject()
      this.$http['post']('/co/setdiyimportmoduledetail', tableList).then(({ data: res }) => {
        this.buttonLoading = false
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 500,
          onClose: () => {
            this.visible = false
            this.$emit('refreshDataList')
          }
        })
      }).catch(() => {
        this.buttonLoading = false
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    // 设置表单值
    setTableValue (row, index) {
      let exportFieldObj = this.importFieldMap.get(row)
      if (this.dataList[0][index] === '' || this.dataList[0][index] === undefined) {
        // 将表头名设置为字段名
        this.$set(this.dataList[0], index, exportFieldObj.fullName.split('-')[1])
      }
      this.$set(this.dataList[2], index, exportFieldObj === undefined ? '' : exportFieldObj.describe)
      this.dataList[3][index] = exportFieldObj === undefined ? '' : exportFieldObj.typeEnumValue
    },
    showSelect (index) {
      this.showSelectList.push(index)
    },
    // 检验是否复制
    fullByExcelCopy (text, startIndex) {
      let textArr = text.split('\t')
      if (textArr.length > 0) {
        this.copyLoading = true
        for (let index = 0; index < textArr.length; index++) {
          let colIndex = startIndex + index
          let colTableTitle = this.dataList[0][colIndex]
          if (colTableTitle !== undefined) {
            this.$set(this.dataList[0], colIndex, textArr[index])
            this.$set(this.dataList[1], colIndex, '')
            this.$set(this.dataList[2], colIndex, '')
            this.$set(this.dataList[3], colIndex, '')
          } else {
            this.addCol(colIndex, false)
            this.addRow(textArr[index], '', '', '')
          }
        }
        this.copyLoading = false
      }
    },
    // 填充必填列
    fullRequired() {
      this.excelFieldList.forEach((item) => {
        if (!item.allowEmpty && !this.dataList[1].includes(item.fullFieldName)) {
          let lastColIndex = this.dataList[1].length - 1
          if ((this.dataList[0][lastColIndex] === '' || this.dataList[0][lastColIndex] === undefined) && (this.dataList[1][lastColIndex] === '' || this.dataList[1][lastColIndex] === undefined)) {
            this.$set(this.dataList[1], lastColIndex, item.fullFieldName)
          } else {
            this.addCol(lastColIndex, false)
            this.addRow('', item.fullFieldName, '', '')
            lastColIndex = this.dataList[1].length - 1
          }
          this.setTableValue(item.fullFieldName, lastColIndex)
        }
      })
    },
    headerCellStyle ({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === this.selectCol + 1) {
        // return 'background-color: #ebb563'
        // return 'background-color: #1890FF'
        return 'background-color: #DDE0E7'
      }
      return ''
    },
    cellStyle ({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === this.selectCol + 1) {
        // return 'background-color: #ebb563'
        // return 'background-color: #1890FF'
        return 'background-color: #ECECEC'
      }
      return ''
    },
    headerClick (column, event) {
      if (this.selectCol === -2 || this.selectCol !== column.index) {
        this.selectCol = column.index
        this.tableColumns.forEach((value, index) => {
          if (value.minWidth === '240') {
            this.$set(value, 'minWidth', '120')
          }
        })
        this.$set(this.tableColumns[column.index], 'minWidth', '240')
      } else if (!this.inputFocus) {
        this.selectCol = -2
        this.$set(this.tableColumns[column.index], 'minWidth', '120')
      }
    },
    cellClick (row, column, cell, event) {
      if (this.selectCol === -2 || this.selectCol !== column.index) {
        this.selectCol = column.index
        this.tableColumns.forEach((value, index) => {
          if (value.minWidth === '240') {
            this.$set(value, 'minWidth', '120')
          }
        })
        this.$set(this.tableColumns[column.index], 'minWidth', '240')
      } else if (!this.inputFocus) {
        this.selectCol = -2
        this.$set(this.tableColumns[column.index], 'minWidth', '120')
      }
    },
    showTipsFu (isShow, highlight) {
      if (highlight === 0 && this.requiredFieldList.length < 1) {
        return
      }
      this.showTips = isShow
      if (isShow) {
        this.highlight = highlight
      } else {
        this.highlight = 2
      }
    },
    inputFocusHandle () {
      this.inputFocus = true
    },
    inputBlurHandle () {
      this.inputFocus = false
    },
    // 格式化列显示
    formatterFn (fullFieldName) {
      let value = ''
      this.excelFieldList.forEach(object => {
        if (object.fullFieldName === fullFieldName) {
          value = object.fullName
          return false
        }
      })
      return value
    }
  },
  computed: {
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    dataArr () {
      let arr = [this.dataList[0], this.dataList[1], this.dataList[2]]
      return arr
    },
    excelFieldListOptions () {
      let objectList = []
      const map = new Map()
      let fullFieldList = this.tableColumns.map((column) => this.dataList[1][column.value])
      this.excelFieldList.map((item) => {
        if (!item.disabled && fullFieldList.includes(item.fullFieldName)) {
          // i 为选中的索引
          item.disabled = true
        } else if (item.disabled && !fullFieldList.includes(item.fullFieldName)) {
          item.disabled = false
        }
        return item
      }).forEach((item, index, arr) => {
        if (!map.has(item.objectName)) {
          map.set(item.objectName, arr.filter(a => a.objectName === item.objectName))
        }
      })
      map.forEach((value, key, map) => {
        let groupObj = {
          objectName: key,
          fieldList: value
        }
        objectList.push(groupObj)
      })
      return objectList
    },
    requiredFieldList () {
      const objectList = []
      const map = new Map()
      let fullFieldList = this.tableColumns.map((column) => this.dataList[1][column.value])
      this.excelFieldList.filter((item) => {
        return !item.allowEmpty && !fullFieldList.includes(item.fullFieldName)
      }).forEach((item, index, arr) => {
        if (!map.has(item.objectName)) {
          map.set(item.objectName, arr.filter(a => a.objectName === item.objectName))
        }
      })
      map.forEach((value, key, map) => {
        let groupObj = {
          objectName: key,
          fieldList: value
        }
        objectList.push(groupObj)
      })
      return objectList
    }
  }
}
</script>

<style scoped>

</style>
