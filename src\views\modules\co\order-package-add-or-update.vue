<template>
  <div class="add-body panel_body">
    <div class="panel-hd">{{!dataForm.id ? $t('add') : $t('update')}}</div>
    <div class="addOrUpdatePanel" >
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" label-width="80px">
        <el-row type="flex" justify="start">
          <el-col :md="{span:13, offset: 2}">
            <el-form-item :label="$t('coOrderPackage.orderId')" prop="orderId">
              <el-input v-model="dataForm.orderId" :placeholder="$t('coOrderPackage.orderId')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderPackage.packageSerialNo')" prop="packageSerialNo">
              <el-input v-model="dataForm.packageSerialNo" :placeholder="$t('coOrderPackage.packageSerialNo')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderPackage.packageDeliveryNo')" prop="packageDeliveryNo">
              <el-input v-model="dataForm.packageDeliveryNo" :placeholder="$t('coOrderPackage.packageDeliveryNo')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderPackage.packageCustomerNo')" prop="packageCustomerNo">
              <el-input v-model="dataForm.packageCustomerNo" :placeholder="$t('coOrderPackage.packageCustomerNo')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderPackage.packageWeight')" prop="packageWeight">
              <el-input v-model="dataForm.packageWeightD" :placeholder="$t('coOrderPackage.packageWeight')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderPackage.packageLength')" prop="packageLength">
              <el-input v-model="dataForm.packageLengthD" :placeholder="$t('coOrderPackage.packageLength')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderPackage.packageWidth')" prop="packageWidth">
              <el-input v-model="dataForm.packageWidthD" :placeholder="$t('coOrderPackage.packageWidth')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderPackage.packageHeight')" prop="packageHeight">
              <el-input v-model="dataForm.packageHeightD" :placeholder="$t('coOrderPackage.packageHeight')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderPackage.channelLabelUrl')" prop="channelLabelUrl">
              <el-input v-model="dataForm.channelLabelUrl" :placeholder="$t('coOrderPackage.channelLabelUrl')"></el-input>
            </el-form-item>
          </el-col>

        </el-row>
      </el-form>
      <div id="cs_FormFooter" class="el-form-footer">
        <el-button @click="cancelFn">{{ $t('cancel') }}</el-button>
        <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      dataForm: {
        id: '',
        creator: '',
        createDate: '',
        orderId: '',
        packageSerialNo: '',
        packageDeliveryNo: '',
        packageCustomerNo: '',
        packageWeightD: '',
        packageLengthD: '',
        packageWidthD: '',
        packageHeightD: '',
        channelLabelUrl: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        creator: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        createDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        orderId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        packageSerialNo: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        packageDeliveryNo: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        packageCustomerNo: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        packageWeight: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        packageLength: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        packageWidth: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        packageHeight: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        channelLabelUrl: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/co/orderpackage/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/co/orderpackage/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    // 取消
    cancelFn () {
      this.visible = false
      this.$emit('cancelAddOrUpdate')
    }
  }
}
</script>
