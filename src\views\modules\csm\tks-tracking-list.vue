<template>
  <div class="aui-card--fill">
    <div class="mod-co__inorder flex_wrap" v-show="!panelShow" ref="listPanel">
      <el-card class="search_box" shadow="never">
        <el-form ref="searchForm" class="form_no_margin" :rules="dataRule" :model="dataForm" @keyup.enter.native="searchHandle()" label-width="90px">
          <el-row :gutter="10" type="flex">
            <el-col :span="8">
              <multiple-no-input ref="threeNoInput" :attribute-list.sync="multipleNoList"
                                 @setValue="commonMultipleNoInputSetValueEmit"
                                 @clearValue="commonMultipleNoInputClearValueEmit"
                                 :autosize="multipleNoInputAutoSize" :noSize="5000"/>
            </el-col>
            <el-col :span="16">
              <el-row :gutter="10">
                <div class="fl width100">
                  <el-col :span="12">
                    <el-form-item :label="$t('tksTrackingWaybill.logisticsProductCode')" prop="logisticsProductCode">
                      <el-select v-model="dataForm.logisticsProductCode" filterable clearable>
                        <el-option v-for="item in baseData.logisticsProductByParamsList" :key="item.code" :label="item.name" :value="item.code"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="$t('tksTrackingWaybill.lastTrackingCode')" prop="lastTrackingCode">
                      <el-select v-model="dataForm.lastTrackingCode"  :placeholder="$t('tksTrackingWaybill.lastTrackingCode')"  filterable clearable>
                        <el-option v-for="item in dict.statusList" :key="item.id" :label="item.content" :value="item.code"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="$t('tksTrackingWaybill.destinationCountry')" prop="destinationCountry">
                      <el-select v-model="dataForm.destinationCountry" filterable placeholder="" clearable>
                        <el-option v-for="item in baseData.countryList" :key="item.code" :label="`${item.name} ${item.code}`" :value="item.code"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="$t('wsComMawb.mawbNo')" prop="lastTrackingCode">
                      <el-input v-model="dataForm.mawbNo" :placeholder="$t('wsComHawb.mawbNo')" clearable ></el-input>
                    </el-form-item>
                  </el-col>
                </div>
                <div class="fl width100">
                  <el-col :span="24" style='height: 32px'>
                    <el-form-item label-width='131px'  id='dateType' >
                      <span slot="label">
                         <el-select  v-model="dataForm.dateType"  filterable>
                          <el-option :key="1" :label="$t('system.createDate')" value="CREATE_DATE"></el-option>
                          <el-option :key="2" :label="$t('tksTrackingWaybill.lastTrackingTime')" value="TIMEOUT_FROM_DATE"></el-option>
                        </el-select>
                    </span>
                      <div v-if='dataForm.dateType === "CREATE_DATE"'>
                        <el-date-picker  v-model="createDateArray" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange" :start-placeholder="$t('startTime')" :end-placeholder="$t('endTime')" style="width: 100%"></el-date-picker>
                      </div>
                      <div v-if='dataForm.dateType === "TIMEOUT_FROM_DATE"'>
                        从
                        <el-form-item prop='fromTime' style='display: inline-block'>
                          <el-date-picker  v-model="dataForm.fromTime" style='width: 135px'  value-format="yyyy-MM-dd"  type="date" :editable='false' :clearable='false' placeholder="选择日期"/>
                        </el-form-item>
                        起，超过
                        <el-form-item prop='timeOutHour' style='display: inline-block'>
                          <el-input v-model="dataForm.timeOutHour"  style='width: 93px' :placeholder="$t('tksTracking.timeOutHour')"></el-input>小时
                        </el-form-item>
                      </div>
                    </el-form-item>
                  </el-col>
                </div>
              </el-row>
            </el-col>
            <div class="search_box_btn">
              <el-button type="primary" @click="searchHandle()" icon="el-icon-search">查询</el-button>
              <el-button @click.native="resetHandle()" icon="el-icon-refresh-right">重置</el-button>
            </div>
          </el-row>
        </el-form>
      </el-card>

      <el-tabs class="flex_tab no_shadow margin_top10" type="border-card" v-model="activeName" @tab-click="tabsClick" :stretch="false">
        <el-tab-pane v-for="(item, key) in statusArr" :key="'tab' + key" :name="item.tabName">
          <div slot="label" style="min-width: 4em; text-align: center">{{ item.tabName || '&nbsp;' }}
            <el-badge  v-loading='item.loading || dataListLoading' element-loading-spinner="el-icon-loading" v-if='key !== 0'
                       :value="item.num >0 || !item.loading ? item.num||0:''" :max="99999" class="item" type="primary"></el-badge>
          </div>
          <el-row class="optBtn_panel">
          <el-col :md='12' class='optBtn_leftFixed'>
<!--            <el-button size="mini" type="primary" plain  v-if="$hasPermission('csm:tksTracking:batchDel')" @click="trackBatchDelHandle(dataListSelections)">{{ $t('tksTrackingWaybill.batchDel') }}</el-button>-->
<!--            <el-button size="mini" type="primary" plain  v-if="$hasPermission('csm:tksTracking:batchToAutonomousPush')" @click="batchToAutonomousPushHandle()">{{ $t('tksTrackingWaybill.batchToAutonomousPush') }}</el-button>-->
<!--            <el-button size="mini" type="primary" plain  v-if="$hasPermission('csm:tksTrackingList:updatePostCodeForDelivery')" @click="updatePostCodeForDeliveryHandle(dataListSelections)">{{ $t('tksTrackingWaybill.updatePostCodeForDelivery') }}</el-button>-->
          </el-col>
          <el-col :md="12" class="text-right">
<!--            plain v-if="$hasPermission('csm:tksTracking:export')"-->
            <el-button size="mini" type="primary" :loading='expButtonLoading' plain  @click="exportHandle()">{{ $t('export') }}</el-button>
<!--            <el-button size="mini" type="primary" plain v-if="$hasPermission('csm:tksTracking:import')" @click="importHandle()">{{ $t('import') }}</el-button>-->
          </el-col>
        </el-row>
        </el-tab-pane>
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <ux-grid ref="tableData" v-loading="dataListLoading"  size='mini' :widthResize="true" :border="false"
                   :max-height="tableHeight"  :show-overflow="true"
                   @selection-change="dataListSelectionChangeHandle">
            <ux-table-column type="checkbox" width='50' fixed="left"></ux-table-column>
            <!-- 动态显示表格 -->
            <ux-table-column v-for="(item, index) in tableColumnsArr" sortable
                             :key="index" :type="item.type" :field="item.prop"
                             header-align="center" :align="item.align" :resizable="true" :border="false"
                             :min-width="item.width" :title="item.label">
              <template  v-slot:header='scope' >
                <div class="header-wrapper">
                  <span>{{ scope.column.title }}</span>
                  <el-tooltip  placement="top" :content="$t('tksTrackingWaybill.deliveryPackageTimeliness') + ' = 尾程供应商上网日期 - 入仓日期'" effect='light'>
                    <el-link v-show="scope.column.title === $t('tksTrackingWaybill.deliveryPackageTimeliness')"
                             :underline="false" class='icon-tips' icon="el-icon-info" ></el-link>
                  </el-tooltip>
                  <el-tooltip  placement="top" :content="$t('tksTrackingWaybill.collectPackageTimeliness') + ' = 尾程供应商派送签收日期 - 入仓日期'" effect='light'>
                    <el-link v-show="scope.column.title === $t('tksTrackingWaybill.collectPackageTimeliness')"
                           :underline="false" class='icon-tips' icon="el-icon-info" ></el-link>
                  </el-tooltip>
                </div>
              </template>
              <template slot-scope="scope">
                  <template v-if="item.prop === 'createDate'">
                    <span>{{formatterFn(scope,item.prop)}}</span>
                  </template>
                  <template v-else-if="item.prop === 'subDeliveryNo'">
                    <span>{{scope.row.subDeliveryNo || scope.row.deliveryNo}}</span>
                  </template>
                  <template v-else-if="item.prop === 'trackingFlag'">
                    <span v-if='scope.row.trackingFlag === 0 && !scope.row.planTrackingTime'>未跟踪</span>
                    <span v-else-if='scope.row.trackingFlag === 1 && scope.row.planTrackingTime'>跟踪中</span>
                    <span v-else-if='scope.row.trackingFlag === 0 && scope.row.planTrackingTime'>跟踪完成</span>
                  </template>
                  <template v-else-if="item.prop === 'lastTrackingCode'">
                    <span v-if="errorTrackCodeList.includes(scope.row.lastTrackingCode)" style='color: red'>{{formatterFn(scope,item.prop)}}</span>
                    <span v-else-if="signTrackCodeList.includes(scope.row.lastTrackingCode)" style='color: #16ad00'>{{formatterFn(scope,item.prop)}}</span>
                    <span v-else>{{formatterFn(scope,item.prop)}}</span>
                  </template>
                  <template v-else>
                    {{formatterFn(scope,item.prop)}}
                  </template>
              </template>
            </ux-table-column>
            <ux-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="80">
              <template slot="header" slot-scope="scope">
                <span>{{$t('handle')}}</span>
                <el-tooltip :content="$t('tableConfigTooltip')" placement="top">
                  <el-link style="margin-left:10px; font-size:16px" class="el-icon-setting" @click="settingCol(tableColumns)"></el-link>
                </el-tooltip>
              </template>
              <template slot-scope="scope">
                <el-link :underline="false" @click="viewTrackHandle(scope.row.subDeliveryNo || scope.row.deliveryNo || scope.row.waybillNo, scope.row.id )">{{ $t('view') }}</el-link>
<!--                <el-link :underline="false" class='margin_left10' v-if="$hasPermission(`csm:tksTrackingList:update`)" @click="editTrackHandle(scope.row.subDeliveryNo || scope.row.deliveryNo || scope.row.waybillNo, scope.row.id )">{{ $t('update') }}</el-link>-->
<!--                <popconfirm i18nOperateValue="tksTrackingWaybill.registerNo" @clickHandle="registerTrackHandle(scope.row.id, scope.row)"-->
<!--                            :disabled.sync='scope.row.loading' :loading.sync='scope.row.loading'-->
<!--                            :condition="Boolean(scope.row.logisticsChannelCode) && (scope.row.trackingFlag === 0 && !Boolean(scope.row.planTrackingTime))"></popconfirm>-->
              </template>
            </ux-table-column>
          </ux-grid>
        </div>
        <el-pagination  background :current-page.sync="page" :disabled="dataListLoading || pageCountLoading"
                        :page-sizes="[10, 20, 50, 100, 1000, 5000, 10000]" :page-size="limit"
                        :total="total" layout="slot, sizes, prev, pager, next, jumper"
                        @size-change="pageSizeChangeHandle" @current-change="pageCurrentChangeHandle">
          <template >
            <span key="paginationTotal" id="paginationTotal" class="el-pagination__total" element-loading-spinner="el-icon-loading"
                  v-loading="dataListLoading || pageCountLoading">共 {{ total }} 条</span>
          </template>
        </el-pagination>
      </el-tabs>
    </div>
    <!-- 自定义列显示弹窗 -->
    <table-set ref="tableSet"></table-set>
    <!-- 详情页面 -->
    <viewDetail v-if="viewVisible" ref="viewDetail" @backView="backView"></viewDetail>
    <!--轨迹批量删除-->
  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import listPage from '@/mixins/listPage'
import {
  formatterType,
  gtmToLtm,
  timestampFormat,
  formatterCodeNativeName,
  formatterShowName,
  formatterUser,
  formatterCodeShowName
} from '@/filters/filters'
import dictTypeMixins from '@/mixins/dictTypeMixins'
import comMixins from '@/mixins/comMixins'
// table 自定义显示
import tableSet from '@/components/tableSet'
import ViewDetail from './tks-tracking-timeline-detail'
import multipleNoInput from '@/components/common-multiple-no-input'
import baseDataApi from '@/api'
import baseData from '@/api/baseData'
import { getBeforeDay, getNowDate } from '@/utils/tools'
import cloneDeep from 'lodash/cloneDeep'
import { isPlusInteger2 } from '@/utils/validate'
import debounce from 'lodash/debounce'

export default {
  mixins: [listPage, mixinViewModule, dictTypeMixins, comMixins],
  data () {
    return {
      tableColumns: [
        { type: '', width: '120', prop: 'customerOrderNo', label: this.$t('tksTrackingWaybill.customerOrderNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'waybillNo', label: this.$t('tksTrackingWaybill.waybillNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'subDeliveryNo', label: this.$t('tksTrackingWaybill.subDeliveryNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'deliveryNo', label: this.$t('tksTrackingWaybill.masterDeliveryNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'postalTrackingNo', label: this.$t('tksTrackingWaybill.postalTrackingNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'lastTrackingCode', label: this.$t('tksTrackingWaybill.lastTrackingCode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'lastTrackingContent', label: this.$t('tksTrackingWaybill.lastTrackingContent'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'destinationCountry', label: this.$t('tksTrackingWaybill.destinationCountry'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'lastTrackingTime', label: this.$t('tksTrackingWaybill.lastTrackingTime'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'createDate', label: this.$t('system.createDate'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'diffHour', label: this.$t('tksTrackingWaybill.diffHour'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'lastTrackingLocation', label: this.$t('tksTrackingWaybill.lastTrackingLocation'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '96', prop: 'deliveryPackageTimeliness', label: this.$t('tksTrackingWaybill.deliveryPackageTimeliness'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '90', prop: 'collectPackageTimeliness', label: this.$t('tksTrackingWaybill.collectPackageTimeliness'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'logisticsProductCode', label: this.$t('tksTrackingWaybill.logisticsProductCode'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'trackingFlag', label: this.$t('tksTrackingWaybill.trackingFlag'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'planTrackingTime', label: this.$t('tksTrackingWaybill.planTrackingTime'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'inTime', label: this.$t('tksTrackingWaybill.inTime'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'outTime', label: this.$t('tksTrackingWaybill.outTime'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'id', label: this.$t('system.id'), align: 'center', isShow: false, disabled: true }
      ],
      mixinViewModuleOptions: {
        getDataListURL: '/tks/trackingwaybill/page',
        exportURL: '/tks/trackingwaybill/export',
        getDataListURLOfRequestType: 'post',
        exportRequestMethod: 'post',
        getDataListIsPage: true,
        activatedIsNeed: false
      },
      dataForm: {
        customerOrderNos: '',
        waybillNos: '',
        deliveryNos: '',
        postalTrackingNos: '',
        outBatchNos: '',
        bagNos: '',
        lastTrackingCode: '',
        logisticsProductCode: '',
        logisticsChannelCode: '',
        mawbNo: '',
        customerId: this.$store.state.user.customerId,
        status: '',
        lastTrackingContent: '',
        createDateFrom: '',
        createDateTo: '',
        dateType: 'CREATE_DATE',
        fromTime: '',
        timeOutHour: '',
        destinationCountry: ''
      },
      multipleNoList: [
        { attribute: 'waybillNos', label: this.$t('tksTrackingWaybill.waybillNo') },
        { attribute: 'customerOrderNos', label: this.$t('tksTrackingWaybill.customerOrderNo') },
        { attribute: 'deliveryNos', label: this.$t('tksTrackingWaybill.deliveryNo') },
        { attribute: 'postalTrackingNos', label: this.$t('tksTrackingWaybill.postalTrackingNo') }
      ],
      loading: false,
      pageCountLoading: false,
      trackBatchDelDialogConfirmLoading: false,
      // 数据字典
      dict: {
        statusList: [],
        orderLogisticsTypeList: []
      },
      customerList: [],
      createDateArray: [getBeforeDay(7), getNowDate()],
      activeName: '全部',
      tableName: 'tks_tracking_queue',
      baseData: {
        logisticsProductByParamsList: [],
        logisticsChannelByParamsList: [],
        countryList: [],
        userList: []
      },
      batchInterceptDialogVisible: false,
      addMemoVisible: false,
      batchInterceptDataForm: {
        opeareteDescription: '',
        tksTrackingWaybillDTOList: null
      },
      changeChannelDialog: {
        visible: false,
        dataForm: {
          waybillNos: '',
          logisticsChannelCode: '',
          changeReason: ''
        }
      },
      errorTrackCodeList: ['C02', 'E01', 'E02', 'E03', 'E04', 'E05', 'E06', 'G02', 'G04', 'G05', 'G06', 'G07', 'G08'],
      signTrackCodeList: ['G99', 'G10'],
      statusArr: [
        // 所有订单
        {
          tabName: '全部',
          lastTrackingCodeList: [],
          num: null,
          loading: false
        },
        {
          tabName: '在途',
          lastTrackingCodeList: [],
          num: null,
          loading: false
        }
      ]
    }
  },
  watch: {
    createDateArray: {
      handler (value, oldName) {
        if (value !== undefined && value !== '' && value !== null) {
          this.dataForm.createDateFrom = timestampFormat(value[0])
          this.dataForm.createDateTo = timestampFormat(value[1])
        } else {
          this.createDateArray = [getBeforeDay(7), getNowDate()]
        }
      },
      // immediate: true 第一次初始化的时候会被执行 immediate: false 第一次初始化的时候不会被执行
      immediate: true
    }
  },
  created () {
    // 获取基础数据
    this.getBaseData()
  },
  activated () {
    if (this.$route.query.dateType && this.$route.query.fromTime && this.$route.query.timeOutHour) {
      this.$nextTick(() => {
        this.$set(this.dataForm, 'dateType', this.$route.query.dateType)
        this.$set(this.dataForm, 'fromTime', this.$route.query.fromTime)
        this.$set(this.dataForm, 'timeOutHour', this.$route.query.timeOutHour)
        this.dataForm.fromTime = this.$route.query.fromTime
        this.dataForm.dateType = this.$route.query.dateType
        this.dataForm.timeOutHour = this.$route.query.timeOutHour
        this.waitOnWayArrayToHaveValue().then(() => {
          this.searchHandle()
        })
      })
    } else {
      this.waitOnWayArrayToHaveValue().then(() => {
        this.searchHandle()
      })
    }
  },
  methods: {
    $getDataListCallback () {
      this.$refs.tableData.reloadData(this.dataList)
      this.getPageDataCount()
      this.concurrentCount()
    },
    // 检查在途数组是否有值了
    waitOnWayArrayToHaveValue () {
      return new Promise((resolve, reject) => {
        const checkArray = () => {
          if (this.statusArr[1].lastTrackingCodeList.length > 0) {
            resolve() // 数组有值，解决Promise
          } else {
            setTimeout(checkArray, 100) // 继续检查数组是否有值
          }
        }
        checkArray()
      })
    },
    trackBatchDelHandle (data) {
      if (data.length <= 0) {
        return this.$message({
          message: this.$t('prompt.actionStatusBatch'),
          type: 'warning',
          duration: 1000
        })
      }
      this.$nextTick(() => {
        this.$refs.trackBatchDelDialog.init()
      })
    },
    updatePostCodeForDeliveryHandle (data) {
      if (data.length <= 0) {
        return this.$message({
          message: this.$t('prompt.actionStatusBatch'),
          type: 'warning',
          duration: 1000
        })
      }
      this.$nextTick(() => {
        let idArr = data.map(item => item.id)
        this.$http.post('/tks/trackingwaybill/batchUpdatePostCodeForDelivery', idArr)
          .then(({ data: res }) => {
            if (res.code !== 0) {
              return this.$message({
                message: res.msg,
                type: 'error',
                duration: 5000,
                dangerouslyUseHTMLString: true,
                onClose: () => {
                  this.searchHandle()
                }
              })
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 1000,
              onClose: () => {
                this.searchHandle()
              }
            })
          }).catch(() => {})
      })
    },
    // todo 批量移动到自主推送
    batchToAutonomousPushHandle () {
      if (this.dataListSelections.length <= 0) {
        return this.$message({
          message: this.$t('prompt.actionStatusBatch'),
          type: 'warning',
          duration: 1000
        })
      }
    },
    registerTrackHandle: debounce(function (id, row) {
      if (!id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: this.$t('prompt.actionStatusBatch'),
          type: 'warning',
          duration: 1000
        })
      }
      this.popoverLoading(row)
      let idArr = id ? [id] : this.dataListSelections.map(item => item.id)
      this.$http.post('/tks/trackingwaybill/register', idArr)
        .then(({ data: res }) => {
          this.popoverStopLoading(row)
          if (res.code !== 0) {
            return this.$message({
              message: res.msg,
              type: 'error',
              duration: 5000,
              dangerouslyUseHTMLString: true,
              onClose: () => {
                this.searchHandle()
              }
            })
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 1000,
            onClose: () => {
              this.searchHandle()
            }
          })
        }).catch(() => {})
    }, 1000, { 'leading': true, 'trailing': false }),
    popoverLoading (row) {
      this.$nextTick(() => {
        this.$set(row, 'loading', true)
      })
    },
    // 冒泡弹窗停止Loading
    popoverStopLoading (row) {
      this.$nextTick(() => {
        this.$set(row, 'loading', false)
      })
    },
    trackBatchDelDialogClose () {
      this.$refs.tableData.clearSelection()
    },
    trackBatchDelConfirm (data) {
      let delObj = cloneDeep(data)
      let masterWaybillNos = this.dataListSelections.filter(item => item.subWaybillId === null).map(item => item.waybillNo)
      delObj.masterWaybillNoList = Array.from(new Set(masterWaybillNos))
      delObj.subWaybillIdList = this.dataListSelections.filter(item => item.subWaybillId !== null && !masterWaybillNos.includes(item.waybillNo)).map(item => item.subWaybillId)
      this.$http.post('/tks/trackingdetail/batchDel', delObj).then(({ data: res }) => {
        if (res.code !== 0) {
          this.trackBatchDelDialogConfirmLoading = false
          this.$refs.trackBatchDelDialog.localConfirmLoading = false
          return this.$message.error(res.msg)
        }
        this.$refs.trackBatchDelDialog.trackBatchDelDialogClose()
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 1000,
          onClose: () => {
            this.searchHandle()
          }
        })
      }).catch(() => {
        this.trackBatchDelDialogConfirmLoading = false
      })
    },
    getAllTrackCodeList () {
      return new Promise((resolve, reject) => {
        this.$http.get('tks/trackingwaybill/getAllTrackCodeList?status=1').then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          resolve(res.data)
        }).catch(() => {})
      })
    },
    async getBaseData () {
      this.dict.statusList = await this.getAllTrackCodeList()
      this.dict.orderLogisticsTypeList = await this.getDictTypeList('orderLogisticsType')
      await baseData(baseDataApi.tksTrackingGroupList, { autoFilter: true }).then((res) => {
        let dataList = res
        const renamedArray = dataList.map(({ groupName: tabName, codeList: lastTrackingCodeList }) => ({ tabName, lastTrackingCodeList, loading: false, num: null }))
        this.$nextTick(() => {
          Array.prototype.push.apply(this.statusArr, renamedArray)
          let statusList = cloneDeep(this.dict.statusList)
          // 过滤掉状态为"全部"和"在途"的订单，获取剩余状态的lastTrackingCodeList
          const filteredStatusArr = this.statusArr.filter(status => status.tabName !== '全部' && status.tabName !== '在途')
          const useTrackCodeList = filteredStatusArr.map(status => status.lastTrackingCodeList).flat()
          this.statusArr[1].lastTrackingCodeList = statusList.filter(item => !useTrackCodeList.includes(item.code)).map(item => item.code)
          if (this.statusArr[1].lastTrackingCodeList.length === 0) {
            this.statusArr[1].lastTrackingCodeList = ['empty']
          }
        })
      }).catch(() => {})
      this.dataForm.timeOutHour = await this.getDefaultTrackTimeOutHour()
      this.baseData.logisticsProductByParamsList = await baseData(baseDataApi.listEnableAllByCurrent, { autoFilter: true }).catch(() => {})
      this.baseData.logisticsChannelByParamsList = await baseData(baseDataApi.logisticsChannelByParamsList, { autoFilter: true }).catch(() => {})
      this.baseData.warehouseList = await baseData(baseDataApi.warehouseInfoList).catch(() => {})
      this.baseData.countryList = await baseData(baseDataApi.countryList).catch(() => {})
      // 用户信息
      this.baseData.userList = await baseData(baseDataApi.allUserList).catch(() => {})
    },
    getDefaultTrackTimeOutHour () {
      return new Promise((resolve, reject) => {
        this.$http.get(`/sys/params/getValueByCode/TRACK_TIME_OUT_COUNT`).then(({ data: res }) => {
          if (res.code !== 0) {
            resolve(this.$message.error(res.msg))
          }
          resolve(res.data)
        }).catch(() => {
          resolve(null)
        })
      })
    },
    backView () {
      this.viewVisible = false
      this.searchHandle()
    },
    exportHandle () {
      let initFlag = this.$refs.threeNoInput.setValue()
      let activeTabItem = this.statusArr.filter(item => item.tabName === this.activeName)[0]
      console.log('this.activeName', this.activeName)
      console.log('activeTabItem', activeTabItem)
      this.dataForm.lastTrackingCodeList = activeTabItem.lastTrackingCodeList.toString()
      if (initFlag) {
        this.$refs['searchForm'].validate((valid) => {
          if (!valid) {
            return false
          }
          this.exportByMsgHandle()
        })
      }
    },
    searchHandle () {
      let initFlag = this.$refs.threeNoInput.setValue()
      let activeTabItem = this.statusArr.filter(item => item.tabName === this.activeName)[0]
      console.log('this.activeName', this.activeName)
      console.log('activeTabItem', activeTabItem)
      this.dataForm.lastTrackingCodeList = activeTabItem.lastTrackingCodeList
      if (initFlag) {
        this.$refs['searchForm'].validate((valid) => {
          if (!valid) {
            return false
          }
          this.queryPageByParam()
        })
      }
    },
    getPageDataCount() {
      this.pageCountLoading = true
      let tempData = {
        page: this.mixinViewModuleOptions.getDataListIsPage ? this.page : null,
        limit: this.mixinViewModuleOptions.getDataListIsPage ? this.limit : null,
        ...this.dataForm
      }
      this.$http.post('/tks/trackingwaybill/pageCount', tempData).then(({ data: res }) => {
        this.pageCountLoading = false
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.total = res.data.total || 0
        this.page = res.data.pageNumber
      }).catch(() => {
      }).finally(() => {
        this.pageCountLoading = false
      })
    },
    importHandle () {
      this.$router.push({ name: `tks-tracking-import` })
    },
    resetHandle () {
      this.createDateArray = [getBeforeDay(7), getNowDate()]
      this.$refs.threeNoInput.clearValue()
      this._resetForm('searchForm')
    },
    viewTrackHandle (no, id) {
      this.viewVisible = true
      this.$nextTick(() => {
        this.$refs.viewDetail.edit = false
        this.$refs.viewDetail.init(no, id)
      })
    },
    editTrackHandle (no, id) {
      this.viewVisible = true
      this.$nextTick(() => {
        this.$refs.viewDetail.edit = true
        this.$refs.viewDetail.init(no, id)
      })
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'logisticsProductCode':
          value = formatterCodeNativeName(scope.row.logisticsProductCode, this.baseData.logisticsProductByParamsList)
          break
        case 'logisticsChannelCode':
          value = formatterCodeNativeName(scope.row.logisticsChannelCode, this.baseData.logisticsChannelByParamsList)
          break
        case 'lastTrackingCode':
          value = formatterCodeShowName(scope.row.lastTrackingCode, this.dict.statusList, 'content') || scope.row.lastTrackingCode
          break
        case 'destinationCountry':
          value = formatterCodeNativeName(scope.row.destinationCountry, this.baseData.countryList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    // 自定义列显示
    settingCol (tableColumns) {
      this.$nextTick(() => {
        this.$refs['tableSet'].init(this.tableName, tableColumns)
      })
    },
    tabsClick (tab, event) {
      this.searchHandle()
    },
    // 分页, 每页条数
    pageSizeChangeHandle (val) {
      this.page = 1
      this.limit = val
      this.$refs.threeNoInput.setValue()
      let activeTabItem = this.statusArr.filter(item => item.tabName === this.activeName)[0]
      this.dataForm.lastTrackingCodeList = activeTabItem.lastTrackingCodeList
      this.getDataList()
    },
    // 分页, 当前页
    pageCurrentChangeHandle (val) {
      this.page = val
      this.$refs.threeNoInput.setValue()
      let activeTabItem = this.statusArr.filter(item => item.tabName === this.activeName)[0]
      this.dataForm.lastTrackingCodeList = activeTabItem.lastTrackingCodeList
      this.getDataList()
    },
    // 逐个统计
    async concurrentCount () {
      let statusList = cloneDeep(this.statusArr)
      let tabNameArr = statusList.map(item => item.tabName)
      console.log('now count tabName Array =>', tabNameArr)
      try {
        for (let tabName of tabNameArr) {
          this.setTabLoading(tabName, true)
        }
        for (let tabName of tabNameArr) {
          let result = await this.count(tabName)
          console.log('Request completed for tabName:', tabName, 'Result:', result)
        }
        console.log('All requests completed successfully')
      } catch (error) {
        console.log('An error occurred during requests')
        console.error(error)
      } finally {
        for (let tabName of tabNameArr) {
          this.setTabLoading(tabName, false)
        }
      }
    },
    // 设置 tab 的 loading 状态
    setTabLoading (tabName, isLoading) {
      let activeTabItem = this.statusArr.find(item => item.tabName === tabName)
      if (activeTabItem) {
        this.$set(activeTabItem, 'loading', isLoading)
      }
    },
    // 单个统计
    count (tabName) {
      return new Promise((resolve, reject) => {
        this.$refs.threeNoInput.setValue()
        let tempDataForm = { ...this.dataForm }
        let activeTabItem = this.statusArr.filter(item => item.tabName === tabName)[0]

        if (!activeTabItem.lastTrackingCodeList || activeTabItem.lastTrackingCodeList.length <= 0) {
          return resolve(null)
        }

        tempDataForm.lastTrackingCodeList = activeTabItem.lastTrackingCodeList
        this.$set(activeTabItem, 'loading', true)

        this.$http.post(`/tks/trackingwaybill/sortCount`, tempDataForm).then(({ data: res }) => {
          this.$nextTick(() => {
            this.$set(activeTabItem, 'loading', false)
          })

          if (res.code !== 0) {
            resolve(this.$message.error(res.msg))
          }
          console.log('count => ' + res.data)
          activeTabItem.num = res.data === '0' ? '' : res.data
          this.$forceUpdate()
          resolve(res.data)
        }).catch(() => {
          resolve(null)
        })
      })
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat,
    formatterCodeNativeName,
    formatterShowName,
    formatterUser
  },
  computed: {
    multipleNoInputAutoSize () {
      this.$nextTick(() => {
        this.$refs.threeNoInput.resizeTextarea()
      })
      return { minRows: 5, maxRows: 5 }
    },
    dataRule () {
      const validateIsPlusInteger2 = (rule, value, callback) => {
        if (!value || !isPlusInteger2(value)) {
          return callback(new Error(this.$t('validate.format', { 'attr': this.$t('comSingleInWarehouse.isPlusInteger2') })))
        }
        callback()
      }
      return {
        fromTime: [
          { required: this.dataForm.dateType === 'TIMEOUT_FROM_DATE', message: this.$t('validate.required'), trigger: 'blur' }
        ],
        timeOutHour: [
          { required: this.dataForm.dateType === 'TIMEOUT_FROM_DATE', message: this.$t('validate.required'), trigger: ['blur', 'change'] },
          { validator: validateIsPlusInteger2, trigger: 'change' }
        ]
      }
    },
    batchInterceptDataRule () {
      return {
        opeareteDescription: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    },
    changeChannnelDialogDataRule () {
      return {
        changeReason: [{ required: true, message: this.$t('validate.required'), trigger: 'blur' }],
        logisticsChannelCode: [{ required: true, message: this.$t('validate.required'), trigger: 'blur' }]
      }
    },
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    panelShow () {
      let ret = false
      if (this.addOrUpdateVisible) {
        ret = true
      } else if (this.viewVisible) {
        ret = true
      }
      return ret
    },
    threeNoInputAutoSize () {
      this.$nextTick(() => {
        this.$refs.threeNoInput.resizeTextarea()
      })
      if (this.searchBoxShow) {
        return { minRows: 7, maxRows: 7 }
      } else {
        return { minRows: 7, maxRows: 7 }
      }
    },
    // 过滤启用的物流渠道--用于强制更换渠道
    logisticsChannelEnableList () {
      return this.baseData.logisticsChannelByParamsList.filter(d => d.status === 1)
    }
  },
  components: {
    tableSet,
    ViewDetail,
    multipleNoInput
  }
}
</script>

<style lang='scss' scoped>
#dateType /deep/ .el-form-item__label:after {
  margin: 0 -7px 0 2px !important;
  top: -32.5px !important;
}
.header-wrapper {
  display: flex;
  align-items: center;
}

.icon-tips {
  font-size: 15px;
  margin-left: 1px;
  cursor: pointer;
  color: #b3d8ff !important;
  &:hover{
    color: #409EFF !important;
  }
}
//::v-deep .tks-badge .el-loading-mask {
//  position: absolute;
//  z-index: 2000;
//  background-color: rgba(255, 255, 255, .9) !important;
//  margin: 0;
//  top: 7px !important;
//  right: 0;
//  bottom: 0;
//  left: 3px !important;
//  height: 14px !important;
//  width: 14px !important;
//  border-radius: 100% !important;
//  -webkit-transition: opacity .3s;
//  transition: opacity .3s;
//}
::v-deep .elx-cell .el-popover__reference-wrapper .el-loading-mask .el-loading-spinner {
  margin-top: -12px !important;
  background-color: #e6f7ff !important;
}
::v-deep #paginationTotal .el-loading-mask{
  top: 2px !important;
  .el-loading-spinner {
    margin-top: -15px !important;
  }
}
</style>
