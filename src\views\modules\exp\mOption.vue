<template>
  <el-dialog
    title="作业成本维护"
    :visible.sync="dialogVisible"
    width="30%"
    :before-close="handleClose">
    <el-form ref="form" :model="form" label-width="80px">
      <el-form-item label="作业节点">
        <el-select v-model="dataForm.jobNode" placeholder="placeholder">
          <el-option></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="供应商">
        <el-select v-model="dataForm.province" placeholder="placeholder">
          <el-option></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="发生时间">
        <el-date-picker
          v-model="dataForm.time"
          class="width100"
          type="datetime"
          placeholder="选择日期时间"
          default-time="12:00:00">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="总实际重量">
        <el-input v-model="dataForm.totalWeight" placeholder="placeholder"></el-input>
      </el-form-item>
      <el-form-item label="总体积">
        <el-input v-model="dataForm.totalWeight" placeholder="placeholder"></el-input>
      </el-form-item>
      <el-form-item label="总体积重">
        <el-input v-model="dataForm.totalWeight" placeholder="placeholder"></el-input>
      </el-form-item>
      <el-form-item label="费用项">
        <el-row type="flex" justify="center" gutter="10" v-for="(item, index) in dataForm.costItem" :key="index">
         <el-col :span="10">
           <el-form-item>
             <el-select v-model="item.itemType" placeholder="placeholder">
               <el-option></el-option>
             </el-select>
           </el-form-item>
         </el-col>
          <el-col :span="10">
            <el-form-item>
              <el-input type="number" v-model="item.totalCost" placeholder="请输入总成本"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4" >
            <el-button type="text" v-if="index > 0" @click="delHandler(index)"> <i class="el-icon-close"></i></el-button>
          </el-col>
        </el-row>
        <el-button type="primary" @click="addHandler"><i class="el-icon-plus"></i> 新增费用项</el-button>
      </el-form-item>
    </el-form>
    <p class="warning">成本一旦提交将无法回滚，请再三确认无问题后提交</p>
    <span slot="footer" class="dialog-footer">
      <el-button >取 消</el-button>
      <el-button type="primary" >确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'mOption',
  data () {
    return {
      dialogVisible: true,
      dataForm: {
        jobNode: '',
        province: '',
        time: '',
        totalWeight: '',
        costItem: [
          {
            itemType: '',
            totalCost: ''
          }
        ]
      }
    }
  },
  methods: {
    addHandler () {
      // 新增
      this.dataForm.costItem.push({
        itemType: '',
        totalCost: ''
      })
    },
    delHandler (index) {
      // 删除
      this.dataForm.costItem.splice(index, 1)
    }
  }
}
</script>

<style scoped>

</style>
