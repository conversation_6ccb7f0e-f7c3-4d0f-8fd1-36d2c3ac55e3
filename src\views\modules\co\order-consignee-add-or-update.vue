<template>
  <div class="add-body panel_body">
    <div class="panel-hd">{{!dataForm.id ? $t('add') : $t('update')}}</div>
    <div class="addOrUpdatePanel" >
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" label-width="80px">
        <el-row type="flex" justify="start">
          <el-col :md="{span:13, offset: 2}">
            <el-form-item :label="$t('coOrderConsignee.consigneeName')" prop="consigneeName">
              <el-input v-model="dataForm.consigneeName" :placeholder="$t('coOrderConsignee.consigneeName')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderConsignee.consigneeCompany')" prop="consigneeCompany">
              <el-input v-model="dataForm.consigneeCompany" :placeholder="$t('coOrderConsignee.consigneeCompany')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderConsignee.consigneePhone')" prop="consigneePhone">
              <el-input v-model="dataForm.consigneePhone" :placeholder="$t('coOrderConsignee.consigneePhone')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderConsignee.consigneeEmail')" prop="consigneeEmail">
              <el-input v-model="dataForm.consigneeEmail" :placeholder="$t('coOrderConsignee.consigneeEmail')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderConsignee.consigneeCountry')" prop="consigneeCountry">
              <el-input v-model="dataForm.consigneeCountry" :placeholder="$t('coOrderConsignee.consigneeCountry')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderConsignee.consigneeProvince')" prop="consigneeProvince">
              <el-input v-model="dataForm.consigneeProvince" :placeholder="$t('coOrderConsignee.consigneeProvince')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderConsignee.consigneeCity')" prop="consigneeCity">
              <el-input v-model="dataForm.consigneeCity" :placeholder="$t('coOrderConsignee.consigneeCity')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderConsignee.consigneeDistrict')" prop="consigneeDistrict">
              <el-input v-model="dataForm.consigneeDistrict" :placeholder="$t('coOrderConsignee.consigneeDistrict')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderConsignee.consigneeAddress')" prop="consigneeAddress">
              <el-input v-model="dataForm.consigneeAddress" :placeholder="$t('coOrderConsignee.consigneeAddress')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderConsignee.consigneePostcode')" prop="consigneePostcode">
              <el-input v-model="dataForm.consigneePostcode" :placeholder="$t('coOrderConsignee.consigneePostcode')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderConsignee.consigneeDoorplate')" prop="consigneeDoorplate">
              <el-input v-model="dataForm.consigneeDoorplate" :placeholder="$t('coOrderConsignee.consigneeDoorplate')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderConsignee.consigneeStreet')" prop="consigneeStreet">
              <el-input v-model="dataForm.consigneeStreet" :placeholder="$t('coOrderConsignee.consigneeStreet')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderConsignee.consigneeIdcard')" prop="consigneeIdcard">
              <el-input v-model="dataForm.consigneeIdcard" :placeholder="$t('coOrderConsignee.consigneeIdcard')"></el-input>
            </el-form-item>
          </el-col>

        </el-row>
      </el-form>
      <div id="cs_FormFooter" class="el-form-footer">
        <el-button @click="cancelFn">{{ $t('cancel') }}</el-button>
        <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      dataForm: {
        id: '',
        creator: '',
        createDate: '',
        consigneeName: '',
        consigneeCompany: '',
        consigneePhone: '',
        consigneeEmail: '',
        consigneeCountry: '',
        consigneeProvince: '',
        consigneeCity: '',
        consigneeDistrict: '',
        consigneeAddress: '',
        consigneePostcode: '',
        consigneeDoorplate: '',
        consigneeStreet: '',
        consigneeIdcard: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        creator: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        createDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        consigneeName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        consigneeCompany: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        consigneePhone: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        consigneeEmail: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        consigneeCountry: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        consigneeProvince: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        consigneeCity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        consigneeDistrict: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        consigneeAddress: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        consigneePostcode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        consigneeDoorplate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        consigneeStreet: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        consigneeIdcard: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/co/orderconsignee/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/co/orderconsignee/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    // 取消
    cancelFn () {
      this.visible = false
      this.$emit('cancelAddOrUpdate')
    }
  }
}
</script>
