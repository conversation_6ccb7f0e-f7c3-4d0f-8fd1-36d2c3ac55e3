<template>
  <div></div>
</template>

<script>
export default {

  created () {
    this.route()
  },
  methods: {
    route () {
      let params = this.$route.query
      let dataForm = {
        id: '',
        weightD: 1,
        objectType: 0,
        objectId: 0,
        productType: 10,
        payable: 0,
        client: 1,
        city: params.city,
        customerOrderNo: params.customerOrderNo,
        province: params.province,
        postcode: params.postcode,
        address: params.address,
        country: params.country,
        street: '',
        name: '',
        phone: '',
        district: params.district,
        fbaWarehouseCode: '',
        logisticsProductCode: params.logisticsProductCode,
        psCalculateExpenseDetailList: [{
          lengthD: params.length,
          widthD: params.width,
          heightD: params.height,
          weightD: params.weight
        }]
      }
      this.$http['post']('/co/order/createOrder', dataForm).then(({ data: res }) => {
        if (res.code !== 0) {
          this.$alert(res.msg, '错误信息', {
            confirmButtonText: '确定',
            dangerouslyUseHTMLString: true
          })
          return false
        } else {
          console.log('orderLogisticsType = ' + res.data.orderLogisticsType)
          if (res.data.orderLogisticsType === 10) {
            this.$router.push({ name: 'co-orderEntry', query: { res: res, opType: 'coOrder.rcopy' }, params: { metaTitle: '订单录入' } })
          } else if (res.data.orderLogisticsType === 11) {
            this.$router.push({ name: 'express-orderEntry', query: { res: res, opType: 'express.header.copy' }, params: { metaTitle: '快递订单录入' } })
          } else {
            this.$router.push({ name: 'fba-orderEntry', query: { res: res, opType: 'fba.header.copy' }, params: { metaTitle: 'FBA订单录入' } })
          }
        }
      }).catch(() => {})
    }
  }
}
</script>
