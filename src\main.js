import Vue from 'vue'
import { UxGrid, UxTableColumn } from 'umy-ui'
import Element from 'element-ui'
import App from '@/App'
import i18n from '@/i18n'
import router from '@/router'
import store from '@/store'
import 'umy-ui/lib/theme-chalk/index.css'
import '@/icons'
import '@/element-ui/theme/index.css'
import '@/assets/scss/aui.scss'
import description from '@/components/description'
import http from '@/utils/request'
import { hasPermission, footerScroll, naturalNumberFormat } from '@/utils'
import cloneDeep from 'lodash/cloneDeep'
import config from '@/utils/config'
import popconfirm from '@/components/popconfirm'

Vue.config.productionTip = false

Vue.use(Element, {
  size: 'default',
  i18n: (key, value) => i18n.t(key, value)
})
Vue.use(UxGrid)
Vue.use(UxTableColumn)
Vue.component('popconfirm', popconfirm)
Vue.component('description', description)
// 挂载全局
Vue.prototype.$http = http
Vue.prototype.$hasPermission = hasPermission
Vue.prototype.$naturalNumberFormat = naturalNumberFormat
Vue.prototype.$baseUrl = config.apiURL
Vue.prototype.$src = process.env.VUE_APP_SRC
Vue.prototype.$footerScroll = footerScroll
Vue.prototype.$bus = new Vue()

// 保存整站vuex本地储存初始状态
window.SITE_CONFIG['storeState'] = cloneDeep(store.state)
// 监听本地存储删除刷新页面
// var checkCookie = (function() {
//   var lastCookie = document.cookie // 'static' memory between function calls
//   return function() {
//     var currentCookie = document.cookie
//     if (currentCookie !== lastCookie) {
//       // something useful like parse cookie, run a callback fn, etc.
//       lastCookie = currentCookie // store latest cookie
//       location.reload()
//     } else {
//       this.$router.push({ name: 'home' })
//     }
//   }
// }())

// window.setInterval(checkCookie, 50) // run every 50 ms
new Vue({
  i18n,
  router,
  store,
  render: h => h(App)
}).$mount('#app')
