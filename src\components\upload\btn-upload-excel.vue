<template>
  <el-dialog :visible.sync="visible" :title="$t('title.excelUpload')" min-widt="400" :close-on-click-modal="false" :close-on-press-escape="false" :lock-scroll="true">
    <el-upload
      :data="param"
      :action="url"
      :before-upload="beforeUploadHandle"
      :on-success="successHandle"
      :on-error="errorHandle"
      :headers="getToken"
      :multiple="false"
      accept=".xls,.xlsx"
      class="text-center"
      style="padding-bottom: 10px;">
      <el-button size="small" type="primary">{{$t('btn.upload')}}</el-button>
      <div class="el-upload__tip" slot="tip">{{ $t('upload.tip', { 'format': 'xls、xlsx' }) }}且不超过{{sizeLimit}}M
        <a :href="fileUrl" target="_blank">{{$t('import_module')}}<i class="el-icon-download"></i></a>
      </div>
    </el-upload>
    <template slot="footer">
      <el-button @click="cancelFn">{{ $t('cancel') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import Cookies from 'js-cookie'
import { clearLoginInfo } from '@/utils'

export default {
  props: {
    importExcelUrl: '',
    downLoadUrl: '',
    excelSizeLimit: ''
  },
  data () {
    return {
      visible: false,
      uploadDisable: false, // 避免重复上传
      token: '',
      lang: '',
      fileUrl: '',
      // 添加参数
      param: {},
      // 修改上传路径
      url: '',
      exportURL: '',
      sizeLimit: 2
    }
  },
  methods: {
    init (param) {
      this.token = Cookies.get('cs_token')
      this.visible = true
      this.url = this.importExcelUrl
      this.lang = Cookies.get('language') || 'zh-CN'
      this.sizeLimit = this.excelSizeLimit
      if (param) {
        this.param = param
      } else {
        this.param = null
      }
      let url = process.env.NODE_ENV === 'production' ? location.origin : (location.origin || this.$baseUrl)
      this.fileUrl = `${url}/static/` + this.downLoadUrl
    },
    beforeUploadHandle (file) {
      this.uploadDisable = true
      let regStr = '(.xlsx)$|(.xls)$'
      let regx = new RegExp(regStr)
      if (!regx.test(file.name)) {
        this.$message.error(this.$t('upload.tip', { 'format': `xlsx、xls` }))
        return false
      }
      const sizeLimit = file.size / 1024 / 1024 < this.sizeLimit
      if (this.sizeLimit > 0 && !sizeLimit) {
        this.$message.error(this.$t('upload.sizeMsg', { 'size': `${this.sizeLimit}` }))
        return false // 必须返回false
      }
    },
    // 上传失败
    errorHandle (error) {
      console.log('error', error)
      this.uploadDisable = false
      let myError = error.toString()
      myError = myError.replace('Error: ', '')
      console.log('myError')
      return this.$message({
        dangerouslyUseHTMLString: true,
        message: myError || '发生错误',
        type: 'error',
        duration: 2500
      })
    },
    successHandle (res, file) {
      if (res.code !== 0) {
        this.uploadDisable = false
        this.message = res.msg && res.msg.replace(/<br>/g, '\r\n')
        if (this.message === '未授权') {
          clearLoginInfo()
          this.$router.push({ name: 'login' })
        }
      } else {
        this.uploadDisable = false
        this.message = this.$t('prompt.success')
        this.$emit('successHandle')
      }
    },
    cancelFn () {
      this.visible = false
    }
  },
  computed: {
    getToken () {
      return {
        token: this.token,
        'Accept-Language': this.lang
      }
    }
  }
}
</script>

<style scoped>

</style>
