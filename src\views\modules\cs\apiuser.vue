<template>
  <div class="add-body panel_body">
    <div class="panel-hd">
      <span v-text="$t('bdCustomerApiuser.title')"></span>
    </div>
    <div class="detail-desc">
      <el-form ref="form" label-width="80px">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item :label="$t('bdCustomerApiuser.usertoken')">
              <span v-text="dataForm.usertoken"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('bdCustomerApiuser.dom')">
              <a :href="parcelFileUrl" target="_blank">{{$t('bdCustomerApiuser.parceldom')}}<i class="el-icon-download"></i></a>
              <a :href="fbaFileUrl" target="_blank" style="margin-left: 20px;">{{$t('bdCustomerApiuser.fbadom')}}<i class="el-icon-download"></i></a>
              <a :href="expressFileUrl" target="_blank" style="margin-left: 20px;">{{$t('bdCustomerApiuser.expressdom')}}<i class="el-icon-download"></i></a>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      parcelFileUrl: '',
      fbaFileUrl: '',
      expressFileUrl: '',
      dataForm: {
        id: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: '',
        status: '',
        version: '',
        customerId: '',
        usertoken: ''
      }
    }
  },
  mounted () {
    let url = process.env.NODE_ENV === 'production' ? location.origin : (location.origin || this.$baseUrl)
    this.parcelFileUrl = `${url}/static/doc/ITS-API-DOCUMENT.pdf`
    this.fbaFileUrl = `${url}/static/doc/ITS-FBA-API-DOCUMENT.pdf`
    this.expressFileUrl = `${url}/static/doc/ITS-EXPRESS-API-DOCUMENT.pdf`
  },
  created () {
    this.init()
  },
  methods: {
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        this.getInfo()
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/cs/apiuser`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 返回
    backFn () {
      this.visible = false
      this.$emit('backView')
    }
  }
}
</script>
<style lang='scss' scoped>
  .detail-desc{
    padding: 20px 0 0 0;
    max-width: 560px;
    margin: 0 auto;
  }
</style>
