<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-sys__role flex_wrap">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="queryPageByParam()">
        <el-form-item>
          <el-input v-model="dataForm.name" :placeholder="$t('role.name')" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="queryPageByParam()">{{ $t('query') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('cs:role:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('cs:role:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
        </el-form-item>
      </el-form>
      <div class="flex_1" ref="tableElm" v-domResize="redraw">
      <el-table
        v-loading="dataListLoading"
        :data="dataList"
        @selection-change="dataListSelectionChangeHandle"
        @sort-change="dataListSortChangeHandle"
        :max-height="tableHeight">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="name" :label="$t('role.name')" header-align="center" align="center"></el-table-column>
        <!--<el-table-column prop="deptId" :label="$t('user.warehouseName')" header-align="center" align="center">-->
          <!--<template slot-scope="scope">-->
            <!--{{ (deptList.filter(item => item.id === scope.row.deptId)[0]||{}).name }}-->
          <!--</template>-->
        <!--</el-table-column>-->
        <el-table-column prop="remark" :label="$t('role.remark')" header-align="center" align="center"></el-table-column>
        <el-table-column prop="createDate" :label="$t('role.createDate')" sortable="custom" header-align="center" align="center" width="180">
          <template slot-scope="scope">
            <span>{{scope.row.createDate | gtmToLtm}}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-button v-if="$hasPermission('cs:role:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
            <el-button v-if="$hasPermission('cs:role:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      </div>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import listPage from '@/mixins/listPage'
import AddOrUpdate from './role-add-or-update'
import { gtmToLtm } from '@/filters/filters'
export default {
  mixins: [listPage, mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/cs/role/page',
        getDataListIsPage: true,
        deleteURL: '/cs/role',
        deleteIsBatch: true
      },
      deptList: [],
      dataForm: {
        name: ''
      }
    }
  },
  mounted () {
    // this.getDeptList()
  },
  methods: {
    // // 获取部门列表
    // getDeptList () {
    //   return this.$http.get('/cs/dept/list').then(({ data: res }) => {
    //     if (res.code !== 0) {
    //       return this.$message.error(res.msg)
    //     }
    //     this.deptList = res.data
    //     console.log(this.deptList)
    //   }).catch(() => {})
    // }
  },
  components: {
    AddOrUpdate
  },
  filters: {
    gtmToLtm
  }
}
</script>
