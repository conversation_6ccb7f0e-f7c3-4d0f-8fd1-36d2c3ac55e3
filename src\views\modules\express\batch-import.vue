<template>
  <div>
    <batchImportOrderExcel ref='batchImportOrderExcel'
                           title="fba.batchOrderImport"
                           bodyClass='panel_body'
                           :selectTemplate="true"
                           :defaultSelect="true"
                           :importExcelUrl="batchOrderImportUrl"
                           @hasDeclareInfoChange='hasDeclareInfoChange'
                           @backImportExcel="$closeFn"
                           return-btn-name='close'
    ></batchImportOrderExcel>
  </div>
</template>

<script>
import closeMixins from '@/mixins/closeMixins'
import batchImportOrderExcel from '@/components/importExcel/fba-multiple-mode-import'
import Cookies from 'js-cookie'
export default {
  mixins: [closeMixins],
  data () {
    return {
      hasDeclareInfoVal: 1,
      templateList: [
        { 'hasDeclareInfo': true, 'name': '快递-多票批量导入', 'downloadUrl': 'xls/Express-MultiOrdersBatchImport.xlsx', 'value': 7 },
        { 'hasDeclareInfo': false, 'name': '快递-多票批量导入-无申报信息', 'downloadUrl': 'xls/Express-MultiOrdersBatchImport-NoDeclare.xlsx', 'value': 8 }
      ]
    }
  },
  filters: {
  },
  computed: {
    // 赋值导入URL
    batchOrderImportUrl() {
      return `${this.$baseUrl}/express/order/batchImport?node=11`
    },
    downLoadBatchOrderImportTempUrl() {
      return `xls/Express-batchOrderImport.xlsx`
    },
    panelShow() {
      let ret = false
      if (this.importExcelVisible) {
        ret = true
      }
      return ret
    },
    getToken() {
      return {
        token: Cookies.get('token')
      }
    }
  },
  created () {
    this.$nextTick(() => {
      this.$refs.batchImportOrderExcel.visible = true
      this.$refs.batchImportOrderExcel.templateList = this.allTemplateList()
      this.$refs.batchImportOrderExcel.init()
    })
  },
  methods: {
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        this.$refs['dataForm'].resetFields()
      })
    },
    hasDeclareInfoChange(val) {
      this.hasDeclareInfoVal = val
      this.$nextTick(() => {
        this.$refs.batchImportOrderExcel.templateList = this.allTemplateList()
      })
    },
    allTemplateList () {
      let items = this.templateList.filter(item => item.hasDeclareInfo === Boolean(this.hasDeclareInfoVal))
      return items
    }
  },
  components: {
    batchImportOrderExcel
  }
}
</script>
<style lang="scss" scoped>

</style>
