<template>
  <el-dialog width="90%" top="10px" :visible.sync="visible" id='billsAndBiz-all-detail-dialog' :title="$t('view')" :close-on-click-modal="false" :close-on-press-escape="false" @closed="closedDialogCallback()" :lock-scroll="true">
    <div>
        <el-tabs class="no_shadow" v-model="activeTagName" type="card">
          <el-tab-pane label="应收信息" name="fee">
            <el-row :gutter="10">
              <el-col :span="4">
                <div style="padding: 5px;">{{formatterLabel('infoTitle')}}</div>
                <el-card shadow="never">
                  <description label="包裹件数" layout='vertical' :col='{span:24}'>{{dataForm.orderInfo.totalQty}}</description>
                  <description label="总实重(KG)" layout='vertical' :col='{span:24}'>{{dataForm.orderInfo.totalWeightD}}</description>
                  <description label="总体积(CM³)" layout='vertical' :col='{span:24}'>{{dataForm.orderInfo.volD}}</description>
                  <description label="结算重(KG)" layout='vertical' :col='{span:24}'>{{dataForm.orderInfo.balanceWeightD}}</description>
                  <description :label="$t('baReceivableFee.waybillNo')" layout='vertical' :col='{span:24}'>{{dataForm.orderInfo.waybillNo}}</description>
                  <description  v-if="waybillType==10" :label="$t('baReceivableFee.deliveryNo')" layout='vertical' :col='{span:24}'>{{dataForm.orderInfo.deliveryNo}}</description>
                  <description v-if="waybillType==10" :label="formatterLabel('logistics')" layout='vertical' :col='{span:24}'>{{dataForm.orderInfo.logisticsProductCode | formatterCodeName(logisticsProductList)}}</description>
                  <description :label="$t('baReceivableFee.consigneeCountry')" layout='vertical' :col='{span:24}'>{{dataForm.orderInfo.consigneeCountry}}</description>
                  <description :label="$t('baReceivableFee.consigneeProvince')" layout='vertical' :col='{span:24}'>{{dataForm.orderInfo.consigneeProvince}}</description>
                  <description :label="$t('baReceivableFee.consigneeCity')" layout='vertical' :col='{span:24}'>{{dataForm.orderInfo.consigneeCity}}</description>
                  <description :label="$t('baReceivableFee.consigneePostcode')" layout='vertical' :col='{span:24}'>{{dataForm.orderInfo.consigneePostcode}}</description>
                </el-card>
              </el-col>
              <el-col :span="20">
                <div style="padding: 5px;">
                  <span >{{  '应收费用' }}
                  <span v-if='waybillType === 10' class='margin_left15 fontSize12' id='showReferencePrice'>
                     <el-checkbox v-model="showReferencePrice"  label='显示参考公斤价'></el-checkbox>
                  </span>
                  </span>
                </div>
                <div style='display: flex;flex-direction: column;' id='auditAndNotAuditCards'>
                  <!-- 未审核费用               -->
                  <el-card shadow="never" v-if="dataForm.auditData.notAudit.length>0" :style="{order: (dataForm.auditData.audit.length>0 && dataForm.auditData.notAudit.length<=0 ?2:1), marginBottom: 10+'px',marginTop:0+'px'}">
                    <div class="flex_table" ref="tableElm">
                      <el-row style="padding: 5px;">
                        <el-col :md="8" class="optBtn_leftFixed">
                          <span class='fontSize12'>未审核费用</span>
                        </el-col>
                        <el-col :md="16" class="fontSize12 text-right">
                          <!--保留空格符-->
                          <div v-if="dataForm.auditData.notAuditSumList.length===0">
                            {{ $t('baReceivableFee.currency') }}:&nbsp;<b>--</b>
                            <el-divider direction="vertical"></el-divider>
                            {{ $t('baReceivableFee.sum') }}:&nbsp;<b>--</b>
                          </div>
                          <div v-else v-for="(currencySum, index) in dataForm.auditData.notAuditSumList" :key="index">
                            {{ $t('baReceivableFee.currency') }}:&nbsp;<b>{{ currencySum.currency }}</b>
                            <el-divider direction="vertical"></el-divider>
                            {{ $t('baReceivableFee.sum') }}:&nbsp;<b>{{ currencySum.sumD | numberFormat(3) }}</b>
                          </div>
                        </el-col>
                      </el-row>
                      <el-table class="flex_table" ref="tableData" size='mini' :key='0' border v-loading="receivableDataListLoading"
                                :data="dataForm.auditData.notAudit" :max-height="dataForm.auditData.audit.length>0 ? '290px' : '390px'">
                        <el-table-column prop="feeTypeId" :label="$t('baOperatingCostFee.feeTypeId')" min-width="80" header-align="center" align="center">
                          <template slot-scope="scope">
                            <span>{{ formatterFeeType(scope.row.feeTypeId, usingFeeTypeList) }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column prop="sumD" :label="$t('baOperatingCostFee.sum')" min-width="80" header-align="center" align="center">
                          <template slot-scope="scope">
                            <span>{{scope.row.sumD}}</span>
                          </template>
                        </el-table-column>
                        <el-table-column v-if=" showReferencePrice && waybillType === 10" prop="referencePrice" :label="$t('baReceivableBizOrder.referencePrice')" min-width="90" header-align="center" align="center">
                          <template slot-scope="scope">
                            <span>{{scope.row.referencePrice}}</span>
                          </template>
                        </el-table-column>
                        <el-table-column prop="currency" :label="$t('baOperatingCostFee.currency')" min-width="80" header-align="center" align="center">
                          <template slot-scope="scope">
                            <span>{{scope.row.currency | formatterCodeName(currencyList)}}</span>
                          </template>
                        </el-table-column>
                        <el-table-column prop="remark" :label="$t('baOperatingCostFee.remark')" min-width="80" header-align="center" align="center">
                          <template slot-scope="scope">
                            <span>{{ waybillType === 10 ? scope.row.memo : scope.row.remark}}</span>
                          </template>
                        </el-table-column>
                        <el-table-column prop="billingDate" :label="$t('baReceivableFee.billingDate')"  width="150">
                          <template slot-scope="itemscope">
                            <el-popover placement="right" width="790" trigger="hover"  :open-delay='300' :close-delay='300'>
                              <el-table v-if="logShow" size='mini' :data="logDataList">
                                <el-table-column width="80" property="feeTypeId" :label="$t('baReceivableFee.feeTypeId')">
                                  <template slot-scope="logscope">
                                    {{ formatterFn(logscope, 'feeTypeId') }}
                                  </template>
                                </el-table-column>
                                <el-table-column width="80" property="currency" :label="$t('baReceivableFee.currency')">
                                  <template slot-scope="logscope">
                                    {{ formatterFn(logscope, 'currency') }}
                                  </template>
                                </el-table-column>
                                <el-table-column width="80" align="right" property="sumD" :label="$t('baReceivableFee.sum')">
                                  <template slot-scope="logscope">
                                          <span v-if="logscope.row.secondFee===1 && logscope.row.sumD > 0" style="color: blue;">
                                            {{ formatterFn(logscope, 'sumD') }}
                                          </span>
                                    <span v-else-if="logscope.row.secondFee===1 && logscope.row.sumD < 0" style="color: red;">
                                            {{ formatterFn(logscope, 'sumD') }}
                                          </span>
                                    <span v-else>
                                            {{ formatterFn(logscope, 'sumD') }}
                                          </span>
                                  </template>
                                </el-table-column>
                                <el-table-column width="120" property="createType" :label="$t('baReceivableFee.createType')">
                                  <template slot-scope="logscope">
                                    {{ formatterFn(logscope, 'createType') }}
                                  </template>
                                </el-table-column>
                                <el-table-column width="150" property="billingDate" :label="$t('baReceivableFee.billingDate')"></el-table-column>
                                <el-table-column width="100" property="creator" :label="$t('system.creator')">
                                  <template slot-scope="logscope">
                                    {{ formatterFn(logscope, 'creator') }}
                                  </template>
                                </el-table-column>
                                <el-table-column width="150" property="remark" :label="$t('baReceivableFee.memo')"></el-table-column>
                              </el-table>
                              <el-link slot="reference" @mouseover.native="getReceivableLogDataList(itemscope.row)">{{ formatterFn(itemscope, 'billingDate') }}</el-link>
                            </el-popover>
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </el-card>
                  <!-- 已审核费用              -->
                  <el-card shadow="never" v-if="dataForm.auditData.audit.length>0" :style="{marginBottom: 10+'px',marginTop:0+'px',order: dataForm.auditData.audit.length>0 && dataForm.auditData.notAudit.length<=0 ?1:2}">
                    <div class="flex_table" ref="tableElm">
                      <!--已审核费用-->
                      <el-row style="padding: 5px;">
                        <el-col :md="8" class="optBtn_leftFixed">
                          <span class='fontSize12'>已审核费用</span>
                        </el-col>
                        <el-col :md="16" class="fontSize12 text-right">
                          <!--保留空格符-->
                          <div v-if="dataForm.auditData.auditSumList.length===0">
                            {{ $t('baReceivableBizOrder.currency') }}:&nbsp;<b>--</b>
                            <el-divider direction="vertical"></el-divider>
                            {{ $t('baReceivableBizOrder.sum') }}:&nbsp;<b>--</b>
                          </div>
                          <div v-else v-for="(currencySum, index) in dataForm.auditData.auditSumList" :key="index">
                            {{ $t('baReceivableBizOrder.currency') }}:&nbsp;<b>{{ currencySum.currency }}</b>
                            <el-divider direction="vertical"></el-divider>
                            {{ $t('baReceivableBizOrder.sum') }}:&nbsp;<b>{{ currencySum.sumD | numberFormat(3) }}</b>
                          </div>
                        </el-col>
                      </el-row>
                      <el-table v-if="dataForm.auditData.audit.length>0" :key='1' size='mini' class="flex_table" ref="tableData" border v-loading="receivableDataListLoading"  :data="dataForm.auditData.audit" max-height="290px">
                        <el-table-column prop="feeTypeId" :label="$t('baOperatingCostFee.feeTypeId')" min-width="80" header-align="center" align="center">
                          <template slot-scope="scope">
                            <span>{{ formatterFeeType(scope.row.feeTypeId, usingFeeTypeList) }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column prop="sumD" :label="$t('baOperatingCostFee.sum')" min-width="80" header-align="center" align="center">
                          <template slot-scope="scope">
                            <span>{{scope.row.sumD}}</span>
                          </template>
                        </el-table-column>
                        <el-table-column v-if=" showReferencePrice && waybillType === 10" prop="referencePrice" :label="$t('baReceivableBizOrder.referencePrice')" min-width="90" header-align="center" align="center">
                          <template slot-scope="scope">
                            <span>{{scope.row.referencePrice}}</span>
                          </template>
                        </el-table-column>
                        <el-table-column prop="currency" :label="$t('baOperatingCostFee.currency')" min-width="80" header-align="center" align="center">
                          <template slot-scope="scope">
                            <span>{{scope.row.currency | formatterCodeName(currencyList)}}</span>
                          </template>
                        </el-table-column>
                        <el-table-column prop="remark" :label="$t('baOperatingCostFee.remark')" min-width="80" header-align="center" align="center">
                          <template slot-scope="scope">
                            <span>{{ waybillType === 10 ? scope.row.memo : scope.row.remark}}</span>
                          </template>
                        </el-table-column>
                        <el-table-column prop="billingDate" :label="$t('baReceivableFee.billingDate')"  width="150">
                          <template slot-scope="itemscope">
                            <el-popover placement="right" width="790" trigger="hover"  :open-delay='300' :close-delay='300'>
                              <el-table v-if="logShow" size='mini' :data="logDataList">
                                <el-table-column width="80" property="feeTypeId" :label="$t('baReceivableFee.feeTypeId')">
                                  <template slot-scope="logscope">
                                    {{ formatterFn(logscope, 'feeTypeId') }}
                                  </template>
                                </el-table-column>
                                <el-table-column width="80" property="currency" :label="$t('baReceivableFee.currency')">
                                  <template slot-scope="logscope">
                                    {{ formatterFn(logscope, 'currency') }}
                                  </template>
                                </el-table-column>
                                <el-table-column width="80" align="right" property="sumD" :label="$t('baReceivableFee.sum')">
                                  <template slot-scope="logscope">
                                          <span v-if="logscope.row.secondFee===1 && logscope.row.sumD > 0" style="color: blue;">
                                            {{ formatterFn(logscope, 'sumD') }}
                                          </span>
                                    <span v-else-if="logscope.row.secondFee===1 && logscope.row.sumD < 0" style="color: red;">
                                            {{ formatterFn(logscope, 'sumD') }}
                                          </span>
                                    <span v-else>
                                            {{ formatterFn(logscope, 'sumD') }}
                                          </span>
                                  </template>
                                </el-table-column>
                                <el-table-column width="120" property="createType" :label="$t('baReceivableFee.createType')">
                                  <template slot-scope="logscope">
                                    {{ formatterFn(logscope, 'createType') }}
                                  </template>
                                </el-table-column>
                                <el-table-column width="150" property="billingDate" :label="$t('baReceivableFee.billingDate')"></el-table-column>
                                <el-table-column width="100" property="creator" :label="$t('system.creator')">
                                  <template slot-scope="logscope">
                                    {{ formatterFn(logscope, 'creator') }}
                                  </template>
                                </el-table-column>
                                <el-table-column width="150" property="remark" :label="$t('baReceivableFee.memo')"></el-table-column>
                              </el-table>
                              <el-link slot="reference" @mouseover.native="getReceivableLogDataList(itemscope.row)">{{ formatterFn(itemscope, 'billingDate') }}</el-link>
                            </el-popover>
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </el-card>
                </div>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="包裹信息" name="package">
            <el-row>
                <description :label="$t('baReceivableFee.businessId')" size='small' :col='{span:5}'>{{orderDataForm.id}}</description>
                <description :label="$t('baReceivableFee.totalWeight')" size='small' :col='{span:4}'>{{orderDataForm.totalWeightD}}</description>
                <description :label="$t('baReceivableFee.consigneeCountry')" size='small' :col='{span:2}'>
                  {{orderDataForm.consigneeCountry | formatterCodeName(countryList)}}
                </description>
                <description :label="$t('baReceivableFee.consigneePostcode')" size='small' :col='{span:4}'>{{ orderDataForm.consigneePostcode}}</description>
            </el-row>
            <el-card  class="search_box" shadow="never">
                <el-form ref="searchSubForm"  class='form-divider' :model="subDataForm" @keyup.enter.native="searchSubHandle()" label-width="0px">
                    <el-row :gutter="10" type="flex">
                      <el-col :span="21">
                        <three-no-input ref="subThreeNoInput" :deliveryNo.sync="subDataForm.deliveryNos" label='' :orderAreaList='orderAreaList'
                                        :packageNo.sync="subDataForm.packageNos" :autosize="{ minRows: 2, maxRows: 2}" :noSize="50" />
                      </el-col>
                      <el-col :span="3">
                        <div class="sub_search_box_btn no_more">
                          <div>
                            <el-button type="primary" @click="searchSubHandle()" icon="el-icon-search">查询</el-button>
                          </div>
                          <div style="padding-top: 5px;">
                            <el-button @click.native="resetSubForm('searchSubForm')" icon="el-icon-refresh-right">重置</el-button>
                          </div>
                        </div>
                      </el-col>
                    </el-row>
                  </el-form>
                <div class="flex_table" ref="subTableElm" v-domResize="redraw">
                  <el-table v-loading="subDataListLoading" :key='3' highlight-current-row :data="subDataList" tooltip-effect='light' :border='true' size='mini'
                            row-key="id" :expand-row-keys="subExpands" @expand-change="subExpandChange" max-height="50%" :row-class-name='getRowClassName'>
                    <!--费用明细-->
                    <el-table-column type="expand" fixed="left">
                      <template slot-scope="expand_scope">
                        <div v-if="expand_scope.row.feeItemList">
                          <el-table size="mini" :data="expand_scope.row.feeItemList" style='width: fit-content !important;' border>
                              <el-table-column prop="feeTypeId" :label="$t('baReceivableFee.feeTypeId')"  width="80">
                                <template slot-scope="itemscope">
                                  {{ formatterFn(itemscope, 'feeTypeId') }}
                                </template>
                              </el-table-column>
                              <el-table-column prop="currency" :label="$t('baReceivableFee.currency')"  width="80">
                                <template slot-scope="itemscope">
                                  {{ formatterFn(itemscope, 'currency') }}
                                </template>
                              </el-table-column>
                              <el-table-column align="right" prop="sumD" :label="$t('baReceivableFee.sum')"  width="120">
                                <template slot-scope="itemscope">
                                  {{ formatterFn(itemscope, 'sumD') }}
                                </template>
                              </el-table-column>
                              <el-table-column prop="remark" :label="$t('baReceivableFee.memo')"  width="150">
                                <template slot-scope="itemscope">
                                  {{ formatterFn(itemscope, 'remark') }}
                                </template>
                              </el-table-column>
                              <el-table-column prop="auditDate" :label="$t('baReceivableFee.billingDate')"  width="150">
                                <template slot-scope="itemscope">
                                  <el-popover placement="right" width="790" trigger="hover" :open-delay='300' :close-delay='300'>
                                    <el-table  :data="logDataList">
                                      <el-table-column width="80" property="feeTypeId" :label="$t('baReceivableFee.feeTypeId')">
                                        <template slot-scope="logscope">
                                          {{ formatterFn(logscope, 'feeTypeId') }}
                                        </template>
                                      </el-table-column>
                                      <el-table-column width="60" property="currency" :label="$t('baReceivableFee.currency')">
                                        <template slot-scope="logscope">
                                          {{ formatterFn(logscope, 'currency') }}
                                        </template>
                                      </el-table-column>
                                      <el-table-column width="80" align="right" property="sumD" :label="$t('baReceivableFee.sum')">
                                        <template slot-scope="logscope">
                                          {{ formatterFn(logscope, 'sumD') }}
                                        </template>
                                      </el-table-column>
                                      <el-table-column width="100" property="createType" :label="$t('baReceivableFee.createType')">
                                        <template slot-scope="logscope">
                                          {{ formatterFn(logscope, 'createType') }}
                                        </template>
                                      </el-table-column>
                                      <el-table-column width="150" property="billingDate" :label="$t('baReceivableFee.billingDate')"></el-table-column>
                                      <el-table-column width="100" property="creator" :label="$t('system.creator')">
                                        <template slot-scope="logscope">
                                          {{ formatterFn(logscope, 'creator') }}
                                        </template>
                                      </el-table-column>
                                      <el-table-column width="150" property="remark" :label="$t('baReceivableFee.memo')"></el-table-column>
                                    </el-table>
                                    <el-link slot="reference" @mouseover.native="getReceivableLogDataList(itemscope.row)">{{ formatterFn(itemscope, 'billingDate') }}</el-link>
                                  </el-popover>
                                </template>
                              </el-table-column>
                            </el-table>
                        </div>
                      </template>
                    </el-table-column>
                    <!-- 动态显示表格 -->
                    <el-table-column v-for="(item, index) in subTableColumnsArr"  :key="index" :type="item.type"
                                     :prop="item.prop" show-overflow-tooltip :sortable='item.sortable' resizable :border='false'
                                     header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
                      <template slot-scope="scope">
                          <template v-if="item.prop === 'createDate'">
                            <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                              <span>{{formatterFn(scope,item.prop)}}</span>
                            </el-tooltip>
                          </template>
                          <template v-else-if="item.prop === 'businessId'">
                            <el-link  type="primary" :underline="false" @click="viewHandle(scope.row.businessId)">{{scope.row.businessId}}</el-link>
                          </template>
                          <template v-else-if="item.prop === 'status'">
                            <span style="color: red" v-if="scope.row.status === 20">{{formatterFn(scope,item.prop)}}</span>
                            <span v-else>{{formatterFn(scope,item.prop)}}</span>
                          </template>
                          <template v-else-if="item.prop === 'sumList'">
                            <template v-for="(item, index) in scope.row.sumList"  >
                              <span :key='index + Math.random()'>{{ item.sum | numberFormat(3)}}</span>
                              <span :key='index + Math.random()' style="color: green;font-size: 8px;padding-left: 3px;" class="order-sum">{{item.currency }}</span>
                            </template>
                          </template>
                          <template v-else>
                            <span >{{formatterFn(scope,item.prop)}}</span>
                          </template>
                      </template>
                    </el-table-column>
                    <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="80" v-if='hasSubAdjustFee'>
                      <template slot="header" slot-scope="scope">
                        <span>{{$t('handle')}}</span>
                        <el-tooltip :content="$t('tableConfigTooltip')" placement="top">
                          <el-link style="margin-left:10px; font-size:16px" class="el-icon-setting" @click="settingCol(subTableColumns)"></el-link>
                        </el-tooltip>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
                <el-pagination background :current-page="subPage" :page-sizes="[10, 20, 50, 100]" :page-size="subLimit" :total="subTotal" layout="total, sizes, prev, pager, next, jumper" @size-change="subPageSizeChangeHandle" @current-change="subPageCurrentChangeHandle">
                </el-pagination>
              </el-card>
          </el-tab-pane>
        </el-tabs>
      <!-- 详情页面 -->
      <receivableViewDetail v-if="waybillType == 10 && viewVisible" ref="viewDetail" @refreshDataList='refreshDataList' @backView="backView" />
    </div>
    <template slot="footer">
      <el-button @click="closedDialogCallback">{{ $t('close') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import listPage from '@/mixins/listPage'
import {
  multiply,
  formatterCodeName,
  formatterType,
  formatterName,
  numberFormat,
  timestampFormat, gtmToLtm, formatterShowName
} from '@/filters/filters'
import api from '@/api'
import baseData from '@/api/baseData'
import dictTypeMixins from '@/mixins/dictTypeMixins'
import mixinViewModule from '@/mixins/view-module'
import threeNoInput from '@/components/three-no-input'
export default {
  mixins: [listPage, dictTypeMixins, mixinViewModule],
  props: {
    // 10=应收 20=应付
    waybillType: {
      type: Number,
      required: true
    },
    // 是否有子单调节计费
    hasSubAdjustFee: {
      type: Boolean,
      default: true,
      required: false
    },
    // 包裹列表查询默认参数名
    subDataFormParamName: {
      type: String,
      required: false,
      default: 'billsId'
    }
  },
  data () {
    return {
      mixinViewModuleOptions: {
        getSubDataListURL: '/ba/subbillsreceivable/page',
        getSubDataListIsPage: true,
        initFeeItemListIsNeed: true
      },
      dataForm: {
        bizOrBillOrderId: '',
        businessId: '',
        bizOrderId: '',
        waybillType: '',
        orderInfo: {},
        billsInfo: {},
        auditData: {
          'audit': [],
          'notAudit': [],
          'auditSumList': [],
          'notAuditSumList': []
        },
        viewDataList: {
          'feeList': [],
          'sumList': []
        }
      },
      orderDataForm: {
        cargoDTOS: [],
        id: '',
        customerVoucherNo: '',
        customerName: '',
        totalWeightD: '',
        balanceWeight: '',
        consigneeCountry: ''
      },
      pickerOptions: {
        disabledDate: (time) => {
          return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
        }
      },
      // 订单查询
      orderAreaList: [
        {
          label: '派送单号',
          value: 3
        },
        {
          label: '箱号/FBA唛头',
          value: 6
        }
      ],
      activeTagName: 'fee',
      usingFeeTypeList: [],
      currencyList: [],
      logDataList: [],
      logShow: false,
      logisticsChannelList: [],
      logisticsProductList: [],
      provideList: [],
      countryList: [],
      userList: [],
      divideMethodList: [],
      orderTypeList: [],
      createTypeList: [],
      weightUnitList: [],
      subExpands: [],
      subDataList: [],
      groupByFeeType: 1,
      visible: false,
      viewVisible: false,
      subDataListLoading: false,
      confirmLoading: false,
      receivableDataListLoading: false,
      showReferencePrice: false,
      subTableColumns: [
        { type: '', width: '160', prop: 'packageNo', label: this.$t('baSubBillsReceivable.packageNo'), sortable: true, align: 'center', isShow: true, disabled: false },
        { type: '', width: '160', prop: 'deliveryNo', label: this.$t('baSubBillsReceivable.deliveryNo'), sortable: true, align: 'center', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'sumList', label: this.$t('baReceivableBill.totalSum'), sortable: true, align: 'center', isShow: true, disabled: false },
        { type: '', width: '85', prop: 'balanceWeightD', label: this.$t('baSubBillsReceivable.balanceWeight'), sortable: true, align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'weightD', label: this.$t('baSubBillsReceivable.weight'), sortable: true, align: 'center', isShow: true, disabled: false },
        { type: '', width: '60', prop: 'lengthD', label: this.$t('baSubBillsReceivable.length'), sortable: true, align: 'center', isShow: true, disabled: false },
        { type: '', width: '60', prop: 'widthD', label: this.$t('baSubBillsReceivable.width'), sortable: true, align: 'center', isShow: true, disabled: false },
        { type: '', width: '60', prop: 'heightD', label: this.$t('baSubBillsReceivable.height'), sortable: true, align: 'center', isShow: true, disabled: false },
        { type: '', width: '105', prop: 'volD', label: this.$t('baSubBillsReceivable.volD'), sortable: true, align: 'center', isShow: true, disabled: false },
        { type: '', width: '80', prop: 'girthD', label: this.$t('baSubBillsReceivable.girthD'), sortable: true, align: 'center', isShow: true, disabled: false },
        { type: '', width: '85', prop: 'volumeWeightD', label: this.$t('baSubBillsReceivable.volumeWeightD'), sortable: true, align: 'center', isShow: true, disabled: false },
        { type: '', width: '110', prop: 'volumeCarryWeightD', label: this.$t('baSubBillsReceivable.volumeCarryWeightD'), sortable: true, align: 'center', isShow: true, disabled: false },
        { type: '', width: '110', prop: 'weightCarryWeightD', label: this.$t('baSubBillsReceivable.weightCarryWeightD'), sortable: true, align: 'center', isShow: true, disabled: false },
        // { type: '', width: '50', prop: 'status', label: this.$t('system.status'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '90', prop: 'balanceWeightUnit', label: this.$t('baSubBillsReceivable.balanceWeightUnit'), sortable: false, align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'receivableRemark', label: this.$t('baSubBillsReceivable.receivableRemark'), sortable: false, align: 'center', isShow: true, disabled: false }
      ]
    }
  },
  created () {
    this.getBaseData()
    this.getDict()
  },
  computed: {
    subTableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.subTableColumns).map((key) => this.subTableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    }
  },
  methods: {
    formatVol (value) {
      return numberFormat(value, 0)
    },
    // 包裹信息-头部描述
    openOrderInfo (bizOrBillOrderId) {
      this.dataForm.bizOrBillOrderId = bizOrBillOrderId
      let subDataFormParamName = this.subDataFormParamName
      this.subDataForm[subDataFormParamName] = this.dataForm.bizOrBillOrderId
      this.querySubPageByParam()
      let typeUrl = 'inorder'
      this.$http.get(`/ba/${typeUrl}/${this.dataForm.businessId}`).then(({ data: res }) => {
        if (res.code !== 0) {
          this.orderDataForm = {}
          return this.$message.error(res.msg)
        }
        this.orderDataForm = res.data
      }).catch(() => {})
    },
    // 自定义列显示
    settingCol (tableColumns) {
      this.$nextTick(() => {
        this.$refs['tableSet'].init(this.tableName, tableColumns)
      })
    },
    async getDict () {
      this.getDictTypeList('createType').then(res => {
        this.createTypeList = res
      })
      this.orderTypeList = await this.getDictTypeList('operatingOrderType')
      this.divideMethodList = await this.getDictTypeList('bdFeeTypeDivideMethod')
      this.getDictTypeList('weightUnit').then(res => {
        this.weightUnitList = res
      })
    },
    getReceivableLogDataList (row) {
      let businessId
      let listUrl
      if (row.businessId) {
        businessId = row.businessId
        listUrl = 'ba/receivablefeelog/listMasterByParams'
      } else {
        businessId = row.subBusinessId
        listUrl = 'ba/receivablefeelog/listSubByParams'
      }
      this.logShow = false
      this.$http.get(listUrl,
        {
          params: {
            businessId: businessId,
            feeTypeId: row.feeTypeId
          }
        }
      ).then(({ data: res }) => {
        if (res.code !== 0) {
          this.dataList = []
          this.total = 0
          return this.$message.error(res.msg)
        }
        this.logDataList = res.data || []
        this.logShow = true
      }).catch(() => {
      })
    },
    async getBaseData () {
      // 费用项
      this.usingFeeTypeList = await baseData(api.usingFeeTypeList).catch((res) => {
        const msg = res.msg ? res.msg : res
        return this.$message.error(msg)
      })
      this.currencyList = await baseData(api.currencyList).catch((res) => {
        const msg = res.msg ? res.msg : res
        this.$message.error(msg)
      })
      this.logisticsChannelList = await baseData(api.listByCurrent).catch((res) => {
        const msg = res.msg ? res.msg : res
        this.$message.error(msg)
      })
      // 所有物流产品 包含未启用
      baseData(api.logisticsProductByParamsList).then(res => {
        this.logisticsProductList = res
      })
      this.provideList = await baseData(api.providerList, { status: 11 }).catch((res) => {
        const msg = res.msg ? res.msg : res
        return this.$message.error(msg)
      })
      // 国家
      this.countryList = await baseData(api.countryList)
      // 用户信息
      baseData(api.userList).then(res => {
        this.userList = res
      })
    },
    searchSubHandle () {
      this.subExpands = []
      let initFlag = this.$refs.subThreeNoInput.setValue()
      if (initFlag) {
        this.querySubPageByParam()
      }
    },
    refreshDataList () {
      this.searchSubHandle()
      this.getReceivableFee()
    },
    // 重置表单
    resetSubForm (searchSubForm) {
      this.$refs.subThreeNoInput.clearValue()
      this._resetForm(searchSubForm)
    },
    getRowClassName ({ row, rowIndex }) {
      if (!row.sumList || (row.sumList && row.sumList.length <= 0)) {
        return 'row-expand-cover'
      }
    },
    subExpandChange (row, expandedRows) {
      if (expandedRows.length) {
        this.subExpands = []
        if (row) {
          this.subExpands.push(row.id)
          let currentTargetDom = event.currentTarget
          // 读取事件触发的元素，以样式为key获取要修改class的子元素。
          let iels = currentTargetDom.getElementsByClassName('el-icon-arrow-right')
          // 若iels长度为0，则为非展开按钮触发。递归父节点找到tr节点后再向下找到指定className的元素。递归深度：10层
          if (iels.length === 0) {
            let deep = 1
            while (deep < 10 && currentTargetDom.tagName !== 'TR') {
              currentTargetDom = currentTargetDom.parentNode
              deep++
            }
            iels = currentTargetDom.getElementsByClassName('el-icon-arrow-right')
          }
          let iel = iels[0]
          // console.log('iel.className修改前:' + iel.className)
          if (iel && row.feeItemList.length === 0) {
            // 修改为加载中样式
            iel.className = 'el-icon el-icon-loading'
            let url = '/ba/subreceivablefee/listByParams'
            this.$http
              .get(url, {
                params: {
                  groupByFeeType: this.groupByFeeType,
                  businessId: row.subBusinessId
                }
              })
              .then(({ data: res }) => {
                if (res.code !== 0) {
                  return this.$message.error(res.msg)
                }
                row.feeItemList = res.data || []
                // console.log('iel.className修改后:' + iel.className)
                setTimeout(() => {
                  // 修改回右箭头样式
                  iel.className = 'el-icon el-icon-arrow-right'
                }, 300)
              })
              .catch(() => {})
          }
        } else {
          this.subExpands = []
        }
      } else {
        this.subExpands = []
      }
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'createDate':
          value = timestampFormat(gtmToLtm(scope.row.createDate), 'YYYY-MM-DD')
          break
        case 'logisticsProductCode':
          value = formatterCodeName(scope.row.logisticsProductCode, this.logisticsProductByParamsList)
          break
        case 'balanceWeightD':
          value = scope.row.balanceWeightD === 0 ? 'N/A' : scope.row.balanceWeightD
          break
        case 'status':
          value = formatterType(scope.row.status, this.billsStatusList)
          break
        case 'billsType':
          value = formatterType(scope.row.billsType, this.billsTypeList)
          break
        case 'balanceWeightUnit':
          value = formatterType(scope.row.balanceWeightUnit, this.weightUnitList)
          break
        case 'feeTypeId':
          value = formatterName(scope.row.feeTypeId, this.usingFeeTypeList)
          break
        case 'createType':
          value = formatterType(scope.row.createType, this.createTypeList)
          break
        case 'businessType':
          value = formatterType(scope.row.businessType, this.businessTypeList)
          break
        case 'auditor':
          value = formatterShowName(scope.row.auditor, this.userList, 'realName')
          break
        case 'creator':
          value = formatterShowName(scope.row.creator, this.userList, 'realName')
          break
        case 'volD':
          value = numberFormat(scope.row.volD, 0)
          break
        case 'sumD':
          value = numberFormat(scope.row.sumD, 3)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.activeTagName = 'fee'
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        if (this.dataForm.businessId) {
          this.subExpands = []
          this.getOrderInfo()
          this.getReceivableFee()
          this.getBillsInfo().then(id => this.openOrderInfo(id))
          this.resetSubForm('searchSubForm')
        }
      })
    },
    // 获取计费原始单据信息
    getOrderInfo () {
      let typeUrl = 'inorder'
      if (this.waybillType === 20) {
        typeUrl = 'outorder'
      }
      this.$http.get(`/ba/${typeUrl}/info/${this.dataForm.businessId}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm.orderInfo = res.data
      }).catch(() => {})
    },
    // 获取计费单信息
    getBillsInfo () {
      return new Promise((resolve, reject) => {
        let typeUrl = 'billsreceivable'
        this.$http.get(`/ba/${typeUrl}/infoByBusinessId/${this.dataForm.businessId}`).then(({ data: res }) => {
          if (res.code !== 0) {
            return reject(this.$message.error(res.msg))
          }
          this.dataForm.billsInfo = res.data
          resolve(res.data.id)
        }).catch(() => {
        })
      })
    },
    // 获取应收费用
    getReceivableFee () {
      let requestId = this.dataForm.businessId
      let typeUrl = 'getByBusinessId'
      if (this.waybillType === 20) {
        typeUrl = 'getByOutBusinessId'
      } else if (this.dataForm.bizOrderId !== null && this.dataForm.bizOrderId !== '') {
        typeUrl = 'getByBizOrderIdy'
        requestId = this.dataForm.bizOrderId
      }
      this.$http.get(`/ba/receivablefee/${typeUrl}/${requestId}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        if (this.waybillType === 20) {
          this.dataForm.viewDataList = res.data
        } else {
          this.dataForm.auditData = res.data
        }
      }).catch(() => {})
    },
    // 关闭
    closedDialogCallback () {
      this.visible = false
      this.confirmLoading = false
      this.$emit('refreshDataList')
    },
    formatterLabel (prop) {
      let value
      switch (prop) {
        case 'settlementObjectName':
          value = this.$t('baReceivableFee.settlementObjectName')
          break
        case 'logistics':
          value = this.$t('baReceivableFee.logisticsProductCode')
          break
        case 'infoTitle':
          value = '应收计费信息'
          break
        default:
          value = ''
          break
      }
      return value
    },
    // 特殊处理费用项名称
    formatterFeeType (value, list) {
      if (value === '-1') {
        return '合计'
      } else {
        return formatterName(value, list)
      }
    }
  },
  components: {
    threeNoInput
  },
  filters: {
    formatterType,
    formatterShowName,
    gtmToLtm,
    timestampFormat,
    formatterCodeName,
    formatterName,
    numberFormat,
    multiply
  }
}
</script>
<style lang="scss" scoped>
/deep/ .row-expand-cover .el-table__expand-icon {
  visibility: hidden !important;
}
.form-divider{
  border-bottom: 1px solid #f5f7fa;
  margin-bottom: 10px;
}
/deep/ .orderArea{
  .areaBox{
    .el-form-item__content{
      margin-left:0px !important;
    }
  }
  .orderAreaTab {
    left: 0px !important;
  }
}
/deep/ .search_box .sub_search_box_btn {
  margin-left: 20px !important;
}
::v-deep #showReferencePrice .el-checkbox .el-checkbox__label {
  font-size: 12px !important;
}
</style>
