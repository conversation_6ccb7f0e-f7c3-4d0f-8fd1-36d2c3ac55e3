/**
 * 邮箱
 * (.[a-zA-Z0-9_-]{2,}){1,} -> {2,}指逗号后匹配两次（至少两个字符），{1,}整个(.[a-zA-Z0-9_-]{2,})至少匹配一次
 * @param {*} s
 */
export function isEmail (s) {
  return /^([a-zA-Z0-9._-])+@([a-zA-Z0-9_-])+((.[a-zA-Z0-9_-]{2,}){1,})$/.test(s)
}
/**
 * 包含中文
 * @param s
 */
export function hasChinese (s) {
  return /[\u4E00-\u9FA5\uF900-\uFA2D]+/.test(s)
}
/**
 * 是否是有效的GMT时区
 * 时区的格式要求是+/-HH:mm，GMT+/- HH:mm
 */
export function gmtTimeZoneCheck (s) {
  return /(^GMT(\+|-)\d{2,2}:\d{2,2}$)|(^(\+|-)\d{2,2}:\d{2,2}$)/g.test(s)
}
/**
 * 手机号码
 * @param {*} s
 */
export function isMobile (s) {
  return /^[1][3,4,5,6,7,8,9][0-9]{9}$/.test(s)
}
/**
 * 支持+86手机号码
 * @param {*} s
 */
export function is86Mobile (s) {
  return /^((\\+86)|(86))?[1][3456789][0-9]{9}$/.test(s)
}
/**
 * 电话号码
 * @param {*} s
 */
export function isPhone (s) {
  return /^([0-9]{3,4}-)?[0-9]{7,8}$/.test(s)
}

/**
 *
 * @description   判断是否为URL地址
 * @param  {String} str
 * @return {Boolean}
 */
export function isUrl (str) {
  return /[-a-zA-Z0-9@:%.\\_\\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%\\_\\+.~#?&//=]*)/i.test(str)
}

/**
 * URL地址
 * @param {*} s
 */
export function isURL (s) {
  return /^http[s]?:\/\/.*/.test(s)
}

/**
 * 校验是否为空(先删除二边空格再验证)
 * @param s
 * @returns {boolean}
 */
export function isBank (s) {
  if (s === null || s.trim() === '') {
    return true
  } else {
    return false
  }
}

/**
 * 全是数字
 * @param s
 * @returns {boolean}
 */
export function isDigit (s) {
  return /^\d+$/.test(s)
}

/**
 * 整数
 * @param s
 * @returns {boolean}
 */
export function isInteger (s) {
  return /^([+-]?)(\d+)$/.test(s)
}

/**
 * 正整数
 * @param s
 * @returns {boolean}
 */
export function isPlusInteger (s) {
  return /^([+]?)(\d+)$/.test(s)
}

/**
 * 负整数
 * @param s
 * @returns {boolean}
 */
export function isMinusInteger (s) {
  return /^-(\d+)$/.test(s)
}

/**
 * 浮点数
 * @param s
 * @returns {boolean}
 */
export function isFloat (s) {
  return /^([+-]?)\d*\.\d+$/.test(s)
}

/**
 * 正浮点数
 * @param s
 * @returns {boolean}
 */
export function isPlusFloat (s) {
  return /^([+]?)\d*\.\d+$/.test(s)
}

/**
 * 负浮点型
 * @param s
 * @returns {boolean}
 */
export function isMinusFloat (s) {
  return /^-\d*\.\d+$/.test(s)
}

/**
 * 仅中文
 * @param s
 */
export function isChinese (s) {
  return /[\u4E00-\u9FA5\uF900-\uFA2D]+/.test(s)
}

/**
 * 仅ACSII字符
 * @param s
 * @returns {boolean}
 */
export function isAcsii (s) {
  return /^[\\x00-\\xFF]+$/.test(s)
}

/**
 * 中国邮编
 * @returns {boolean}
 */
export function isPostcode (s) {
  return /^\d{6}$/.test(s)
}

/**
 * 合法时间
 * @param s
 * @returns {boolean}
 */
export function isDate (s) {
  if (!/\d{4}(\.|\/|\\-)\d{1,2}(\.|\/|\\-)\d{1,2}/.test(s)) {
    return false
  }
  let r = s.match(/\d{1,4}/g)
  if (r === null) { return false }
  let d = new Date(r[0], r[1] - 1, r[2])
  return (d.getFullYear() === r[0] && (d.getMonth() + 1) === r[1] && d.getDate() === r[2])
}

/**
 * 合法时间 yyyy-MM-dd HH:mm:ss
 * @param s
 * @returns {boolean}
 */
export function isDateTime (s) {
  let datePattern = /((([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29))\s([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])$/
  return datePattern.test(s)
}

/**
 * 只能输入3-32个字母、数字、下划线
 * 用于校验编码、用户名等
 * @param s
 */
export function isCode (s) {
  return /^(\w){3,32}$/.test(s)
}

/**
 * 校验最大长度64位
 * @param s
 * @returns {boolean}
 */
export function isLength64 (s) {
  return /^.{1,64}$/.test(s)
}

export function isLength32 (s) {
  return /^.{1,32}$/.test(s)
}

/**
 * 1-3位小数
 * @param s
 * @returns {boolean}
 */
export function isDecimal3 (s) {
  return /^([1-9]\d*|0)(\.\d{1,3})?$/.test(s)
}

/**
 * 1-3位小数(正负)
 * @param s
 * @returns {boolean}
 */
export function isSignDecimal3 (s) {
  return /^([+-]?)\d+(\.\d{1,3})?$/.test(s)
}

/**
 * 1位小数
 * @param s
 * @returns {boolean}
 */
export function isDecimal1 (s) {
  return /^([1-9]\d*|0)(\.\d{1,1})?$/.test(s)
}

/**
 * 2位小数
 * @param s
 * @returns {boolean}
 */
export function isDecimal2 (s) {
  return /^([1-9]\d*|0)(\.\d{1,2})?$/.test(s)
}

/**
 * 非0正整数
 * @param s
 * @returns {boolean}
 */
export function isPlusInteger2 (s) {
  return /^[0-9]*[1-9][0-9]*$/.test(s)
}

/**
 * 字母和数字组合
 * @param s
 * @returns {boolean}
 */
export function letterAndNumber (s) {
  return /^[0-9a-zA-Z]*$/g.test(s)
}

/**
 * 字母和数字和中划线组合和下划线
 * @param s
 * @returns {boolean}
 */
export function letterAndNumberLine (s) {
  return /^[0-9a-zA-Z_\\-]*$/g.test(s)
}

/**
 * 判断是否是图片文件
 * @param s
 * @returns {boolean}
 */
export function isImgFile (str) {
  let reg = /\.(png|jpe?g|gif|svg|bmp)(\?.*)?$/
  return str.match(reg)
}

export function isOverLength (s, maxLenght) {
  return new RegExp('^.{0,' + maxLenght + '}$').test(s)
}

/**
 * 数字、字母、中划线、下划线、 空格, 井号 国际邮编校验
 * @param s
 * @returns {boolean}
 */
export function postcodeCheck (s) {
  return /^[a-zA-Z0-9-_\\\s]*$/g.test(s)
}

/**
 * 数字、中划线、逗号
 * @param s
 * @returns {boolean}
 */
export function serialNumberCheck (s) {
  return /^[0-9，,-]*$/g.test(s)
}

/**
 * 是否有效电话
 * @param value
 * @returns {boolean}
 */
export function isValidPhone(value) {
  return (/^\d{7,}$/).test(value.replace(/[\s()+\-.]|ext/gi, ''))
}
