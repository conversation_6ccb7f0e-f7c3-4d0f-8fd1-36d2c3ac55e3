<template>
  <div class="add-body panel_body">
    <div class="panel-hd" >
      <el-page-header @back="backFn" :content="$t('detail')">
      </el-page-header>
      <!--<span v-text="$t('detail')"></span>-->
    </div>
    <div class="detail-desc">
      <h2 class="detail-title">用户信息</h2>
      <el-form ref="form" label-width="80px">
        <el-row :gutter="20">
          <el-col :span="6">
          <el-form-item label="用户姓名">
            <span>付小小</span>
          </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="联系电话">
              <span>18100000000</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="常用快递">
              <span>菜鸟仓储</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="邮编号码">
              <span>518000</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="取货地址">
              <el-popover
                placement="top-start"
                width="300"
                trigger="hover"
                content="浙江省杭州市西湖区万塘路18号">
                <div slot="reference" class="text-overflow">浙江省杭州市西湖区万塘路18号</div>
              </el-popover>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注">
              <el-popover
                placement="top-start"
                width="300"
                trigger="hover"
                content="这段描述很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长">
                <div slot="reference" class="text-overflow">这段描述很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长...</div>
              </el-popover>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-divider></el-divider>
      <h2 class="detail-title">地址信息</h2>
      <div>
        <el-table
          :data="tableData"
          style="width: 100%">
          <el-table-column
            header-align="center"
            align="center"
            prop="goodNo"
            label="编号"
          >
          </el-table-column>
          <el-table-column
            prop="date"
            label="日期"
            width="180">
          </el-table-column>
          <el-table-column
            prop="name"
            label="姓名"
            width="180">
          </el-table-column>
          <el-table-column
            prop="address"
            label="地址">
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            width="100">
            <template slot-scope="scope">
              <el-button type="text"  size="small">查看</el-button>
              <el-button type="text" size="small">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div id="cs_FormFooter" class="el-form-footer" slot="footer">
      <el-button @click="backFn">{{ $t('back') }}</el-button>
    </div>
  </div>
</template>

<script>
import areaBox from '@/components/areaBox'
export default {
  name: 'detail',
  data () {
    return {
      dataForm: {
        id: ''
      },
      tableData: [{
        goodNo: '1234561',
        date: '2016-05-02',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1518 弄'
      }, {
        goodNo: '1234562',
        date: '2016-05-04',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1517 弄'
      }, {
        goodNo: '1234563',
        date: '2016-05-01',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1519 弄'
      }, {
        goodNo: '1234564',
        date: '2016-05-03',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1516 弄'
      }]
    }
  },
  methods: {
    init () {
      // 如果菜单浮动位置 需要初始化
      this.$footerScroll()
    },
    // 返回
    backFn () {
      this.visible = false
      this.$emit('backView')
    }
  },
  beforeDestroy () {
    // 释放监听事件
    window.addEventListener('scroll')
  },
  components: {
    areaBox
  }
}
</script>

<style scoped>

</style>
