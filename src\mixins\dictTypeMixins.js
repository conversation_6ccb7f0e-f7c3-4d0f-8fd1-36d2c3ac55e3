export default {
  methods: {
    // 根据类型获取数据字典集合
    getDictTypeList (type, url = '/sys/dict/dictList') {
      return new Promise((resolve, reject) => {
        this.$http.get(url, { params: { dictType: type } }).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          if (res.data.length) {
            res.data.forEach(item => {
              item.dictValue = isNaN(Number(item.dictValue)) ? item.dictValue : Number(item.dictValue)
            })
          }
          // this.dictTypeList = res.data
          // callback && callback.call(this)
          resolve(res.data)
        }).catch(() => {})
      })
    },
    // 根据类型获取数据字典集合--通过url（需要无权限查询的时候）
    getDictTypeListByUrl (type, requestUrl) {
      return new Promise((resolve, reject) => {
        this.$http.get(requestUrl, { params: { dictType: type } }).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          if (res.data.length) {
            res.data.forEach(item => {
              item.dictValue = isNaN(Number(item.dictValue)) ? item.dictValue : Number(item.dictValue)
            })
          }
          // this.dictTypeList = res.data
          // callback && callback.call(this)
          resolve(res.data)
        }).catch(() => {})
      })
    }
  }
}
