<template>
  <div class="aui-wrapper aui-page__login">
    <div class="aui-content__wrapper">
      <main class="aui-content">
        <div class="login-header">
          <p class="pf">{{ $t('reset.successMsgPerfix') }}</p>
          <p class="pf">{{this.total}}{{ $t('reset.successMsgSuffix') }}</p>
        </div>
        <div class="login-footer">
          <p>
            <a :href="$baseUrl + '/its'" target="_blank">{{ $t('login.demo') }}</a>
          </p>
          <p><a href="http://www.xx.cn/" target="_blank">{{ $t('login.copyright') }}</a>2019 © xx.cn</p>
        </div>
      </main>
    </div>
  </div>
</template>
<script>
import { messages } from '@/i18n'
export default {
  data () {
    return {
      total: 5,
      i18nMessages: messages
    }
  },
  computed: {
  },
  created () {
    this.countDown()
  },
  methods: {
    countDown () {
      this.setInterval = window.setInterval(() => {
        this.total--
        if (this.total < 1) {
          clearInterval(this.setInterval)
          this.$router.replace({ name: 'login' })
        }
      }, 1000)
    }
  }
}
</script>
<style lang="scss" scoped>
.bg{
  background:$--color-primary;
  border-color: $--color-primary;
}
.pf{
  font-size: large;
}
</style>
