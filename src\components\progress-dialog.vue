
<template>
  <el-dialog :title="title + ' 进度'" :visible.sync=" visible" width="50%" :before-close="beforeCloseHandle" :body-style = "{padding:0}">
  <div>
    <div>
      <el-row :gutter="20">
        <el-col :span="8" :offset="8">
          <el-progress type="circle" :percentage="progress"/>
        </el-col>
      </el-row>
    </div>
    <div style="padding-top: 10px">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-badge :value="totalCount" class="item" type="warning">
            <el-button size="small">全部数量</el-button>
          </el-badge>
        </el-col>
        <el-col :span="8">
          <el-badge :value="successCount" class="item" type="primary">
            <el-button size="small">成功数量</el-button>
          </el-badge>
        </el-col>
        <el-col :span="8">
          <el-badge :value="failCount" class="item">
            <el-button size="small">失败数量</el-button>
          </el-badge>
          <el-link type="warning" style="float:right; padding-top: 8px;" @click="copyFailNos($event)">复制失败单号</el-link>
        </el-col>
      </el-row>
      <!-- 错误日志 -->
      <div style="padding-top: 20px;" v-show='errorInfo'>
        <el-card class="no_shadow" type="border-card">
          <el-input v-model="errorInfo" type="textarea" :autosize="{ minRows: 5, maxRows: 10}" readonly></el-input>
        </el-card>
      </div>
    </div>
  </div>
</el-dialog>

</template>

<script>
// import { includes, isArray } from 'lodash'
import NP from 'number-precision'
import { sleep } from '@/utils/tools'
import clip from '@/utils/clipboard'

export default {
  props: {
    title: {
      type: String
    },
    no: {
      type: String
    },
    noLabel: {
      type: String
    }
  },
  data () {
    return {
      visible: false,
      failList: [],
      activeCount: 0,
      totalCount: 0,
      successCount: 0,
      failCount: 0,
      progress: 0,
      processSmoothInterval: ''
    }
  },
  watch: {
    progress: {
      handler (newVal, oldVal) {
        if (newVal >= 90) {
          if (this.processSmoothInterval) {
            clearInterval(this.processSmoothInterval)
          }
        }
      }
    }
  },
  computed: {
    errorInfo () {
      let result = ''
      for (let i = 0; i < this.failList.length; i++) {
        let failMsg = this.failList[i].no ? (this.noLabel + '[' + this.failList[i].no + ']: ' + this.failList[i].message + '\n')
          : (this.failList[i].message + '\n')
        result += (i + 1) + '. ' + failMsg
        this.$nextTick(() => {
          let erroInfoDiv = document.getElementById('errorInfo')
          if (erroInfoDiv) {
            erroInfoDiv.scrollTop = erroInfoDiv.scrollHeight
          }
        })
      }
      return result
    }
  },
  methods: {
    clip, // 复制函数
    // 初始化进度条，必须先调用此方法
    init (totalCount) {
      if (!totalCount || totalCount <= 0) {
        return this.$message.error('请选择需处理的数据')
      }
      this.visible = true
      this.totalCount = totalCount
      this.successCount = 0
      this.failCount = 0
      this.progress = 0
      this.failList = []
      this.processSmoothHandle()
    },
    /**
     * 并发请求
     * @param {*} data 请求的总数据（列表）
     * @param {*} threadCount 并发线程数
     * @param {*} requestFunc 业务方法
     */
    async concurrencyRequest (data, threadCount, requestFunc) {
      for (let i = 0; i < data.length; i++) {
        // console.log('activeCount', this.activeCount)
        if (this.activeCount >= threadCount) {
          await sleep(1000, '并发处理')
        }
        requestFunc(data[i])
        this.activeCount++
      }
    },
    // 业务方法执行成功后调用
    success () {
      this.successCount++
      this.activeCount--
      this.progressHandle()
    },
    // 业务方法执行失败后调用
    fail (no, message) {
      this.failCount++
      this.activeCount--
      this.progressHandle()
      this.failList.push({ no, message })
    },
    // 进度条处理
    progressHandle () {
      this.progress = Number(Number((this.successCount + this.failCount) / this.totalCount * 100).toFixed(0))
      if (this.successCount + this.failCount === this.totalCount && this.failCount === 0) {
        // 等2秒
        setTimeout(() => {
          this.closeHandle()
        }, 2000)
      }
    },
    // 进度条平滑处理，视觉上进度一直在动
    processSmoothHandle () {
      this.processSmoothInterval = setInterval(() => {
        this.progress = NP.plus(this.progress, 0.01)
      }, 2000)
    },
    beforeCloseHandle () {
      if (this.totalCount !== this.successCount + this.failCount) {
        this.$message.warning('数据处理中请勿关闭')
        return false
      }
      this.closeHandle()
    },
    closeHandle () {
      this.visible = false
      let value = {
        successCount: this.successCount,
        failCount: this.failCount,
        totalCount: this.totalCount
      }
      this.$emit('callback', value)
    },
    copyFailNos (e) {
      let result = ''
      this.failList.forEach(item => {
        result += item.no + '\n'
      })
      clip(result, e)
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-textarea__inner {
color: #F56C6C !important;
}

</style>
