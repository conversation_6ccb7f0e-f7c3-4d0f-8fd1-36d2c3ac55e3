<template>
  <div class="aui-card--fill">
    <div class="mod-co__inorder flex_wrap" ref="listPanel">
      <el-card class="search_box" shadow="never" style='min-height: 174px'>
        <el-form ref="dataForm"  :model="dataForm" :rules="dataRule" label-width="120px" :disabled='dataListLoading'>
          <el-row :gutter="20"  type="flex">
            <el-col :span="6">
              <el-form-item :label="$t('coOrder.amazonWarehouse')" prop="fbaWarehouseCode" >
                <div id='fbaWarehouseSelect'>
                  <el-select  filterable clearable v-model="dataForm.fbaWarehouseCode"  @change="fbaWarehouseChange">
                    <el-option  v-for="item in fbaWarehouseList" :key="item.id" :label="item.name + (item.name === item.code ? ':  ' : '(' + item.code + '):  ') +  item.street + ' ' + item.city + ((item.district && item.district === item.city) ? '' : (item.district || '' +' '))
                  + ( (item.district && item.province && item.province === item.district ) ? '' : ('，' + item.province|| '' +' ')) + ' '+ item.postcode + '，' + item.country" :value="item.code"></el-option>
                  </el-select>
                </div>
              </el-form-item>
              <el-form-item :label="$t('psCalculateExpense.city')" prop="city">
                <el-input v-model="dataForm.city" clearable ></el-input>
              </el-form-item>
              <el-form-item :label="$t('psCalculateExpense.province')" prop="province">
                <el-input v-model="dataForm.province" clearable ></el-input>
              </el-form-item>
              <el-form-item :label="$t('psCalculateExpense.postcode')" prop="postcode">
                <el-input v-model="dataForm.postcode" clearable ></el-input>
              </el-form-item>
              <el-form-item :label="$t('psCalculateExpense.address')" prop="address">
                <el-input v-model="dataForm.address" clearable ></el-input>
              </el-form-item>
              <el-form-item :label="$t('psCalculateExpense.street')" prop="street">
                <el-input v-model="dataForm.street" clearable ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('psCalculateExpense.country')" prop="country">
                <el-select  filterable v-model="dataForm.country"
                            clearable>
                  <el-option v-for="(item, index) in countryList" :key="index" :label="`${item.name} ${item.code}`"
                             :value="item.code"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('psCalculateExpense.logisticsProduct')" prop="logisticsChannel">
                <el-select  filterable v-model="dataForm.logisticsChannel"
                            clearable>
                  <el-option v-for="(item, index) in logisticsProductList" :key="index" :label="item.name"
                             :value="item.code"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="箱数" >
                <el-input v-model="packageQty" placeholder="录入材积" @change="packageQtyChanged">
                  <el-button :disabled="packageQty <= 1" size='small' plain type='primary' slot="append" @click='inputBoxInfo' > 录入材积 </el-button>
                </el-input>
              </el-form-item>
              <el-form-item :label="$t('psCalculateExpense.weight')" prop="weightD" >
                <el-input v-model="dataForm.weightD" clearable ></el-input>
              </el-form-item>
              <el-form-item :label="$t('psCalculateExpense.name')" prop="name">
                <el-input v-model="dataForm.name" clearable ></el-input>
              </el-form-item>
              <el-form-item :label="$t('psCalculateExpense.district')" prop="district">
                <el-input v-model="dataForm.district" clearable ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item :label="$t('psCalculateExpense.productType')" prop="productType">
                <el-select  filterable v-model="dataForm.productType"
                            clearable>
                  <el-option v-for="(item, index) in parcelTypeList" :key="index" :label="item.dictName"
                             :value="item.dictValue"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('psCalculateExpense.specialItem')" prop="specialItem">
                <el-select filterable v-model="dataForm.specialItem"
                           clearable>
                  <el-option v-for="item in specialItemList" :key="item.dictValue" :value="item.dictValue" :label="item.dictName">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="长 * 宽 * 高 (CM)" >
                <el-input :disabled="packageQty > 1" style="width:110px;" v-model="dataForm.lengthD" :placeholder="$t('psCalculateExpense.length')" clearable ></el-input>
                <span> * </span>
                <el-input :disabled="packageQty > 1" style="width:110px;" v-model="dataForm.widthD" :placeholder="$t('psCalculateExpense.width')" clearable ></el-input>
                <span> * </span>
                <el-input :disabled="packageQty > 1" style="width:110px;" v-model="dataForm.heightD" :placeholder="$t('psCalculateExpense.height')" clearable ></el-input>
              </el-form-item>
              <el-form-item :label="$t('psCalculateExpense.phone')" prop="phone">
                <el-input v-model="dataForm.phone"  clearable ></el-input>
              </el-form-item>
              <el-row>
                <el-col :span="12">
                  <el-form-item :label="$t('psCalculateExpense.declareCurrency')" prop="declareCurrency">
                    <el-select v-model="dataForm.declareCurrency" :placeholder="$t('coOrder.declareCurrency')" filterable >
                      <el-option v-for="item in currencyList" :key="item.code" :label="item.name + ' (' + item.code + ')'" :value="item.code"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$t('psCalculateExpense.declareSum')" prop="declareSum">
                    <el-input v-model="dataForm.declareSumD"  clearable ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-button :loading="buttonLoading" style="margin-left: 120px; width:100px;" type="primary" @click="getTransSum()" icon="el-icon-search">试&nbsp;&nbsp;&nbsp;算</el-button>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
      <el-card class="flex_tab no_shadow" type="border-card" >
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <el-table v-loading="dataListLoading" :data="dataList"  :max-height="tableHeight" >
            <el-table-column type="expand" fixed="left">
              <template slot-scope="scope">
                <el-table :data="scope.row.resultDTOList"  border :max-height="tableHeight">
                  <!-- 动态显示表格 -->
                  <el-table-column v-for="(item, index) in tableItemColumnsArr"  v-if="item.prop === 'orderPackageId' ? scope.row.billingItemType!==0:true" :key="index" :type="item.type" :prop="item.prop"
                                   header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
                    <template slot-scope="scope">
                      <div>
                        {{formatterFn(scope,item.prop)}}
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </el-table-column>
            <!-- 动态显示表格 -->
            <el-table-column v-for="(item, index) in tableColumnsArr" :key="index" :type="item.type" :prop="item.prop"
                             header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
              <template slot-scope="scope">
                <div v-if="item.prop === 'specialItemArray'" :class="scope.row.billingItemType === 1?'warning':'default'">
                  <span v-for="(item, index) in scope.row.specialItemArray" :key="index">{{index>0? '，': ''}}{{item | formatterType(specialItemList)}}</span>
                </div>
                <div v-else :class="scope.row.billingItemType === 1?'warning':'default'">
                  {{formatterFn(scope,item.prop)}}
                </div>
              </template>
            </el-table-column>
            <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" min-width="150">
              <template slot-scope="scope">
                <el-link v-if="$hasPermission('ps:calculateExpense:getTransSum')" @click="createOrderHandle(scope)" :underline="false" >{{ $t('createOrder') }}</el-link>
                <el-link v-show='scope.row.quotationId' :underline="false" @click="viewQuotationRemark(scope.row.quotationId)">{{$t('psCalculateExpense.quotationRemark')}}</el-link>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>
    <!-- 自定义列显示弹窗 -->
    <inputBoxInfoDialog ref='inputBoxInfoDialog'  @closeInputBoxInfo='closeInputBoxInfo' @inputBoxInfoEmit='inputBoxInfoEmit' v-if='inputBoxInfoDialogShow' ></inputBoxInfoDialog>
    <!-- 走货说明页面 -->
    <viewRemark v-if="viewRemarkVisible" ref="viewRemark" @backView="backViewRemark"></viewRemark>
  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import listPage from '@/mixins/listPage'
import { formatterType, gtmToLtm, timestampFormat, formatterName, formatterCodeName } from '@/filters/filters'
import dictTypeMixins from '@/mixins/dictTypeMixins'
import baseApi from '@/api'
import baseData from '@/api/baseData'
import { isLength32 } from '@/utils/validate'
import inputBoxInfoDialog from './input-box-info-dialog'
import viewRemark from './express-customer-calculate-remark.vue'
export default {
  mixins: [listPage, mixinViewModule, dictTypeMixins],
  data () {
    return {
      tableColumns: [
        { type: '', width: '150', prop: 'logisticsChannelId', label: this.$t('psCalculateExpense.logisticsProduct'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '160', prop: 'specialItemArray', label: this.$t('psCalculateExpense.specialItem'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'billingItemType', label: this.$t('psCalculateExpense.billingItemType'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'aging', label: this.$t('psCalculateExpense.aging'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'sum', label: this.$t('psCalculateExpense.sum'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'balanceWeight', label: this.$t('psCalculateExpense.balanceWeight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'sysSum', label: this.$t('psCalculateExpense.sysSum'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'sysBalanceWeight', label: this.$t('psCalculateExpense.sysBalanceWeight'), align: 'center', isShow: true, disabled: false }
        // { type: '', width: '100', prop: 'quotationRemark', label: this.$t('psCalculateExpense.quotationRemark'), align: 'center', isShow: true, disabled: false }
      ],
      tableItemColumns: [
        { type: '', width: '100', prop: 'orderPackageId', label: this.$t('fba.boxSerialNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'feeType', label: this.$t('psCalculateExpense.feeType'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'sum', label: this.$t('psCalculateExpense.sum'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'balanceWeight', label: this.$t('psCalculateExpense.balanceWeight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'sysSum', label: this.$t('psCalculateExpense.sysSum'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'sysBalanceWeight', label: this.$t('psCalculateExpense.sysBalanceWeight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'formula', label: this.$t('psCalculateExpense.formula'), align: 'center', isShow: true, disabled: false }
      ],
      mixinViewModuleOptions: {
        activatedIsNeed: false
      },
      dataForm: {
        id: '',
        weightD: 1,
        objectType: 0,
        objectId: 0,
        productType: 10,
        payable: 0,
        client: 1,
        city: '',
        province: '',
        postcode: '',
        address: '',
        country: '',
        street: '',
        name: '',
        phone: '',
        district: '',
        fbaWarehouseCode: '',
        logisticsChannel: '',
        psCalculateExpenseDetailList: []
      },
      changeStatusForm: {
        ids: '',
        status: ''
      },
      buttonLoading: false,
      viewRemarkVisible: false,
      inputBoxInfoDialogShow: false,
      packageQty: 1,
      showTransFormulaInClient: 'N',
      boxDataList: [],
      fbaWarehouseList: [],
      parcelTypeList: [],
      countryList: [],
      weightUnitList: [],
      billingItemTypeList: [],
      currencyList: [],
      specialItemList: [],
      logisticsProductList: [],
      baseQuotationAllList: [],
      usingFeeTypeList: [],
      logisticsProductTableList: []
    }
  },
  created () {
    this.getDict()
    this.getBaseData()
    this.dataForm.objectId = this.$store.state.user.customerId
    // 是否显示公式
    this.tableItemColumnsArr.splice(6, 1)
  },
  methods: {
    async getDict () {
      this.weightUnitList = await this.getDictTypeList('weightUnit')
      this.parcelTypeList = await this.getDictTypeList('OrderParcelType')
      this.billingItemTypeList = await this.getDictTypeList('billingItemType')
      this.specialItemList = await this.getDictTypeList('specialItem') // 特殊物品类型
    },
    // 同步通过接口获取基础数据
    getBaseData () {
      baseData(baseApi.currencyList).then((res) => {
        this.currencyList = res
      })
      baseData(baseApi.enableLogisticsProductByCurrent).then((res) => {
        this.logisticsProductList = res
      })
      baseData(baseApi.countryList, { type: 0 }).then((res) => {
        this.countryList = res
      })
      baseData(baseApi.fbaWarehouseList, { status: 1 }).then((res) => {
        this.fbaWarehouseList = res
      })
      baseData(baseApi.usingFeeTypeList).then((res) => {
        this.usingFeeTypeList = res
      })
      baseData(baseApi.logisticsProductByParamsList).then((res) => {
        this.logisticsProductTableList = res
      })
      baseData(baseApi.sysParams + '/SHOW_TRANS_FORMULA_IN_CLIENT').then((res) => {
        this.showTransFormulaInClient = res
      })
    },
    async viewQuotationRemark (quotationId) {
      this.viewRemarkVisible = true
      this.$nextTick(() => {
        this.$refs.viewRemark.dataForm.quotationId = quotationId
        this.$refs.viewRemark.init()
      })
    },
    backViewRemark () {
      this.viewRemarkVisible = false
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'billingItemType':
          value = formatterType(scope.row.billingItemType + '', this.billingItemTypeList)
          break
        case 'logisticsChannelId':
          value = formatterName(scope.row.logisticsChannelId, this.logisticsProductTableList)
          break
        case 'feeType':
          value = formatterName(scope.row.feeType, this.usingFeeTypeList)
          break
        case 'sum':
          value = `${scope.row.sum} (${formatterCodeName(scope.row.currency, this.currencyList)})`
          break
        case 'balanceWeight':
          value = `${scope.row.balanceWeight} (${formatterType(scope.row.weightUnit, this.weightUnitList)})`
          break
        case 'sysSum':
          value = `${scope.row.sysSum} (${formatterCodeName(scope.row.sysCurrency, this.currencyList)})`
          break
        case 'sysBalanceWeight':
          value = `${scope.row.sysBalanceWeight} (${formatterType(scope.row.sysWeightUnit, this.weightUnitList)})`
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    packageQtyChanged () {
      this.boxDataList = []
    },
    inputBoxInfo () {
      this.inputBoxInfoDialogShow = true
      this.$nextTick(() => {
        this.$refs.inputBoxInfoDialog.init(this.packageQty, this.boxDataList)
      })
    },
    closeInputBoxInfo () {
      this.inputBoxInfoDialogShow = false
    },
    inputBoxInfoEmit (boxDataList, totalWeightD) {
      this.boxDataList = boxDataList
      this.packageQty = boxDataList.length || null
      this.dataForm.weightD = totalWeightD || ''
      this.$refs.inputBoxInfoDialog.submitLoading = false
      this.inputBoxInfoDialogShow = false
    },
    getTransSum () {
      this.buttonLoading = true
      this.dataListLoading = true
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          this.dataListLoading = false
          this.buttonLoading = false
          return false
        }
        // console.log('lengthD', this.dataForm.lengthD)
        // console.log('weightD', this.dataForm.weightD)
        // console.log('this.boxDataList', this.boxDataList)
        if (this.boxDataList && this.boxDataList.length > 1) {
          let detailList = []
          this.boxDataList.forEach(item => {
            detailList.push({
              lengthD: item.packageLengthD,
              widthD: item.packageWidthD,
              heightD: item.packageHeightD,
              weightD: item.packageWeightD
            })
          })
          this.dataForm.psCalculateExpenseDetailList = [...detailList]
        } else {
          this.dataForm.psCalculateExpenseDetailList[0] = {
            lengthD: this.dataForm.lengthD,
            widthD: this.dataForm.widthD,
            heightD: this.dataForm.heightD,
            weightD: this.dataForm.weightD
          }
        }
        this.$http['post']('/ps/expresscommonsquotation/getTransSumForTrial', this.dataForm).then(({ data: res }) => {
          this.buttonLoading = false
          this.dataListLoading = false
          if (res.code !== 0) {
            this.$alert(res.msg, '错误信息', {
              confirmButtonText: '确定',
              dangerouslyUseHTMLString: true
            })
            this.dataList = []
            return false
          } else {
            this.dataList = res.data
          }
        }).catch(() => {
          this.buttonLoading = false
          this.dataListLoading = false
        })
      })
    },
    createOrderHandle (data) {
      if (this.boxDataList && this.boxDataList.length > 1) {
        let detailList = []
        this.boxDataList.forEach(item => {
          detailList.push({
            lengthD: item.packageLengthD,
            widthD: item.packageWidthD,
            heightD: item.packageHeightD,
            weightD: item.packageWeightD
          })
        })
        this.dataForm.psCalculateExpenseDetailList = [...detailList]
      } else {
        this.dataForm.psCalculateExpenseDetailList[0] = {
          lengthD: this.dataForm.lengthD,
          widthD: this.dataForm.widthD,
          heightD: this.dataForm.heightD,
          weightD: this.dataForm.weightD
        }
      }
      this.dataForm.logisticsChannelId = data.row.logisticsChannelId
      this.$http['post']('/co/order/createOrder', this.dataForm).then(({ data: res }) => {
        if (res.code !== 0) {
          this.$alert(res.msg, '错误信息', {
            confirmButtonText: '确定',
            dangerouslyUseHTMLString: true
          })
          return false
        } else {
          console.log('orderLogisticsType = ' + res.data.orderLogisticsType)
          if (res.data.orderLogisticsType === 10) {
            this.$router.push({ name: 'co-orderEntry', query: { res: res, opType: 'coOrder.copy' }, params: { metaTitle: '订单录入' } })
          } else if (res.data.orderLogisticsType === 11) {
            this.$router.push({ name: 'express-orderEntry', query: { res: res, opType: 'express.header.copy' }, params: { metaTitle: '快递订单录入' } })
          } else {
            this.$router.push({ name: 'fba-orderEntry', query: { res: res, opType: 'fba.header.copy' }, params: { metaTitle: 'FBA订单录入' } })
          }
        }
      }).catch(() => {})
    },
    fbaWarehouseChange (code) {
      if (code) {
        let arr = this.fbaWarehouseList.filter((item) => {
          return item.code === code
        })
        if (arr.length > 0) {
          let item = arr[0]
          this.dataForm.postcode = item.postcode
          this.dataForm.province = item.province
          this.dataForm.city = item.city
          this.dataForm.address = item.street
          this.dataForm.country = item.country
          this.dataForm.name = item.name
          this.dataForm.street = item.street
          this.dataForm.phone = item.phone
        } else {
          this.clearConsigneeInfoAndFbaWarehouseCode()
        }
      } else {
        this.clearConsigneeInfoAndFbaWarehouseCode()
      }
    },
    clearConsigneeInfoAndFbaWarehouseCode () {
      this.dataForm.fbaWarehouseCode = null
      this.dataForm.postcode = null
      this.dataForm.province = null
      this.dataForm.city = null
      this.dataForm.address = null
      this.dataForm.country = null
      this.dataForm.name = null
      this.dataForm.street = null
      this.dataForm.phone = null
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat,
    formatterName,
    formatterCodeName
  },
  computed: {
    dataRule () {
      const postCodeValidate = (rule, value, callback) => {
        if (value && !isLength32(value)) {
          return callback(new Error(this.$t('validate.postCodeValidate', { 'attr': this.$t('psCalculateExpense.postcode') })))
        }
        callback()
      }
      const lengthValidate = (rule, value, callback) => {
        let pattrn = /^[0-9]+(.[0-9]{0,1})?$/
        if (value && !(pattrn.test(value) && value > 0)) {
          return callback(new Error(this.$t('validate.lengthValidate', { 'attr': this.$t('psCalculateExpense.length') })))
        }
        if (value > 99999) {
          return callback(new Error(this.$t('validate.max', { 'attr': this.$t('validate.max'), 'max': '99999' })))
        }
        callback()
      }
      const widthValidate = (rule, value, callback) => {
        let pattrn = /^[0-9]+(.[0-9]{0,1})?$/
        if (value && !(pattrn.test(value) && value > 0)) {
          return callback(new Error(this.$t('validate.lengthValidate', { 'attr': this.$t('psCalculateExpense.width') })))
        }
        if (value > 99999) {
          return callback(new Error(this.$t('validate.max', { 'attr': this.$t('validate.max'), 'max': '99999' })))
        }
        callback()
      }
      const heightValidate = (rule, value, callback) => {
        let pattrn = /^[0-9]+(.[0-9]{0,1})?$/
        if (value && !(pattrn.test(value) && value > 0)) {
          return callback(new Error(this.$t('validate.lengthValidate', { 'attr': this.$t('psCalculateExpense.height') })))
        }
        if (value > 99999) {
          return callback(new Error(this.$t('validate.max', { 'attr': this.$t('validate.max'), 'max': '99999' })))
        }
        callback()
      }
      const weightValidate = (rule, value, callback) => {
        let pattrn = /^[0-9]+(.[0-9]{0,3})?$/
        if (value && !(pattrn.test(value) && value > 0)) {
          return callback(new Error(this.$t('validate.weight', { 'attr': this.$t('psCalculateExpense.weight') })))
        }
        if (value > 99999) {
          return callback(new Error(this.$t('validate.max', { 'attr': this.$t('validate.max'), 'max': '99999' })))
        }
        callback()
      }
      return {
        productType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        country: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        postcode: [
          { validator: postCodeValidate, trigger: 'blur' }
        ],
        lengthD: [
          { validator: lengthValidate, trigger: 'blur' }
        ],
        widthD: [
          { validator: widthValidate, trigger: 'blur' }
        ],
        heightD: [
          { validator: heightValidate, trigger: 'blur' }
        ],
        weightD: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: weightValidate, trigger: 'blur' }
        ]
      }
    },
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    tableItemColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableItemColumns).map((key) => this.tableItemColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    }
  },
  components: {
    inputBoxInfoDialog,
    viewRemark
  }
}
</script>
