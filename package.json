{"name": "its-admin", "version": "1.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --open", "start": "vue-cli-service serve", "build": "vue-cli-service build --mode production", "build:report": "vue-cli-service build --mode production --report", "build:prod": "vue-cli-service build --mode production", "build:prod:saas": "vue-cli-service build --mode production-saas", "postbuild": "node ./version/build.js", "lint": "vue-cli-service lint", "et": "et", "et:init": "et -i", "et:list": "gulp themes"}, "dependencies": {"@handsontable-pro/vue": "^3.1.1", "@handsontable/vue": "^12.3.3", "axios": "^0.19.0", "clipboard": "^2.0.4", "compression-webpack-plugin": "^5.0.1", "echarts": "^4.2.1", "element-resize-detector": "^1.2.4", "element-ui": "^2.12.0", "good-storage": "^1.1.0", "handsontable": "^12.3.3", "handsontable-pro": "^6.2.3", "js-cookie": "^2.2.0", "lodash": "^4.17.11", "number-precision": "^1.5.1", "print-js": "^1.0.54", "qs": "^6.5.2", "quill": "^1.3.6", "screenfull": "^3.3.3", "splitpanes": "^2.2.0", "umy-ui": "^1.1.6", "v-charts": "^1.19.0", "vue": "^2.5.17", "vue-count-to": "^1.0.13", "vue-i18n": "^8.1.0", "vue-router": "^3.1.1", "vue-seamless-scroll": "^1.1.17", "vuex": "^3.0.1"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.0.4", "@vue/cli-plugin-eslint": "^3.0.4", "@vue/cli-service": "^3.0.4", "@vue/eslint-config-standard": "^3.0.4", "babel-plugin-component": "^1.1.1", "babel-plugin-transform-remove-console": "^6.9.4", "element-theme": "^2.0.1", "element-theme-chalk": "^2.9.1", "eslint-plugin-html": "^5.0.3", "eslint-plugin-import": "^2.16.0", "eslint-plugin-node": "^8.0.1", "eslint-plugin-promise": "^4.0.1", "gulp-autoprefixer": "^6.0.0", "gulp-clean-css": "^3.10.0", "gulp-load-plugins": "^1.5.0", "gulp-rename": "^1.4.0", "gulp-sass": "^4.0.2", "node-sass": "^4.9.3", "sass-loader": "^7.3.1", "svg-sprite-loader": "^4.1.1", "vue-template-compiler": "^2.5.17", "webpack-dev-server": "^3.10.1"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "@vue/standard"], "plugins": ["html", "vue"], "rules": {"space-before-function-paren": 0, "no-debugger": "off"}, "parserOptions": {"parser": "babel-es<PERSON>"}}, "postcss": {"plugins": {"autoprefixer": {}}}, "engines": {"node": ">= 8.11.1", "npm": ">= 5.6.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "element-theme": {"config": "./src/element-ui/theme-variables.scss", "out": "./src/element-ui/theme", "minimize": true, "browsers": ["> 1%", "last 2 versions", "not ie <= 10"]}, "description": "```\r npm install\r ```", "main": "babel.config.js", "author": "", "license": "ISC"}