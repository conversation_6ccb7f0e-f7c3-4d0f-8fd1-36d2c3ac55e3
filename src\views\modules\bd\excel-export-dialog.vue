<template>
  <div>
    <!-- 导出类型选择 -->
    <el-dialog :visible.sync="exportDialogVisible" width="450px" :title="$t('export')" :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form :model="dataForm" :inline="true">
        <el-form-item :label="$t('template.type')">
          <el-select v-model="dataForm.moduleId" filterable clearable>
            <el-option v-for="item in exportTemplateList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button class="blue" size="mini" v-if="$hasPermission('bd:excelexporttemplate:update')" @click="exportHandle()">{{ $t('template.edit') }}</el-button>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="exportDialogVisible = false">{{ this.$t('cancel') }}</el-button>
        <el-button type="primary" @click="exportByDiy()" :loading="buttonLoading" :disabled="false">{{this.$t('export')}}</el-button>
      </span>
    </el-dialog>
    <exportTemplateDetail v-if="exportVisible" ref="exportDetail" @backView="backView"/>
  </div>
</template>

<script>
import dictTypeMixins from '@/mixins/dictTypeMixins'
import mixinViewModule from '@/mixins/view-module'
import ExportTemplateDetail from './excel-export-template-dialog'
import Cookies from 'js-cookie'
import qs from 'qs'
import axios from 'axios'
import request from '@/utils/request'

// table 自定义显示
export default {
  mixins: [dictTypeMixins, mixinViewModule],
  data () {
    return {
      dataForm: {
        id: '',
        url: '',
        masterDTOName: ''
      },
      queryDataForm: {},
      exportListURL: '/bd/excelexporttemplate/listByUrlClient',
      exportURL: '/bd/excelexporttemplate/export',
      exportMethod: 'get',
      exportTemplateList: [],
      unusedObjectArray: [],
      unusedAttributeArray: [],
      exportVisible: false,
      buttonLoading: false,
      exportDialogVisible: false
    }
  },
  methods: {
    init (unusedObjectArray, unusedAttributeArray) {
      if (unusedObjectArray) {
        this.unusedObjectArray = unusedObjectArray
      }
      if (unusedAttributeArray) {
        this.unusedAttributeArray = unusedAttributeArray
      }
      this.dataForm.url = this.dataForm.masterDTOName
      this.$http
        .get(this.exportListURL + '/' + this.dataForm.url)
        .then(({ data: res }) => {
          if (res.code === 500) {
            return this.$message.error(res.msg)
          }
          this.exportTemplateList = res.data
          this.exportDialogVisible = true
        })
        .catch(() => {})
    },
    // 返回
    backFn () {
      this.visible = false
      this.buttonLoading = false
      this.$emit('backView')
    },
    exportByDiy () {
      if (!this.dataForm.moduleId) {
        return this.$message.error(this.$t('validate.declareExportTemplate'))
      }
      this.$http.post('/common/cacheQueryParams', this.queryDataForm).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.buttonLoading = true
        let exportDataForm = { 'queryId': res.data }
        exportDataForm.moduleId = this.dataForm.moduleId
        exportDataForm.token = Cookies.get('cs_token')
        let params = qs.stringify(exportDataForm)
        let requestUrl = `${this.$baseUrl}${this.exportURL}?${params}`
        let httpUtil = axios.create({
          baseURL: request.localtionUrl,
          timeout: 1000 * 180,
          withCredentials: true
        })
        let fileName = ''
        /**
         * 响应拦截
         */
        httpUtil.interceptors.response.use(response => {
          if (response.headers['content-type'] !== 'application/vnd.ms-excel') {
            let enc = new TextDecoder('utf-8')
            let resJson = JSON.parse(enc.decode(new Uint8Array(response.data))) // 转化成json对象
            return this.$message.error(resJson.msg)
          } else {
            fileName = decodeURIComponent(response.headers['filename'])
            return response
          }
        }, error => {
          console.error(error)
          return Promise.reject(error)
        })
        httpUtil({
          method: this.exportMethod,
          url: requestUrl,
          data: '',
          responseType: 'arraybuffer'
        })
          .then(({ data: res }) => {
            if (undefined === res || res === null) {
              this.buttonLoading = false
              return
            }
            const link = document.createElement('a')
            let blob = new Blob([res], { type: 'application/vnd.ms-excel' })
            link.style.display = 'none'
            link.href = URL.createObjectURL(blob)
            link.download = fileName // 下载的文件名
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            this.buttonLoading = false
          })
          .catch(() => {})
      }).catch(() => {})
    },
    // 导出
    exportHandle () {
      // let url = 'inorder'
      // let masterDTOName = 'CoInOrderDTO'
      this.exportVisible = true
      this.$nextTick(() => {
        this.$refs.exportDetail.dataForm.url = this.dataForm.url
        this.$refs.exportDetail.dataForm.masterDTOName = this.dataForm.masterDTOName
        this.$refs.exportDetail.init(this.unusedObjectArray, this.unusedAttributeArray)
      })
    }
  },
  components: {
    ExportTemplateDetail
  }
}
</script>
