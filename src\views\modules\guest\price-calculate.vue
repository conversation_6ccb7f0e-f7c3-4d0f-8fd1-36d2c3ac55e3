<template>
  <div class="aui-card--fill">
    <div class="mod-co__inorder flex_wrap" ref="listPanel">
      <el-card class="search_box" shadow="never">
        <el-form ref="dataForm"  :model="dataForm" :rules="dataRule" label-width="120px">
          <el-row :gutter="20"  type="flex">
            <el-col :span="12">
              <el-form-item :label="$t('psCalculateExpense.country')" prop="country">
                <el-select  filterable v-model="dataForm.country"
                            :placeholder="$t('psCalculateExpense.country')" clearable>
                  <el-option v-for="(item, index) in countryList" :key="index" :label="`${item.name} ${item.code}`"
                             :value="item.code"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('psCalculateExpense.postcode')" prop="postcode">
                <el-input v-model="dataForm.postcode" :placeholder="$t('psCalculateExpense.postcode')" clearable ></el-input>
              </el-form-item>
              <el-form-item :label="$t('psCalculateExpense.weight')" prop="weightD" >
                <el-input v-model="dataForm.weightD" :placeholder="$t('psCalculateExpense.weight')" clearable ></el-input>
              </el-form-item>
              <el-form-item label="箱数" >
                <el-input v-model="packageQty" placeholder="录入材积" @change="packageQtyChanged">
                  <el-button :disabled="packageQty <= 1" size='small' plain type='primary' slot="append" @click='inputBoxInfo' > 录入材积 </el-button>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <!-- <el-form-item :label="$t('psCalculateExpense.logisticsProduct')" prop="logisticsChannel">
                <el-select  filterable v-model="dataForm.logisticsChannel"
                            :placeholder="$t('psCalculateExpense.logisticsProduct')" clearable>
                  <el-option v-for="(item, index) in logisticsProductList" :key="index" :label="item.name"
                             :value="item.code"></el-option>
                </el-select>
              </el-form-item> -->
              <el-form-item :label="$t('psCalculateExpense.productType')" prop="productType">
                <el-select  filterable v-model="dataForm.productType"
                            :placeholder="$t('psCalculateExpense.productType')" clearable>
                  <el-option v-for="(item, index) in parcelTypeList" :key="index" :label="item.dictName"
                             :value="item.dictValue"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="长 * 宽 * 高 (CM)" >
                <el-input :disabled="packageQty > 1" style="width:110px;" v-model="dataForm.lengthD" :placeholder="$t('psCalculateExpense.length')" clearable ></el-input>
                <span> * </span>
                <el-input :disabled="packageQty > 1" style="width:110px;" v-model="dataForm.widthD" :placeholder="$t('psCalculateExpense.width')" clearable ></el-input>
                <span> * </span>
                <el-input :disabled="packageQty > 1" style="width:110px;" v-model="dataForm.heightD" :placeholder="$t('psCalculateExpense.height')" clearable ></el-input>
              </el-form-item>

              <el-form-item :label="$t('psCalculateExpense.declareSum')" prop="declareSum">
                <el-input v-model="dataForm.declareSumD"  clearable  type="number">
                  <el-select slot="prepend" style="width:120px;" v-model="dataForm.declareCurrency" :placeholder="$t('coOrder.declareCurrency')" filterable >
                    <el-option v-for="item in currencyList" :key="item.code" :label="item.name + ' (' + item.code + ')'" :value="item.code"></el-option>
                  </el-select>
                </el-input>
              </el-form-item>
              <!-- <el-form-item :label="$t('psCalculateExpense.declareSum')" prop="declareSum">
                <el-input v-model="dataForm.declareSumD"  clearable ></el-input>
              </el-form-item> -->

              <el-button style="margin-left: 120px; width:100px;" type="primary" @click="getTransSum()" icon="el-icon-search">试&nbsp;&nbsp;&nbsp;算</el-button>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
      <el-card class="flex_tab no_shadow" type="border-card" >
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <el-table v-loading="dataListLoading" :data="dataList"  :max-height="tableHeight">
            <el-table-column type="expand" fixed="left">
              <template slot-scope="scope">
                <el-table :data="scope.row.resultDTOList"  border :max-height="tableHeight">
                  <!-- 动态显示表格 -->
                  <el-table-column v-for="(item, index) in tableItemColumnsArr"  v-if="item.prop === 'orderPackageId' ? scope.row.billingItemType!==0:true" :key="index" :type="item.type" :prop="item.prop"
                                   header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
                    <template slot-scope="scope">
                      <div>
                        {{formatterFn(scope,item.prop)}}
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </el-table-column>
            <!-- 动态显示表格 -->
            <el-table-column v-for="(item, index) in tableColumnsArr" :key="index" :type="item.type" :prop="item.prop"
                             header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
              <template slot-scope="scope">
                <div :class="scope.row.billingItemType === 1?'warning':'default'">
                  {{formatterFn(scope,item.prop)}}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>
    <!-- 自定义列显示弹窗 -->
    <inputBoxInfoDialog ref='inputBoxInfoDialog'  @closeInputBoxInfo='closeInputBoxInfo' @inputBoxInfoEmit='inputBoxInfoEmit' v-if='inputBoxInfoDialogShow' ></inputBoxInfoDialog>
  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import listPage from '@/mixins/listPage'
// import baseApi from '@/api'
import baseData from '@/api/baseData'
import { formatterType, gtmToLtm, timestampFormat, formatterName, formatterCodeName } from '@/filters/filters'
import { isLength32 } from '@/utils/validate'
import inputBoxInfoDialog from '../ps/input-box-info-dialog'
export default {
  mixins: [listPage, mixinViewModule],
  data () {
    return {
      tableColumns: [
        { type: '', width: '150', prop: 'logisticsChannelName', label: this.$t('psCalculateExpense.logisticsProduct'), align: 'center', isShow: true, disabled: false },
        // { type: '', width: '150', prop: 'logisticsChannelId', label: this.$t('psCalculateExpense.logisticsProduct'), align: 'center', isShow: true, disabled: false },
        // { type: '', width: '100', prop: 'billingItemType', label: this.$t('psCalculateExpense.billingItemType'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'aging', label: this.$t('psCalculateExpense.aging'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'sum', label: this.$t('psCalculateExpense.sum'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'balanceWeight', label: this.$t('psCalculateExpense.balanceWeight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'sysSum', label: this.$t('psCalculateExpense.sysSum'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'sysBalanceWeight', label: this.$t('psCalculateExpense.sysBalanceWeight'), align: 'center', isShow: true, disabled: false }
      ],
      tableItemColumns: [
        { type: '', width: '100', prop: 'feeTypeName', label: this.$t('psCalculateExpense.feeType'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'sum', label: this.$t('psCalculateExpense.sum'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'balanceWeight', label: this.$t('psCalculateExpense.balanceWeight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'sysSum', label: this.$t('psCalculateExpense.sysSum'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'sysBalanceWeight', label: this.$t('psCalculateExpense.sysBalanceWeight'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'formula', label: this.$t('psCalculateExpense.formula'), align: 'center', isShow: true, disabled: false }
      ],
      mixinViewModuleOptions: {
        activatedIsNeed: false
      },
      dataForm: {
        id: '',
        companyId: '',
        country: '',
        postcode: '',
        weightD: 1,
        lengthD: '',
        widthD: '',
        heightD: '',
        declareSumD: 1,
        declareCurrency: 'USD',
        objectType: 0,
        objectId: 0,
        productType: 10,
        payable: 0,
        client: 1,
        psCalculateExpenseDetailList: []
      },
      changeStatusForm: {
        ids: '',
        status: ''
      },
      showTransFormulaInClient: 'N',
      inputBoxInfoDialogShow: false,
      packageQty: 1,
      boxDataList: [],
      parcelTypeList: [{ 'dictValue': 11, 'dictName': 'PAK袋' }, { 'dictValue': 12, 'dictName': '文件' }, { 'dictValue': 10, 'dictName': '包裹' }],
      countryList: [ { code: 'AD', name: '安道尔' }, { code: 'AE', name: '阿拉伯联合酋长国' }, { code: 'AF', name: '阿富汗' }, { code: 'AG', name: '安提瓜和巴布达' }, { code: 'AI', name: '安圭拉岛' }, { code: 'AL', name: '阿尔巴尼亚' }, { code: 'AM', name: '亚美尼亚' }, { code: 'AN', name: '荷属安的列斯群岛' }, { code: 'AO', name: '安哥拉' }, { code: 'AQ', name: '南极洲' }, { code: 'AR', name: '阿根廷' }, { code: 'AS', name: '萨摩亚(美国属土)' }, { code: 'AT', name: '奥地利' }, { code: 'AU', name: '澳大利亚' }, { code: 'AW', name: '阿鲁巴' }, { code: 'AX', name: '奥兰群岛' }, { code: 'AZ', name: '阿塞拜疆' }, { code: 'BA', name: '波斯尼亚-黑塞哥维那' }, { code: 'BB', name: '巴巴多斯' }, { code: 'BD', name: '孟加拉国' }, { code: 'BE', name: '比利时' }, { code: 'BF', name: '布基纳法索' }, { code: 'BG', name: '保加利亚' }, { code: 'BH', name: '巴林' }, { code: 'BI', name: '布隆迪' }, { code: 'BJ', name: '贝宁' }, { code: 'BL', name: '圣巴泰勒米岛' }, { code: 'BM', name: '百慕大' }, { code: 'BN', name: '文莱达鲁萨兰国' }, { code: 'BO', name: '玻利维亚' }, { code: 'BQ', name: '博奈尔、圣尤斯特歇斯和萨巴' }, { code: 'BR', name: '巴西' }, { code: 'BS', name: '巴哈马' }, { code: 'BT', name: '不丹' }, { code: 'BV', name: '布维岛' }, { code: 'BW', name: '博茨瓦纳' }, { code: 'BY', name: '白俄罗斯' }, { code: 'BZ', name: '伯利兹' }, { code: 'CA', name: '加拿大' }, { code: 'CC', name: '科科斯群岛' }, { code: 'CD', name: '刚果(民主共和国)' }, { code: 'CF', name: '中非共和国' }, { code: 'CG', name: '刚果' }, { code: 'CH', name: '瑞士' }, { code: 'CI', name: '科特迪瓦(象牙海岸)' }, { code: 'CK', name: '库克群岛' }, { code: 'CL', name: '智利' }, { code: 'CM', name: '喀麦隆' }, { code: 'CN', name: '中国' }, { code: 'CO', name: '哥伦比亚' }, { code: 'CR', name: '哥斯达黎加' }, { code: 'CU', name: '古巴' }, { code: 'CV', name: '佛得角群岛' }, { code: 'CW', name: '库拉索' }, { code: 'CX', name: '圣诞岛' }, { code: 'CY', name: '塞浦路斯' }, { code: 'CZ', name: '捷克' }, { code: 'DE', name: '德国' }, { code: 'DJ', name: '吉布提' }, { code: 'DK', name: '丹麦' }, { code: 'DM', name: '多米尼克' }, { code: 'DO', name: '多米尼加共和国' }, { code: 'DZ', name: '阿尔及利亚' }, { code: 'EC', name: '厄瓜多尔' }, { code: 'EE', name: '爱沙尼亚' }, { code: 'EG', name: '埃及' }, { code: 'EH', name: '西撒哈拉' }, { code: 'ER', name: '厄立特里亚' }, { code: 'ES', name: '西班牙' }, { code: 'ET', name: '埃塞俄比亚' }, { code: 'FI', name: '芬兰' }, { code: 'FJ', name: '斐济' }, { code: 'FK', name: '福克兰群岛' }, { code: 'FM', name: '密克罗尼西亚' }, { code: 'FO', name: '法罗群岛' }, { code: 'FR', name: '法国' }, { code: 'FX', name: '法属美特罗波利坦' }, { code: 'GA', name: '加蓬' }, { code: 'GB', name: '英国' }, { code: 'GD', name: '格林纳达' }, { code: 'GE', name: '格鲁吉亚' }, { code: 'GF', name: '法属圭亚那' }, { code: 'GG', name: '根西岛' }, { code: 'GH', name: '加纳' }, { code: 'GI', name: '直布罗陀' }, { code: 'GL', name: '格陵兰岛' }, { code: 'GM', name: '冈比亚' }, { code: 'GN', name: '几内亚' }, { code: 'GP', name: '瓜德罗普岛' }, { code: 'GQ', name: '赤道几内亚' }, { code: 'GR', name: '希腊' }, { code: 'GS', name: '南乔治亚岛和南桑德韦奇岛' }, { code: 'GT', name: '危地马拉' }, { code: 'GU', name: '关岛' }, { code: 'GW', name: '几内亚比绍' }, { code: 'GY', name: '圭亚那' }, { code: 'HI', name: '夏威夷' }, { code: 'HK', name: '香港' }, { code: 'HM', name: '赫德岛和麦克唐岛' }, { code: 'HN', name: '洪都拉斯' }, { code: 'HR', name: '克罗地亚' }, { code: 'HT', name: '海地' }, { code: 'HU', name: '匈牙利' }, { code: 'ID', name: '印度尼西亚' }, { code: 'IE', name: '爱尔兰' }, { code: 'IL', name: '以色列' }, { code: 'IM', name: '马恩岛' }, { code: 'IN', name: '印度' }, { code: 'IO', name: '英属印度洋地区' }, { code: 'IQ', name: '伊拉克' }, { code: 'IR', name: '伊朗' }, { code: 'IS', name: '冰岛' }, { code: 'IT', name: '意大利' }, { code: 'JE', name: '泽西岛' }, { code: 'JM', name: '牙买加' }, { code: 'JO', name: '约旦' }, { code: 'JP', name: '日本' }, { code: 'KE', name: '肯尼亚' }, { code: 'KG', name: '吉尔吉斯斯坦' }, { code: 'KH', name: '柬埔寨' }, { code: 'KI', name: '基里巴斯' }, { code: 'KM', name: '科摩罗' }, { code: 'KN', name: '圣基茨和尼维斯' }, { code: 'KO', name: '科索沃' }, { code: 'KP', name: '朝鲜' }, { code: 'KR', name: '韩国' }, { code: 'KT', name: '科特迪瓦共和国' }, { code: 'KW', name: '科威特' }, { code: 'KY', name: '开曼群岛' }, { code: 'KZ', name: '哈萨克斯坦' }, { code: 'LA', name: '老挝' }, { code: 'LB', name: '黎巴嫩' }, { code: 'LC', name: '圣卢西亚' }, { code: 'LI', name: '列支敦士登' }, { code: 'LK', name: '斯里兰卡' }, { code: 'LR', name: '利比里亚' }, { code: 'LS', name: '莱索托' }, { code: 'LT', name: '立陶宛' }, { code: 'LU', name: '卢森堡' }, { code: 'LV', name: '拉脱维亚' }, { code: 'LY', name: '利比亚' }, { code: 'MA', name: '摩洛哥' }, { code: 'MC', name: '摩纳哥' }, { code: 'MD', name: '摩尔多瓦' }, { code: 'ME', name: '黑山' }, { code: 'MF', name: '圣马丁' }, { code: 'MG', name: '马达加斯加' }, { code: 'MH', name: '马绍尔群岛' }, { code: 'MK', name: '马其顿' }, { code: 'ML', name: '马里' }, { code: 'MM', name: '缅甸' }, { code: 'MN', name: '蒙古' }, { code: 'MO', name: '澳门' }, { code: 'MP', name: '马里亚纳群岛 (北)' }, { code: 'MQ', name: '马提尼克岛' }, { code: 'MR', name: '毛里塔尼亚' }, { code: 'MS', name: '蒙特塞拉特' }, { code: 'MT', name: '马耳他' }, { code: 'MU', name: '毛里求斯' }, { code: 'MV', name: '马尔代夫' }, { code: 'MW', name: '马拉维' }, { code: 'MX', name: '墨西哥' }, { code: 'MY', name: '马来西亚' }, { code: 'MZ', name: '莫桑比克' }, { code: 'NA', name: '纳米比亚' }, { code: 'NC', name: '新喀里多尼亚' }, { code: 'NE', name: '尼日尔' }, { code: 'NF', name: '诺福克岛' }, { code: 'NG', name: '尼日利亚' }, { code: 'NI', name: '尼加拉瓜' }, { code: 'NK', name: '纳戈尔诺-卡拉巴赫' }, { code: 'NL', name: '荷兰' }, { code: 'NO', name: '挪威' }, { code: 'NP', name: '尼泊尔' }, { code: 'NR', name: '瑙鲁' }, { code: 'NU', name: '纽埃' }, { code: 'NZ', name: '新西兰' }, { code: 'OM', name: '阿曼' }, { code: 'PA', name: '巴拿马' }, { code: 'PE', name: '秘鲁' }, { code: 'PF', name: '法属波利尼西亚' }, { code: 'PG', name: '巴布亚新几内亚' }, { code: 'PH', name: '菲律宾' }, { code: 'PK', name: '巴基斯坦' }, { code: 'PL', name: '波兰' }, { code: 'PM', name: '圣皮埃尔和密克隆群岛' }, { code: 'PN', name: '皮特凯恩岛' }, { code: 'PR', name: '波多黎各' }, { code: 'PS', name: '巴勒斯坦' }, { code: 'PT', name: '葡萄牙' }, { code: 'PW', name: '帛琉(帕劳)' }, { code: 'PY', name: '巴拉圭' }, { code: 'QA', name: '卡塔尔' }, { code: 'RE', name: '留尼旺岛' }, { code: 'RO', name: '罗马尼亚' }, { code: 'RS', name: '塞尔维亚' }, { code: 'RU', name: '俄罗斯' }, { code: 'RW', name: '卢旺达' }, { code: 'SA', name: '沙特阿拉伯' }, { code: 'SB', name: '所罗门群岛' }, { code: 'SC', name: '塞舌尔' }, { code: 'SD', name: '苏丹' }, { code: 'SE', name: '瑞典' }, { code: 'SG', name: '新加坡' }, { code: 'SH', name: '圣赫勒拿岛' }, { code: 'SI', name: '斯洛文尼亚' }, { code: 'SJ', name: '斯匹次卑尔根群岛' }, { code: 'SK', name: '斯洛伐克' }, { code: 'SL', name: '塞拉里昂' }, { code: 'SM', name: '圣马力诺' }, { code: 'SN', name: '塞内加尔' }, { code: 'SO', name: '索马里' }, { code: 'SR', name: '苏里南' }, { code: 'SS', name: '南苏丹' }, { code: 'ST', name: '圣多美和普林西比' }, { code: 'SV', name: '萨尔瓦多' }, { code: 'SX', name: '荷属圣马丁' }, { code: 'SY', name: '阿拉伯叙利亚共和国(叙利亚)' }, { code: 'SZ', name: '斯威士兰' }, { code: 'TC', name: '特克斯和凯科斯群岛' }, { code: 'TD', name: '乍得' }, { code: 'TF', name: '法属南部领土' }, { code: 'TG', name: '多哥' }, { code: 'TH', name: '泰国' }, { code: 'TJ', name: '塔吉克' }, { code: 'TK', name: '托克劳' }, { code: 'TM', name: '土库曼' }, { code: 'TN', name: '突尼斯' }, { code: 'TO', name: '汤加' }, { code: 'TL', name: '东帝汶' }, { code: 'TR', name: '土耳其' }, { code: 'TT', name: '特立尼达和多巴哥' }, { code: 'TV', name: '图瓦卢' }, { code: 'TW', name: '中国台湾' }, { code: 'TZ', name: '坦桑尼亚' }, { code: 'UA', name: '乌克兰' }, { code: 'UG', name: '乌干达' }, { code: 'UM', name: '美国本土外小岛屿' }, { code: 'US', name: '美国' }, { code: 'UY', name: '乌拉圭' }, { code: 'UZ', name: '乌兹别克斯坦' }, { code: 'VA', name: '梵蒂冈' }, { code: 'VC', name: '圣文森特和格林纳丁斯' }, { code: 'VE', name: '委内瑞拉' }, { code: 'VG', name: '托尔托拉岛(英属处女群岛)' }, { code: 'VI', name: '美属维尔京群岛' }, { code: 'VN', name: '越南' }, { code: 'VU', name: '瓦努阿图' }, { code: 'WF', name: '瓦利斯群岛和富图纳群岛' }, { code: 'WS', name: '西萨摩亚' }, { code: 'XA', name: '加那利群岛' }, { code: 'XB', name: '特里斯坦-达库尼亚岛' }, { code: 'XC', name: '海峡群岛' }, { code: 'XD', name: '阿森松' }, { code: 'XE', name: '加沙及汗尤尼斯' }, { code: 'XF', name: '科西嘉岛' }, { code: 'XG', name: '北非西班牙属土' }, { code: 'XH', name: '亚速尔' }, { code: 'XI', name: '马德拉' }, { code: 'XJ', name: '巴利阿里群岛' }, { code: 'XK', name: '加罗林群岛(帕劳)' }, { code: 'XL', name: '新西兰属土岛屿(库克群岛)' }, { code: 'XM', name: '威克岛' }, { code: 'XN', name: '尼维斯' }, { code: 'XO', name: '科索沃' }, { code: 'XS', name: '索马里兰' }, { code: 'XY', name: '圣巴特勒米岛' }, { code: 'YE', name: '也门' }, { code: 'YT', name: '马约特' }, { code: 'YU', name: '南斯拉夫' }, { code: 'ZA', name: '南非' }, { code: 'ZM', name: '赞比亚' }, { code: 'ZW', name: '津巴布韦' } ],
      weightUnitList: [],
      billingItemTypeList: [],
      currencyList: [],
      logisticsProductList: [],
      baseQuotationAllList: [],
      usingFeeTypeList: [],
      logisticsProductTableList: []
    }
  },
  created () {
    this.params = this.$route.query
    this.dataForm.companyId = this.params['companyId']
    this.dataForm.country = this.params['country']
    this.dataForm.postcode = this.params['postcode']
    this.dataForm.weightD = this.params['weightD']
    this.dataForm.lengthD = this.params['lengthD']
    this.dataForm.widthD = this.params['widthD']
    this.dataForm.heightD = this.params['heightD']

    this.getBaseData()
    this.$nextTick(() => {
      // 是否显示公式
      this.tableItemColumnsArr.splice(5, 1)
      this.getTransSum()
    })
  },
  methods: {
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'billingItemType':
          value = formatterType(scope.row.billingItemType + '', this.billingItemTypeList)
          break
        case 'logisticsChannelId':
          value = formatterName(scope.row.logisticsChannelId, this.logisticsProductTableList)
          break
        case 'feeType':
          value = formatterName(scope.row.feeType, this.usingFeeTypeList)
          break
        case 'sum':
          value = `${scope.row.sum} (${scope.row.currency})`
          break
        case 'balanceWeight':
          value = `${scope.row.balanceWeight} (${scope.row.weightUnit})`
          break
        case 'sysSum':
          value = `${scope.row.sysSum} (${scope.row.sysCurrency})`
          break
        case 'sysBalanceWeight':
          value = `${scope.row.sysBalanceWeight} (${scope.row.sysWeightUnit})`
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    // 同步通过接口获取基础数据
    getBaseData () {
      // baseData(baseApi.sysParams + '/SHOW_TRANS_FORMULA_IN_CLIENT').then((res) => {
      //   this.showTransFormulaInClient = res
      // })
      baseData(`/guest/currencyList?companyId=${this.dataForm.companyId}`).then((res) => {
        this.currencyList = res
      })
    },
    packageQtyChanged () {
      this.boxDataList = []
    },
    inputBoxInfo () {
      this.inputBoxInfoDialogShow = true
      this.$nextTick(() => {
        this.$refs.inputBoxInfoDialog.init(this.packageQty, this.boxDataList)
      })
    },
    closeInputBoxInfo () {
      this.inputBoxInfoDialogShow = false
    },
    inputBoxInfoEmit (boxDataList, totalWeightD) {
      this.boxDataList = boxDataList
      this.packageQty = boxDataList.length || null
      this.dataForm.weightD = totalWeightD || ''
      this.$refs.inputBoxInfoDialog.submitLoading = false
      this.inputBoxInfoDialogShow = false
    },
    getTransSum () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        // console.log('lengthD', this.dataForm.lengthD)
        // console.log('weightD', this.dataForm.weightD)
        // console.log('this.boxDataList', this.boxDataList)
        if (this.boxDataList && this.boxDataList.length > 1) {
          let detailList = []
          this.boxDataList.forEach(item => {
            detailList.push({
              lengthD: item.packageLengthD,
              widthD: item.packageWidthD,
              heightD: item.packageHeightD,
              weightD: item.packageWeightD
            })
          })
          this.dataForm.psCalculateExpenseDetailList = [...detailList]
        } else {
          this.dataForm.psCalculateExpenseDetailList[0] = {
            lengthD: this.dataForm.lengthD,
            widthD: this.dataForm.widthD,
            heightD: this.dataForm.heightD,
            weightD: this.dataForm.weightD
          }
        }
        this.$http['post']('/guest/queryTransSum', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            this.$alert(res.msg, '错误信息', {
              confirmButtonText: '确定',
              dangerouslyUseHTMLString: true
            })
            this.dataList = []
            return false
          } else {
            this.dataList = res.data
            // console.log('transSum', this.dataList)
          }
        }).catch(() => {})
      })
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat,
    formatterName,
    formatterCodeName
  },
  computed: {
    dataRule () {
      const postCodeValidate = (rule, value, callback) => {
        if (value && !isLength32(value)) {
          return callback(new Error(this.$t('validate.postCodeValidate', { 'attr': this.$t('psCalculateExpense.postcode') })))
        }
        callback()
      }
      const lengthValidate = (rule, value, callback) => {
        let pattrn = /^[0-9]+(.[0-9]{0,1})?$/
        if (value && !(pattrn.test(value) && value > 0)) {
          return callback(new Error(this.$t('validate.lengthValidate', { 'attr': this.$t('psCalculateExpense.length') })))
        }
        if (value > 99999) {
          return callback(new Error(this.$t('validate.max', { 'attr': this.$t('validate.max'), 'max': '99999' })))
        }
        callback()
      }
      const widthValidate = (rule, value, callback) => {
        let pattrn = /^[0-9]+(.[0-9]{0,1})?$/
        if (value && !(pattrn.test(value) && value > 0)) {
          return callback(new Error(this.$t('validate.lengthValidate', { 'attr': this.$t('psCalculateExpense.width') })))
        }
        if (value > 99999) {
          return callback(new Error(this.$t('validate.max', { 'attr': this.$t('validate.max'), 'max': '99999' })))
        }
        callback()
      }
      const heightValidate = (rule, value, callback) => {
        let pattrn = /^[0-9]+(.[0-9]{0,1})?$/
        if (value && !(pattrn.test(value) && value > 0)) {
          return callback(new Error(this.$t('validate.lengthValidate', { 'attr': this.$t('psCalculateExpense.height') })))
        }
        if (value > 99999) {
          return callback(new Error(this.$t('validate.max', { 'attr': this.$t('validate.max'), 'max': '99999' })))
        }
        callback()
      }
      const weightValidate = (rule, value, callback) => {
        let pattrn = /^[0-9]+(.[0-9]{0,3})?$/
        if (value && !(pattrn.test(value) && value > 0)) {
          return callback(new Error(this.$t('validate.weight', { 'attr': this.$t('psCalculateExpense.weight') })))
        }
        if (value > 99999) {
          return callback(new Error(this.$t('validate.max', { 'attr': this.$t('validate.max'), 'max': '99999' })))
        }
        callback()
      }
      return {
        productType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        country: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        postcode: [
          { validator: postCodeValidate, trigger: 'blur' }
        ],
        lengthD: [
          { validator: lengthValidate, trigger: 'blur' }
        ],
        widthD: [
          { validator: widthValidate, trigger: 'blur' }
        ],
        heightD: [
          { validator: heightValidate, trigger: 'blur' }
        ],
        weightD: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: weightValidate, trigger: 'blur' }
        ]
      }
    },
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    tableItemColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableItemColumns).map((key) => this.tableItemColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    }
  },
  components: {
    inputBoxInfoDialog
  }
}
</script>
