<template>
  <el-card shadow="never" class="tksTrack-panel">
        <div class="login-header">
          <h2 class="login-brand">运单轨迹</h2>
        </div>
    <el-row :gutter="10" type="flex">
     <el-col :sm="12">
       <el-input type="textarea" class="margin_bottom15" v-model="nos" :autosize="{ minRows: 5, maxRows: 20}" placeholder="请输入1个或最多不超过100个运单号，用逗号或换行分隔" @input="setNo"></el-input>
<!--       <el-row :gutter="20" type="flex" justify="space-between">-->
<!--       </el-row>-->
     </el-col>
      <el-col :sm="6" class="text-center">
        <el-button type="primary" :loading="loadingFlag" @click="getTrackInfo()">{{$t('query')}}</el-button>
      </el-col>
    </el-row>
    <h2 class="margin_bottom15">
      {{lang.tksTracking.result}}：{{result.normalCount}}
    </h2>
    <el-divider />

    <el-card class="box-card" style="width:100%;" v-for="(item, pindex) in this.result.list" :key="pindex">
      <div>
        <transition name="on"  mode="out-in">
        <div v-if="trackActivating" >
        <el-row :gutter="10">
         <el-col :sm="22" :offset="1">
           <div class="margin_bottom22">
             <h3>
              <p><span style="padding-right:5px;">{{lang.tksTracking.queryNO}}:</span><span>{{item.number}}</span></p>
              <p>
                 <span style="padding-right:5px;">{{lang.tksTracking.trackingNumber}}:</span><span>{{item.oriNumber}}</span>
                 <span style='float: right; color:forestgreen; padding-right:10px;'>{{item.destCountry | formatterCodeName(countryList)}}</span>
              </p>
             </h3>
             <!-- <p>
               <span key="s3" class="font-size14 danger">{{lang.tksTracking.trackDescription}}</span>
             </p> -->
           </div>
           <div class="timeline-box block">
             <el-timeline>
               <el-timeline-item
                 v-for="(activity, index) in getActivities(item)"
                 :key="index"
                 :icon="activity.icon"
                 :type="activity.type"
                 :color="activity.color"
                 :size="activity.size"
                 :timestamp="activity.timestamp">
                 {{activity.content}}
               </el-timeline-item>
             </el-timeline>
           </div>
         </el-col>
        </el-row>
        </div>
        </transition>
      </div>
    </el-card>
  </el-card>
</template>

<script>
import tksCN from '@/i18n/tks-CN'
export default {
  data () {
    return {
      nos: '',
      companyId: '',
      loadingFlag: false,
      // 查询单号个数限制
      noSizeInner: 100,
      // 查询结果
      result: {
        // 查询正常条数
        normalCount: 0,
        // 查询异常条数
        abnormalCount: 0,
        // 轨迹信息
        list: []
      },
      lang: tksCN,
      trackActive: 0,
      trackActivating: true,
      // 中英文标识--便于数据转换 tksCN:中文,tksEN:英文
      countryList: [ { code: 'AD', name: '安道尔' }, { code: 'AE', name: '阿拉伯联合酋长国' }, { code: 'AF', name: '阿富汗' }, { code: 'AG', name: '安提瓜和巴布达' }, { code: 'AI', name: '安圭拉岛' }, { code: 'AL', name: '阿尔巴尼亚' }, { code: 'AM', name: '亚美尼亚' }, { code: 'AN', name: '荷属安的列斯群岛' }, { code: 'AO', name: '安哥拉' }, { code: 'AQ', name: '南极洲' }, { code: 'AR', name: '阿根廷' }, { code: 'AS', name: '萨摩亚(美国属土)' }, { code: 'AT', name: '奥地利' }, { code: 'AU', name: '澳大利亚' }, { code: 'AW', name: '阿鲁巴' }, { code: 'AX', name: '奥兰群岛' }, { code: 'AZ', name: '阿塞拜疆' }, { code: 'BA', name: '波斯尼亚-黑塞哥维那' }, { code: 'BB', name: '巴巴多斯' }, { code: 'BD', name: '孟加拉国' }, { code: 'BE', name: '比利时' }, { code: 'BF', name: '布基纳法索' }, { code: 'BG', name: '保加利亚' }, { code: 'BH', name: '巴林' }, { code: 'BI', name: '布隆迪' }, { code: 'BJ', name: '贝宁' }, { code: 'BL', name: '圣巴泰勒米岛' }, { code: 'BM', name: '百慕大' }, { code: 'BN', name: '文莱达鲁萨兰国' }, { code: 'BO', name: '玻利维亚' }, { code: 'BQ', name: '博奈尔、圣尤斯特歇斯和萨巴' }, { code: 'BR', name: '巴西' }, { code: 'BS', name: '巴哈马' }, { code: 'BT', name: '不丹' }, { code: 'BV', name: '布维岛' }, { code: 'BW', name: '博茨瓦纳' }, { code: 'BY', name: '白俄罗斯' }, { code: 'BZ', name: '伯利兹' }, { code: 'CA', name: '加拿大' }, { code: 'CC', name: '科科斯群岛' }, { code: 'CD', name: '刚果(民主共和国)' }, { code: 'CF', name: '中非共和国' }, { code: 'CG', name: '刚果' }, { code: 'CH', name: '瑞士' }, { code: 'CI', name: '科特迪瓦(象牙海岸)' }, { code: 'CK', name: '库克群岛' }, { code: 'CL', name: '智利' }, { code: 'CM', name: '喀麦隆' }, { code: 'CN', name: '中国' }, { code: 'CO', name: '哥伦比亚' }, { code: 'CR', name: '哥斯达黎加' }, { code: 'CU', name: '古巴' }, { code: 'CV', name: '佛得角群岛' }, { code: 'CW', name: '库拉索' }, { code: 'CX', name: '圣诞岛' }, { code: 'CY', name: '塞浦路斯' }, { code: 'CZ', name: '捷克' }, { code: 'DE', name: '德国' }, { code: 'DJ', name: '吉布提' }, { code: 'DK', name: '丹麦' }, { code: 'DM', name: '多米尼克' }, { code: 'DO', name: '多米尼加共和国' }, { code: 'DZ', name: '阿尔及利亚' }, { code: 'EC', name: '厄瓜多尔' }, { code: 'EE', name: '爱沙尼亚' }, { code: 'EG', name: '埃及' }, { code: 'EH', name: '西撒哈拉' }, { code: 'ER', name: '厄立特里亚' }, { code: 'ES', name: '西班牙' }, { code: 'ET', name: '埃塞俄比亚' }, { code: 'FI', name: '芬兰' }, { code: 'FJ', name: '斐济' }, { code: 'FK', name: '福克兰群岛' }, { code: 'FM', name: '密克罗尼西亚' }, { code: 'FO', name: '法罗群岛' }, { code: 'FR', name: '法国' }, { code: 'FX', name: '法属美特罗波利坦' }, { code: 'GA', name: '加蓬' }, { code: 'GB', name: '英国' }, { code: 'GD', name: '格林纳达' }, { code: 'GE', name: '格鲁吉亚' }, { code: 'GF', name: '法属圭亚那' }, { code: 'GG', name: '根西岛' }, { code: 'GH', name: '加纳' }, { code: 'GI', name: '直布罗陀' }, { code: 'GL', name: '格陵兰岛' }, { code: 'GM', name: '冈比亚' }, { code: 'GN', name: '几内亚' }, { code: 'GP', name: '瓜德罗普岛' }, { code: 'GQ', name: '赤道几内亚' }, { code: 'GR', name: '希腊' }, { code: 'GS', name: '南乔治亚岛和南桑德韦奇岛' }, { code: 'GT', name: '危地马拉' }, { code: 'GU', name: '关岛' }, { code: 'GW', name: '几内亚比绍' }, { code: 'GY', name: '圭亚那' }, { code: 'HI', name: '夏威夷' }, { code: 'HK', name: '香港' }, { code: 'HM', name: '赫德岛和麦克唐岛' }, { code: 'HN', name: '洪都拉斯' }, { code: 'HR', name: '克罗地亚' }, { code: 'HT', name: '海地' }, { code: 'HU', name: '匈牙利' }, { code: 'ID', name: '印度尼西亚' }, { code: 'IE', name: '爱尔兰' }, { code: 'IL', name: '以色列' }, { code: 'IM', name: '马恩岛' }, { code: 'IN', name: '印度' }, { code: 'IO', name: '英属印度洋地区' }, { code: 'IQ', name: '伊拉克' }, { code: 'IR', name: '伊朗' }, { code: 'IS', name: '冰岛' }, { code: 'IT', name: '意大利' }, { code: 'JE', name: '泽西岛' }, { code: 'JM', name: '牙买加' }, { code: 'JO', name: '约旦' }, { code: 'JP', name: '日本' }, { code: 'KE', name: '肯尼亚' }, { code: 'KG', name: '吉尔吉斯斯坦' }, { code: 'KH', name: '柬埔寨' }, { code: 'KI', name: '基里巴斯' }, { code: 'KM', name: '科摩罗' }, { code: 'KN', name: '圣基茨和尼维斯' }, { code: 'KO', name: '科索沃' }, { code: 'KP', name: '朝鲜' }, { code: 'KR', name: '韩国' }, { code: 'KT', name: '科特迪瓦共和国' }, { code: 'KW', name: '科威特' }, { code: 'KY', name: '开曼群岛' }, { code: 'KZ', name: '哈萨克斯坦' }, { code: 'LA', name: '老挝' }, { code: 'LB', name: '黎巴嫩' }, { code: 'LC', name: '圣卢西亚' }, { code: 'LI', name: '列支敦士登' }, { code: 'LK', name: '斯里兰卡' }, { code: 'LR', name: '利比里亚' }, { code: 'LS', name: '莱索托' }, { code: 'LT', name: '立陶宛' }, { code: 'LU', name: '卢森堡' }, { code: 'LV', name: '拉脱维亚' }, { code: 'LY', name: '利比亚' }, { code: 'MA', name: '摩洛哥' }, { code: 'MC', name: '摩纳哥' }, { code: 'MD', name: '摩尔多瓦' }, { code: 'ME', name: '黑山' }, { code: 'MF', name: '圣马丁' }, { code: 'MG', name: '马达加斯加' }, { code: 'MH', name: '马绍尔群岛' }, { code: 'MK', name: '马其顿' }, { code: 'ML', name: '马里' }, { code: 'MM', name: '缅甸' }, { code: 'MN', name: '蒙古' }, { code: 'MO', name: '澳门' }, { code: 'MP', name: '马里亚纳群岛 (北)' }, { code: 'MQ', name: '马提尼克岛' }, { code: 'MR', name: '毛里塔尼亚' }, { code: 'MS', name: '蒙特塞拉特' }, { code: 'MT', name: '马耳他' }, { code: 'MU', name: '毛里求斯' }, { code: 'MV', name: '马尔代夫' }, { code: 'MW', name: '马拉维' }, { code: 'MX', name: '墨西哥' }, { code: 'MY', name: '马来西亚' }, { code: 'MZ', name: '莫桑比克' }, { code: 'NA', name: '纳米比亚' }, { code: 'NC', name: '新喀里多尼亚' }, { code: 'NE', name: '尼日尔' }, { code: 'NF', name: '诺福克岛' }, { code: 'NG', name: '尼日利亚' }, { code: 'NI', name: '尼加拉瓜' }, { code: 'NK', name: '纳戈尔诺-卡拉巴赫' }, { code: 'NL', name: '荷兰' }, { code: 'NO', name: '挪威' }, { code: 'NP', name: '尼泊尔' }, { code: 'NR', name: '瑙鲁' }, { code: 'NU', name: '纽埃' }, { code: 'NZ', name: '新西兰' }, { code: 'OM', name: '阿曼' }, { code: 'PA', name: '巴拿马' }, { code: 'PE', name: '秘鲁' }, { code: 'PF', name: '法属波利尼西亚' }, { code: 'PG', name: '巴布亚新几内亚' }, { code: 'PH', name: '菲律宾' }, { code: 'PK', name: '巴基斯坦' }, { code: 'PL', name: '波兰' }, { code: 'PM', name: '圣皮埃尔和密克隆群岛' }, { code: 'PN', name: '皮特凯恩岛' }, { code: 'PR', name: '波多黎各' }, { code: 'PS', name: '巴勒斯坦' }, { code: 'PT', name: '葡萄牙' }, { code: 'PW', name: '帛琉(帕劳)' }, { code: 'PY', name: '巴拉圭' }, { code: 'QA', name: '卡塔尔' }, { code: 'RE', name: '留尼旺岛' }, { code: 'RO', name: '罗马尼亚' }, { code: 'RS', name: '塞尔维亚' }, { code: 'RU', name: '俄罗斯' }, { code: 'RW', name: '卢旺达' }, { code: 'SA', name: '沙特阿拉伯' }, { code: 'SB', name: '所罗门群岛' }, { code: 'SC', name: '塞舌尔' }, { code: 'SD', name: '苏丹' }, { code: 'SE', name: '瑞典' }, { code: 'SG', name: '新加坡' }, { code: 'SH', name: '圣赫勒拿岛' }, { code: 'SI', name: '斯洛文尼亚' }, { code: 'SJ', name: '斯匹次卑尔根群岛' }, { code: 'SK', name: '斯洛伐克' }, { code: 'SL', name: '塞拉里昂' }, { code: 'SM', name: '圣马力诺' }, { code: 'SN', name: '塞内加尔' }, { code: 'SO', name: '索马里' }, { code: 'SR', name: '苏里南' }, { code: 'SS', name: '南苏丹' }, { code: 'ST', name: '圣多美和普林西比' }, { code: 'SV', name: '萨尔瓦多' }, { code: 'SX', name: '荷属圣马丁' }, { code: 'SY', name: '阿拉伯叙利亚共和国(叙利亚)' }, { code: 'SZ', name: '斯威士兰' }, { code: 'TC', name: '特克斯和凯科斯群岛' }, { code: 'TD', name: '乍得' }, { code: 'TF', name: '法属南部领土' }, { code: 'TG', name: '多哥' }, { code: 'TH', name: '泰国' }, { code: 'TJ', name: '塔吉克' }, { code: 'TK', name: '托克劳' }, { code: 'TM', name: '土库曼' }, { code: 'TN', name: '突尼斯' }, { code: 'TO', name: '汤加' }, { code: 'TP', name: '东帝汶' }, { code: 'TR', name: '土耳其' }, { code: 'TT', name: '特立尼达和多巴哥' }, { code: 'TV', name: '图瓦卢' }, { code: 'TW', name: '中国台湾' }, { code: 'TZ', name: '坦桑尼亚' }, { code: 'UA', name: '乌克兰' }, { code: 'UG', name: '乌干达' }, { code: 'UM', name: '美国本土外小岛屿' }, { code: 'US', name: '美国' }, { code: 'UY', name: '乌拉圭' }, { code: 'UZ', name: '乌兹别克斯坦' }, { code: 'VA', name: '梵蒂冈' }, { code: 'VC', name: '圣文森特和格林纳丁斯' }, { code: 'VE', name: '委内瑞拉' }, { code: 'VG', name: '托尔托拉岛(英属处女群岛)' }, { code: 'VI', name: '美属维尔京群岛' }, { code: 'VN', name: '越南' }, { code: 'VU', name: '瓦努阿图' }, { code: 'WF', name: '瓦利斯群岛和富图纳群岛' }, { code: 'WS', name: '西萨摩亚' }, { code: 'XA', name: '加那利群岛' }, { code: 'XB', name: '特里斯坦-达库尼亚岛' }, { code: 'XC', name: '海峡群岛' }, { code: 'XD', name: '阿森松' }, { code: 'XE', name: '加沙及汗尤尼斯' }, { code: 'XF', name: '科西嘉岛' }, { code: 'XG', name: '北非西班牙属土' }, { code: 'XH', name: '亚速尔' }, { code: 'XI', name: '马德拉' }, { code: 'XJ', name: '巴利阿里群岛' }, { code: 'XK', name: '加罗林群岛(帕劳)' }, { code: 'XL', name: '新西兰属土岛屿(库克群岛)' }, { code: 'XM', name: '威克岛' }, { code: 'XN', name: '尼维斯' }, { code: 'XO', name: '科索沃' }, { code: 'XS', name: '索马里兰' }, { code: 'XY', name: '圣巴特勒米岛' }, { code: 'YE', name: '也门' }, { code: 'YT', name: '马约特' }, { code: 'YU', name: '南斯拉夫' }, { code: 'ZA', name: '南非' }, { code: 'ZM', name: '赞比亚' }, { code: 'ZW', name: '津巴布韦' } ]
      // activities: []
    }
  },
  filters: {
    formatterCodeName: (value, list) => {
      let name = ''
      if (!list) {
        console.warn('formatterName filtes must be a list in the parameter')
        return
      }
      list.forEach(object => {
        if (object.code === value) {
          name = object.name
          return false
        }
      })
      return name
    },
    formatterStatus: (value, list) => {
      let name = ''
      if (!list) {
        console.warn('formatterName filtes must be a list in the parameter')
        return
      }
      list.forEach(object => {
        if (object.dictValue === value) {
          name = object.dictName
          return false
        }
      })
      return name
    }
  },
  created () {
    this.params = this.$route.query
    this.nos = this.params['nos']
    this.companyId = this.params['companyId']
    this.setNo()
    this.getTrackInfo()
  },
  methods: {
    getActivities (val) {
      let activities = []
      // 加载明细
      if (!val || !val.events) {
        return activities
      } else {
        for (var i = 0; i < val.events.length; i++) {
          let data
          // 轨迹时间
          let trackingTime = val.events[i].time
          // 轨迹内容
          let content = val.events[i].location ? `[${val.events[i].location}] ${val.events[i].content}` : `${val.events[i].content}`
          switch (i) {
            case 0 :
              data = {
                content: content,
                timestamp: trackingTime,
                size: 'large',
                type: 'primary',
                icon: 'el-icon-more'
              }
              break
            case 1 :
              data = {
                content: content,
                timestamp: trackingTime,
                color: '#0bbd87'
              }
              break
            case 2 :
              data = {
                content: content,
                timestamp: trackingTime,
                size: 'large'
              }
              break
            default :
              data = {
                content: content,
                timestamp: trackingTime
              }
              break
          }
          activities.push(data)
        }
      }
      return activities
    },
    // 获取轨迹信息
    getTrackInfo () {
      this.loadingFlag = true
      this.trackActivating = true
      if (!this.nos) {
        this.loadingFlag = false
        this.result.normalCount = 0
        this.result.abnormalCount = 0
        this.result.list = []
        this.curItem = ''
        return
      }
      if (this.nos.split(',').length > this.noSizeInner) {
        this.$message.warning(this.$t('threeNoInput.outMessage', { 'size': `${this.noSizeInner}` }))
        this.loadingFlag = false
        return
      }
      this.$http.get('/guest/queryTrack', { params: { nos: this.nos, companyId: this.companyId } }).then(({ data: res }) => {
        if (res.code === 0 && res.data) {
          if (Array.isArray(res.data)) {
            this.result.list = res.data
          } else {
            this.result.list = [ res.data ]
          }
          this.result.normalCount = this.result.list.length + ' 条数据'
          // this.result.abnormalCount = res.data.abnormalCount
        } else {
          this.result.list = []
          this.result.normalCount = res.msg
        }
        this.loadingFlag = false
      }).catch((e) => {
        console.log(e)
      })
    },
    // 将单号转换为,隔开的格式
    setNo () {
      if (!this.nos) {
        return
      }
      this.nos = this.nos.replace(/\n|\s+/g, ',').trim()
    }
  }
}
</script>

<style lang="scss">
  .tksTrack-panel{
    .timeline-box{
      margin-left: 102px;
    }
    .el-timeline-item__content{
      padding-top: 4px;
    }
    .el-timeline-item__timestamp {
      transform: translate(-170px, -22px);
    }
  }
</style>
