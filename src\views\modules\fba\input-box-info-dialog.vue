<template>
  <el-dialog :visible.sync="visible" :title="$t('fba.enterBoxInfo')" :close-on-click-modal="false" :modal-append-to-body="false" width='80%' top="5px"
             :close-on-press-escape="false" :show-close='false' :lock-scroll="true" class="location_model" >
    <div>
      <el-row>
        <el-col :span='8'>
          <span>{{ $t('coOrder.customerOrderNo') }}：<span class='masterColor'>{{customerOrderNo}}</span></span>
        </el-col>
        <el-col :span='16' class='text-right' >
          <span class='margin_right15'>{{ $t('fba.totalBox') }}：{{getTotalBoxCount}}</span>
          <span class='margin_right15'>{{ $t('fba.totalWeight') }}：{{getTotalWeightD }}</span>
        </el-col>
      </el-row>
      <el-divider></el-divider>
      <el-row>
        <el-col :span='13' :offset='9'>
          <span>以下表格可以像excel表格一样使用</span>
          <el-popover
            placement="top-start"
            :title="$t('description')"
            width="500"
            trigger="hover">
            <div class="popover-content">1、可以复制、粘贴、删除、新增行或者单元格</div>
            <div class="popover-content">2、SHIFT+鼠标左键可以选中多行或者多列数据,并可以使用Delete键清除选中的数据,或者点击鼠标右键删除行</div>
            <div class="popover-content">3、从Excel表格或者该表格中选中多行、多列数据进行复制、粘贴</div>
            <div class="popover-content">4、在表格中点击鼠标右键,可以进行更多的操作</div>
            <div class="popover-content">5、快捷键：CTRL+C 复制, CTRL+V 粘贴, CTRL+Z 复原</div>
            <el-button type="text" style="margin-left: 5px;font-size: 15px;color: orange" icon="el-icon-question" slot="reference"></el-button>
          </el-popover>
        </el-col>
        <el-col :span="2" class="text-right">
          <el-button type='primary' size='mini' plain @click='clearTable'>{{ $t('fba.clear') }}</el-button>
        </el-col>
      </el-row>
      <div class="flex_table" ref="tableElm" v-domResize="redraw" id='boxDataFormId'>
        <hot-table :data="boxDataList" :settings="hotSettings"
                   :stretchH="'all'" ref="hotTable" height="300px" class='margin_top10'>
<!--          <hot-column data="packageSerialNo" :title="$t('fba.serialNo')" type="text" :read-only='true' :renderer='indexRenderer'></hot-column>-->
              <hot-column v-for="(item) in tableColumns" :key="item.colIndex"
                          :data="item.prop" :title="item.label"  type="text" :width="item.width"
                          :validator='(value, callback) => validateData(value, callback, item.prop)' />
          <!--  <hot-column :title="$t('handle')" :type="'text'"  :renderer='handleRenderer'></hot-column>-->
        </hot-table>
        <el-button class='el-icon-plus width100 margin_top10' size='mini' type='primary' plain @click='addRow()'>{{ $t('add') }}</el-button>
      </div>
    </div>
    <template slot="footer">
      <div style='justify-content: center;display: flex;'>
        <el-button @click='closeHandle'>{{ $t('close') }}</el-button>
        <el-button type='primary' :loading='submitLoading' @click='handleSubmit()'>{{ $t('save') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import areaBox from '@/components/areaBox'
import listPage from '@/mixins/listPage'
import NP from 'number-precision'
import { HotTable, HotColumn } from '@handsontable/vue'
import 'handsontable/dist/handsontable.full.css'
import { registerAllModules } from 'handsontable/registry'
import { isDecimal3, isDecimal1, isOverLength, isPlusFloat, isPlusInteger2 } from '@/utils/validate'
import { isEqual } from 'lodash'
// import Sheetclip from 'sheetclip'

registerAllModules()
export default {
  mixins: [listPage],
  data () {
    return {
      visible: false,
      submitLoading: false,
      customerOrderNo: null,
      clipboardCache: '',
      orderId: '',
      boxDataList: [],
      tableColumns: [
        { colIndex: 0, prop: 'packageWeightD', label: `<span style='color:red;'>*</span>` + this.$t('fba.weight'), width: '100px', isShow: true },
        { colIndex: 1, prop: 'packageLengthD', label: `<span style='color:red;'>*</span>` + this.$t('fba.length'), width: '100px', isShow: true },
        { colIndex: 2, prop: 'packageWidthD', label: `<span style='color:red;'>*</span>` + this.$t('fba.width'), width: '100px', isShow: true },
        { colIndex: 3, prop: 'packageHeightD', label: `<span style='color:red;'>*</span>` + this.$t('fba.height'), width: '100px', isShow: true },
        { colIndex: 4, prop: 'packageQty', label: `<span style='color:red;'>*</span>` + this.$t('fba.boxCount'), width: '100px', isShow: true },
        { colIndex: 5, prop: 'packageCustomerNo', label: this.$t('fba.boxNoOrMarkNo'), width: '200px', isShow: true },
        { colIndex: 6, prop: 'subCustomerOrderNo', label: this.$t('fba.subCustomerOrderNo'), width: '200px', isShow: true },
        { colIndex: 7, prop: 'packageDeliveryNo', label: this.$t('fba.waybillNo'), width: '200px', isShow: true }
      ]
    }
  },
  computed: {
    getTotalBoxCount () {
      let totalBoxCount = 0
      for (let item of this.boxDataList) {
        totalBoxCount = NP.plus(totalBoxCount, Number.parseFloat(item.packageQty) || 0)
      }
      return totalBoxCount
    },
    getTotalWeightD () {
      let totalWeightD = 0
      for (let item of this.boxDataList) {
        let weight = NP.times(Number.parseFloat(item.packageQty) || 1, Number.parseFloat(item.packageWeightD) || 0)
        totalWeightD = NP.plus(totalWeightD, weight)
      }
      return this.$naturalNumberFormat(totalWeightD)
    },
    boxRule () {
      const everyItemIsLength36 = (rule, value, callback) => {
        if (value) {
          let split = value.split(/[,，]/) || []
          for (const item of split) {
            if (!isOverLength(item, 36)) {
              return new Error(this.$t('validate.whoIsOverLength', { who: item, max: 36 }))
            }
          }
        }
        callback && callback.call(true)
      }
      const isInt = (rule, value, callback) => {
        if (!value) {
          return callback && callback.call(true)
        }
        if (value && !(isPlusInteger2(value))) {
          return new Error('请输入大于0的整数')
        }
        if (value <= 0) {
          return new Error('请输入大于0的整数')
        }
        callback && callback.call(true)
      }
      const isFloat1 = (rule, value, callback) => {
        if (value && !(isPlusFloat(value) || isPlusInteger2(value))) {
          return new Error('请输入大于0的小数或整数')
        }
        if (value <= 0) {
          return new Error('请输入大于0的小数或整数')
        }
        if (value && !isDecimal1(value)) {
          return new Error('仅保留1位小数')
        }
        callback && callback.call(true)
      }
      const isFloat3 = (rule, value, callback) => {
        if (value && !(isPlusFloat(value) || isPlusInteger2(value))) {
          return new Error('请输入大于0的小数或整数')
        }
        if (value <= 0) {
          return new Error('请输入大于0的小数或整数')
        }
        if (value && !isDecimal3(value)) {
          return new Error('仅保留3位小数')
        }
        callback && callback.call(true)
      }
      const repeatBoxNo = (rule, value, callback) => {
        if (value && this.boxDataList && this.boxDataList.length > 0) {
          let valueArr = value.split(/[,，]/) || []
          let packageCustomerNoArr = this.boxDataList.map(item => {
            if (!item.packageCustomerNo) {
              return []
            }
            let itemArr = item.packageCustomerNo ? (item.packageCustomerNo.split(/[,，]/) || []) : []
            if (itemArr.some(itemValue => valueArr.includes(itemValue))) {
              return itemArr
            } else {
              return []
            }
          }).filter(item => item && item.length > 0).reduce((acc, curr) => acc.concat(curr), [])
          console.log('packageCustomerNoArr', packageCustomerNoArr)

          let packageCustomerNoSet = new Set(this.boxDataList.map(item => {
            if (!item.packageCustomerNo) {
              return []
            }
            let itemArr = item.packageCustomerNo ? (item.packageCustomerNo.split(/[,，]/) || []) : []
            if (itemArr.some(itemValue => valueArr.includes(itemValue))) {
              return itemArr
            }
          }).filter(item => item && item.length > 0).reduce((acc, curr) => acc.concat(curr), []))
          console.log('packageCustomerNoSet', packageCustomerNoSet)

          if (packageCustomerNoSet.size !== packageCustomerNoArr.length) {
            return new Error('箱号不能重复')
          }
          if (!isEqual([...packageCustomerNoSet], packageCustomerNoArr)) {
            return new Error('箱号不能重复')
          }
        }

        callback && callback.call(true)
      }
      // 判断箱数与箱号是否一致
      const boxNoAndQtySame = (rule, value, callback) => {
        console.log('rule', rule)
        if (this.boxDataList && this.boxDataList.length > 0) {
          let needSameQty = this.boxDataList.some(item => {
            if (item.packageCustomerNo) {
              return true
            }
          })
          if (needSameQty && !value) {
            console.log('needSameQty && !value')
            return new Error('箱号数量要与箱数一致')
          }
          if (needSameQty && value) {
            let notSameQtyResultArr = this.boxDataList.map(item => {
              if (item.packageCustomerNo === value) {
                let tempNoArr = item.packageCustomerNo ? (item.packageCustomerNo.split(/[,，]/) || []) : []
                let same = Number(item.packageQty) === tempNoArr.length
                console.log(' item.packageQty', item.packageQty)
                console.log('  tempNoArr', tempNoArr)
                console.log('  tempNoArr.length', tempNoArr.length)
                if (!same) {
                  return 'F'
                }
              }
            }).filter(item => item && item === 'F')
            console.log('notSameQtyResultArr', notSameQtyResultArr)
            if (notSameQtyResultArr && notSameQtyResultArr.length > 0) {
              return new Error('箱号数量要与箱数不一致')
            }
          }
        }

        callback && callback.call(true)
      }

      return {
        packageDeliveryNo: [
          { validator: everyItemIsLength36, trigger: 'blur' }
        ],
        subCustomerOrderNo: [
          { validator: everyItemIsLength36, trigger: 'blur' }
        ],
        packageCustomerNo: [
          { validator: everyItemIsLength36, trigger: 'blur' },
          { validator: repeatBoxNo, trigger: ['change', 'blur'] },
          { validator: boxNoAndQtySame, trigger: ['change', 'blur'] }
        ],
        packageLengthD: [
          { required: true, message: this.$t('validate.required'), trigger: 'change' },
          { validator: isFloat1, trigger: 'blur' }
        ],
        packageQty: [
          { required: true, message: this.$t('validate.required'), trigger: 'change' },
          { validator: isInt, trigger: 'blur' }
        ],
        packageWidthD: [
          { required: true, message: this.$t('validate.required'), trigger: 'change' },
          { validator: isFloat1, trigger: 'blur' }
        ],
        packageHeightD: [
          { required: true, message: this.$t('validate.required'), trigger: 'change' },
          { validator: isFloat1, trigger: 'blur' }
        ],
        packageWeightD: [
          { required: true, message: this.$t('validate.required'), trigger: 'change' },
          { validator: isFloat3, trigger: 'blur' }
        ]
      }
    },
    hotSettings() {
      return {
        width: '99.9%',
        readOnly: false,
        copyPaste: true,
        contextMenuCopyPaste: true, // 允许复制粘贴
        rowHeaders: true,
        autoColumnSize: false,
        contextMenu: {
          items: {
            'row_above': {
              name: '上方插入一行'
            },
            'row_below': {
              name: '下方插入一行'
            },
            'hsep1': '---------', // 提供分隔线
            'remove_row': {
              name: '删除行'
            },
            'copy': {
              name: '复制(Ctrl+C)'
            },
            'paste': {
              name: '粘贴,请使用快捷键(Ctrl+V)操作',
              disabled: () => {
                return true
              },
              callback: (key, selection, clickEvent) => {
                let hotInstance = this.$refs.hotTable.hotInstance
                let plugin = hotInstance.getPlugin('copyPaste')
                hotInstance.listen()
                console.log('this.clipboardCache', this.clipboardCache)
                plugin.paste(this.clipboardCache)
              }
            },
            'cut': {
              name: '剪切'
            },
            'hsep2': '---------'
          }
        },
        allowInsertColumn: false, // 允许插入列
        manualColumnFreeze: true, // 手动固定列
        manualColumnMove: true, // 手动移动列
        manualRowMove: true, // 手动移动行
        manualColumnResize: true, // 手工更改列距
        manualRowResize: true, // 手动更改行距
        comments: true, // 添加注释
        afterCreateRow: this.afterCreateRowHandler,
        // afterCopy: this.afterCopyHandler,
        // beforeCreateRow: this.beforeCreateRowHandler,
        licenseKey: 'non-commercial-and-evaluation'
      }
    }
  },
  methods: {
    init (customerOrderNo, boxDataList, orderId) {
      this.visible = true
      this.submitLoading = false
      this.customerOrderNo = customerOrderNo
      this.orderId = orderId
      if (boxDataList && boxDataList.length > 0) {
        this.boxDataList = boxDataList
      } else {
        this.addRow(1)
      }
    },
    // 清空表格数据
    clearTable () {
      this.boxDataList = []
      this.$nextTick(() => {
        if (this.$refs['hotTable'] && this.$refs['hotTable'].hotInstance) {
          this.$refs['hotTable'].hotInstance.updateData(this.boxDataList)
        }
      })
      this.addRow(1)
    },
    // 序号渲染
    indexRenderer(instance, td, row, col, prop, value, cellProperties) {
      td.innerHTML = row + 1
      return td
    },
    // 操作列渲染
    handleRenderer(instance, td, row, col, prop, value, cellProperties) {
      td.innerHTML = `
      <a href="#" class="htCore" onclick="event.preventDefault(); this.$root.copyRow(${row})">${this.$t('copy')}</a>
      <popconfirm i18nOperateValue="delete" @clickHandle="deleteRow(${row})">${this.$t('delete')}</popconfirm>
    `
      return td
    },
    afterCopyHandler (changes) {
      // const clip = new Sheetclip()
      // this.clipboardCache = clip.stringify(changes)
    },
    afterCreateRowHandler(index, amount, source) {
      console.log('afterCreateRowHandler index', index)
      console.log('afterCreateRowHandler amount', amount)
      console.log('afterCreateRowHandler source', source)
      // 向上插入新行
      if (source === 'ContextMenu.rowAbove') {
        console.log('向上插入新行')
        for (let i = 0; i < amount; i++) {
          index--
          if (index < 0) {
            index = 0
          }
          let data = this.boxDataList[index]
          console.log('data', data, index)
          if (data) {
            delete data['id']
            delete data['allSerialNo']
            delete data['packageSerialNo']
            delete data['curAllPackageIdSerialNoBoxNoMap']
          }
        }
      } else if (source === 'ContextMenu.rowBelow' || source === 'CopyPaste.paste') {
        for (let i = 0; i < amount; i++) {
          let data = this.boxDataList[index]
          console.log('data', data, index)
          if (data) {
            delete data['id']
            delete data['allSerialNo']
            delete data['packageSerialNo']
            delete data['curAllPackageIdSerialNoBoxNoMap']
          }
          index++
        }
      }
      return false
    },
    // 干扰到CopyPaste.pastel，留备，改用afterCreateRowHandler
    beforeCreateRowHandler(index, amount, source) {
      console.log('beforeCreateRowHandler index', index)
      console.log('beforeCreateRowHandler amount', amount)
      console.log('beforeCreateRowHandler source', source)
      // const newRow = {}
      // // 遍历需要保留的属性，将其复制到新行中
      // for (const prop in this.boxDataList[index - 1]) {
      //   newRow[prop] = ''
      // }
      // delete newRow['id']
      // delete newRow['allSerialNo']
      // delete newRow['packageSerialNo']
      // delete newRow['curAllPackageIdSerialNoBoxNoMap']
      // 向上插入新行
      // if (source === 'ContextMenu.rowAbove') {
      //   console.log('向上插入新行')
      //   this.boxDataList.splice(index, 0, newRow)
      // } else if (source === 'ContextMenu.rowBelow') {
      //   console.log('向下插入新行')
      //   this.boxDataList.splice(index + 1, 0, newRow)
      // } else
      if (source === 'CopyPaste.paste') {
        if (amount === 1) {
          return false
        }
      }
      // return false 阻止handsontable默认创建行行为
      return true
    },
    // excel表格数据校验
    validateData (value, callback, prop, popupError = true) {
      let rules = this.boxRule[prop]
      let _callback = callback
      for (let i = 0; i < rules.length; i++) {
        let rule = rules[i]
        if (rule.required && !value) {
          _callback(false)
          if (popupError) {
            this.$parent.$message.error(rule.message)
          }
          return false
        }
        if (rule.validator) {
          let error = rule.validator(rule, value)
          if (error) {
            _callback(false)
            if (popupError) {
              this.$parent.$message.error(error.message)
            }
            return false
          }
        }
      }
      _callback(true)
      return true
    },
    checkBoxNo (boxNo) {
      return new Promise(resolve => {
        if (!boxNo || boxNo.length <= 0) {
          resolve(null)
        } else {
          this.$http.get(`/co/orderpackage/list?packageCustomerNos=` + boxNo).then(({ data: res }) => {
            let boxNos = [...new Set(res.data.map(item => {
              if (item.orderId !== this.orderId) {
                return item.packageCustomerNo
              }
            }))]
            boxNos = boxNos.filter(item => item !== undefined)
            resolve(boxNos)
          })
        }
      })
    },
    addRow (row = 1) {
      for (let i = 0; i < row; i++) {
        let obj = {
          packageDeliveryNo: '',
          subCustomerOrderNo: '',
          packageCustomerNo: '',
          packageQty: '1',
          packageLengthD: '',
          packageWidthD: '',
          packageHeightD: '',
          packageWeightD: ''
        }
        this.boxDataList.push(obj)
      }
      this.$nextTick(() => {
        if (this.$refs['hotTable'] && this.$refs['hotTable'].hotInstance) {
          this.$refs['hotTable'].hotInstance.updateData(this.boxDataList)
        }
      })
    },
    copyRow (row) {
      let obj = {
        id: null,
        packageCustomerNo: '',
        ...row
      }
      this.boxDataList.push(obj)
    },
    deleteRow (index) {
      this.boxDataList.splice(index, 1)
    },
    handleSubmit: debounce(async function() {
      this.submitLoading = true
      let isContinue = await this.validateCells()
      if (!isContinue) {
        return
      }

      let boxNos = this.boxDataList.map(item => item.packageCustomerNo).filter(no => no !== undefined && no !== '' && no !== null)
      console.log('boxNos', boxNos)
      console.log('boxNos.length', boxNos.length)
      await this.checkBoxNo(boxNos).then(repeatBoxNos => {
        if (repeatBoxNos && repeatBoxNos.length > 0) {
          return this.$message({
            message: '箱号/FBA唛头：' + repeatBoxNos + '已被使用',
            type: 'warning',
            duration: 2000,
            onClose: () => {
              this.submitLoading = false
            }
          })
        }
        let totalWeightD = 0
        let totalBoxCount = 0
        let boxNoList = []
        for (let item of this.boxDataList) {
          totalWeightD = NP.plus(totalWeightD, NP.times(Number.parseFloat(item.packageQty || 1), Number.parseFloat(item.packageWeightD || 0)))
          totalBoxCount = NP.plus(totalBoxCount, Number.parseFloat(item.packageQty))
          let split = item.packageCustomerNo ? (item.packageCustomerNo.split(/[,，]/) || []) : []
          for (const i of split) {
            if (i) {
              boxNoList.push(i)
            }
          }
          if (item.curAllPackageIdSerialNoBoxNoMap) {
            const entries = Object.entries(item.curAllPackageIdSerialNoBoxNoMap)
            for (let i = 0; i < entries.length; i++) {
              const [, serialNoBoxNoMap] = entries[i]
              const [serialNo] = Object.keys(serialNoBoxNoMap)
              serialNoBoxNoMap[serialNo] = split[i] || ''
            }
          }
        }
        console.log('inputBoxInfoEmit boxNoList', this.boxDataList)
        this.$emit('inputBoxInfoEmit', this.boxDataList, this.$naturalNumberFormat(totalWeightD), totalBoxCount, boxNoList)
        this.closeHandle()
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    closeHandle () {
      this.visible = false
      this.$emit('closeInputBoxInfo')
    },
    // 校验excel表格
    async validateCells() {
      let errors = []
      const hotInstance = this.$refs['hotTable'].hotInstance
      const totalColNum = hotInstance.countCols()
      const cellPromises = []
      for (let rowNum = 0; rowNum < this.boxDataList.length; rowNum++) {
        for (let colNum = 0; colNum < totalColNum; colNum++) {
          let cell = hotInstance.getCellMeta(rowNum, colNum)
          if (hotInstance.getCellValidator(cell)) {
            let oldPrototype = Object.getPrototypeOf(cell)
            let newPrototype = Object.create(oldPrototype)
            // 通过临时改写原型方法来传参数popupError来抑制所有this.$message.error
            newPrototype.validator = (value, callback) => this.validateData(value, callback, cell.prop, false)
            Object.setPrototypeOf(cell, newPrototype)
            cellPromises.push(new Promise((resolve, reject) => {
              hotInstance.validateCell(hotInstance.getDataAtCell(rowNum, colNum), cell, (valid) => {
                if (!valid) {
                  errors.push({ prop: cell.prop, row: rowNum, column: colNum, errMsg: undefined })
                }
                Object.setPrototypeOf(cell, oldPrototype)
                resolve()
              })
            }))
          }
        }
      }
      await Promise.all(cellPromises)
      if (errors.length > 0) {
        this.$message({
          message: '请把箱信息填写完整',
          type: 'warning',
          duration: 2000,
          onClose: () => {
            this.submitLoading = false
          }
        })
        return false
      }
      return true
    }
  },
  components: {
    areaBox,
    HotColumn,
    HotTable
  }
}
</script>

<style lang='scss' scoped>
#areabox ::v-deep .el-form-item {
  padding: 0px 15px !important;
}
#boxDataFormId{
  ::v-deep .el-form-item {
    margin-bottom: 0!important;
  }
}
::v-deep .el-dialog__body {
  padding: 0px 20px !important;
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}
/deep/ .ht_clone_top_inline_start_corner.ht_clone_top_left_corner.handsontable .colHeader:first-child::before {
  content: "行号" !important;
}
/deep/ .el-checkbox__label {
  display: inline-block;
  padding-left: 10px;
  line-height: 19px;
  font-size: 14px;
  padding-top: 7px !important;
}
.masterColor {
  color: $--color-primary;
  font-weight: bold;
}
</style>
<style lang='scss'>
.htContextMenu:not(.htGhostTable) {
  display: none;
  position: absolute;
  z-index: 9999 !important;
}
</style>
