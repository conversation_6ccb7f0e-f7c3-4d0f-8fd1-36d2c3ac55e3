import Cookies from 'js-cookie'
import store from '@/store'

/**
 * 权限
 * @param {*} key
 */
export function hasPermission (key) {
  return window.SITE_CONFIG['permissions'].indexOf(key) !== -1 || false
}

/**
 * 清除登录信息
 */
export function clearLoginInfo () {
  store.commit('resetStore')
  Cookies.remove('cs_token')
  window.SITE_CONFIG['dynamicMenuRoutesHasAdded'] = false
}

/**
 * 获取uuid
 */
export function getUUID () {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
    return (c === 'x' ? (Math.random() * 16 | 0) : ('r&0x3' | '0x8')).toString(16)
  })
}

/**
 * 根据长度生成UUID
 * @param len UUID长度
 * @return {string}
 */
export function getUUIDOfLength(len) {
  let radix = 16 // 16进制
  const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('')
  let uuid = []
  let i
  radix = radix || chars.length
  if (len) {
    for (i = 0; i < len; i++) {
      uuid[i] = chars[0 | Math.random() * radix]
    }
  } else {
    let r
    uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-'
    uuid[14] = '4'
    for (i = 0; i < 36; i++) {
      if (!uuid[i]) {
        r = 0 | Math.random() * 16
        uuid[i] = chars[(i === 19) ? (r & 0x3) | 0x8 : r]
      }
    }
  }
  return uuid.join('').toUpperCase()
}

/**
 * 获取svg图标(id)列表
 */
export function getIconList () {
  var res = []
  document.querySelectorAll('svg symbol').forEach(item => {
    res.push(item.id)
  })
  return res
}

/**
 * 树形数据转换
 * @param {*} data
 * @param {*} id
 * @param {*} pid
 */
export function treeDataTranslate (data, id = 'id', pid = 'pid') {
  var res = []
  var temp = {}
  for (var i = 0; i < data.length; i++) {
    temp[data[i][id]] = data[i]
  }
  for (var k = 0; k < data.length; k++) {
    if (!temp[data[k][pid]] || data[k][id] === data[k][pid]) {
      res.push(data[k])
      continue
    }
    if (!temp[data[k][pid]]['children']) {
      temp[data[k][pid]]['children'] = []
    }
    temp[data[k][pid]]['children'].push(data[k])
    data[k]['_level'] = (temp[data[k][pid]]._level || 0) + 1
  }
  return res
}
/**
 * 详情页底部按钮位置
 * @param {*} data
 */
export function footerScroll () {
  let scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
  // 变量windowHeight是可视区的高度
  let windowHeight = document.documentElement.clientHeight || document.body.clientHeight
  // 变量scrollHeight是滚动条的总高度
  let scrollHeight = document.documentElement.scrollHeight || document.body.scrollHeight
  let formFooter = document.querySelector('#cs_FormFooter')
  if (!formFooter) {
    return
  }
  if (document.documentElement.offsetHeight > windowHeight) {
    formFooter.style.bottom = 0
  }
  if (scrollTop === scrollHeight - windowHeight) {
    formFooter.style.bottom = '16px'
    formFooter.style.bottom = '16px'
  } else {
    formFooter.style.bottom = 0
  }
}

export function naturalNumberFormat (num) {
  num = num.toString()
  if (num.indexOf('+') !== -1) {
    num = num.replace('+', '')
  }
  if (num.indexOf('E') !== -1 || num.indexOf('e') !== -1) {
    let resValue = ''
    let power = ''
    let result = null
    let dotIndex = 0
    let resArr = []
    let sym = ''
    let numStr = num.toString()
    if (numStr[0] === '-') {
      // 如果为负数，转成正数处理，先去掉‘-’号，并保存‘-’.
      numStr = numStr.substr(1)
      sym = '-'
    }
    if (numStr.indexOf('E') !== -1 || numStr.indexOf('e') !== -1) {
      let regExp = new RegExp(
        '^(((\\d+.?\\d+)|(\\d+))[Ee]{1}((-(\\d+))|(\\d+)))$',
        'ig'
      )
      result = regExp.exec(numStr)
      if (result != null) {
        resValue = result[2]
        power = result[5]
        result = null
      }
      if (!resValue && !power) {
        return false
      }
      dotIndex = resValue.indexOf('.') === -1 ? 0 : resValue.indexOf('.')
      resValue = resValue.replace('.', '')
      resArr = resValue.split('')
      if (Number(power) >= 0) {
        let subres = resValue.substr(dotIndex)
        power = Number(power)
        // 幂数大于小数点后面的数字位数时，后面加0
        for (let i = 0; i <= power - subres.length; i++) {
          resArr.push('0')
        }
        if (power - subres.length < 0) {
          resArr.splice(dotIndex + power, 0, '.')
        }
      } else {
        power = power.replace('-', '')
        power = Number(power)
        // 幂数大于等于 小数点的index位置, 前面加0
        for (let i = 0; i < power - dotIndex; i++) {
          resArr.unshift('0')
        }
        let n = power - dotIndex >= 0 ? 1 : -(power - dotIndex)
        resArr.splice(n, 0, '.')
      }
    }
    resValue = resArr.join('')
    return sym + resValue
  } else {
    return num
  }
}
