<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-sys__user flex_wrap">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="queryPageByParam()">
        <el-form-item>
          <el-input v-model="dataForm.username" :placeholder="$t('user.username')" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="queryPageByParam()"  type="primary" plain>{{ $t('query') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('cs:user:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('cs:user:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('cs:user:export')" type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
      </el-form>
      <div class="flex_1" ref="tableElm" v-domResize="redraw">
      <el-table
        v-loading="dataListLoading"
        :data="dataList"
        @selection-change="dataListSelectionChangeHandle"
        @sort-change="dataListSortChangeHandle"
        :max-height="tableHeight">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="username" :label="$t('user.username')" sortable="custom" header-align="center" align="center"></el-table-column>
        <!-- <el-table-column prop="deptId" :label="$t('user.companyName')" header-align="center" align="center">
          <template slot-scope="scope">
            {{ (deptList.filter(item => item.id === scope.row.deptId)[0] || {}).name }}
          </template>
        </el-table-column> -->
        <el-table-column prop="roleIdList" :label="$t('user.roleIdList')" header-align="center" align="center">
          <template slot-scope="scope">
            <el-tag size="small" v-for="v in scope.row.roleIdList" :key="v">{{ (roleList.filter(item => item.id === v)[0] || {}).name }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="email" :label="$t('user.email')" header-align="center" align="center"></el-table-column>
        <el-table-column prop="mobile" :label="$t('user.mobile')" sortable="custom" header-align="center" align="center"></el-table-column>
        <el-table-column prop="status" :label="$t('user.status')" sortable="custom" header-align="center" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status === 0" size="small" type="danger">{{ $t('user.status0') }}</el-tag>
            <el-tag v-else size="small" type="success">{{ $t('user.status1') }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createDate" :label="$t('user.createDate')" sortable="custom" header-align="center" align="center" width="180">
          <template slot-scope="scope">
            <span>{{scope.row.createDate | gtmToLtm}}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-button v-if="$hasPermission('cs:user:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
            <el-button v-if="$hasPermission('cs:user:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      </div>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import listPage from '@/mixins/listPage'
import AddOrUpdate from './user-add-or-update'
import { gtmToLtm } from '@/filters/filters'
export default {
  mixins: [listPage, mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/cs/user/page',
        getDataListIsPage: true,
        deleteURL: '/cs/user',
        deleteIsBatch: true,
        exportURL: '/cs/user/export'
      },
      dataForm: {
        username: ''
      },
      deptList: [],
      roleList: []
    }
  },
  methods: {
    // 获取部门列表
    // getDeptList () {
    //   return this.$http.get('/cs/dept/list').then(({ data: res }) => {
    //     if (res.code !== 0) {
    //       return this.$message.error(res.msg)
    //     }
    //     this.deptList = res.data
    //   }).catch(() => {})
    // },
    // 获取角色列表
    getRoleList () {
      return this.$http.get('/cs/role/list').then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.roleList = res.data
      }).catch(() => {})
    }
  },
  activated () {
    // this.getDeptList()
    this.getRoleList()
  },
  components: {
    AddOrUpdate
  },
  filters: {
    gtmToLtm
  }
}
</script>
