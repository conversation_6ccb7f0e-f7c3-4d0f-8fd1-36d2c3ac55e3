<template>
  <div class="add-body panel_body">
    <div class="panel-hd">{{!dataForm.id ? $t('add') : $t('update')}}</div>
    <div class="addOrUpdatePanel" >
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" label-width="80px">
        <el-row type="flex" justify="start">
          <el-col :md="{span:13, offset: 2}">
            <el-form-item :label="$t('coOrderDeclare.orderId')" prop="orderId">
              <el-input v-model="dataForm.orderId" :placeholder="$t('coOrderDeclare.orderId')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderDeclare.chineseName')" prop="chineseName">
              <el-input v-model="dataForm.chineseName" :placeholder="$t('coOrderDeclare.chineseName')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderDeclare.englishName')" prop="englishName">
              <el-input v-model="dataForm.englishName" :placeholder="$t('coOrderDeclare.englishName')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderDeclare.quantity')" prop="quantity">
              <el-input v-model="dataForm.quantity" :placeholder="$t('coOrderDeclare.quantity')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderDeclare.unitNetWeight')" prop="unitNetWeight">
              <el-input v-model="dataForm.unitNetWeight" :placeholder="$t('coOrderDeclare.unitNetWeight')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderDeclare.unitDeclarePrice')" prop="unitDeclarePrice">
              <el-input v-model="dataForm.unitDeclarePrice" :placeholder="$t('coOrderDeclare.unitDeclarePrice')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderDeclare.brand')" prop="brand">
              <el-input v-model="dataForm.brand" :placeholder="$t('coOrderDeclare.brand')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderDeclare.goodsBarcode')" prop="goodsBarcode">
              <el-input v-model="dataForm.goodsBarcode" :placeholder="$t('coOrderDeclare.goodsBarcode')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderDeclare.sku')" prop="sku">
              <el-input v-model="dataForm.sku" :placeholder="$t('coOrderDeclare.sku')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderDeclare.hsCode')" prop="hsCode">
              <el-input v-model="dataForm.hsCode" :placeholder="$t('coOrderDeclare.hsCode')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderDeclare.productModel')" prop="productModel">
              <el-input v-model="dataForm.productModel" :placeholder="$t('coOrderDeclare.productModel')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderDeclare.material')" prop="material">
              <el-input v-model="dataForm.material" :placeholder="$t('coOrderDeclare.material')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderDeclare.purpose')" prop="purpose">
              <el-input v-model="dataForm.purpose" :placeholder="$t('coOrderDeclare.purpose')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderDeclare.origin')" prop="origin">
              <el-input v-model="dataForm.origin" :placeholder="$t('coOrderDeclare.origin')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderDeclare.pickingRemark')" prop="pickingRemark">
              <el-input v-model="dataForm.pickingRemark" :placeholder="$t('coOrderDeclare.pickingRemark')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrderDeclare.productUrl')" prop="productUrl">
              <el-input v-model="dataForm.productUrl" :placeholder="$t('coOrderDeclare.productUrl')"></el-input>
            </el-form-item>
          </el-col>

        </el-row>
      </el-form>
      <div id="cs_FormFooter" class="el-form-footer">
        <el-button @click="cancelFn">{{ $t('cancel') }}</el-button>
        <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      dataForm: {
        id: '',
        creator: '',
        createDate: '',
        orderId: '',
        chineseName: '',
        englishName: '',
        quantity: '',
        unitNetWeight: '',
        unitDeclarePrice: '',
        brand: '',
        goodsBarcode: '',
        sku: '',
        hsCode: '',
        productModel: '',
        material: '',
        purpose: '',
        origin: '',
        pickingRemark: '',
        productUrl: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        creator: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        createDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        orderId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        chineseName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        englishName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        quantity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        unitNetWeight: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        unitDeclarePrice: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        brand: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        goodsBarcode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        sku: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        hsCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        productModel: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        material: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        purpose: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        origin: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        pickingRemark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        productUrl: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/co/orderdeclare/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/co/orderdeclare/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    // 取消
    cancelFn () {
      this.visible = false
      this.$emit('cancelAddOrUpdate')
    }
  }
}
</script>
