<template>
  <el-dialog :visible.sync="visible" :title="'配置'" :close-on-click-modal="false" :close-on-press-escape="false" :append-to-body="true" >
    <el-form :model="dataForm" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" label-width="120px">
      <el-form-item
        v-for="(item, index) in dataForm.resourceList"
        :key="item.key"
        :prop="`resourceList.${index}`"
        :label="$t('fastMenu') + '_'+(index+1)"
        class="resource-list">
        <el-row >
          <el-col :span="20">
            <el-popover popper-class="menuPopper" v-model="menuListVisible[index]" @show="setIndex(index)" placement="bottom-start" trigger="click">
              <el-tree
                :data="menuList"
                :props="{ label: 'name', children: 'children' }"
                node-key="id"
                ref="menuListTree"
                :highlight-current="true"
                :expand-on-click-node="false"
                accordion
                @node-click="menuListTreeCurrentChangeHandle">
              </el-tree>
              <el-input slot="reference" v-model="item.name" :readonly="true" ></el-input>
            </el-popover>
          </el-col>
          <!-- <el-col :span="10">
            <el-input-number class="w-percent-100" v-model="dataForm.sort" controls-position="right" :min="0" :placeholder="$t('menu.sort')"></el-input-number>
          </el-col> -->
          <el-col :span="2" class="text-center">
            <el-button @click="resourceDeleteHandle(item)" size="small" type="text">{{ $t('delete') }}</el-button>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item :label="dataForm.resourceList.length <= 0 ? $t('fastMenu') : ''">
        <el-button size="mini" type="primary" @click="resourceAddHandle()" v-show="addBtnVisible">{{ $t('menu.resourceAddItem') }}</el-button>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import storage from 'good-storage'
import { getIconList } from '@/utils'
export default {
  data () {
    return {
      visible: false,
      menuList: [],
      menuListVisible: [false, false, false, false, false],
      iconList: [],
      iconListVisible: false,
      dataForm: {
        resourceList: []
      },
      addBtnVisible: true,
      menuIndex: 0
    }
  },
  computed: {
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.iconList = getIconList()
        this.dataForm.parentName = this.$t('menu.parentNameDefault')
        let topMenu = storage.get('topMenu')
        if (topMenu) {
          this.dataForm.resourceList = topMenu.resourceList
        } else {
          this.dataForm.resourceList = []
        }
        if (this.dataForm.resourceList.length >= 5) {
          this.addBtnVisible = false
        }
        this.getMenuList().then(() => {
          if (this.dataForm.id) {
            this.getInfo()
          }
        })
      })
    },
    // 获取菜单列表
    getMenuList () {
      return this.$http.get('/cs/menu/list?type=0').then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.menuList = res.data
      }).catch(() => {})
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/cs/menu/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
        if (this.dataForm.pid === '0') {
          return this.deptListTreeSetDefaultHandle()
        }
        this.$refs.menuListTree.setCurrentKey(this.dataForm.pid)
      }).catch(() => {})
    },
    // 上级菜单树, 设置默认值
    deptListTreeSetDefaultHandle () {
      this.dataForm.pid = '0'
      this.dataForm.parentName = this.$t('menu.parentNameDefault')
    },
    // 展开的第几个菜单
    setIndex (index) {
      this.menuIndex = index
    },
    // 上级菜单树, 选中
    menuListTreeCurrentChangeHandle (data) {
      let isDiff = this.dataForm.resourceList.findIndex((val) => {
        return val.id === data.id
      })
      if (isDiff !== -1) {
        this.$message({
          message: '不能添加相同的',
          type: 'warning'
        })
        return false
      }

      this.dataForm.resourceList.splice(this.menuIndex, 1, data)
      this.menuListVisible[this.menuIndex] = false
    },
    // 菜单资源, 添加
    resourceAddHandle () {
      this.dataForm.resourceList.push('')
      let len = this.dataForm.resourceList.length
      if (len >= 5) {
        this.addBtnVisible = false
      } else {
        this.addBtnVisible = true
      }
    },
    // 菜单资源, 删除
    resourceDeleteHandle (resource) {
      this.dataForm.resourceList = this.dataForm.resourceList.filter(item => item.id !== resource.id)
      this.addBtnVisible = true
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        storage.set('topMenu', this.dataForm)
        this.$store.state.topMenu = this.dataForm
        this.visible = false
        this.$emit('setTopMenu', this.dataForm)
        /*
        this.$http[!this.dataForm.id ? 'post' : 'put']('/cs/menu', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
        */
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>

<style lang="scss">
  .menuPopper{
    max-height: calc(100vh - 60vh);
    .el-tree{
      max-height: calc(100vh - 60vh - 24px);
      overflow: hidden;
      overflow-y: auto;
    }
  }
.mod-sys__menu {
  .resource-list {
    .el-select .el-input__inner {
      min-width: 110px;
      text-align: center;
    }
  }
  .menu-list,
  .icon-list {
    .el-input__inner,
    .el-input__suffix {
      cursor: pointer;
    }
  }
  &-icon-popover {
    width: 458px;
    overflow: hidden;
  }
  &-icon-inner {
    width: 478px;
    max-height: 258px;
    overflow-x: hidden;
    overflow-y: auto;
  }
  &-icon-list {
    width: 458px;
    padding: 0;
    margin: -8px 0 0 -8px;
    > .el-button {
      padding: 8px;
      margin: 8px 0 0 8px;
      > span {
        display: inline-block;
        vertical-align: middle;
        width: 18px;
        height: 18px;
        font-size: 18px;
      }
    }
  }
}
</style>
