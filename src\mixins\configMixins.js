// import { getTableConfig } from '@/utils/cache'
/**
 * 读取服务端Table配置
 * Created by <PERSON><PERSON> on 2019/7/12.
 */
export default {
  data () {
    return {
      configMixins: {
        isGetTableConfig: false, // 是否获取table配置
        tableName: ''
      }
    }
  },
  methods: {
    // 获取table配置,先成storage获取,再成服务端Redis获取
    getConfigTableList (callback) {
      let key = this.tableName + '-' + this.$store.state.user.id
      let setting = window.localStorage.getItem(key)
      if (setting) {
        setting = JSON.parse(setting)
        this.tableColumns = Object.assign({}, setting)
        return false
      } else {
        return this.$http.get('/user/tableConfig/' + this.tableName).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          callback && callback(res)
        }).catch(() => { })
      }
    }
  }
}
