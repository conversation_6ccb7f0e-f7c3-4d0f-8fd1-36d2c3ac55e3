<template>
  <div class="aui-card--fill">
    <div class="mod-co__inorder flex_wrap" v-show="!panelShow" ref="listPanel">
      <el-card class="search_box" shadow="never">
        <el-form ref="searchForm" class="form_no_margin" :model="dataForm" @keyup.enter.native="searchHandle()" label-width="120px">
          <el-row :gutter="10" type="flex">
            <el-col >
              <el-row :gutter="10">
                <el-col :span="8">
                  <three-no-input ref="threeNoInput" :autosize="threeNoInputAutoSize()" :orderAreaList='orderAreaList' :deliveryNo.sync="dataForm.deliveryNos"
                                  :waybillNo.sync="dataForm.waybillNos" :customerOrderNo.sync="dataForm.customerVoucherNos"
                                  :noSize="5000" />
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('baReceivableFee.feeType')" prop="feeType">
                    <el-select filterable v-model="dataForm.feeType"
                               :placeholder="$t('baReceivableFee.feeType')" clearable>
                      <el-option v-for="(item, index) in orderTypeList" :key="index" :label="item.name"
                                 :value="item.code"/>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="24" :md="8">
                  <el-form-item :label="$t('baReceivableFee.optDate')" prop="optDate">
                    <el-date-picker v-model="optDateArr" type="datetimerange"
                                    class="w-percent-100"
                                    :start-placeholder="$t('datePicker.start')"
                                    :end-placeholder="$t('datePicker.end')"
                                    :default-time="['00:00:00', '23:59:59']">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <div class="search_box_btn no_more">
              <el-button type="primary" @click="searchHandle()" icon="el-icon-search">查询</el-button>
              <el-button @click.native="resetForm('searchForm')" icon="el-icon-refresh-right">重置</el-button>
            </div>
          </el-row>
        </el-form>
      </el-card>

      <el-card class="flex_tab no_shadow" type="border-card" >
        <el-row class="optBtn_panel">
          <el-col :md="12" class='optBtn_leftFixed'>
            <!--保留空格符-->
            <span style="margin-left: 10px;"> 可用金额: </span>
            <span v-for="(item, index) in currencyUsableList" :key="index" >
              <span style="margin-left: 10px; color: red; font-size: 15px;">{{ item.usableSum | numberFormat(3)}}</span>
              <span style="margin-left: 10px; color: red; font-size: 15px;" class="order-sum">{{item.currency }}</span>
            </span>
          </el-col>
          <el-col :md="12" class="text-right">
            <el-button size="mini" type="primary" plain @click="exportHandle()">{{ $t('export') }}</el-button>
          </el-col>
        </el-row>
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <el-table v-loading="dataListLoading" :data="dataList"  :max-height="tableHeight">
            <!-- 动态显示表格 -->
            <el-table-column v-for="(item, index) in tableColumnsArr" :key="index" :type="item.type" :prop="item.prop" header-align="center" :align="item.align" :min-width="item.width" :label="item.label">
              <template slot-scope="scope">
                <div>
                  <div v-if="item.prop === 'createDate'">
                    <el-tooltip :content="scope.row.createDate | gtmToLtm | timestampFormat('hh:mm:ss')" effect="light" placement="top">
                      <span>{{formatterFn(scope,item.prop)}}</span>
                    </el-tooltip>
                  </div>
                  <div v-else-if="item.prop === 'feeType'">
                    <div v-if="scope.row.orderType === 30">
                      <span>{{ $t('baReceivableFee.accountTopIp') }}</span>
                    </div>
                    <div v-else>
                      <span>{{ $t('baReceivableFee.businessDeductions') }}</span>
                    </div>
                  </div>
                  <div v-else-if="item.prop === 'income'">
                    <div v-if="scope.row.orderType === 30">
                      <span>{{ scope.row.sumD | numberFormat(2)}}</span>
                      <span class="order-sum">{{scope.row.currency }}</span>
                    </div>
                  </div>
                  <div v-else-if="item.prop === 'expend'">
                    <div v-if="scope.row.orderType !== 30">
                      <span>{{ scope.row.sumD | numberFormat(2)}}</span>
                      <span class="order-sum">{{scope.row.currency }}</span>
                    </div>
                  </div>
                  <div v-else>
                    {{formatterFn(scope,item.prop)}}
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination background :current-page="page"
                       :page-sizes="[10, 20, 50, 100]"
                       :page-size="limit" :total="total"
                       layout="total, sizes, prev, pager, next, jumper"
                       @size-change="pageSizeChangeHandle"
                       @current-change="pageCurrentChangeHandle">
        </el-pagination>
      </el-card>
    </div>
    <!-- 自定义列显示弹窗 -->
    <table-set ref="tableSet"></table-set>
    <!-- 自定义列导出 -->
    <exportDetail v-if="exportVisible" ref="exportDetail" @backView="backView"/>
    <!-- 新增 / 修改 -->
<!--    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList" @cancelAddOrUpdate="cancelAddOrUpdate"></add-or-update>-->
    <!-- 详情页面 -->
<!--    <viewDetail v-if="viewVisible" ref="viewDetail" @backView="backView"></viewDetail>-->
  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import listPage from '@/mixins/listPage'
import { formatterType, gtmToLtm, timestampFormat, numberFormat, formatterName } from '@/filters/filters'
import dictTypeMixins from '@/mixins/dictTypeMixins'
// table 自定义显示
import tableSet from '@/components/tableSet'
import api from '@/api'
import baseData from '@/api/baseData'
import threeNoInput from '@/components/three-no-input.vue'
import ExportDetail from '../bd/excel-export-dialog'
// import AddOrUpdate from './receivable-fee-add-or-update'
// import ViewDetail from './receivable-fee-view-detail'
export default {
  mixins: [listPage, mixinViewModule, dictTypeMixins],
  data () {
    return {
      tableColumns: [
        // { type: '', width: '150', prop: 'businessId', label: this.$t('baReceivableFee.businessId'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'waybillNo', label: this.$t('baReceivableFee.waybillNoOrBusinessId'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'customerVoucherNo', label: this.$t('baReceivableFee.customerVoucherNoOrSerialNumber'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'deliveryNo', label: this.$t('baReceivableFee.deliveryNo'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'feeType', label: this.$t('baReceivableFee.feeType'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'orderTypeName', label: this.$t('baReceivableFee.orderType'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '100', prop: 'feeTypeId', label: this.$t('baReceivableFee.feeTypeId'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'income', label: this.$t('baReceivableFee.income'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '120', prop: 'expend', label: this.$t('baReceivableFee.expend'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'optDate', label: this.$t('baReceivableFee.optDate'), align: 'center', isShow: true, disabled: false },
        { type: '', width: '150', prop: 'createDate', label: this.$t('baReceivableFee.billingDate'), align: 'center', isShow: true, disabled: false }
        // { type: '', width: '150', prop: 'memo', label: this.$t('baReceivableFee.memo'), align: 'center', isShow: true, disabled: false }
      ],
      mixinViewModuleOptions: {
        getDataListURL: '/ba/receivablefee/flowPage',
        getDataListURLOfRequestType: 'POST',
        getDataListIsPage: true
      },
      dataForm: {
        id: '',
        feeType: '',
        feeFlow: 1,
        customerVoucherNos: '',
        waybillNos: '',
        deliveryNos: '',
        optBeginDate: '',
        optEndDate: ''
      },
      activeName: 'all',
      tableName: 'ba-receivablefee',
      optDateArr: [],
      orderTypeList: [
        { code: 30, name: this.$t('baReceivableFee.accountTopIp') },
        { code: 10, name: this.$t('baReceivableFee.businessDeductions') }
      ],
      // 订单查询
      orderAreaList: [
        {
          label: '运单号',
          value: 2
        },
        {
          label: '客户单号',
          value: 1
        },
        {
          label: '派送单号',
          value: 3
        }
      ],
      exportVisible: false,
      optDateArray: [],
      orderType: [],
      feeTypeList: [],
      currencyUsableList: []
    }
  },
  created () {
    this.getBaseData()
  },
  methods: {
    queryByParam () {
      this.page = 1
      this.getDataList()
    },
    getBaseData () {
      // 客户余额
      baseData(api.currencyUsableList).then(res => {
        this.currencyUsableList = res
      })
      baseData(api.usingFeeTypeList).then(res => {
        this.feeTypeList = res
      })
    },
    getDict () {
      this.getDictTypeList('weightUnit').then(res => {
        this.orderTypeList = res
      })
    },
    // 多单号高度调节事件
    threeNoInputAutoSize () {
      this.$nextTick(() => {
        this.$refs.threeNoInput.resizeTextarea()
      })
      if (this.searchBoxShow) {
        return { minRows: 2, maxRows: 2 }
      } else {
        return { minRows: 5, maxRows: 5 }
      }
    },
    // 列表查询方法
    searchHandle () {
      let initFlag = this.$refs.threeNoInput.setValue()
      if (initFlag) {
        this.queryByParam()
      }
    },
    // 重置表单
    resetForm (searchForm) {
      this.optDateArr = []
      this.$refs.threeNoInput.clearValue()
      this._resetForm(searchForm)
    },
    // 导出
    exportHandle () {
      let masterDTOName = 'BaReceivableFeeDTO'
      this.exportVisible = true
      this.$nextTick(() => {
        this.$refs.exportDetail.dataForm.masterDTOName = masterDTOName
        this.$refs.exportDetail.exportURL = '/ba/receivablefee/exportByDiy'
        this.$refs.exportDetail.exportMethod = 'post'
        this.$refs.exportDetail.queryDataForm = this.dataForm
        this.$refs.exportDetail.init(['入仓子运单', '出仓子运单'], ['运单-入库操作重(kg)', '运单-出库操作重(kg)'])
      })
    },
    // 格式化列显示
    formatterFn (scope, prop) {
      let value
      switch (prop) {
        case 'status':
          value = formatterType(scope.row.status, this.statusList)
          break
        case 'feeTypeId':
          value = formatterName(scope.row.feeTypeId, this.feeTypeList)
          break
        case 'orderType':
          value = formatterType(scope.row.orderType, this.orderTypeList)
          break
        default:
          value = scope.row[prop]
          break
      }
      return value
    },
    // 自定义列显示
    settingCol (tableColumns) {
      this.$nextTick(() => {
        this.$refs['tableSet'].init(this.tableName, tableColumns)
      })
    }
  },
  watch: {
    optDateArr: {
      handler (newVal, oldVal) {
        if (newVal !== null) {
          this.dataForm.optBeginDate = newVal[0]
          this.dataForm.optEndDate = newVal[1]
          return
        }
        this.dataForm.optBeginDate = this.dataForm.optEndDate = ''
      },
      deep: true
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat,
    numberFormat,
    formatterName
  },
  computed: {
    tableColumnsArr () {
      let arr = []
      let tableColumns = Object.keys(this.tableColumns).map((key) => this.tableColumns[key])
      arr = tableColumns.filter((item) => {
        return item.isShow
      })
      return arr
    },
    panelShow () {
      let ret = false
      if (this.addOrUpdateVisible) {
        ret = true
      } else if (this.viewVisible) {
        ret = true
      }
      return ret
    }
  },
  components: {
    threeNoInput,
    // AddOrUpdate,
    // ViewDetail,
    tableSet,
    ExportDetail
  }
}
</script>
<style lang="scss">
  .order-sum {
    font-size:8px;
    transform: translateY(2px) scale(0.8);
    display: inline-block;
  }
</style>
