<template>
  <div class="add-body panel_body">
    <div class="panel-hd">{{!dataForm.id ? $t('add') : $t('update')}}</div>
    <div class="addOrUpdatePanel" >
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" label-width="80px">
        <el-row type="flex" justify="start">
          <el-col :md="{span:13, offset: 2}">
            <el-form-item :label="$t('coOrder.customerOrderNo')" prop="customerOrderNo">
              <el-input v-model="dataForm.customerOrderNo" :placeholder="$t('coOrder.customerOrderNo')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrder.waybillNo')" prop="waybillNo">
              <el-input v-model="dataForm.waybillNo" :placeholder="$t('coOrder.waybillNo')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrder.deliveryNo')" prop="deliveryNo">
              <el-input v-model="dataForm.deliveryNo" :placeholder="$t('coOrder.deliveryNo')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrder.logisticsProductCode')" prop="logisticsProductCode">
              <el-input v-model="dataForm.logisticsProductCode" :placeholder="$t('coOrder.logisticsProductCode')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrder.taxPayMode')" prop="taxPayMode">
              <el-input v-model="dataForm.taxPayMode" :placeholder="$t('coOrder.taxPayMode')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrder.forecastWeight')" prop="forecastWeight">
              <el-input v-model="dataForm.forecastWeight" :placeholder="$t('coOrder.forecastWeight')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrder.parcelType')" prop="parcelType">
              <el-input v-model="dataForm.parcelType" :placeholder="$t('coOrder.parcelType')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrder.goodsCategory')" prop="goodsCategory">
              <el-input v-model="dataForm.goodsCategory" :placeholder="$t('coOrder.goodsCategory')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrder.electric')" prop="electric">
              <el-input v-model="dataForm.electric" :placeholder="$t('coOrder.electric')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrder.remote')" prop="remote">
              <el-input v-model="dataForm.remote" :placeholder="$t('coOrder.remote')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrder.insuredAmount')" prop="insuredAmount">
              <el-input v-model="dataForm.insuredAmount" :placeholder="$t('coOrder.insuredAmount')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrder.insuredCurrency')" prop="insuredCurrency">
              <el-input v-model="dataForm.insuredCurrency" :placeholder="$t('coOrder.insuredCurrency')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrder.codAmount')" prop="codAmount">
              <el-input v-model="dataForm.codAmount" :placeholder="$t('coOrder.codAmount')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrder.codCurrency')" prop="codCurrency">
              <el-input v-model="dataForm.codCurrency" :placeholder="$t('coOrder.codCurrency')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrder.declareCurrency')" prop="declareCurrency">
              <el-input v-model="dataForm.declareCurrency" :placeholder="$t('coOrder.declareCurrency')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrder.consigneeCountry')" prop="consigneeCountry">
              <el-input v-model="dataForm.consigneeCountry" :placeholder="$t('coOrder.consigneeCountry')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrder.customerRemark')" prop="customerRemark">
              <el-input v-model="dataForm.customerRemark" :placeholder="$t('coOrder.customerRemark')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrder.serviceId')" prop="serviceId">
              <el-input v-model="dataForm.serviceId" :placeholder="$t('coOrder.serviceId')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrder.salesmanId')" prop="salesmanId">
              <el-input v-model="dataForm.salesmanId" :placeholder="$t('coOrder.salesmanId')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrder.customerId')" prop="customerId">
              <el-input v-model="dataForm.customerId" :placeholder="$t('coOrder.customerId')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrder.customerName')" prop="customerName">
              <el-input v-model="dataForm.customerName" :placeholder="$t('coOrder.customerName')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrder.customerSimpleCode')" prop="customerSimpleCode">
              <el-input v-model="dataForm.customerSimpleCode" :placeholder="$t('coOrder.customerSimpleCode')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrder.franchiseeId')" prop="franchiseeId">
              <el-input v-model="dataForm.franchiseeId" :placeholder="$t('coOrder.franchiseeId')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrder.warehouseId')" prop="warehouseId">
              <el-input v-model="dataForm.warehouseId" :placeholder="$t('coOrder.warehouseId')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrder.platformType')" prop="platformType">
              <el-input v-model="dataForm.platformType" :placeholder="$t('coOrder.platformType')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrder.salesUrl')" prop="salesUrl">
              <el-input v-model="dataForm.salesUrl" :placeholder="$t('coOrder.salesUrl')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrder.packageQty')" prop="packageQty">
              <el-input v-model="dataForm.packageQty" :placeholder="$t('coOrder.packageQty')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coOrder.print')" prop="print">
              <el-input v-model="dataForm.print" :placeholder="$t('coOrder.print')"></el-input>
            </el-form-item>
          </el-col>

        </el-row>
      </el-form>
      <div id="cs_FormFooter" class="el-form-footer">
        <el-button @click="cancelFn">{{ $t('cancel') }}</el-button>
        <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      dataForm: {
        id: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: '',
        status: '',
        version: '',
        customerOrderNo: '',
        waybillNo: '',
        deliveryNo: '',
        logisticsProductCode: '',
        taxPayMode: '',
        forecastWeight: '',
        parcelType: '',
        goodsCategory: '',
        electric: '',
        remote: '',
        insuredAmount: '',
        insuredCurrency: '',
        codAmount: '',
        codCurrency: '',
        declareCurrency: '',
        consigneeCountry: '',
        customerRemark: '',
        serviceId: '',
        salesmanId: '',
        customerId: '',
        customerName: '',
        customerSimpleCode: '',
        franchiseeId: '',
        warehouseId: '',
        platformType: '',
        salesUrl: '',
        packageQty: '',
        print: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        creator: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        createDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        updater: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        updateDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        status: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        version: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        customerOrderNo: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        waybillNo: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        deliveryNo: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        logisticsProductCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        taxPayMode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        forecastWeight: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        parcelType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        goodsCategory: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        electric: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        remote: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        insuredAmount: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        insuredCurrency: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        codAmount: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        codCurrency: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        declareCurrency: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        consigneeCountry: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        customerRemark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        serviceId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        salesmanId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        customerId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        customerName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        customerSimpleCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        franchiseeId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        warehouseId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        platformType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        salesUrl: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        packageQty: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        print: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/co/order/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/co/order/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    // 取消
    cancelFn () {
      this.visible = false
      this.$emit('cancelAddOrUpdate')
    }
  }
}
</script>
