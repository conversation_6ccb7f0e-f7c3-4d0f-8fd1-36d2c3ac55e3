<template>
  <div class="aui-card--fill">
    <div class="mod-co__inorder flex_wrap" >
      <el-card class="flex_tab box-card">
        <!--<el-row :gutter="10" class="margin_bottom15">
          <el-col :span="12">
            <el-button size="mini" @click="q()">{{ $t('query') }}</el-button>
          </el-col>
        </el-row>-->
        <div class="flex_table" ref="tableElm" v-domResize="redraw">
          <el-table :data="dataList" border @selection-change="dataListSelectionChangeHandle" :max-height="tableHeight">
            <el-table-column
              prop="title"
              label="标题"
              show-overflow-tooltip
              min-width="280"
            >
            </el-table-column>
            <el-table-column
              prop="type"
              label="类型"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                <span>{{scope.row.type | formatterType(noticeType)}}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="nsNoticeUserDTO.readStatus"
              label="状态"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                <span>{{scope.row.nsNoticeUserDTO.readStatus | formatterType(nsNoticeReadStatus)}}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="createDate"
              label="发布时间"
              align="center"
              width="150">
              <template slot-scope="scope">
                <span>{{scope.row.createDate | gtmToLtm(scope.row.createDate)}}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="address"
              label="操作" width="180">
              <template slot-scope="scope">
                <el-button type="text" size="mini" @click="viewDetail(scope.row)">查看</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination
          :current-page="page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="limit"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="pageSizeChangeHandle"
          @current-change="pageCurrentChangeHandle">
        </el-pagination>
      </el-card>
    </div>
  </div>
</template>

<script>
import listPage from '@/mixins/listPage'
import mixinViewModule from '@/mixins/view-module'
import { formatterType, gtmToLtm } from '@/filters/filters'
import dictTypeMixins from '@/mixins/dictTypeMixins'
import { getNoticeuser } from '@/api/notice'

export default {
  name: 'list',
  mixins: [listPage, mixinViewModule, dictTypeMixins],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/woms/ns/noticeuser/pageByUser',
        getDataListIsPage: true,
        deleteURL: '/woms/ns/notice',
        deleteIsBatch: true
      },
      dataForm: {
        templateId: '',
        mailTo: '',
        status: null,
        order: 'desc',
        orderField: 'create_date'
      },
      nsNoticePubStatus: [],
      nsNoticeReadStatus: [],
      noticeType: [],
      noticePubType: []
    }
  },
  created () {
    this.getDict()
    this.$bus.$on('noticeList', () => {
      this.queryPageByParam()
    })
  },
  activated () {
    this.queryPageByParam()
  },
  methods: {
    viewDetail (item) {
      item.nsNoticeUserDTO.readStatus = 20
      getNoticeuser(item.nsNoticeUserDTO).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$router.push({ name: 'notice-detail', query: { id: item.id } })
      })
    },
    // 状态字典
    getDict () {
      this.getDictTypeList('nsNoticePubStatus', function () {
        this.nsNoticePubStatus = this.dictTypeList
      })
      this.getDictTypeList('nsNoticeReadStatus', function () {
        this.nsNoticeReadStatus = this.dictTypeList
      })
      this.getDictTypeList('noticeType', function () {
        this.noticeType = this.dictTypeList
      })
      this.getDictTypeList('noticePubType', function () {
        this.noticePubType = this.dictTypeList
      })
    }

  },
  filters: {
    formatterType,
    gtmToLtm
  }
}
</script>

<style scoped>

</style>
