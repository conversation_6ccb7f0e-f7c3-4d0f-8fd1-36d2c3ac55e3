<template>
  <div class="add-body panel_body">
    <div class="panel-hd">
      <span v-text="$t('detail')"></span>
    </div>
    <div class="detail-desc">
      <el-container>
        <el-aside width="35%">
          <el-form ref="form" label-width="120px">
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item :label="$t('baRecharge.payerContacts')">
                  <span v-text="dataForm.payerContacts"/>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="$t('baRecharge.payerPhone')">
                  <span v-text="dataForm.payerPhone"/>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="$t('baRecharge.currency')">
                  <template> {{dataForm.currency | formatterCodeName(currencyList)}}</template>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="$t('baRecharge.sum')">
                  <template v-text="dataForm.sumD">{{ dataForm.sumD | numberFormat(2)}}</template>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="$t('baRecharge.serialNumber')">
                  <span v-text="dataForm.serialNumber"/>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="$t('baRecharge.bankAccountId')">
                  <template> {{dataForm.bankAccountId | formatterName(bankAccountList)}}</template>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="$t('baRecharge.bookkeepingTime')">
                  <span v-text="dataForm.bookkeepingTime"/>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="$t('baRecharge.bookkeeper')">
                  <template>{{dataForm.bookkeeper | formatterUserName(this.userList)}}</template>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="$t('system.status')">
                  <template>{{dataForm.status | formatterType(rechargeStatusList)}}</template>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="$t('baRecharge.customerRemark')">
                  <span class="text-overflow" v-text="dataForm.customerRemark"/>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="$t('baRecharge.receivableRemark')">
                  <span class="text-overflow" v-text="dataForm.receivableRemark"/>
                </el-form-item>
              </el-col>
              <el-col v-if="this.dataForm.status === 40" :span="24">
                <el-form-item :label="$t('baRecharge.invalidRemark')">
                  <span v-text="dataForm.invalidRemark"/>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item :label="$t('baRecharge.attachmentUrl')">
                  <a :href="dataForm.attachmentUrl">查看附件</a>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-aside>
        <el-main style="margin-top:-20px">
          <img :src="dataForm.attachmentUrl" class="avatar" fit="cover" lazy>
        </el-main>
      </el-container>
    </div>
    <div id="cs_FormFooter" class="el-form-footer" slot="footer">
      <el-button @click="backFn">{{ $t('back') }}</el-button>
    </div>
  </div>
</template>

<script>
import { formatterType, gtmToLtm, timestampFormat, formatterCodeName, formatterName, formatterUserName, numberFormat } from '@/filters/filters'
import dictTypeMixins from '@/mixins/dictTypeMixins'
import api from '@/api'
import baseData from '@/api/baseData'
export default {
  mixins: [dictTypeMixins],
  data () {
    return {
      dataForm: {
        id: '',
        payerContacts: '',
        payerPhone: '',
        currency: '',
        sumD: '',
        serialNumber: '',
        bankAccountId: '',
        attachmentUrl: '',
        invalidRemark: '',
        customerRemark: '',
        receivableRemark: '',
        bookkeepingTime: '',
        bookkeeper: ''
      },
      rechargeStatusList: [],
      currencyList: [],
      bankAccountList: [],
      userList: []

    }
  },
  created () {
    this.getDict()
    this.getBaseData()
  },
  methods: {
    getDict () {
      this.getDictTypeList('rechargeType').then(res => {
        this.rechargeStatusList = res
      })
    },
    getBaseData () {
      // 币种
      baseData(api.currencyList).then(res => {
        this.currencyList = res
      })
      // 银行卡号
      baseData(api.bankAccountList).then(res => {
        this.bankAccountList = res
      })
      baseData(api.userList).then(res => {
        this.userList = res
      })
    },
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/ba/recharge/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 返回
    backFn () {
      this.visible = false
      this.$emit('backView')
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat,
    formatterCodeName,
    formatterName,
    formatterUserName,
    numberFormat
  }
}
</script>
