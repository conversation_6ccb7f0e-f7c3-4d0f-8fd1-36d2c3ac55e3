<template>
  <div class="aui-wrapper aui-page__login">
    <div class="aui-content__wrapper">
      <main class="aui-content">
        <div class="login-header">
          <h2 class="login-brand">{{ $t('brand.lg') }}</h2>
        </div>
        <div class="login-body">
          <h3 class="login-title">{{ $t('reset.title') }}</h3>
          <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" status-icon>
            <el-form-item>
              <el-select v-model="$i18n.locale" class="w-percent-100">
                <el-option v-for="(val, key) in i18nMessages" :key="key" :label="val._lang" :value="key"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="passwordFirst">
              <el-input ref="passwordFirst" v-model="dataForm.passwordFirst" type="password" :placeholder="$t('reset.passwordFirst')">
                <span slot="prefix" class="el-input__icon">
                  <svg class="icon-svg" aria-hidden="true"><use xlink:href="#icon-lock"></use></svg>
                </span>
              </el-input>
            </el-form-item>
            <el-form-item prop="passwordSecond">
              <el-input ref="passwordSecond" v-model="dataForm.passwordSecond" type="password" :placeholder="$t('reset.passwordSecond')">
                <span slot="prefix" class="el-input__icon">
                  <svg class="icon-svg" aria-hidden="true"><use xlink:href="#icon-lock"></use></svg>
                </span>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="dataFormSubmitHandle()" :loading="isSave" class="w-percent-100 bg">{{ $t('reset.title') }}</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="login-footer">
          <p>
            <a :href="$baseUrl + '/its'" target="_blank">{{ $t('login.demo') }}</a>
          </p>
          <p><a href="http://www.xx.cn/" target="_blank">{{ $t('login.copyright') }}</a>2019 © xx.cn</p>
        </div>
      </main>
    </div>
  </div>
</template>

<script>
import debounce from 'lodash/debounce'
import { messages } from '@/i18n'
export default {
  data () {
    return {
      i18nMessages: messages,
      isSave: false,
      dataForm: {
        id: '',
        timestamp: '',
        sign: '',
        newPassword: '',
        passwordSecond: '',
        passwordFirst: ''
      },
      params: {}
    }
  },
  computed: {
    dataRule () {
      const isPassed = (rule, value, callback) => {
        if (!this.validatedPassword(this.dataForm.passwordFirst, this.dataForm.passwordSecond)) {
          return callback(new Error(this.$t('reset.passwordDiff')))
        }
        callback()
      }
      return {
        passwordSecond: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isPassed, trigger: 'blur' }
        ],
        passwordFirst: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    this.params = this.$route.query
    this.dataForm.id = this.params.id
    this.dataForm.timestamp = this.params.timestamp
    this.dataForm.sign = this.params.sign
  },
  methods: {
    validatedPassword (firstInput, secondInput) {
      if (firstInput === secondInput) {
        this.dataForm.newPassword = firstInput
        return true
      }
      return false
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      // 聚焦点
      if (!this.dataForm.passwordSecond) {
        this.$refs.passwordSecond.focus()
        return
      } else if (!this.dataForm.passwordFirst) {
        this.$refs.passwordFirst.focus()
        return
      }

      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.isSave = true
        this.$http.post('/auth/resetPwd', this.dataForm).then(({ data: res }) => {
          this.isSave = false
          this.id = ''
          this.timestamp = ''
          this.sign = ''
          if (res.code !== 0) {
            return this.$message.error(res.data)
          }
          this.$router.replace({ name: 'resetSuccess' })
        }).catch(() => {
          this.isSave = false
        })
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
<style lang="scss" scoped>
.bg{
  background:$--color-primary;
  border-color: $--color-primary;
}
</style>
