<template>
  <div class="add-body panel_body">
    <div class="panel-hd">
      <span v-text="$t('detail')"></span>
    </div>
    <div class="detail-desc">
      <el-form ref="form" label-width="80px">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item :label="$t('coOrderShipper.shipperName')">
              <span v-text="dataForm.shipperName"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderShipper.shipperCompany')">
              <span v-text="dataForm.shipperCompany"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderShipper.shipperPhone')">
              <span v-text="dataForm.shipperPhone"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderShipper.shipperEmail')">
              <span v-text="dataForm.shipperEmail"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderShipper.shipperCountry')">
              <span v-text="dataForm.shipperCountry"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderShipper.shipperProvince')">
              <span v-text="dataForm.shipperProvince"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderShipper.shipperCity')">
              <span v-text="dataForm.shipperCity"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderShipper.shipperDistrict')">
              <span v-text="dataForm.shipperDistrict"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderShipper.shipperAddress')">
              <span v-text="dataForm.shipperAddress"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderShipper.shipperPostcode')">
              <span v-text="dataForm.shipperPostcode"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderShipper.shipperDoorplate')">
              <span v-text="dataForm.shipperDoorplate"></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('coOrderShipper.shipperStreet')">
              <span v-text="dataForm.shipperStreet"></span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div id="cs_FormFooter" class="el-form-footer" slot="footer">
      <el-button @click="backFn">{{ $t('back') }}</el-button>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      dataForm: {
        id: '',
        creator: '',
        createDate: '',
        shipperName: '',
        shipperCompany: '',
        shipperPhone: '',
        shipperEmail: '',
        shipperCountry: '',
        shipperProvince: '',
        shipperCity: '',
        shipperDistrict: '',
        shipperAddress: '',
        shipperPostcode: '',
        shipperDoorplate: '',
        shipperStreet: ''
      }
    }
  },
  methods: {
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/co/ordershipper/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 返回
    backFn () {
      this.visible = false
      this.$emit('backView')
    }
  }
}
</script>
