<template>
  <div  class="add-body panel_body">
    <div v-show="!panelShow">
      <!-- <div class="panel-hd">
        <span>{{ opType ? $t(opType): '快递订单录入'}}</span>
      </div> -->
      <div class="addOrUpdatePanel orderAdd" >
        <el-form :model="dataForm" :rules="dataRule" ref="dataForm"  key='0' label-width="auto">
          <h2 class="detail-title">
            <span v-text="$t('header.baseInfo')"></span>
            <el-checkbox  v-model="hasSender"  size="mini" @change='hasSenderChange'>{{$t('coOrder.fillInSenderInfo')}}</el-checkbox>
          </h2>
          <el-row :gutter="22">
            <el-col :span='8'>
              <el-form-item :label="$t('wsComWaybill.logisticsProductCode')" prop="logisticsProductCode">
                <el-select v-model="dataForm.logisticsProductCode" :placeholder="$t('wsComWaybill.logisticsProductCode')" filterable >
                  <el-option v-for="(item, i) in logisticsProductList" :key="item.code + i" :label="item.name" :value="item.code">
                    <span style="float: left">{{ item.name }}</span>
                    <span style="float: right; color: #DCDFE6; font-size: 13px">{{ item.code }}</span>
                  </el-option>
                  <el-divider v-if='showAllProductBtn || showCustomerBindProductBtn'></el-divider>
                  <el-button type='type' class='logisticsproductcode-select-btn' v-if='showAllProductBtn' @click="showAllProductHandle()" icon="el-icon-d-caret">展示全部物流产品</el-button>
                  <el-button type='type' class='logisticsproductcode-select-btn' v-if='showCustomerBindProductBtn' @click="showCustomerBindProductHandle()" icon="el-icon-caret-top">展示常用物流产品</el-button>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('coOrder.shopName')" prop="shopName">
                <el-input v-model="dataForm.shopName" :placeholder="$t('coOrder.shopName')"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item :label="$t('coOrder.waybillNo')" prop="waybillNo">
                <el-input v-model="dataForm.waybillNo" :placeholder="$t('coOrder.waybillNo')" clearable></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="22">
            <el-col :span="8">
              <el-form-item :label="$t('coOrder.customerOrderNo')" prop="customerOrderNo">
                <el-input v-model="dataForm.customerOrderNo" :placeholder="$t('coOrder.customerOrderNo')" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="$t('coOrderShipper.vatNo')" prop="shipper.vatNo">
                <el-input v-model="dataForm.shipper.vatNo" :placeholder="$t('coOrderShipper.vatNo')" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item :label="$t('coOrder.parcelType')" prop="parcelType">
                <el-select  filterable v-model="dataForm.parcelType" :placeholder="$t('coOrder.parcelType')" @change="parcelTypeChange" >
                  <el-option v-for="(item, index) in parcelTypeList" :key="index" :label="item.dictName" :value="item.dictValue"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item :label="$t('coOrder.customsMethod')" prop="customsMethod">
                <el-select v-model="dataForm.customsMethod" :placeholder="$t('coOrder.customsMethod')" filterable >
                  <el-option v-for="item in customsMethodList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item :label="$t('coOrder.electricCode')" prop="electricCode">
                <el-select v-model="dataForm.electricCode" :placeholder="$t('coOrder.electricCode')" filterable >
                  <el-option v-for="item in electricCodeList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span='8'>
              <el-form-item :label="$t('coOrderShipper.iossNo')" prop="iossNo">
                <el-input v-model="dataForm.shipper.iossNo" :placeholder="$t('coOrderShipper.iossNo')">
                  <el-select slot="prepend" v-model="dataForm.shipper.iossTaxType" :placeholder="$t('coOrderShipper.iossTaxType')" style="width:110px;">
                    <el-option v-for="item in iossTypeList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                  </el-select>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row  :gutter="22">
            <el-col :span='8'>
              <el-form-item :label="$t('coOrder.taxPayMode')" prop="taxPayMode">
                <el-select v-model.trim="dataForm.taxPayMode" :placeholder="$t('coOrder.taxPayMode')" filterable >
                  <el-option v-for="item in taxPayModeList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="dataForm.taxPayMode !== 11">
              <el-form-item :label="$t('coOrder.taxPayAccount')" prop="taxPayAccount">
                <el-input v-model="dataForm.taxPayAccount" :placeholder="$t('coOrder.taxPayAccount')" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item :label="$t('coOrder.codAmount')" prop="codAmount">
                <el-input v-model="dataForm.codAmount" :placeholder="$t('coOrder.codAmount')">
                  <el-select slot="prepend" v-model="dataForm.codCurrency" :placeholder="$t('coOrder.codCurrency')" style="width:110px;">
                    <el-option v-for="item in currencyList" :key="item.code" :label="item.name + ' (' + item.code + ')'" :value="item.code"></el-option>
                  </el-select>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-divider v-if='hasSender'/>
          <div class='flex' v-if='hasSender'>
            <h2 class="detail-title">
              <span v-text="$t('header.senderInfo')"></span>
            </h2>
          </div>
          <el-row :gutter="20" v-if='hasSender'>
            <div >
              <div class="clearfix">
                <el-col :span="6">
                  <el-form-item :label="$t('coOrderShipper.shipperCountry')" prop="shipper.shipperCountry">
                    <el-select v-model="dataForm.shipper.shipperCountry" :placeholder="$t('coOrderShipper.shipperCountry')" clearable filterable remote reserve-keyword auto-complete="new-password"
                               :remote-method="getCountryListByCodeOrName" element-loading-spinner="el-icon-loading" :loading="countrySelectLoading" @change='countrySelectChange($event)'>
                      <el-option v-for="(item, i) in countryList" :key="item.code + i" :label="item.name" :value="item.code">
                        <span style="float: left">{{ item.name }}</span>
                        <span style="float: right; color: #DCDFE6; font-size: 13px">{{ item.code }}</span>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item :label="$t('coSetCustomerShipper.shipperShortName')">
                    <el-select v-model="dataForm.setShipper" :placeholder="$t('coSetCustomerShipper.shortName')" filterable clearable @change="selectSetShipper">
                      <el-option v-for="item in setShipperByCustomerCodeList" :key="item.id" :label="item.shortName" :value="item.id"></el-option>
                    </el-select>
                  </el-form-item>
              </el-col>
              </div>
              <div class="clearfix">
                <el-col :span='6'>
                  <el-form-item :label="$t('coOrderShipper.shipperName')" prop='shipper.shipperName'>
                    <el-input v-model='dataForm.shipper.shipperName' :placeholder="$t('coOrderShipper.shipperName')" auto-complete="new-password"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span='6'>
                  <el-form-item :label="$t('coOrderShipper.shipperPhone')" prop='shipper.shipperPhone'>
                    <el-input v-model='dataForm.shipper.shipperPhone' :placeholder="$t('coOrderShipper.shipperPhone')" auto-complete="new-password"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span='6'>
                  <el-form-item :label="$t('coOrderShipper.shipperCompany')" prop='shipper.shipperCompany'>
                    <el-input v-model='dataForm.shipper.shipperCompany' :placeholder="$t('coOrderShipper.shipperCompany')" auto-complete="new-password"></el-input>
                  </el-form-item>
                </el-col>
              </div>
              <el-col :span='6'>
                <el-form-item :label="$t('coOrderShipper.shipperProvince')" prop='shipper.shipperProvince'>
                  <el-input v-model='dataForm.shipper.shipperProvince' :placeholder="$t('coOrderShipper.shipperProvince')" auto-complete="new-password"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span='6'>
                <el-form-item :label="$t('coOrderShipper.shipperCity')" prop='shipper.shipperCity'>
                  <el-input v-model='dataForm.shipper.shipperCity' :placeholder="$t('coOrderShipper.shipperCity')" auto-complete="new-password"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span='6'>
                <el-form-item :label="$t('coOrderShipper.shipperPostcode')" prop='shipper.shipperPostcode'>
                  <el-input v-model='dataForm.shipper.shipperPostcode' :placeholder="$t('coOrderShipper.shipperPostcode')" auto-complete="new-password"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span='6'>
                <el-form-item :label="$t('coOrderShipper.shipperDistrict')" prop='shipper.shipperDistrict'>
                  <el-input v-model='dataForm.shipper.shipperDistrict' :placeholder="$t('coOrderShipper.shipperDistrict')" auto-complete="new-password"></el-input>
                </el-form-item>
              </el-col>
              <div class="clearfix">
                <el-col :span='12'>
                  <el-form-item :label="$t('coOrderShipper.shipperAddress')" prop='shipper.shipperAddress'>
                    <el-input v-model='dataForm.shipper.shipperAddress' :placeholder="$t('coOrderShipper.shipperAddress')" type="textarea" :rows="2" auto-complete="new-password"></el-input>
                  </el-form-item>
                </el-col>
              </div>
              <div class="clearfix">
                <el-col :span='6'>
                  <el-form-item :label="$t('coOrderShipper.shipperStreet')" prop='shipper.shipperStreet'>
                    <el-input v-model='dataForm.shipper.shipperStreet' :placeholder="$t('coOrderShipper.shipperStreet')" auto-complete="new-password"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span='6'>
                  <el-form-item :label="$t('coOrderShipper.shipperDoorplate')" prop='shipper.shipperDoorplate'>
                    <el-input v-model='dataForm.shipper.shipperDoorplate' :placeholder="$t('coOrderShipper.shipperDoorplate')" auto-complete="new-password"></el-input>
                  </el-form-item>
                </el-col>
              </div>
              <el-col :span='12'>
                <el-form-item :label="$t('coOrderShipper.shipperEmail')" prop='shipper.shipperEmail'>
                  <el-input v-model='dataForm.shipper.shipperEmail' :placeholder="$t('coOrderShipper.shipperEmail')" auto-complete='new-password'></el-input>
                </el-form-item>
              </el-col>
            </div>
          </el-row>
          <el-divider/>
          <div class='flex'>
            <h2 class="detail-title">
              <span v-text="$t('header.recipientInfo')"></span>
            </h2>
          </div>
          <el-row :gutter="20">
            <div >
              <div class="clearfix">
              <el-col :span="6">
                <el-form-item :label="$t('coOrderConsignee.consigneeCountry')" prop="consignee.consigneeCountry">
                  <el-select v-model="dataForm.consignee.consigneeCountry" :placeholder="$t('coOrderConsignee.consigneeCountry')" clearable filterable remote reserve-keyword auto-complete="new-password"
                             :remote-method="getCountryListByCodeOrName" element-loading-spinner="el-icon-loading" :loading="countrySelectLoading" @change='countrySelectChange($event)'>
                    <el-option v-for="(item, i) in countryList" :key="item.code + i" :label="item.name" :value="item.code">
                      <span style="float: left">{{ item.name }}</span>
                      <span style="float: right; color: #DCDFE6; font-size: 13px">{{ item.code }}</span>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="$t('coSetCustomerShipper.consigneeShortName')">
                  <el-select v-model='dataForm.setSConsignee' :placeholder="$t('coSetCustomerShipper.consigneeShortName')" filterable clearable @change="selectSetConsignee">
                    <el-option v-for="item in setConsigneeByCustomerCodeList" :key="item.id" :label="item.shortName" :value="item.id"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              </div>
              <div class="clearfix">
                <el-col :span='6'>
                  <el-form-item :label="$t('coOrderConsignee.consigneeName')" prop='consignee.consigneeName'>
                    <el-input v-model='dataForm.consignee.consigneeName' :placeholder="$t('coOrderConsignee.recipient')" auto-complete="new-password"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span='6'>
                  <el-form-item :label="$t('coOrderConsignee.consigneePhone')" prop='consignee.consigneePhone'>
                    <el-input v-model='dataForm.consignee.consigneePhone' :placeholder="$t('coOrderConsignee.consigneePhone')" auto-complete="new-password"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span='6'>
                  <el-form-item :label="$t('coOrderConsignee.consigneeCompany')" prop='consignee.consigneeCompany'>
                    <el-input v-model='dataForm.consignee.consigneeCompany' :placeholder="$t('coOrderConsignee.consigneeCompany')" auto-complete="new-password"></el-input>
                  </el-form-item>
                </el-col>
              </div>
              <el-col :span='6'>
                <el-form-item :label="$t('coOrderConsignee.consigneeProvince')" prop='consignee.consigneeProvince'>
                  <el-input v-model='dataForm.consignee.consigneeProvince' :placeholder="$t('coOrderConsignee.consigneeProvince')" auto-complete="new-password"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span='6'>
                <el-form-item :label="$t('coOrderConsignee.consigneeCity')" prop='consignee.consigneeCity'>
                  <el-input v-model='dataForm.consignee.consigneeCity' :placeholder="$t('coOrderConsignee.consigneeCity')" auto-complete="new-password"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span='6'>
                <el-form-item :label="$t('coOrderConsignee.consigneePostcode')" prop='consignee.consigneePostcode'>
                  <el-input v-model='dataForm.consignee.consigneePostcode' :placeholder="$t('coOrderConsignee.consigneePostcode')" auto-complete="new-password"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span='6'>
                <el-form-item :label="$t('coOrderConsignee.consigneeDistrict')" prop='consignee.consigneeDistrict'>
                  <el-input v-model='dataForm.consignee.consigneeDistrict' :placeholder="$t('coOrderConsignee.consigneeDistrict')" auto-complete="new-password"></el-input>
                </el-form-item>
              </el-col>
              <div class="clearfix">
                <el-col :span='12'>
                  <el-form-item :label="$t('coOrderConsignee.detailAddress')" prop='consignee.consigneeAddress'>
                    <el-input v-model='dataForm.consignee.consigneeAddress' :placeholder="$t('coOrderConsignee.detailAddress')"   auto-complete="new-password"></el-input>
                  </el-form-item>
                </el-col>
              </div>
              <div class="clearfix">
                <el-col :span='6'>
                <el-form-item :label="$t('coOrderConsignee.consigneeStreet')" prop='consignee.consigneeStreet'>
                  <el-input v-model='dataForm.consignee.consigneeStreet' :placeholder="$t('coOrderConsignee.consigneeStreet')" auto-complete="new-password"></el-input>
                </el-form-item>
              </el-col>
                <el-col :span='6'>
                <el-form-item :label="$t('coOrderConsignee.consigneeDoorplate')" prop='consignee.consigneeDoorplate'>
                  <el-input v-model='dataForm.consignee.consigneeDoorplate' :placeholder="$t('coOrderConsignee.consigneeDoorplate')" auto-complete="new-password"></el-input>
                </el-form-item>
              </el-col>
              </div>
              <el-col :span='8'>
                <el-form-item :label="$t('coOrderConsignee.consigneeEmail')" prop='consignee.consigneeEmail'>
                  <el-input v-model='dataForm.consignee.consigneeEmail' :placeholder="$t('coOrderConsignee.consigneeEmail')" auto-complete='new-password'></el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8'>
                <el-form-item :label="$t('coOrderConsignee.consigneeTaxNo')" prop='consignee.consigneeTaxNo'>
                  <el-input v-model='dataForm.consignee.consigneeTaxNo' :placeholder="$t('coOrderConsignee.consigneeTaxNo')" auto-complete='new-password'></el-input>
                </el-form-item>
              </el-col>
              <el-col :span='8'>
                <el-form-item :label="$t('coOrderConsignee.consigneeIdcard')" prop="consignee.consigneeIdcard">
                    <el-input v-model="dataForm.consignee.consigneeIdcard" :placeholder="$t('coOrderConsignee.consigneeIdcard')"></el-input>
                  </el-form-item>
              </el-col>
            </div>
          </el-row>
          <el-divider/>
          <div class='flex'>
          <h2 class="detail-title">
            <span v-text="$t('header.boxCargoInfo')"></span>
<!--            <el-button size='mini' type='primary' v-if="$hasPermission('fba:order:importBoxCargoInfo')" style='margin-left: 20px;margin-top: 10px' @click='importBoxCargoInfo' plain>{{$t('fba.importBoxCargoInfo')}}</el-button>-->
          </h2>
          </div>
          <el-row type="flex">
            <el-col :span='18'>
              <el-row :gutter='2'>
                <div class='flex'>
                  <el-col :span='7'>
                    <el-form-item :label="$t('fba.standardUnit')" prop="standardUnit">
                      <el-radio-group v-model='dataForm.standardUnit' @change="standardUnitChange">
                        <el-radio v-for='item in standardUnitList' :key='item.dictValue' :label='item.dictValue' disabled>
                          {{ item.dictName }}
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :span='5'>
                    <el-form-item :label="$t('fba.weightUnit')" prop="weightUnit">
                      <span style='font-weight: bolder'>{{dataForm.weightUnit === 20 ? 'KG' : 'LB' }}</span>
                    </el-form-item>

                  </el-col>
                  <el-col :span='5'>
                    <el-form-item :label="$t('fba.lengthUnit')" prop="lengthUnit">
                      <span style='font-weight: bolder'>{{dataForm.lengthUnit === 10 ? 'CM' : 'IN' }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span='7'>
                    <el-form-item :label="$t('coOrder.goodsCategory')" prop="goodsCategory">
                      <el-select v-model="dataForm.goodsCategory" :placeholder="$t('coOrder.goodsCategory')" label-width='120px'>
                        <el-option v-for="item in goodsCategoryList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </div>
                <el-col :span='10'>
                  <el-form-item :label="$t('fba.totalBox')" prop="packageQty" :render-header="addRedStar">
                    <el-tooltip content="若要修改总箱数，请点击”录入箱信息“按钮进行调整" :disabled='!(this.boxDataList && this.boxDataList.length > 0)' placement="bottom" effect="light">
                      <el-input v-model="dataForm.packageQty" disabled :placeholder="$t('fba.totalBox')">
                        <el-button  size='small' plain type='primary' slot="append" @click='inputBoxInfo' >{{$t('fba.enterBoxInfo')}}(Ctrl+B)</el-button>
                      </el-input>
                    </el-tooltip>
                  </el-form-item>
                </el-col>
                <el-col :span='7'>
                  <el-form-item :label="$t('fba.totalWeight')" :render-header="addRedStar" prop="forecastWeightD">
                    <el-tooltip content="录入箱信息后,自动计算总重量" placement="bottom" effect="light">
                      <el-input v-model.number='dataForm.forecastWeightD' disabled :placeholder="$t('fba.totalWeight')"></el-input>
                    </el-tooltip>
                  </el-form-item>
                </el-col>
                <el-col :span='7'>
                  <el-form-item :label="$t('coOrder.declareCurrency')" prop="declareCurrency">
                    <el-select v-model="dataForm.declareCurrency" :placeholder="$t('coOrder.declareCurrency')" filterable >
                      <el-option v-for="item in currencyList" :key="item.code" :label="item.name + ' (' + item.code + ')'" :value="item.code"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <div style='border-left:1px solid #DCDFE6;min-height: 100px;min-width: 23vw;margin-left: 10px;'>
              <el-form-item :label="$t('upload.attachment')" label-width='50px' style='padding-left: 10px;'>
                <el-upload multiple class="maxContentLength" ref="upload" accept=".pdf,.xls,.xlsx,.docx" :file-list="fileList"
                           :on-change="handleChange" :before-remove="handleBeforeRemove" :on-preview="handlePreview"
                           :http-request="httpRequest" :show-file-list="true" action="#" :auto-upload="false"
                           :on-error="errorHandle" :data="uploadData" :headers="getToken">
                  <el-button type="primary" size='mini' plain slot="trigger">{{$t('upload.button')}}</el-button>
                  <div slot="tip" class="el-upload__tip">（{{ $t('upload.tip', { format: 'pdf,xls,xlsx,docx' }) }}）</div>
                </el-upload>
                <div v-if='(dataForm.attachmentsList && dataForm.attachmentsList.length >0) || (fileList && fileList.length > 0)'>
                  <ul class="el-upload-list el-upload-list--text">
                    <li tabindex="0" class="el-upload-list__item is-success" v-for="(item, index) in dataForm.attachmentsList" :key="index">
                      <a class="el-upload-list__item-name" @click="openUrl(item)">
                        <i class="el-icon-document" />
                        {{ item.name }}
                      </a>
                      <label class="el-upload-list__item-status-label">
                        <i class="el-icon-upload-success el-icon-circle-check" />
                      </label>
                      <i class="el-icon-close" @click="removeAttachmentsUploaded(item, index)" />
                    </li>
                  </ul>
                </div>
                <transition v-else name='el-zoom-in-bottom'>
                  <div style='min-height: 30px;margin-top: 15px;'>
                    <span style='color: #DCDFE6;text-align: center'>暂无附件</span>
                  </div>
                </transition>
              </el-form-item>
            </div>
          </el-row>
          <el-row>
            <el-checkbox id='enterDeclareInfo' v-model="enterDeclareInfo"  size="mini">{{$t('coOrder.enterDeclareInfo')}}</el-checkbox>
            <el-popover
              placement="right-start"
              :title="$t('description')"
              width="275"
              trigger="hover">
              <div class="popover-content">勾选录入申报信息则要求按箱填写申报明细，否则会默认生成虚拟申报明细。</div>
              <el-button type="text" style="margin-left: 5px;font-size: 15px;color: red" icon="el-icon-question" slot="reference"></el-button>
            </el-popover>
              <div v-show='enterDeclareInfo' class="flex_table" ref="tableElm" v-domResize="redraw" id='declareFormId' style="margin-top: 20px">
                <el-form :model='declareForm' ref="declareForm" key='2' :inline-message='true' >
                  <el-table ref="tableData" v-loading="declareDataListLoading"  :data="declareForm.declareDataList"  border :max-height="tableHeight" >
                    <el-table-column label="序号" type="index" min-width="40" ></el-table-column>
                    <el-table-column prop="englishName" :label="$t('fba.enName')" min-width="150" header-align="center" align="center" :render-header="addRedStar">
                      <template slot-scope="scope">
                        <el-form-item :prop="'declareDataList.' + scope.$index + '.englishName'" :rules='packageRule.englishName'>
                          <span v-show="!scope.row.update" v-text="scope.row.englishName"></span>
                          <el-input v-show="scope.row.update" v-model="scope.row.englishName"></el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column prop="quantity" :label="$t('fba.qty')" min-width="60" header-align="center" align="center" :render-header="addRedStar">
                      <template slot-scope="scope">
                        <el-form-item :prop="'declareDataList.' + scope.$index + '.quantity'" :rules='packageRule.quantity'>
                          <span v-show="!scope.row.update" v-text="scope.row.quantity"></span>
                          <el-input v-show="scope.row.update"  v-model="scope.row.quantity"></el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column prop="unitDeclarePriceD" :label="$t('fba.unitDeclarePrice')" min-width="80" header-align="center" align="center" :render-header="addRedStar">
                      <template slot-scope="scope">
                        <el-form-item :prop="'declareDataList.' + scope.$index + '.unitDeclarePriceD'" :rules='packageRule.unitDeclarePriceD'>
                          <span v-show="!scope.row.update" v-text="scope.row.unitDeclarePriceD"></span>
                          <el-input v-show="scope.row.update"  v-model="scope.row.unitDeclarePriceD"></el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column prop="hsCode" :label="$t('fba.hsCode')" min-width="120" header-align="center" align="center" :render-header="addRedStar">
                      <template slot-scope="scope">
                        <el-form-item :prop="'declareDataList.' + scope.$index + '.hsCode'" :rules='packageRule.hsCode'>
                          <span v-show="!scope.row.update" v-text="scope.row.hsCode"></span>
                          <el-input v-show="scope.row.update" :prop="'declareDataList.' + scope.$index + '.hsCode'" v-model=" scope.row.hsCode"></el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column prop="unitNetWeightD" :label="$t('fba.unitNetWeight')" min-width="80" header-align="center" align="center" :render-header="addRedStar">
                      <template slot-scope="scope">
                        <el-form-item :prop="'declareDataList.' + scope.$index + '.unitNetWeightD'" :rules='packageRule.unitNetWeightD'>
                          <span v-show="!scope.row.update" v-text="scope.row.unitNetWeightD"></span>
                          <el-input v-show="scope.row.update"  v-model="scope.row.unitNetWeightD"></el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column prop="chineseName" :label="$t('fba.cnName')" min-width="150" header-align="center" align="center" >
                      <template slot-scope="scope">
                        <el-form-item :prop="'declareDataList.' + scope.$index + '.chineseName'" :rules='packageRule.chineseName'>
                          <span v-show="!scope.row.update" v-text="scope.row.chineseName"></span>
                          <el-input v-show="scope.row.update" v-model="scope.row.chineseName"></el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column prop="sku" :label="$t('fba.sku')" min-width="100" header-align="center" align="center">
                      <template slot-scope="scope">
                        <el-form-item :prop="'declareDataList.' + scope.$index + '.sku'" :rules='packageRule.sku'>
                          <span v-show="!scope.row.update" v-text="scope.row.sku"></span>
                          <el-input v-show="scope.row.update"  v-model="scope.row.sku"></el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column prop="totalPrice" :label="$t('fba.totalPrice')" min-width="60" header-align="center" align="center">
                      <template slot-scope="scope">
                        <el-form-item>
                          <span>{{scope.row.unitDeclarePriceD | multiply(scope.row.quantity)}}</span>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column :label="$t('fba.boxNo')" prop='packageCustomerNo' min-width="190" header-align="center" align="center" >
                      <template slot-scope="scope">
                        <el-form-item :prop="'declareDataList.' + scope.$index + '.packageCustomerNo'" :rules='packageRule.packageCustomerNo' >
                          <span v-show="!scope.row.update" v-text="scope.row.packageCustomerNo"></span>
                          <el-select v-show="scope.row.update" class="width100" :no-data-text="'无数据，请先录入箱信息'" v-model="scope.row.packageCustomerNo"  filterable clearable>
                            <el-option v-for="item in boxNoList" :key="item" :label="item" :value="item"></el-option>
                          </el-select>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column :label="$t('handle')"  header-align="center"   align="center" min-width="150">
                      <template slot="header" slot-scope="scope">
                        <span>{{$t('handle')}}</span>
                      </template>
                      <template slot-scope="scope">
                        <el-form-item>
                          <el-link  :underline='false' @click="saveRow(scope, scope.row.update ? 'update' : 'save' )" :class="scope.row.update ? 'el-link--warning' : 'el-link--default'">
                            {{ scope.row.update ? $t('save') : $t('update') }}
                          </el-link>
                          <el-link  :underline='false' @click="copyRow(scope.row)">{{ $t('copy') }}</el-link>
                          <popconfirm i18nOperateValue="delete" @clickHandle="deleteRow(scope.$index)" ></popconfirm>
                          <el-link  :underline='false' @click="openDrawer(scope.row, scope.$index)">更多信息</el-link>
                        </el-form-item>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form>
                <el-dropdown split-button plain type="primary"  trigger="click" style='width: 100%;margin: 10px 0px;' size="mini"
                             placement='bottom-end' @click='addRow' @command="addRow">
                  <span class='el-icon-plus'>{{$t('addDeclareCargo')}}</span>
                  <el-dropdown-menu slot="dropdown" style='min-width: 12%;'>
                    <el-dropdown-item :divided='index!==0' v-for="(item, index) in setDeclareByCustomerCodeList" :key='item.id' :command="item.id" >{{item.chineseName}}</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
          </el-row>
        </el-form>
      </div>
      <div id="cs_FormFooter" class="el-form-footer" slot="footer">
        <el-button :loading='submitLoading' @click="$closeFn">{{ $t('close') }}</el-button>
        <el-button size="medium" :loading='submitLoading' @click='placeOrder(10, 11)'>{{ $t('draft') }}</el-button>
        <el-button type="primary" :loading='submitLoading' @click='placeOrder(11, 13)'>{{ $t('forecast') }}</el-button>
      </div>
    </div>
    <el-drawer
      :visible.sync="drawerVisible"
      direction="rtl"
      ref="drawer"
      :destroy-on-close='true'
      :wrapperClosable='false'
      :append-to-body='true'
      size='15%'
    >
      <template slot="title">
        <span>{{$t('fba.moreProductInfo')}}</span>
      </template>
      <div>
        <el-divider></el-divider>
        <el-form :model="rowDrawerData" ref='rowDrawerDataForm' label-width="90px" key='1' style='padding: 0px 10px;'>
          <el-form-item :label="$t('fba.sku')" >
            <span class='rowDrawerDataSku' v-text='rowDrawerData.sku'></span>
          </el-form-item>
          <el-form-item :label="$t('fba.declareUnit')">
            <el-select v-model="rowDrawerData.declareUnit" >
              <el-option v-for="(item, index) in declareUnitList" :key="index" :label="item.dictName" :value="item.dictValue"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('fba.countryOfOrigin')" prop='origin' :rules='packageRule.origin'>
            <el-input v-model="rowDrawerData.origin" :placeholder="$t('fba.countryOfOrigin')" ></el-input>
          </el-form-item>
          <el-form-item :label="$t('fba.material')" prop='material' :rules='packageRule.material'>
            <el-input v-model="rowDrawerData.material" :placeholder="$t('fba.material')" ></el-input>
          </el-form-item>
          <el-form-item :label="$t('fba.specificationAndModel')" prop='productModel' :rules='packageRule.productModel'>
            <el-input v-model="rowDrawerData.productModel" :placeholder="$t('fba.specificationAndModel')" ></el-input>
          </el-form-item>
          <el-form-item :label="$t('fba.purpose')"  prop='purpose' :rules='packageRule.purpose'>
            <el-input v-model="rowDrawerData.purpose" :placeholder="$t('fba.purpose')" ></el-input>
          </el-form-item>
          <el-form-item :label="$t('fba.brand')"  prop='brand' :rules='packageRule.brand'>
            <el-input v-model="rowDrawerData.brand" :placeholder="$t('fba.brand')" ></el-input>
          </el-form-item>
          <el-form-item :label="$t('fba.productUrl')"  prop='productUrl' :rules='packageRule.productUrl'>
            <el-input v-model="rowDrawerData.productUrl" :placeholder="$t('fba.productUrl')" ></el-input>
          </el-form-item>
          <el-form-item :label="$t('fba.pickingRemark')" prop='pickingRemark' :rules='packageRule.pickingRemark'>
            <el-input v-model="rowDrawerData.pickingRemark"  :maxlength="255" show-word-limit :autosize="{ minRows: 4}" type="textarea"  :placeholder="$t('fba.pickingRemark')" ></el-input>
          </el-form-item>
        </el-form>
        <div style='justify-content: center;display: flex;'>
          <el-button class='rowDrawerDataButton' @click="$refs.drawer.closeDrawer()">{{$t('close')}}</el-button>
          <el-button class='rowDrawerDataButton' type="primary" @click="saveRowDrawerData" >{{ $t('save') }}</el-button>
        </div>
      </div>
    </el-drawer>
    <inputBoxInfoDialog ref='inputBoxInfoDialog'  @closeInputBoxInfo='closeInputBoxInfo' @inputBoxInfoEmit='inputBoxInfoEmit' v-if='inputBoxInfoDialogShow' ></inputBoxInfoDialog>
    <returnListDialog ref='returnListDialog'  @closeReturnListDialog='closeReturnListDialog' v-if='returnListDialogShow' ></returnListDialog>

    <importBoxCargoExcel ref='importBoxCargoExcel'
                         title="fba.importBoxCargoInfo"
                         :importExcelUrl="setImportExcelUrl"
                         :downLoadAmazonUrl="setDownLoadAmazonUrl"
                         v-if='importExcelVisible'
                         @backImportExcel="importBoxCargoExcel"
                         @refreshDataList='refreshDataAfterImportBoxCargo'
    ></importBoxCargoExcel>
  </div>
</template>

<script>
import dictTypeMixins from '@/mixins/dictTypeMixins'
import listPage from '@/mixins/listPage'
import closeMixins from '@/mixins/closeMixins'
import debounce from 'lodash/debounce'
import api from '@/api'
import baseData from '@/api/baseData'
import areaBox from '@/components/areaBox'
import inputBoxInfoDialog from './input-box-info-dialog'
import returnListDialog from './return-list-dialog'
import importBoxCargoExcel from '@/components/importExcel/fba-import-template'
import Cookies from 'js-cookie'
import { formatterType, gtmToLtm, timestampFormat, multiply } from '@/filters/filters'
import {
  isOverLength,
  postcodeCheck,
  isEmail,
  isPlusInteger2,
  isPlusFloat,
  isDecimal3,
  letterAndNumberLine
} from '@/utils/validate'
import { Consignee } from '@/utils/fieldLength'
import cloneDeep from 'lodash/cloneDeep'
export default {
  mixins: [listPage, dictTypeMixins, closeMixins],
  data () {
    return {
      dataForm: {
        id: null,
        node: null,
        fbaWarehouseCode: null,
        customerOrderNo: null,
        forecastWeightD: '',
        standardUnit: 0,
        parcelType: 10,
        orderLogisticsType: 11,
        lengthUnit: 10,
        weightUnit: 20,
        waybillNo: null,
        packageQty: null,
        shopName: null,
        status: 10,
        logisticsProductCode: null,
        setSConsignee: null,
        declareCurrency: 'USD',
        codCurrency: 'USD',
        taxPayMode: 10,
        customsMethod: 10,
        goodsCategory: 15,
        electricCode: null,
        consignee: {
          fbaWarehouseCode: null,
          consigneeName: null,
          consigneeCompany: null,
          consigneePhone: null,
          consigneeEmail: null,
          consigneeCountry: null,
          consigneeCountryName: null,
          consigneeProvince: null,
          consigneeCity: null,
          consigneeDistrict: null,
          consigneeAddress: null,
          consigneePostcode: null,
          consigneeDoorplate: null,
          consigneeStreet: null,
          consigneeIdcard: null
        },
        shipper: {
          iossTaxType: 10,
          vatNo: null,
          vatCompanyEnName: null,
          vatRegisterCountry: null,
          vatRegisterAddress: null,
          eoriNo: null,
          shipperName: null,
          shipperCompany: null,
          shipperPhone: null,
          shipperEmail: null,
          shipperCountry: null,
          shipperProvince: null,
          shipperCity: null,
          shipperDistrict: null,
          shipperAddress: null,
          shipperPostcode: null,
          shipperDoorplate: null,
          shipperStreet: null
        },
        declareList: [],
        attachmentsList: [],
        boxList: []
      },
      declareForm: {
        declareDataList: []
      },
      opType: '',
      uploadData: {
        id: ''
      },
      rowDrawerData: {
        index: ''
      },
      hasSender: false,
      drawerVisible: false,
      submitLoading: false,
      enterDeclareInfo: true,
      showAllProductBtn: false,
      countrySelectLoading: false,
      noAttachmentsTipsShow: false,
      inputBoxInfoDialogShow: false,
      returnListDialogShow: false,
      declareDataListLoading: false,
      importExcelVisible: false,
      showCustomerBindProductBtn: false,
      fileList: [],
      statusList: [],
      countryList: [],
      boxDataList: [],
      boxNoList: [],
      parcelTypeList: [],
      taxPayModeList: [],
      customsMethodList: [],
      currencyList: [],
      lengthUnitList: [],
      weightUnitList: [],
      allCountryList: [],
      standardUnitList: [],
      declareUnitList: [],
      fbaWarehouseList: [],
      logisticsProductList: [],
      setDeclareByCustomerCodeList: [],
      setConsigneeByCustomerCodeList: [],
      removeAttachmentsIdList: [],
      electricCodeList: [],
      goodsCategoryList: [],
      iossTypeList: [],
      focusInputIndex: 1
    }
  },
  filters: {
    formatterType,
    gtmToLtm,
    timestampFormat,
    multiply
  },
  computed: {
    getForecastWeightD () {
      return this.$naturalNumberFormat(this.dataForm.forecastWeightD)
    },
    setForecastWeightD () {
      return this.$naturalNumberFormat(this.dataForm.forecastWeightD)
    },
    // 赋值导入URL
    setImportExcelUrl() {
      return `${this.$baseUrl}/express/order/importBoxCargo`
    },
    // 下载amazon模板
    setDownLoadAmazonUrl() {
      return `xls/FBA_template_BoxAndCargos_amazon.xlsx`
    },
    panelShow() {
      let ret = false
      if (this.importExcelVisible) {
        ret = true
      }
      return ret
    },
    getToken() {
      return {
        token: Cookies.get('token')
      }
    },
    dataRule () {
      const isLength36 = (rule, value, callback) => {
        if (value && !isOverLength(value, 36)) {
          return callback(new Error(this.$t('validate.isOverLength', { max: 36 })))
        }
        callback()
      }
      const isLength32 = (rule, value, callback) => {
        if (value && !isOverLength(value, 32)) {
          return callback(new Error(this.$t('validate.isOverLength', { max: 32 })))
        }
        callback()
      }
      const isLength30 = (rule, value, callback) => {
        if (value && !isOverLength(value, 30)) {
          return callback(new Error(this.$t('validate.isOverLength', { max: 30 })))
        }
        callback()
      }
      const isLength20 = (rule, value, callback) => {
        if (value && !isOverLength(value, 20)) {
          return callback(new Error(this.$t('validate.isOverLength', { max: 20 })))
        }
        callback()
      }
      const isLength64 = (rule, value, callback) => {
        if (value && !isOverLength(value, 64)) {
          return callback(new Error(this.$t('validate.isOverLength', { max: 64 })))
        }
        callback()
      }
      const isLength255 = (rule, value, callback) => {
        if (value && !isOverLength(value, 255)) {
          return callback(new Error(this.$t('validate.isOverLength', { max: 255 })))
        }
        callback()
      }
      const letterAndNumberLineValidator = (rule, value, callback) => {
        if (value && value.length < 2) {
          return callback(new Error('至少两位邮编'))
        }
        if (value && !postcodeCheck(value)) {
          return callback(new Error('只能用字母，数字，空格，中划线和下划线组合'))
        }
        callback()
      }
      const onlyLetterAndNumberLineValidator = (rule, value, callback) => {
        if (value && !letterAndNumberLine(value)) {
          return callback(new Error('只能用字母，数字，中划线和下划线组合'))
        }
        callback()
      }
      const checkEmail = (rule, value, callback) => {
        if (value && !isEmail(value)) {
          return callback(new Error('邮箱格式错误'))
        }
        callback()
      }
      const isInteger = (rule, value, callback) => {
        if (value && !isPlusInteger2(value)) {
          return callback(new Error('请输入大于0的正整数'))
        }
        callback()
      }
      const isFloat = (rule, value, callback) => {
        if (value && !(isPlusFloat(value) || isPlusInteger2(value))) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value <= 0) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (!isDecimal3(value)) {
          return callback(new Error('限制1~3位小数'))
        }
        callback()
      }
      const taxPayAccountRequired = (rule, value, callback) => {
        if (!value && this.dataForm.taxPayMode === 12) {
          return callback(new Error('若税费模式为“第三方”，则税费支付人账号必填'))
        }
        callback()
      }
      return {
        taxPayAccount: [
          { validator: taxPayAccountRequired, trigger: 'change' }
        ],
        parcelType: [
          { required: true, message: this.$t('validate.required'), trigger: 'change' }
        ],
        logisticsProductCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'change' }
        ],
        customerOrderNo: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isLength36, trigger: 'blur' },
          { validator: onlyLetterAndNumberLineValidator, trigger: 'blur' }
        ],
        waybillNo: [
          { validator: isLength36, trigger: 'blur' }
        ],
        shopName: [
          { max: 32, message: this.$t('validate.isOverLength', { max: 32 }), trigger: ['blur', 'change'] }
          // { whitespace: true, message: this.$t('validate.existSpaces'), trigger: 'blur' }
        ],
        'consignee.consigneeCountry': [
          { required: true, message: this.$t('validate.required'), trigger: 'change' }
        ],
        'consignee.consigneeProvince': [
          // { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { max: Consignee.consigneeProvinceLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeProvinceLength }), trigger: ['blur', 'change'] }
        ],
        'consignee.consigneeCity': [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { max: Consignee.consigneeCityLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeCityLength }), trigger: ['blur', 'change'] }
        ],
        'consignee.consigneeDistrict': [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' },
          { max: Consignee.consigneeDistrictLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeDistrictLength }), trigger: ['blur', 'change'] }
        ],
        'consignee.consigneeDoorplate': [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' },
          { max: Consignee.consigneeDoorplateLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeDoorplateLength }), trigger: ['blur', 'change'] }
        ],
        'consignee.consigneeStreet': [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' },
          { max: Consignee.consigneeStreetLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeStreetLength }), trigger: ['blur', 'change'] }
        ],
        'consignee.consigneeAddress': [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { max: Consignee.consigneeAddressLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeAddressLength }), trigger: ['blur', 'change'] }
        ],
        'consignee.consigneePostcode': [
          // { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: letterAndNumberLineValidator, trigger: 'blur' },
          { max: Consignee.consigneePostcodeLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneePostcodeLength }), trigger: ['blur', 'change'] }
        ],
        'consignee.consigneeName': [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { max: Consignee.consigneeNameLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeNameLength }), trigger: ['blur', 'change'] }
        ],
        'consignee.consigneePhone': [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { max: Consignee.consigneePhoneLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneePhoneLength }), trigger: ['blur', 'change'] }
        ],
        'consignee.consigneeCompany': [
          { max: Consignee.consigneeCompanyLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeCompanyLength }), trigger: ['blur', 'change'] }
        ],
        'consignee.consigneeIdcard': [
          { max: Consignee.consigneeIdcardLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeIdcardLength }), trigger: ['blur', 'change'] }
        ],
        'consignee.consigneeTaxNo': [
          { max: Consignee.consigneeTaxNoLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeTaxNoLength }), trigger: ['blur', 'change'] }
        ],
        'consignee.consigneeEmail': [
          { validator: checkEmail, trigger: 'blur' },
          { max: Consignee.consigneeEmailLength, message: this.$t('validate.isOverLength', { max: Consignee.consigneeEmailLength }), trigger: ['blur', 'change'] }
        ],
        'shipper.shipperCountry': [
          { required: this.hasSender, message: this.$t('validate.required'), trigger: 'change' }
        ],
        'shipper.shipperIdcard': [
          { validator: isLength20, trigger: 'blur' }
        ],
        'shipper.shipperProvince': [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isLength32, trigger: 'blur' }
        ],
        'shipper.shipperCity': [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isLength32, trigger: 'blur' }
        ],
        'shipper.shipperDistrict': [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isLength32, trigger: 'blur' }
        ],
        'shipper.shipperDoorplate': [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isLength32, trigger: 'blur' }
        ],
        'shipper.shipperStreet': [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isLength32, trigger: 'blur' }
        ],
        'shipper.shipperAddress': [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isLength255, trigger: 'blur' }
        ],
        'shipper.shipperPostcode': [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: letterAndNumberLineValidator, trigger: 'blur' },
          { validator: isLength32, trigger: 'blur' }
        ],
        'shipper.shipperName': [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isLength64, trigger: 'blur' }
        ],
        'shipper.shipperPhone': [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isLength30, trigger: 'blur' }
        ],
        'shipper.shipperCompany': [
          { validator: isLength64, trigger: 'blur' }
        ],
        'shipper.shipperEmail': [
          { validator: checkEmail, trigger: 'blur' },
          { validator: isLength20, trigger: 'blur' }
        ],
        standardUnit: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        lengthUnit: [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        weightUnit: [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        packageQty: [
          { required: true, message: this.$t('validate.required'), trigger: 'change' },
          { validator: isInteger, trigger: 'blur' }
        ],
        forecastWeightD: [
          { required: true, message: this.$t('validate.required'), trigger: 'change' },
          { validator: isFloat, trigger: 'blur' }
        ],
        declareCurrency: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        goodsCategory: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    },
    packageRule () {
      const isLength36 = (rule, value, callback) => {
        if (value && !isOverLength(value, 36)) {
          return callback(new Error(this.$t('validate.isOverLength', { max: 36 })))
        }
        callback()
      }
      const isLength2 = (rule, value, callback) => {
        if (value && !isOverLength(value, 2)) {
          return callback(new Error(this.$t('validate.isOverLength', { max: 2 })))
        }
        callback()
      }
      const isLength255 = (rule, value, callback) => {
        if (value && !isOverLength(value, 255)) {
          return callback(new Error(this.$t('validate.isOverLength', { max: 255 })))
        }
        callback()
      }
      const isLength32 = (rule, value, callback) => {
        if (value && !isOverLength(value, 32)) {
          return callback(new Error(this.$t('validate.isOverLength', { max: 32 })))
        }
        callback()
      }
      const isLength64 = (rule, value, callback) => {
        if (value && !isOverLength(value, 64)) {
          return callback(new Error(this.$t('validate.isOverLength', { max: 64 })))
        }
        callback()
      }
      const isInteger = (rule, value, callback) => {
        if (value && !isPlusInteger2(value)) {
          return callback(new Error('请输入大于0的正整数'))
        }
        if (value > Number.MAX_VALUE) {
          return callback(new Error('数值超过最大值[' + Number.MAX_VALUE + ']限制'))
        }
        callback()
      }
      const isFloat = (rule, value, callback) => {
        if (value && !(isPlusFloat(value) || isPlusInteger2(value))) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value && value <= 0) {
          return callback(new Error('请输入大于0的小数或整数'))
        }
        if (value && !isDecimal3(value)) {
          return callback(new Error('限制1~3位小数'))
        }
        callback()
      }
      const isValidBoxNo = (rule, value, callback) => {
        if (!value) {
          callback()
        }
        let sameBoxNoList = this.boxNoList ? this.boxNoList.filter(item => item === value) : []
        if (!sameBoxNoList || sameBoxNoList.length === 0) {
          return callback(new Error('请先录入该箱信息'))
        }
        callback()
      }
      return {
        packageCustomerNo: [
          { required: false, message: this.$t('validate.required'), trigger: 'change' },
          { validator: isLength36, trigger: 'blur' },
          { validator: isValidBoxNo, trigger: 'blur' }
        ],
        sku: [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isLength32, trigger: 'blur' }
        ],
        englishName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isLength64, trigger: 'blur' }
        ],
        chineseName: [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isLength64, trigger: 'blur' }
        ],
        quantity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isInteger, trigger: 'blur' }
        ],
        unitDeclarePriceD: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isFloat, trigger: 'blur' }
        ],
        unitNetWeightD: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isFloat, trigger: 'blur' }
        ],
        hsCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' },
          { validator: isLength36, trigger: 'blur' }
        ],
        origin: [
          { validator: isLength2, trigger: 'blur' }
        ],
        material: [
          { validator: isLength64, trigger: 'blur' }
        ],
        productModel: [
          { validator: isLength32, trigger: 'blur' }
        ],
        purpose: [
          { validator: isLength64, trigger: 'blur' }
        ],
        brand: [
          { validator: isLength32, trigger: 'blur' }
        ],
        productUrl: [
          { validator: isLength255, trigger: 'blur' }
        ],
        pickingRemark: [
          { validator: isLength255, trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    this.getBaseData()
    this.getDict()
  },
  beforeDestory () {
    document.removeEventListener('keydown', this.handleEvent)
  },
  activated () {
    document.addEventListener('keydown', this.handleEvent)
    if (this.$route.query.opType) {
      this.opType = this.$route.query.opType
    }
    // 查询订单详情
    if (this.$route.query.orderId !== undefined && this.$route.query.orderId !== null) {
      this.getOrderInfo(this.$route.query.orderId)
    } else if (this.$route.query.res) {
      this.fillData(this.$route.query.res)
    }
  },
  methods: {
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        this.$refs['dataForm'].resetFields()
      })
    },
    async getDict () {
      // 获取相关字典
      this.customsMethodList = await this.getDictTypeList('OrderCustomsMethod')
      this.taxPayModeList = await this.getDictTypeList('OrderTaxPayMode')
      this.parcelTypeList = await this.getDictTypeList('OrderParcelType')
      this.standardUnitList = await this.getDictTypeList('standardUnit') // 0=公制 1=英制
      this.declareUnitList = await this.getDictTypeList('declareUnit')
      this.electricCodeList = await this.getDictTypeList('electricCode')
      this.goodsCategoryList = await this.getDictTypeList('OrderGoodsCategory') // 物品种类
      this.iossTypeList = await this.getDictTypeList('IossType') // IOSS类型
      await this.getDictTypeList('lengthUnit').then((res) => {
        this.lengthUnitList = res.filter(item => item.dictValue === 10 || item.dictValue === 30) // 10=厘米 30=英寸
      })
      await this.getDictTypeList('weightUnit').then((res) => {
        this.weightUnitList = res.filter(item => item.dictValue === 20 || item.dictValue === 40) // 20=千克 40=磅
      })
    },
    parcelTypeChange () {
      if (Number(this.dataForm.parcelType) === 12) {
        this.dataForm.packageQty = 1
        this.inputBoxCountDisable = true
        if (this.boxDataList && this.boxDataList.length > 0) {
          let firstBox = this.boxDataList.shift()
          this.boxDataList = [firstBox]
          this.dataForm.forecastWeightD = firstBox.packageWeightD || 0.3
        } else {
          this.dataForm.forecastWeightD = 0.3
        }
        if (this.declareForm.declareDataList && this.declareForm.declareDataList <= 0) {
          let item = {
            chineseName: 'N-PLURAL',
            englishName: 'N-PLURAL',
            quantity: 1,
            unitNetWeightD: 0.1,
            declareUnit: 10,
            unitDeclarePriceD: 1,
            hsCode: '000000'
          }
          this.declareForm.declareDataList.push(item)
        }
      } else {
        this.inputBoxCountDisable = false
      }
    },
    selectSetConsignee(id) {
      let consigneeArray = this.setConsigneeByCustomerCodeList
      for (let i = 0; i < consigneeArray.length; i++) {
        if (id === consigneeArray[i].id) {
          this.dataForm.consignee.consigneeName = consigneeArray[i].consigneeName
          this.dataForm.consignee.consigneeCompany = consigneeArray[i].consigneeCompany
          this.dataForm.consignee.consigneePhone = consigneeArray[i].consigneePhone
          this.dataForm.consignee.consigneeEmail = consigneeArray[i].consigneeEmail
          this.dataForm.consignee.consigneeCountry = consigneeArray[i].consigneeCountry
          this.dataForm.consignee.consigneeProvince = consigneeArray[i].consigneeProvince
          this.dataForm.consignee.consigneeCity = consigneeArray[i].consigneeCity
          this.dataForm.consignee.consigneeDistrict = consigneeArray[i].consigneeDistrict
          this.dataForm.consignee.consigneeAddress = consigneeArray[i].consigneeAddress
          this.dataForm.consignee.consigneePostcode = consigneeArray[i].consigneePostcode
          this.dataForm.consignee.consigneeDoorplate = consigneeArray[i].consigneeDoorplate
          this.dataForm.consignee.consigneeStreet = consigneeArray[i].consigneeStreet
          this.dataForm.consignee.consigneeTaxNo = consigneeArray[i].consigneeTaxNo
          this.dataForm.consignee.consigneeIdcard = consigneeArray[i].consigneeIdcard
          return
        }
      }
    },
    selectSetShipper (data) {
      let shipperArray = this.setShipperByCustomerCodeList
      for (let i = 0; i < shipperArray.length; i++) {
        if (data === shipperArray[i].id) {
          this.dataForm.shipper.shipperName = shipperArray[i].shipperName
          this.dataForm.shipper.shipperCompany = shipperArray[i].shipperCompany
          this.dataForm.shipper.shipperPhone = shipperArray[i].shipperContact
          this.dataForm.shipper.shipperEmail = shipperArray[i].shipperEmail
          this.dataForm.shipper.shipperCountry = shipperArray[i].shipperCountryCode
          this.dataForm.shipper.shipperProvince = shipperArray[i].shipperProvince
          this.dataForm.shipper.shipperCity = shipperArray[i].shipperCity
          this.dataForm.shipper.shipperDistrict = shipperArray[i].shipperDistrict
          this.dataForm.shipper.shipperAddress = shipperArray[i].shipperAddress
          this.dataForm.shipper.shipperPostcode = shipperArray[i].shipperPostcode
          this.dataForm.shipper.shipperDoorplate = shipperArray[i].shipperDoorplate
          this.dataForm.shipper.shipperStreet = shipperArray[i].shipperStreet
          return
        }
      }
    },
    handleEvent (event) {
      if (event.keyCode === 66 && event.ctrlKey) { // 监听Ctrl + B键
        this.inputBoxInfoDialogShow = !this.inputBoxInfoDialogShow
        if (this.inputBoxInfoDialogShow) {
          this.inputBoxInfo()
        }
      } else if (event.keyCode === 13) { // 监听回车键
        this.$nextTick(() => {
          let inputs = document.getElementsByClassName('el-input__inner')
          for (let i = this.focusInputIndex; i < inputs.length; i++) {
            if (i === inputs.length - 1) {
              i = 1
              this.focusInputIndex = i
              inputs[i].focus()
              break
            }
            if (inputs[i].value) {
              continue
            }
            inputs[i].focus()
            if (inputs[i].placeholder === 'Email' && this.declareForm.declareDataList.length > 0) {
              i = i + 2
              this.focusInputIndex = i + 2
            } else if (inputs[i].placeholder === 'Email' && !this.declareForm.declareDataList.length) {
              this.focusInputIndex = 1
              i = 0
            } else {
              this.focusInputIndex = i + 1
            }
            break
          }
        })
      }
    },
    // 自定义列显示
    settingCol (tableColumns) {
      this.$nextTick(() => {
        this.$refs['tableSet'].init(this.tableName, tableColumns)
      })
    },
    handleChange(file, fileList) {
      if (file.length <= 0) {
        return
      }
      this.fileList.push(file.raw)
    },
    handleResize () {
      this.$nextTick(() => {
        this.$refs.tableData.doLayout()
      })
    },
    destroyed () {
      window.removeEventListener('resize', this.handleResize)
    },
    mounted () {
      window.addEventListener('resize', this.handleResize)
    },
    handleBeforeRemove(file, fileList) {
      this.fileList.splice(file.raw, 1)
    },
    // 上传失败
    errorHandle (error) {
      console.log('error', error)
      let myError = error.toString()
      myError = myError.replace('Error: ', '')
      console.log('myError')
      return this.$message({
        dangerouslyUseHTMLString: true,
        message: myError || '发生错误',
        type: 'error',
        duration: 2500
      })
    },
    httpRequest(param) {
      this.fileList.push(param.file)
    },
    removeAttachmentsUploaded(item, index) {
      this.dataForm.attachmentsList.splice(index, 1)
      this.removeAttachmentsIdList.push(item.id)
    },
    handlePreview(file) {
      // 其他格式 pdf、docx、xlsx等，pdf可直接预览，其他格式会直接下载，预览的话需要插件。
      var reader = new FileReader()
      reader.readAsDataURL(file)

      reader.onload = function() {
        var base64Img = reader.result
        var byteString
        if (base64Img.split(',')[0].indexOf('base64') >= 0) {
          byteString = atob(base64Img.split(',')[1])
        } else {
          byteString = unescape(base64Img.split(',')[1])
        }
        var mimeString = base64Img
          .split(',')[0]
          .split(':')[1]
          .split(';')[0]
        var ia = new Uint8Array(byteString.length)
        for (var i = 0; i < byteString.length; i++) {
          ia[i] = byteString.charCodeAt(i)
        }
        var blob = new Blob([ia], {
          type: mimeString
        })
        // for IE 兼容IE，弹出用户操作框，用户可自行选择下载或打开
        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
          window.navigator.msSaveOrOpenBlob(blob)
        } else {
          let fileURL = URL.createObjectURL(blob)
          // 会生成类似 blob:XXX/XXXXX-XXXXX-XXXXX-XXXXX-526fc462d662 地址，并在新标签页打开，可下载
          window.open(fileURL)
        }
        // var win = window.open()
        // var temp = "<body style='margin:0px;'><object data='" + reader.result + "' type='application/pdf' width='100%' height='100%'><iframe src='" + reader.result + "' width='100%' height='100%' style='border: none;'></iframe></object></body>"
        // win.document.write(temp)
      }
    },
    openUrl(item) {
      window.open(item.url)
    },
    async getBaseData () {
      this.logisticsProductList = await baseData('/co/setcustomerlogisticsproduct/list?logisticsType=11&status=1').then((data) => {
        return new Promise(resolve => {
          if (!data) {
            return resolve([])
          }
          let array = []
          for (let i = 0; i < data.length; i++) {
            array.push({
              name: data[i].productName,
              code: data[i].productCode
            })
          }
          return resolve(array)
        }).then(async (arr) => {
          if (!arr || arr.length === 0) {
            this.showAllProductBtn = false
            this.showCustomerBindProductBtn = false
            arr = await baseData(api.enableLogisticsProductByCurrent + '?logisticsType=11')
          } else {
            this.showAllProductBtn = true
          }
          return arr
        })
      }).catch(() => {})
      await baseData(api.countryList).then((data) => {
        this.countryList = data
        this.allCountryList = data
      })
      this.currencyList = await baseData(api.currencyList)
      this.setShipperByCustomerCodeList = await baseData(api.setShipperByCustomerCodeList + this.$store.state.user.customerId)
      this.setConsigneeByCustomerCodeList = await baseData(api.setConsigneeByCustomerCodeList(this.$store.state.user.customerId))
      this.setDeclareByCustomerCodeList = await baseData(api.setDeclareByCustomerCodeList + this.$store.state.user.customerId)
      this.fbaWarehouseList = await baseData(api.fbaWarehouseList, { status: 1 })
    },
    openDrawer(rowData, rowDataIndex) {
      this.rowDrawerData = rowData
      this.rowDrawerData.index = rowDataIndex
      this.drawerVisible = true
    },
    hasSenderChange () {
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate()
      })
    },
    saveRowDrawerData () {
      this.declareForm.declareDataList.splice(this.rowDrawerData.index, 1, this.rowDrawerData)
      this.$nextTick(() => {
        this.$refs.drawer.closeDrawer()
      })
    },
    addRedStar(h, { column }) {
      return [h('span', { style: 'color: red' }, '*'), h('span', ' ' + column.label)]
    },
    selectSetDeclare (id) {
      let declareArray = this.setDeclareByCustomerCodeList
      for (let i = 0; i < declareArray.length; i++) {
        if (id === declareArray[i].id) {
          let selectItem = declareArray[i]
          let obj = {
            ...selectItem,
            id: null,
            declareUnit: 10,
            packageDeliveryNo: null,
            update: true
          }
          this.declareForm.declareDataList.push(obj)
          return
        }
      }
    },
    addRow (id) {
      if (id && typeof id !== 'object') {
        this.selectSetDeclare(id)
        return true
      }
      let obj = {
        id: null,
        packageDeliveryNo: null,
        sku: null,
        englishName: null,
        chineseName: null,
        quantity: null,
        unitDeclarePriceD: null,
        unitDeclareWeightD: null,
        hsCode: null,
        declareUnit: 10,
        update: true
      }
      this.declareForm.declareDataList.push(obj)
      this.total = this.total >= 0 ? this.declareForm.declareDataList.length : this.total
    },
    deleteRow (index) {
      this.declareForm.declareDataList.splice(index, 1)
    },
    saveRow (scope, action) {
      if (!this.boxDataList || this.boxDataList.length === 0) {
        this.$message({
          message: this.$t('fba.boxInfoInputTips'),
          type: 'warning',
          duration: 2000
        })
        return false
      }
      if (action === 'update') {
        let arr = ['declareDataList.' + scope.$index + '.packageCustomerNo',
          'declareDataList.' + scope.$index + '.sku',
          'declareDataList.' + scope.$index + '.englishName',
          'declareDataList.' + scope.$index + '.chineseName',
          'declareDataList.' + scope.$index + '.quantity',
          'declareDataList.' + scope.$index + '.unitDeclarePriceD',
          'declareDataList.' + scope.$index + '.unitNetWeightD',
          'declareDataList.' + scope.$index + '.hsCode']
        let validatedMsgList = []
        this.$refs.declareForm.validateField(arr, (error) => {
          if (error) {
            validatedMsgList.push(error)
          }
        })
        if (validatedMsgList.every((item) => item === '')) {
          this.$nextTick(() => {
            this.$set(scope.row, 'update', !scope.row.update)
          })
        } else {
          this.$message({
            message: this.$t('fba.boxInfoInputTips'),
            type: 'warning',
            duration: 2000
          })
        }
      } else {
        this.$nextTick(() => {
          this.$set(scope.row, 'update', !scope.row.update)
        })
      }
    },
    copyRow (row) {
      let obj = {
        id: null,
        ...row
      }
      obj.update = true
      obj.packageCustomerNo = null
      this.declareForm.declareDataList.push(obj)
    },
    inputBoxInfo () {
      this.inputBoxInfoDialogShow = true
      this.$nextTick(() => {
        this.$refs.inputBoxInfoDialog.init(this.dataForm.customerOrderNo, cloneDeep(this.boxDataList), this.dataForm.id)
      })
    },
    isReturnList () {
      this.returnListDialogShow = true
      this.$nextTick(() => {
        this.$refs.returnListDialog.init()
      })
    },
    closeInputBoxInfo () {
      this.inputBoxInfoDialogShow = false
    },
    closeReturnListDialog () {
      this.returnListDialogShow = false
    },
    inputBoxInfoEmit (boxDataList, totalWeightD, totalBox, boxNoList) {
      this.$nextTick(() => {
        this.boxDataList = boxDataList
        this.boxNoList = boxNoList
        this.dataForm.packageQty = totalBox
        this.dataForm.forecastWeightD = totalWeightD || ''
        this.$refs.inputBoxInfoDialog.submitLoading = false
        this.inputBoxInfoDialogShow = false
      })
    },
    standardUnitChange (value) {
      this.$set(this.dataForm, 'standardUnit', value)
      if (this.dataForm.standardUnit === 0) {
        this.$set(this.dataForm, 'lengthUnit', 10)
        this.$set(this.dataForm, 'weightUnit', 20)
      } else {
        this.$set(this.dataForm, 'lengthUnit', 30)
        this.$set(this.dataForm, 'weightUnit', 40)
      }
    },
    countrySelectChange (country) {
      if (this.dataForm.fbaWarehouseCode && country) {
        let fbaWarehouseItem = this.fbaWarehouseList.filter((item) => { return item.code === this.dataForm.fbaWarehouseCode })[0]
        let countryItem = this.allCountryList.filter((item) => { return item.code === fbaWarehouseItem.country })[0]
        if (countryItem && countryItem.code !== country) {
          this.clearConsigneeInfoAndFbaWarehouseCode()
        }
      }
    },
    fbaWarehouseChange (code) {
      if (code) {
        let arr = this.fbaWarehouseList.filter((item) => {
          return item.code === code
        })
        if (arr.length > 0) {
          let item = arr[0]
          this.dataForm.fbaWarehouseCode = item.code
          this.dataForm.consignee.consigneeName = item.contact
          this.dataForm.consignee.fbaWarehouseCode = item.code
          this.dataForm.consignee.consigneePhone = item.phone
          this.dataForm.consignee.consigneePostcode = item.postcode
          this.dataForm.consignee.consigneeProvince = item.province
          this.dataForm.consignee.consigneeDistrict = item.district
          this.dataForm.consignee.consigneeCity = item.city
          this.dataForm.consignee.consigneeAddress = item.street
          this.countryList = this.allCountryList
          this.dataForm.consignee.consigneeCountry = item.country
        } else {
          this.clearConsigneeInfoAndFbaWarehouseCode()
        }
      } else {
        this.clearConsigneeInfoAndFbaWarehouseCode()
      }
    },
    clearConsigneeInfoAndFbaWarehouseCode () {
      this.dataForm.fbaWarehouseCode = null
      this.dataForm.consignee.consigneeName = null
      this.dataForm.consignee.consigneePhone = null
      this.dataForm.consignee.consigneePostcode = null
      this.dataForm.consignee.consigneeProvince = null
      this.dataForm.consignee.consigneeDistrict = null
      this.dataForm.consignee.consigneeCity = null
      this.dataForm.consignee.consigneeAddress = null
    },
    getCountryListByCodeOrName (codeOrName) {
      if (!codeOrName) {
        return
      }
      this.countrySelectLoading = true
      setTimeout(() => {
        this.$http.get(`/bd/region/listByCodeOrName?codeOrName=` + codeOrName).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.countryList = res.data
        }).catch(() => {
        }).finally(() => {
          this.countrySelectLoading = false
        })
      }, 200)
    },
    async showAllProductHandle () {
      this.logisticsProductList = await baseData(api.enableLogisticsProductByCurrent + '?logisticsType=11')
      this.showCustomerBindProductBtn = true
      this.showAllProductBtn = false
    },
    async showCustomerBindProductHandle () {
      this.logisticsProductList = await baseData('/co/setcustomerlogisticsproduct/list?logisticsType=11&status=1').then((data) => {
        let array = []
        for (let i = 0; i < data.length; i++) {
          array.push({
            name: data[i].productName,
            code: data[i].productCode
          })
        }
        return array
      }).catch(() => {}).finally(() => {
        this.showAllProductBtn = true
        this.showCustomerBindProductBtn = false
      })
    },
    getOrderInfo (id) {
      if (!id) {
        return
      }
      this.$http.get('/co/order/' + id).then(async ({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = res.data
        if (res.data.shipper.shipperCountry) {
          this.hasSender = true
        }
        this.declareForm.declareDataList = res.data.orderDeclareList
        this.boxDataList = res.data.packageList
        this.boxNoList = res.data.boxNoList
        // 黑名单的物流产品不给复制
        await baseData(api.enableLogisticsProductByCurrent + '?logisticsType=11').then((arr) => {
          if (!arr.find(item => item.code === this.dataForm.logisticsProductCode)) {
            this.dataForm.logisticsProductCode = null
            this.$set(this.dataForm, 'logisticsProductCode', null)
            if (!this.logisticsProductList || this.logisticsProductList.length <= 0) {
              this.logisticsProductList = arr
            }
          }
        })
        if (this.dataForm.logisticsProductCode && !this.logisticsProductList.find(item => item.code === res.data.logisticsProductCode)) {
          let obj = { 'code': res.data.logisticsProductCode, 'name': res.data.logisticsProductName }
          this.logisticsProductList.push(obj)
        }
        this.standardUnitChange(this.dataForm.standardUnit)
        if (this.$route.query.opType === 'express.header.copy') {
          this.dataForm.id = null
          this.dataForm.customerOrderNo = ''
          this.dataForm.attachmentsList = []
          //
          this.boxNoList = []
          this.boxDataList.forEach(pack => {
            pack.packageCustomerNo = ''
            pack.subCustomerOrderNo = ''
            pack.curAllPackageIdSerialNoBoxNoMap = {}
          })
          console.log(JSON.stringify(this.dataForm))
          // this.declareForm.declareDataList.forEach((item) => {
          //   item.packageCustomerNo = ''
          // })
          // this.dataForm.packageQty = ''
          // this.$nextTick(() => {
          //   this.$refs.dataForm.clearValidate()
          // })
          // this.dataForm.waybillNo = ''
          // this.boxDataList = []
          // this.declareForm.declareDataList = []
          // this.dataForm.forecastWeightD = ''
        }
        this.standardUnitChange(res.data.standardUnit)
      }).catch(() => {})
    },
    fillData (res) {
      this.dataForm = res.data
      if (res.data.shipper) {
        if (res.data.shipper.shipperCountry) {
          this.hasSender = true
        }
      }

      this.declareForm.declareDataList = res.data.orderDeclareList
      this.boxDataList = res.data.packageList
      // this.boxNoList = res.data.boxNoList
      if (this.dataForm.logisticsProductCode && !this.logisticsProductList.find(item => item.code === res.data.logisticsProductCode)) {
        let obj = { 'code': res.data.logisticsProductCode, 'name': res.data.logisticsProductName }
        this.logisticsProductList.push(obj)
      }
      this.standardUnitChange(this.dataForm.standardUnit)
      if (this.$route.query.opType === 'express.header.copy') {
        this.dataForm.id = null
        this.dataForm.customerOrderNo = ''
        this.dataForm.attachmentsList = []
        // this.declareForm.declareDataList.forEach((item) => {
        //   item.packageCustomerNo = ''
        // })
        this.dataForm.packageQty = res.data.packageList.length
        this.$nextTick(() => {
          this.$refs.dataForm.clearValidate()
        })
        this.dataForm.waybillNo = ''
        // this.boxDataList = []
        this.declareForm.declareDataList = []
        this.dataForm.forecastWeightD = res.data.forecastWeightD
      }
      this.standardUnitChange(res.data.standardUnit)
    },
    getOrderPackage () {
      if (!this.dataForm.id) {
        return
      }
      this.declareDataListLoading = true
      this.$http.get(
        '/co/orderpackage/page',
        {
          params: {
            order: this.order,
            orderField: this.orderField,
            page: this.page,
            limit: this.limit,
            orderId: this.dataForm.id
          }
        }
      ).then(({ data: res }) => {
        this.declareDataListLoading = false
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.declareForm.declareDataList = res.data.list
        this.total = res.data.total
        this.declareForm.declareDataList.forEach((item) => {
          this.$set(item, 'update', false)
        })
      }).catch(() => {
        this.declareDataListLoading = false
      })
    },
    dataFormValidate () {
      let self = this
      return new Promise(function(resolve, reject) {
        self.$refs['dataForm'].validate((valid) => {
          if (!valid) {
            self.submitLoading = false
            self.$message({
              message: self.$t('fba.baseInfoInputTips'),
              type: 'warning',
              duration: 2500
            })
            setTimeout(() => {
              let isError = document.getElementsByClassName('is-error')
              isError[0].querySelector('input').focus()
            }, 100)
            return false
          }
          resolve()
        })
      })
    },
    checkBoxNo () {
      let boxNos = this.boxNoList.filter(item => {
        if (item) {
          return item
        }
      })
      return new Promise((resolve, reject) => {
        if (!boxNos || boxNos.length <= 0) {
          return resolve()
        }
        this.$http.get(`/co/orderpackage/list?packageCustomerNos=` + boxNos).then(({ data: res }) => {
          let repeatBoxNos = [...new Set(res.data.map(item => {
            if (item.orderId !== this.dataForm.id) {
              return item.packageCustomerNo
            }
          }))]
          repeatBoxNos = repeatBoxNos.filter(item => item !== undefined)
          if (repeatBoxNos && repeatBoxNos.length > 0) {
            reject(this.$message({
              message: '箱号/FBA唛头：' + repeatBoxNos + '已被使用',
              type: 'warning',
              duration: 2000
            }))
            this.submitLoading = false
          } else {
            resolve()
          }
        })
      })
    },
    declareFormValidate () {
      let self = this
      return new Promise(function(resolve, reject) {
        self.$refs['declareForm'].validate((valid) => {
          if (!valid) {
            self.submitLoading = false
            self.$message({
              message: '请把箱货信息正确填写完整',
              type: 'warning',
              duration: 2500,
              offset: 70
            })
            setTimeout(() => {
              let isError = document.getElementsByClassName('is-error')
              isError[0].querySelector('input').focus()
            }, 100)
            return false
          }
          resolve()
        })
      })
    },
    placeOrder: debounce(function (status, node) {
      this.submitLoading = true
      let validatorArr = []
      if (this.enterDeclareInfo) {
        validatorArr = [this.dataFormValidate(), this.declareFormValidate(), this.checkBoxNo()]
      } else {
        validatorArr = [this.dataFormValidate(), this.checkBoxNo()]
      }
      Promise.all(validatorArr).then(() => {
        if (this.enterDeclareInfo && this.declareForm.declareDataList && this.declareForm.declareDataList.length === 0) {
          this.submitLoading = false
          return this.$message.error('请录入货品信息')
        }
        this.dataForm.node = node
        this.dataForm.status = status
        this.dataForm.declareList = this.enterDeclareInfo ? this.declareForm.declareDataList : []
        this.dataForm.boxList = this.boxDataList
        let reqData = new FormData()
        this.fileList.forEach(function(file) {
          reqData.append('fileList', file, file.name)
        })
        reqData.append('removeAttachmentsIdList', this.removeAttachmentsIdList)
        for (let key in this.dataForm) {
          if (key === 'consignee') {
            for (let consigneeEleKey in this.dataForm['consignee']) {
              reqData.append('consignee.' + consigneeEleKey, this.dataForm['consignee'][consigneeEleKey] || '')
            }
          } else if (key === 'shipper') {
            for (let shipperEleKey in this.dataForm['shipper']) {
              reqData.append('shipper.' + shipperEleKey, this.dataForm['shipper'][shipperEleKey] || '')
            }
          } else if (key === 'declareList') {
            this.dataForm.declareList.forEach((value, index) => {
              reqData.append(`declareList[${index}].id`, value.id || '')
              reqData.append(`declareList[${index}].packageCustomerNo`, value.packageCustomerNo || '')
              reqData.append(`declareList[${index}].chineseName`, value.chineseName || '')
              reqData.append(`declareList[${index}].declareUnit`, value.declareUnit || '')
              reqData.append(`declareList[${index}].englishName`, value.englishName || '')
              reqData.append(`declareList[${index}].quantity`, value.quantity || '')
              reqData.append(`declareList[${index}].unitNetWeightD`, value.unitNetWeightD || '')
              reqData.append(`declareList[${index}].unitDeclarePriceD`, value.unitDeclarePriceD || '')
              reqData.append(`declareList[${index}].brand`, value.brand || '')
              reqData.append(`declareList[${index}].sku`, value.sku || '')
              reqData.append(`declareList[${index}].productModel`, value.productModel || '')
              reqData.append(`declareList[${index}].hsCode`, value.hsCode || '')
              reqData.append(`declareList[${index}].material`, value.material || '')
              reqData.append(`declareList[${index}].purpose`, value.purpose || '')
              reqData.append(`declareList[${index}].origin`, value.origin || '')
              reqData.append(`declareList[${index}].productUrl`, value.productUrl || '')
              reqData.append(`declareList[${index}].pickingRemark`, value.pickingRemark || '')
            })
          } else if (key === 'boxList') {
            this.dataForm.boxList.forEach((value, index) => {
              reqData.append(`boxList[${index}].id`, value.id || '')
              reqData.append(`boxList[${index}].packageSerialNo`, value.packageSerialNo || '')
              reqData.append(`boxList[${index}].packageCustomerNo`, value.packageCustomerNo || '')
              reqData.append(`boxList[${index}].subCustomerOrderNo`, value.subCustomerOrderNo || '')
              reqData.append(`boxList[${index}].packageDeliveryNo`, value.packageDeliveryNo || '')
              reqData.append(`boxList[${index}].packageWeightD`, value.packageWeightD || '')
              reqData.append(`boxList[${index}].packageLengthD`, value.packageLengthD || '')
              reqData.append(`boxList[${index}].packageWidthD`, value.packageWidthD || '')
              reqData.append(`boxList[${index}].packageHeightD`, value.packageHeightD || '')
              reqData.append(`boxList[${index}].packageQty`, value.packageQty || '')
              reqData.append(`boxList[${index}].curAllPackageIdSerialNoBoxNoMapJson`, JSON.stringify(value.curAllPackageIdSerialNoBoxNoMap) || '')
            })
          } else if (key === 'standardUnit') {
            reqData.append(key, this.dataForm[key])
          } else {
            reqData.append(key, this.dataForm[key] || '')
          }
        }
        reqData.append('hasDeclareInfo', this.enterDeclareInfo)
        this.$http.post(this.dataForm.id ? '/express/order/updateOrder' : '/express/order/placeOrder', reqData).then(({ data: res }) => {
          if (res.code !== 0) {
            this.dataForm.status = 10
            return this.$message.error(res.msg)
          } else if (res.data.failureNum > 0) {
            this.dataForm.status = 10
            return this.$message.error(res.data.failureList[0].message)
          }
          if (res.code === 0) {
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.$refs['dataForm'].resetFields()
                this.$refs['declareForm'].resetFields()
                this.boxDataList = []
                this.declareForm.declareDataList = []
                this.fileList = []
                this.isReturnList()
              }
            })
          }
        }).finally(() => {
          this.submitLoading = false
        })
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    refreshDataAfterImportBoxCargo (data) {
      this.dataForm.forecastWeightD = data.forecastWeightD
      this.dataForm.packageQty = data.packageQty
      this.declareForm.declareDataList = data.declareList
      this.boxDataList = data.boxList
    },
    // 导入箱货
    importBoxCargoInfo () {
      this.importExcelVisible = true
      this.$nextTick(() => {
        this.$refs.importBoxCargoExcel.init()
      })
    },
    importBoxCargoExcel () {
      this.importExcelVisible = false
    }
  },
  components: {
    areaBox,
    inputBoxInfoDialog,
    returnListDialog,
    importBoxCargoExcel
  }
}
</script>
<style lang="scss" scoped>
/deep/ .el-button-group .el-button:not(:last-child) {
  padding-bottom: 7px !important;
}
/deep/ .el-button-group .el-button--primary {
  color: #409EFF;
  background: #ecf5ff;
  border-color: #b3d8ff;
}
/deep/ .el-button-group .el-button--primary:hover {
  background: #409EFF;
  border-color: #409EFF;
  color: #FFF;
}
/deep/.el-dropdown .el-dropdown__caret-button::before {
  background: #409eff !important;
}
/deep/ .el-button-group .el-button:first-child {
  width: 86%;
}
/deep/ .el-button-group .el-button:last-child {
  width: 14%;
}
/deep/ .el-icon-arrow-down:before {
  content: "常用报关项\3\E6DF" !important;
}

::v-deep  #enterDeclareInfo{
  transform: scale(1) !important;
  color: #eeb87e !important;
}

 ::v-deep .el-checkbox {
    transform: scale(0.83) !important;
    color: rgba(16,22,26,0.4) !important;
  }
 ::v-deep .el-form-item {
   margin-bottom: 22px !important;
 }
 ::v-deep #declareFormId .el-form-item{
   margin-bottom: 0px !important;
 }
  .logisticsproductcode-select-btn {
    width: 100% !important;
    text-align: center !important;
    border: 0px !important;
    color: cornflowerblue !important;
  }
  .el-divider--horizontal {
    display: block;
    height: 2px !important;
    width: 100%;
    margin: 5px 0 !important;
  }
  .el-select-dropdown__item_btn {
    font-size: 12px;
    padding: 0 20px;
    position: relative;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #606266;
    height: 34px;
    line-height: 34px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    cursor: pointer;
  }
  .el-checkbox.is-bordered.el-checkbox--mini {
    padding: 6px 15px 3px 10px;
    border-radius: 3px;
    height: 28px;
  }
 /deep/ .splitpanes--vertical>.splitpanes__splitter:before {
  width: 0px !important;
}
/deep/  .default-theme .splitpanes--vertical>.splitpanes__splitter:after {
  width: 0px !important;
}
  #fbaWarehouseSelect ::v-deep .el-input--suffix .el-input__inner {
  font-size: 13px !important;
}
::v-deep .el-input-group__append, .el-input-group__prepend {
  color: $--color-primary;
  background-color: #ecf5ff;
  &:hover {
    color: white;
    background-color: $--color-primary;
    }
}
::v-deep .area-box {
  position: relative;
  margin-top: 5px;
  padding: 0px 10px 10px;
  border: 1px solid #DCDFE6;
}
.el-upload__tip {
  font-size: 1px;
  color: #ada9a9;
  margin-left: 0px !important;
  display: inline;
  /* margin-bottom: 62px; */
}
.maxContentLength {
  width: max-content
}
  ::v-deep  .el-drawer__close-btn {
    border: 1px solid $--color-primary;
    color: #1890ff !important;
    cursor: pointer;
    font-size: 20px;
    background-color: transparent;
    border-radius: 17px;
  }
  ::v-deep  .el-drawer__header {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    color: black;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: 35px;
    padding: 20px 20px 0;
    font-size: 18px;
    font-weight: bolder;
  }
  .rowDrawerDataSku{
    color: $--color-primary;
    font-size: 16px;
    font-weight: bolder;
  }
  .rowDrawerDataButton{
    min-width: 100px;
  }
  .addRowBtn{
    width: 100%;
    margin: 10px 0px;
  }
  .add-body {
    #declareFormId{
      ::v-deep .el-form-item {
        margin-bottom: 0px;
      }
    }
  }
 ::v-deep .el-input.is-disabled .el-input__inner {
   color: #050d1c !important;
 }
 ::v-deep .el-radio-button__inner, .el-radio-group {
   display: inline-flex !important;
 }
 ::v-deep .el-input-group>.el-input__inner {
   min-width: 7vw !important;
 }
 ::v-deep .el-table th.gutter {
   display :table-cell !important
 }
</style>
