<template>
  <div class="add-body panel_body">
    <div class="panel-hd">{{!dataForm.id ? $t('add') : $t('update')}}</div>
    <div class="addOrUpdatePanel" >
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" label-width="100px">
        <el-row type="flex" justify="start">
          <el-col :md="{span:13, offset: 2}">
            <el-form-item :label="$t('csmWorkOrder.workOrderTypeId')" prop="workOrderTypeId">
              <el-row type="flex" justify="center" :gutter="10" style="width: 100%" class="exceptionCodeClassFra">
                <el-col :span="23">
                  <el-row :gutter="10">
                    <el-col :span="8" style="min-width: 150px" v-for="(item, index) in baseData.workOrderTypeList" :key="index">
                      <el-radio v-model="dataForm.workOrderTypeId" :label="item.id">{{item.name}}</el-radio>
                    </el-col>
                  </el-row>
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item :label="$t('csmWorkOrder.waybillNo')" prop="waybillNo">
              <el-input v-model="dataForm.waybillNo" :placeholder="$t('csmWorkOrder.waybillNo')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('csmWorkOrder.problemStatement')" prop="problemStatement">
              <el-input type="textarea" v-model="dataForm.problemStatement" :placeholder="$t('csmWorkOrder.problemStatement')"></el-input>
            </el-form-item>
            <el-form-item prop="fileList">
              <btn-upload-attachment ref="btnUploadAttachment" :attachmentFiles.sync="fileList"></btn-upload-attachment>
            </el-form-item>
            <el-form-item :label="$t('csmWorkOrder.contactPerson')" prop="contactPerson">
              <el-input v-model="dataForm.contactPerson" :placeholder="$t('csmWorkOrder.contactPerson')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('csmWorkOrder.phone')" prop="phone">
              <el-input v-model="dataForm.phone" :placeholder="$t('csmWorkOrder.phone')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('label.warmPrompt')" prop="phone">
              <span>客服上班时间： 8:30-18:00，提交后客服将会在24小时内回复；（若工单在客服答复后一直未收到客户反馈，将在3天后自动处理完结成，处理完结成后请及时评价工单，以便提升我们的客服质量）</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div id="cs_FormFooter" class="el-form-footer">
        <el-button @click="cancelFn">{{ $t('cancel') }}</el-button>
        <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import debounce from 'lodash/debounce'
import baseDataApi from '@/api'
import baseData from '@/api/baseData'
import btnUploadAttachment from '@/components/upload/btn-upload-attachment'
export default {
  data () {
    return {
      dataForm: {
        id: '',
        workOrderTypeParentId: '',
        workOrderTypeId: '',
        waybillNo: '',
        problemStatement: '',
        contactPerson: '',
        phone: '',
        creatorType: 20
      },
      fileList: [],
      baseData: {
        workOrderTypeList: []
      },
      sizeLimit: 2,
      isBreak: false
    }
  },
  computed: {
    dataRule () {
      return {
        workOrderNo: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        workOrderTypeId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        // waybillNo: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        problemStatement: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        contactPerson: [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        phone: [
          { required: false, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init (typeId) {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.workOrderTypeParentId) {
          this.dataForm.workOrderTypeId = typeId
          this.getBaseData()
        }
      })
    },
    async getBaseData () {
      this.baseData.workOrderTypeList = await baseData(baseDataApi.workOrderTypeList, { parentId: this.dataForm.workOrderTypeParentId, status: 1, id: this.dataForm.workOrderTypeId }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let formData = new FormData()
        let isBreak = this.$refs.btnUploadAttachment.validFiles()
        if (isBreak) {
          return ''
        }
        this.fileList.forEach(file => {
          // 此处一定是append file.raw 上传文件只需维护fileList file.raw.name要加上
          formData.append('files', file.file)
        })
        formData.append('workOrderTypeParentId', this.dataForm.workOrderTypeParentId)
        formData.append('workOrderTypeId', this.dataForm.workOrderTypeId)
        formData.append('waybillNo', this.dataForm.waybillNo)
        formData.append('problemStatement', this.dataForm.problemStatement)
        formData.append('contactPerson', this.dataForm.contactPerson)
        formData.append('phone', this.dataForm.phone)
        formData.append('creatorType', this.dataForm.creatorType)
        this.$http[!this.dataForm.id ? 'post' : 'put']('/csm/workorder/', formData).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('cancelAddOrUpdate')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    // 取消
    cancelFn () {
      this.visible = false
      this.$emit('cancelAddOrUpdate')
    }
  },
  components: {
    btnUploadAttachment
  }
}
</script>
