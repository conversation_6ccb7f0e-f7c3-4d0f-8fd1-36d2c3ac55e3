<template>
  <div class="add-body panel_body">
    <div class="panel-hd">{{!dataForm.id ? $t('add') : $t('update')}}</div>
    <div class="addOrUpdatePanel" >
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" label-width="80px">
        <el-row type="flex" justify="start">
          <el-col :md="{span:13, offset: 2}">
            <el-form-item :label="$t('coSetDiyimportModuleDetail.customerId')" prop="customerId">
              <el-input v-model="dataForm.customerId" :placeholder="$t('coSetDiyimportModuleDetail.customerId')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coSetDiyimportModuleDetail.matchId')" prop="matchId">
              <el-input v-model="dataForm.matchId" :placeholder="$t('coSetDiyimportModuleDetail.matchId')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coSetDiyimportModuleDetail.matchDetailId')" prop="matchDetailId">
              <el-input v-model="dataForm.matchDetailId" :placeholder="$t('coSetDiyimportModuleDetail.matchDetailId')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coSetDiyimportModuleDetail.objectType')" prop="objectType">
              <el-input v-model="dataForm.objectType" :placeholder="$t('coSetDiyimportModuleDetail.objectType')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coSetDiyimportModuleDetail.moduleId')" prop="moduleId">
              <el-input v-model="dataForm.moduleId" :placeholder="$t('coSetDiyimportModuleDetail.moduleId')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coSetDiyimportModuleDetail.repeatableColumns')" prop="repeatableColumns">
              <el-input v-model="dataForm.repeatableColumns" :placeholder="$t('coSetDiyimportModuleDetail.repeatableColumns')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coSetDiyimportModuleDetail.fieldValue')" prop="fieldValue">
              <el-input v-model="dataForm.fieldValue" :placeholder="$t('coSetDiyimportModuleDetail.fieldValue')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coSetDiyimportModuleDetail.fieldName')" prop="fieldName">
              <el-input v-model="dataForm.fieldName" :placeholder="$t('coSetDiyimportModuleDetail.fieldName')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coSetDiyimportModuleDetail.digital')" prop="digital">
              <el-input v-model="dataForm.digital" :placeholder="$t('coSetDiyimportModuleDetail.digital')"></el-input>
            </el-form-item>
            <el-form-item :label="$t('coSetDiyimportModuleDetail.allowEmpty')" prop="allowEmpty">
              <el-input v-model="dataForm.allowEmpty" :placeholder="$t('coSetDiyimportModuleDetail.allowEmpty')"></el-input>
            </el-form-item>
          </el-col>

        </el-row>
      </el-form>
      <div id="cs_FormFooter" class="el-form-footer">
        <el-button @click="cancelFn">{{ $t('cancel') }}</el-button>
        <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      dataForm: {
        id: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: '',
        status: '',
        version: '',
        customerId: '',
        matchId: '',
        matchDetailId: '',
        objectType: '',
        moduleId: '',
        repeatableColumns: '',
        fieldValue: '',
        fieldName: '',
        digital: '',
        allowEmpty: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        creator: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        createDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        updater: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        updateDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        status: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        version: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        customerId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        matchId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        matchDetailId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        objectType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        moduleId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        repeatableColumns: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        fieldValue: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        fieldName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        digital: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        allowEmpty: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init () {
      this.$nextTick(() => {
        // 如果菜单浮动位置 需要初始化
        this.$footerScroll()
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/co/setdiyimportmoduledetail/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/co/setdiyimportmoduledetail/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    // 取消
    cancelFn () {
      this.visible = false
      this.$emit('cancelAddOrUpdate')
    }
  }
}
</script>
